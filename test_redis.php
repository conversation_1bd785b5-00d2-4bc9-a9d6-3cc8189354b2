<?php

echo "Testing Redis connection...\n";

try {
    // Test with Redis extension
    if (extension_loaded('redis')) {
        echo "Redis extension is loaded\n";
        $redis = new Redis();
        $redis->connect('redis', 6379);
        echo "Redis connection successful: " . $redis->ping() . "\n";
        $redis->close();
    } else {
        echo "Redis extension is NOT loaded\n";
    }
} catch (Exception $e) {
    echo "Redis connection failed: " . $e->getMessage() . "\n";
}

try {
    // Test with Predis
    require_once '/var/www/vendor/autoload.php';
    
    $client = new Predis\Client([
        'scheme' => 'tcp',
        'host'   => 'redis',
        'port'   => 6379,
    ]);
    
    echo "Predis connection successful: " . $client->ping() . "\n";
} catch (Exception $e) {
    echo "Predis connection failed: " . $e->getMessage() . "\n";
}

echo "Test completed.\n";
