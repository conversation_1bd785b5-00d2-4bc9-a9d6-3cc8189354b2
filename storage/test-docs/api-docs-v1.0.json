{"openapi": "3.0.3", "info": {"title": "ModularEcommerce API", "description": "ModularEcommerce API Documentation", "version": "1.0", "contact": {"name": "API Support", "email": "<EMAIL>", "url": "https://modularecommerce.com/support"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "x-version-info": {"status": "stable", "released_at": "2024-01-01", "deprecated_at": null, "sunset_at": null, "description": "Initial API version"}}, "servers": [{"url": "http://localhost:8000/api", "description": "Production server"}], "paths": {"horizon/api/stats": {"get": {"summary": "Dashboardstats Index", "description": "Dashboardstats Index", "operationId": "horizon_stats_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/workload": {"get": {"summary": "Workload Index", "description": "Workload Index", "operationId": "horizon_workload_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/masters": {"get": {"summary": "Mastersupervisor Index", "description": "Mastersupervisor Index", "operationId": "horizon_masters_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/monitoring": {"get": {"summary": "Monitoring Index", "description": "Monitoring Index", "operationId": "horizon_monitoring_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}, "post": {"summary": "Monitoring Store", "description": "Monitoring Store", "operationId": "horizon_monitoring_store", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "horizon/api/monitoring/{tag}": {"get": {"summary": "Monitoring Paginate", "description": "Monitoring Paginate", "operationId": "horizon_monitoring-tag_paginate", "tags": ["General"], "parameters": [{"name": "tag", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Monitoring Destroy", "description": "Monitoring Destroy", "operationId": "horizon_monitoring-tag_destroy", "tags": ["General"], "parameters": [{"name": "tag", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/metrics/jobs": {"get": {"summary": "Jobmetrics Index", "description": "Jobmetrics Index", "operationId": "horizon_jobs-metrics_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/metrics/jobs/{id}": {"get": {"summary": "Jobmetrics Show", "description": "Jobmetrics Show", "operationId": "horizon_jobs-metrics_show", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/metrics/queues": {"get": {"summary": "Queuemetrics Index", "description": "Queuemetrics Index", "operationId": "horizon_queues-metrics_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/metrics/queues/{id}": {"get": {"summary": "Queuemetrics Show", "description": "Queuemetrics Show", "operationId": "horizon_queues-metrics_show", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/batches": {"get": {"summary": "Batches Index", "description": "Batches Index", "operationId": "horizon_jobs-batches_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/batches/{id}": {"get": {"summary": "Batches Show", "description": "Batches Show", "operationId": "horizon_jobs-batches_show", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/batches/retry/{id}": {"post": {"summary": "Batches Retry", "description": "Batches Retry", "operationId": "horizon_jobs-batches_retry", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/pending": {"get": {"summary": "Pendingjobs Index", "description": "Pendingjobs Index", "operationId": "horizon_pending-jobs_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/completed": {"get": {"summary": "Completedjobs Index", "description": "Completedjobs Index", "operationId": "horizon_completed-jobs_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/silenced": {"get": {"summary": "Silencedjobs Index", "description": "Silencedjobs Index", "operationId": "horizon_silenced-jobs_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/failed": {"get": {"summary": "Failedjobs Index", "description": "Failedjobs Index", "operationId": "horizon_failed-jobs_index", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/failed/{id}": {"get": {"summary": "Failedjobs Show", "description": "Failedjobs Show", "operationId": "horizon_failed-jobs_show", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/retry/{id}": {"post": {"summary": "Retry Store", "description": "Retry Store", "operationId": "horizon_retry-jobs_show", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "horizon/api/jobs/{id}": {"get": {"summary": "Jobs Show", "description": "Jobs Show", "operationId": "horizon_jobs_show", "tags": ["General"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/user": {"get": {"summary": "Get api/user", "description": "Get api/user", "operationId": "get_api_user", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/cart/count": {"get": {"summary": "<PERSON><PERSON>unt", "description": "<PERSON><PERSON>unt", "operationId": "api_cart_count", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/products/search": {"get": {"summary": "Product Search", "description": "Product Search", "operationId": "get_api_products_search", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/products": {"get": {"summary": "Product Index", "description": "Product Index", "operationId": "products_index", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Product Store", "description": "Product Store", "operationId": "products_store", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/products/{product}": {"put": {"summary": "Product Update", "description": "Product Update", "operationId": "products_update", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "patch": {"summary": "Product Update", "description": "Product Update", "operationId": "products_update", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Product Destroy", "description": "Product Destroy", "operationId": "products_destroy", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}, "get": {"summary": "Product Show", "description": "Product Show", "operationId": "products_show", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/api-products/{id}": {"get": {"summary": "Product Show", "description": "Product Show", "operationId": "get_api_api-products_id", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/products/{id}/details": {"get": {"summary": "Product Getproductdetails", "description": "Product Getproductdetails", "operationId": "get_api_products_id_details", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/shipping/available-methods": {"post": {"summary": "Shipping Getavailablemethods", "description": "Shipping Getavailablemethods", "operationId": "post_api_shipping_available-methods", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/shipping/calculate-cost": {"post": {"summary": "Shipping Calculateshippingcost", "description": "Shipping Calculateshippingcost", "operationId": "post_api_shipping_calculate-cost", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/search/autocomplete": {"get": {"summary": "Search Autocomplete", "description": "Search Autocomplete", "operationId": "search_autocomplete", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/my-orders/active": {"get": {"summary": "Customerorder Active", "description": "Customerorder Active", "operationId": "customer_orders_api_active", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/my-orders/completed": {"get": {"summary": "Customerorder Completed", "description": "Customerorder Completed", "operationId": "customer_orders_api_completed", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/my-orders/cancelled": {"get": {"summary": "Customerorder Cancelled", "description": "Customerorder Cancelled", "operationId": "customer_orders_api_cancelled", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/my-orders/summary": {"get": {"summary": "Customerorder Summary", "description": "Customerorder Summary", "operationId": "customer_orders_api_summary", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "admin/admin/api/orders/pending": {"get": {"summary": "Order Pending", "description": "Order Pending", "operationId": "admin_orders_api_pending", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "admin/admin/api/orders/processing": {"get": {"summary": "Order Processing", "description": "Order Processing", "operationId": "admin_orders_api_processing", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "admin/admin/api/orders/ready-to-ship": {"get": {"summary": "Order Readytoship", "description": "Order Readytoship", "operationId": "admin_orders_api_ready-to-ship", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "admin/admin/api/orders/search": {"get": {"summary": "Order Search", "description": "Order Search", "operationId": "admin_orders_api_search", "tags": ["Orders"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/upload-image": {"post": {"summary": "Imageupload Upload", "description": "Imageupload Upload", "operationId": "post_api_upload-image", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/favorites/add": {"post": {"summary": "Favorite Add", "description": "Favorite Add", "operationId": "post_api_favorites_add", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/favorites/remove": {"post": {"summary": "Favorite Remove", "description": "Favorite Remove", "operationId": "post_api_favorites_remove", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/favorites": {"get": {"summary": "Favorite Getuserfavorites", "description": "Favorite Getuserfavorites", "operationId": "get_api_favorites", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/favorites/check": {"get": {"summary": "Favorite Checkfavoritestatus", "description": "Favorite Checkfavoritestatus", "operationId": "get_api_favorites_check", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "api/shipping-companies": {"get": {"summary": "Get api/shipping-companies", "description": "Get api/shipping-companies", "operationId": "get_api_shipping-companies", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/shipping/methods": {"get": {"summary": "Shipping Getmethods", "description": "Shipping Getmethods", "operationId": "get_api_shipping_methods", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/shipping/zones": {"get": {"summary": "Shipping Getzones", "description": "Shipping Getzones", "operationId": "get_api_shipping_zones", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/check-payment-status": {"get": {"summary": "Paymentstatus <PERSON>", "description": "Paymentstatus <PERSON>", "operationId": "get_api_check-payment-status", "tags": ["Payment"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/check-order-status": {"get": {"summary": "Payment Checkorderstatus", "description": "Payment Checkorderstatus", "operationId": "api_check-order-status", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/resume-payment": {"get": {"summary": "Payment Resumepayment", "description": "Payment Resumepayment", "operationId": "api_resume-payment", "tags": ["Payment"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/cancel-order": {"post": {"summary": "Payment Cancelorder", "description": "Payment Cancelorder", "operationId": "api_cancel-order", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/locations/countries": {"get": {"summary": "Location Countries", "description": "Location Countries", "operationId": "get_api_locations_countries", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/locations/countries/{countryId}/states": {"get": {"summary": "Location States", "description": "Location States", "operationId": "get_api_locations_countries_countryId_states", "tags": ["General"], "parameters": [{"name": "countryId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/locations/turkiye/states": {"get": {"summary": "Location Turkeystates", "description": "Location Turkeystates", "operationId": "get_api_locations_turkiye_states", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/locations/states/{stateId}/cities": {"get": {"summary": "Location Cities", "description": "Location Cities", "operationId": "get_api_locations_states_stateId_cities", "tags": ["General"], "parameters": [{"name": "stateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/locations/turkiye/states/{stateId}/cities": {"get": {"summary": "Location Turkeycities", "description": "Location Turkeycities", "operationId": "get_api_locations_turkiye_states_stateId_cities", "tags": ["General"], "parameters": [{"name": "stateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/locations/refresh-cache": {"post": {"summary": "Location Refreshcache", "description": "Location Refreshcache", "operationId": "post_api_locations_refresh-cache", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/categories": {"post": {"summary": "Category Store", "description": "Category Store", "operationId": "categories_store", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/categories/{category}": {"get": {"summary": "Category Show", "description": "Category Show", "operationId": "categories_show", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"bearerAuth": []}]}, "put": {"summary": "Category Update", "description": "Category Update", "operationId": "categories_update", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "patch": {"summary": "Category Update", "description": "Category Update", "operationId": "categories_update", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Category Destroy", "description": "Category Destroy", "operationId": "categories_destroy", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}}, "docs/api/versions": {"get": {"summary": "Get docs/api/versions", "description": "Get docs/api/versions", "operationId": "api_docs_api_versions", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "docs/api/formats": {"get": {"summary": "Get docs/api/formats", "description": "Get docs/api/formats", "operationId": "api_docs_api_formats", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "docs/api/stats": {"get": {"summary": "Get docs/api/stats", "description": "Get docs/api/stats", "operationId": "api_docs_api_stats", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "docs/api/search": {"get": {"summary": "Get docs/api/search", "description": "Get docs/api/search", "operationId": "api_docs_api_search", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/products": {"get": {"summary": "Product Index", "description": "Product Index", "operationId": "api_v1_0_products_index", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Product Store", "description": "Product Store", "operationId": "api_v1_0_products_store", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/v1.0/products/{id}": {"get": {"summary": "Product Show", "description": "Product Show", "operationId": "api_v1_0_products_show", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "put": {"summary": "Product Update", "description": "Product Update", "operationId": "api_v1_0_products_update", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Product Destroy", "description": "Product Destroy", "operationId": "api_v1_0_products_destroy", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}}, "api/v1.0/products/{id}/details": {"get": {"summary": "Product Getproductdetails", "description": "Product Getproductdetails", "operationId": "api_v1_0_products_details", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/products/search": {"get": {"summary": "Product Search", "description": "Product Search", "operationId": "api_v1_0_products_search", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/categories": {"get": {"summary": "Category Index", "description": "Category Index", "operationId": "api_v1_0_categories_index", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Category Store", "description": "Category Store", "operationId": "api_v1_0_categories_store", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/v1.0/categories/tree": {"get": {"summary": "Category Tree", "description": "Category Tree", "operationId": "api_v1_0_categories_tree", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/categories/{id}": {"get": {"summary": "Category Show", "description": "Category Show", "operationId": "api_v1_0_categories_show", "tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "put": {"summary": "Category Update", "description": "Category Update", "operationId": "api_v1_0_categories_update", "tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Category Destroy", "description": "Category Destroy", "operationId": "api_v1_0_categories_destroy", "tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}}, "api/v1.0/locations/countries": {"get": {"summary": "Location Countries", "description": "Location Countries", "operationId": "api_v1_0_locations_countries", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/locations/states/{countryId}": {"get": {"summary": "Location States", "description": "Location States", "operationId": "api_v1_0_locations_states", "tags": ["General"], "parameters": [{"name": "countryId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/locations/cities/{stateId}": {"get": {"summary": "Location Cities", "description": "Location Cities", "operationId": "api_v1_0_locations_cities", "tags": ["General"], "parameters": [{"name": "stateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/locations/turkey/cities/{stateId}": {"get": {"summary": "Location Turkeycities", "description": "Location Turkeycities", "operationId": "api_v1_0_locations_turkey_cities", "tags": ["General"], "parameters": [{"name": "stateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/utils/upload-image": {"post": {"summary": "Imageupload Upload", "description": "Imageupload Upload", "operationId": "api_v1_0_utils_upload_image", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/v1.0/cart/count": {"get": {"summary": "<PERSON><PERSON>unt", "description": "<PERSON><PERSON>unt", "operationId": "api_v1_0_cart_count", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.0/locations/refresh-cache": {"post": {"summary": "Location Refreshcache", "description": "Location Refreshcache", "operationId": "api_v1_0_locations_refresh", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/v1.1/products": {"get": {"summary": "Product Index", "description": "Product Index", "operationId": "api_v1_1_products_index", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Product Store", "description": "Product Store", "operationId": "api_v1_1_products_store", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/v1.1/products/{id}": {"get": {"summary": "Product Show", "description": "Product Show", "operationId": "api_v1_1_products_show", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "put": {"summary": "Product Update", "description": "Product Update", "operationId": "api_v1_1_products_update", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Product Destroy", "description": "Product Destroy", "operationId": "api_v1_1_products_destroy", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}}, "api/v1.1/products/{id}/details": {"get": {"summary": "Product Getproductdetails", "description": "Product Getproductdetails", "operationId": "api_v1_1_products_details", "tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/products/search": {"get": {"summary": "Product Search", "description": "Product Search", "operationId": "api_v1_1_products_search", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/categories": {"get": {"summary": "Category Index", "description": "Category Index", "operationId": "api_v1_1_categories_index", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Category Store", "description": "Category Store", "operationId": "api_v1_1_categories_store", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/v1.1/categories/tree": {"get": {"summary": "Category Tree", "description": "Category Tree", "operationId": "api_v1_1_categories_tree", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/categories/{id}": {"get": {"summary": "Category Show", "description": "Category Show", "operationId": "api_v1_1_categories_show", "tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "put": {"summary": "Category Update", "description": "Category Update", "operationId": "api_v1_1_categories_update", "tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}, "delete": {"summary": "Category Destroy", "description": "Category Destroy", "operationId": "api_v1_1_categories_destroy", "tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "204": {"description": "No content"}}, "security": [{"bearerAuth": []}]}}, "api/v1.1/locations/countries": {"get": {"summary": "Location Countries", "description": "Location Countries", "operationId": "api_v1_1_locations_countries", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/locations/states/{countryId}": {"get": {"summary": "Location States", "description": "Location States", "operationId": "api_v1_1_locations_states", "tags": ["General"], "parameters": [{"name": "countryId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/locations/cities/{stateId}": {"get": {"summary": "Location Cities", "description": "Location Cities", "operationId": "api_v1_1_locations_cities", "tags": ["General"], "parameters": [{"name": "stateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/locations/turkey/cities/{stateId}": {"get": {"summary": "Location Turkeycities", "description": "Location Turkeycities", "operationId": "api_v1_1_locations_turkey_cities", "tags": ["General"], "parameters": [{"name": "stateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/utils/upload-image": {"post": {"summary": "Imageupload Upload", "description": "Imageupload Upload", "operationId": "api_v1_1_utils_upload_image", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/v1.1/cart/count": {"get": {"summary": "<PERSON><PERSON>unt", "description": "<PERSON><PERSON>unt", "operationId": "api_v1_1_cart_count", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1.1/locations/refresh-cache": {"post": {"summary": "Location Refreshcache", "description": "Location Refreshcache", "operationId": "api_v1_1_locations_refresh", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "security": [{"bearerAuth": []}]}}, "api/version": {"get": {"summary": "Get api/version", "description": "Get api/version", "operationId": "api_version_info", "tags": ["General"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1/categories/tree": {"get": {"summary": "Category Tree", "description": "Category Tree", "operationId": "get_api_v1_categories_tree", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}}, "api/v1/categories": {"get": {"summary": "Category Index", "description": "Category Index", "operationId": "categories_index", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Category Store", "description": "Category Store", "operationId": "categories_store", "tags": ["Categories"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/v1/categories/{category}": {"get": {"summary": "Category Show", "description": "Category Show", "operationId": "categories_show", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "put": {"summary": "Category Update", "description": "Category Update", "operationId": "categories_update", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}, "patch": {"summary": "Category Update", "description": "Category Update", "operationId": "categories_update", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}, "delete": {"summary": "Category Destroy", "description": "Category Destroy", "operationId": "categories_destroy", "tags": ["Categories"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "204": {"description": "No content"}}}}, "api/v1/products": {"get": {"summary": "Product Index", "description": "Product Index", "operationId": "products_index", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "post": {"summary": "Product Store", "description": "Product Store", "operationId": "products_store", "tags": ["Products"], "parameters": [], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "201": {"$ref": "#/components/responses/Success"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}}, "api/v1/products/{product}": {"get": {"summary": "Product Show", "description": "Product Show", "operationId": "products_show", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}}, "put": {"summary": "Product Update", "description": "Product Update", "operationId": "products_update", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}, "patch": {"summary": "Product Update", "description": "Product Update", "operationId": "products_update", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}}, "delete": {"summary": "Product Destroy", "description": "Product Destroy", "operationId": "products_destroy", "tags": ["Products"], "parameters": [{"name": "product", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "422": {"$ref": "#/components/responses/ValidationError"}, "204": {"description": "No content"}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "meta": {"type": "object"}, "message": {"type": "string"}}, "required": ["success"]}, "ApiError": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "errors": {"type": "object"}, "meta": {"type": "object"}}, "required": ["success", "message"]}, "PaginationMeta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"current_page": {"type": "integer"}, "per_page": {"type": "integer"}, "total": {"type": "integer"}, "last_page": {"type": "integer"}, "from": {"type": "integer"}, "to": {"type": "integer"}}}}}}, "responses": {"Success": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "Error": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiError"}}}}, "ValidationError": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiError"}}}}, "Unauthorized": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiError"}}}}, "NotFound": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiError"}}}}}, "parameters": {"page": {"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "minimum": 1, "default": 1}}, "per_page": {"name": "per_page", "in": "query", "description": "Items per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 15}}, "sort": {"name": "sort", "in": "query", "description": "Sort field", "schema": {"type": "string"}}, "order": {"name": "order", "in": "query", "description": "Sort order", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "asc"}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}}, "security": [], "tags": [{"name": "Products", "description": "Product management"}, {"name": "Categories", "description": "Category management"}, {"name": "Orders", "description": "Order management"}, {"name": "Users", "description": "User management"}, {"name": "Authentication", "description": "Authentication endpoints"}, {"name": "Payment", "description": "Payment processing"}]}