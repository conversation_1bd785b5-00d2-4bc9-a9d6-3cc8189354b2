-- MySQL dump 10.13  Distrib 8.0.41, for Linux (x86_64)
--
-- Host: localhost    Database: world
-- ------------------------------------------------------
-- Server version	8.0.41-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `subregions`
--

DROP TABLE IF EXISTS `subregions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subregions` (
  `id` mediumint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `translations` text COLLATE utf8mb4_unicode_ci,
  `region_id` mediumint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `flag` tinyint(1) NOT NULL DEFAULT '1',
  `wikiDataId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Rapid API GeoDB Cities',
  PRIMARY KEY (`id`),
  KEY `subregion_continent` (`region_id`),
  CONSTRAINT `subregion_continent_final` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subregions`
--

/*!40000 ALTER TABLE `subregions` DISABLE KEYS */;
INSERT INTO `subregions` VALUES (1,'Northern Africa','{\"ko\":\"북아프리카\",\"pt\":\"Norte de África\",\"nl\":\"Noord-Afrika\",\"hr\":\"Sjeverna Afrika\",\"fa\":\"شمال آفریقا\",\"de\":\"Nordafrika\",\"es\":\"Norte de África\",\"fr\":\"Afrique du Nord\",\"ja\":\"北アフリカ\",\"it\":\"Nordafrica\",\"zh-CN\":\"北部非洲\",\"ru\":\"Северная Африка\",\"uk\":\"Північна Африка\",\"pl\":\"Afryka Północna\"}',1,'2023-08-14 10:41:03','2023-08-24 23:40:23',1,'Q27381'),(2,'Middle Africa','{\"ko\":\"중앙아프리카\",\"pt\":\"África Central\",\"nl\":\"Centraal-Afrika\",\"hr\":\"Srednja Afrika\",\"fa\":\"مرکز آفریقا\",\"de\":\"Zentralafrika\",\"es\":\"África Central\",\"fr\":\"Afrique centrale\",\"ja\":\"中部アフリカ\",\"it\":\"Africa centrale\",\"zh-CN\":\"中部非洲\",\"ru\":\"Средняя Африка\",\"uk\":\"Середня Африка\",\"pl\":\"Afryka Środkowa\"}',1,'2023-08-14 10:41:03','2023-08-24 23:52:09',1,'Q27433'),(3,'Western Africa','{\"ko\":\"서아프리카\",\"pt\":\"África Ocidental\",\"nl\":\"West-Afrika\",\"hr\":\"Zapadna Afrika\",\"fa\":\"غرب آفریقا\",\"de\":\"Westafrika\",\"es\":\"África Occidental\",\"fr\":\"Afrique de l\'Ouest\",\"ja\":\"西アフリカ\",\"it\":\"Africa occidentale\",\"zh-CN\":\"西非\",\"ru\":\"Западная Африка\",\"uk\":\"Західна Африка\",\"pl\":\"Afryka Zachodnia\"}',1,'2023-08-14 10:41:03','2023-08-24 23:52:09',1,'Q4412'),(4,'Eastern Africa','{\"ko\":\"동아프리카\",\"pt\":\"África Oriental\",\"nl\":\"Oost-Afrika\",\"hr\":\"Istočna Afrika\",\"fa\":\"شرق آفریقا\",\"de\":\"Ostafrika\",\"es\":\"África Oriental\",\"fr\":\"Afrique de l\'Est\",\"ja\":\"東アフリカ\",\"it\":\"Africa orientale\",\"zh-CN\":\"东部非洲\",\"ru\":\"Восточная Африка\",\"uk\":\"Східна Африка\",\"pl\":\"Afryka Wschodnia\"}',1,'2023-08-14 10:41:03','2023-08-24 23:52:10',1,'Q27407'),(5,'Southern Africa','{\"ko\":\"남아프리카\",\"pt\":\"África Austral\",\"nl\":\"Zuidelijk Afrika\",\"hr\":\"Južna Afrika\",\"fa\":\"جنوب آفریقا\",\"de\":\"Südafrika\",\"es\":\"África austral\",\"fr\":\"Afrique australe\",\"ja\":\"南部アフリカ\",\"it\":\"Africa australe\",\"zh-CN\":\"南部非洲\",\"ru\":\"Южная Африка\",\"uk\":\"Південна Африка\",\"pl\":\"Afryka Południowa\"}',1,'2023-08-14 10:41:03','2023-08-24 23:52:10',1,'Q27394'),(6,'Northern America','{\"ko\":\"북미\",\"pt\":\"América Setentrional\",\"nl\":\"Noord-Amerika\",\"fa\":\"شمال آمریکا\",\"de\":\"Nordamerika\",\"es\":\"América Norteña\",\"fr\":\"Amérique septentrionale\",\"ja\":\"北部アメリカ\",\"it\":\"America settentrionale\",\"zh-CN\":\"北美地區\",\"ru\":\"Северная Америка\",\"uk\":\"Північна Америка\",\"pl\":\"Ameryka Północna\"}',2,'2023-08-14 10:41:03','2023-08-24 23:52:10',1,'Q2017699'),(7,'Caribbean','{\"ko\":\"카리브\",\"pt\":\"Caraíbas\",\"nl\":\"Caraïben\",\"hr\":\"Karibi\",\"fa\":\"کارائیب\",\"de\":\"Karibik\",\"es\":\"Caribe\",\"fr\":\"Caraïbes\",\"ja\":\"カリブ海地域\",\"it\":\"Caraibi\",\"zh-CN\":\"加勒比地区\",\"ru\":\"Карибы\",\"uk\":\"Кариби\",\"pl\":\"Karaiby\"}',2,'2023-08-14 10:41:03','2024-06-16 04:42:18',1,'Q664609'),(8,'South America','{\"ko\":\"남아메리카\",\"pt\":\"América do Sul\",\"nl\":\"Zuid-Amerika\",\"hr\":\"Južna Amerika\",\"fa\":\"آمریکای جنوبی\",\"de\":\"Südamerika\",\"es\":\"América del Sur\",\"fr\":\"Amérique du Sud\",\"ja\":\"南アメリカ\",\"it\":\"America meridionale\",\"zh-CN\":\"南美洲\",\"ru\":\"Южная Америка\",\"uk\":\"Південна Америка\",\"pl\":\"Ameryka Południowa\"}',2,'2023-08-14 10:41:03','2023-08-24 23:52:10',1,'Q18'),(9,'Central America','{\"ko\":\"중앙아메리카\",\"pt\":\"América Central\",\"nl\":\"Centraal-Amerika\",\"hr\":\"Srednja Amerika\",\"fa\":\"آمریکای مرکزی\",\"de\":\"Zentralamerika\",\"es\":\"América Central\",\"fr\":\"Amérique centrale\",\"ja\":\"中央アメリカ\",\"it\":\"America centrale\",\"zh-CN\":\"中美洲\",\"ru\":\"Центральная Америка\",\"uk\":\"Центральна Америка\",\"pl\":\"Ameryka Środkowa\"}',2,'2023-08-14 10:41:03','2023-08-24 23:52:11',1,'Q27611'),(10,'Central Asia','{\"ko\":\"중앙아시아\",\"pt\":\"Ásia Central\",\"nl\":\"Centraal-Azië\",\"hr\":\"Srednja Azija\",\"fa\":\"آسیای میانه\",\"de\":\"Zentralasien\",\"es\":\"Asia Central\",\"fr\":\"Asie centrale\",\"ja\":\"中央アジア\",\"it\":\"Asia centrale\",\"zh-CN\":\"中亚\",\"ru\":\"Центральная Азия\",\"uk\":\"Центральна Азія\",\"pl\":\"Azja Środkowa\"}',3,'2023-08-14 10:41:03','2023-08-24 23:52:11',1,'Q27275'),(11,'Western Asia','{\"ko\":\"서아시아\",\"pt\":\"Sudoeste Asiático\",\"nl\":\"Zuidwest-Azië\",\"hr\":\"Jugozapadna Azija\",\"fa\":\"غرب آسیا\",\"de\":\"Vorderasien\",\"es\":\"Asia Occidental\",\"fr\":\"Asie de l\'Ouest\",\"ja\":\"西アジア\",\"it\":\"Asia occidentale\",\"zh-CN\":\"西亚\",\"ru\":\"Западная Азия\",\"uk\":\"Західна Азія\",\"pl\":\"Azja Zachodnia\"}',3,'2023-08-14 10:41:03','2023-08-24 23:52:11',1,'Q27293'),(12,'Eastern Asia','{\"ko\":\"동아시아\",\"pt\":\"Ásia Oriental\",\"nl\":\"Oost-Azië\",\"hr\":\"Istočna Azija\",\"fa\":\"شرق آسیا\",\"de\":\"Ostasien\",\"es\":\"Asia Oriental\",\"fr\":\"Asie de l\'Est\",\"ja\":\"東アジア\",\"it\":\"Asia orientale\",\"zh-CN\":\"東亞\",\"ru\":\"Восточная Азия\",\"uk\":\"Східна Азія\",\"pl\":\"Azja Wschodnia\"}',3,'2023-08-14 10:41:03','2023-08-24 23:52:11',1,'Q27231'),(13,'South-Eastern Asia','{\"ko\":\"동남아시아\",\"pt\":\"Sudeste Asiático\",\"nl\":\"Zuidoost-Azië\",\"hr\":\"Jugoistočna Azija\",\"fa\":\"جنوب شرق آسیا\",\"de\":\"Südostasien\",\"es\":\"Sudeste Asiático\",\"fr\":\"Asie du Sud-Est\",\"ja\":\"東南アジア\",\"it\":\"Sud-est asiatico\",\"zh-CN\":\"东南亚\",\"ru\":\"Юго-Восточная Азия\",\"uk\":\"Південно-Східна Азія\",\"pl\":\"Azja Południowo-Wschodnia\"}',3,'2023-08-14 10:41:03','2023-08-24 23:52:12',1,'Q11708'),(14,'Southern Asia','{\"ko\":\"남아시아\",\"pt\":\"Ásia Meridional\",\"nl\":\"Zuid-Azië\",\"hr\":\"Južna Azija\",\"fa\":\"جنوب آسیا\",\"de\":\"Südasien\",\"es\":\"Asia del Sur\",\"fr\":\"Asie du Sud\",\"ja\":\"南アジア\",\"it\":\"Asia meridionale\",\"zh-CN\":\"南亚\",\"ru\":\"Южная Азия\",\"uk\":\"Південна Азія\",\"pl\":\"Azja Południowa\"}',3,'2023-08-14 10:41:03','2023-08-24 23:52:12',1,'Q771405'),(15,'Eastern Europe','{\"ko\":\"동유럽\",\"pt\":\"Europa de Leste\",\"nl\":\"Oost-Europa\",\"hr\":\"Istočna Europa\",\"fa\":\"شرق اروپا\",\"de\":\"Osteuropa\",\"es\":\"Europa Oriental\",\"fr\":\"Europe de l\'Est\",\"ja\":\"東ヨーロッパ\",\"it\":\"Europa orientale\",\"zh-CN\":\"东欧\",\"ru\":\"Восточная Европа\",\"uk\":\"Східна Європа\",\"pl\":\"Europa Wschodnia\"}',4,'2023-08-14 10:41:03','2023-08-24 23:52:12',1,'Q27468'),(16,'Southern Europe','{\"ko\":\"남유럽\",\"pt\":\"Europa meridional\",\"nl\":\"Zuid-Europa\",\"hr\":\"Južna Europa\",\"fa\":\"جنوب اروپا\",\"de\":\"Südeuropa\",\"es\":\"Europa del Sur\",\"fr\":\"Europe du Sud\",\"ja\":\"南ヨーロッパ\",\"it\":\"Europa meridionale\",\"zh-CN\":\"南欧\",\"ru\":\"Южная Европа\",\"uk\":\"Південна Європа\",\"pl\":\"Europa Południowa\"}',4,'2023-08-14 10:41:03','2023-08-24 23:52:12',1,'Q27449'),(17,'Western Europe','{\"ko\":\"서유럽\",\"pt\":\"Europa Ocidental\",\"nl\":\"West-Europa\",\"hr\":\"Zapadna Europa\",\"fa\":\"غرب اروپا\",\"de\":\"Westeuropa\",\"es\":\"Europa Occidental\",\"fr\":\"Europe de l\'Ouest\",\"ja\":\"西ヨーロッパ\",\"it\":\"Europa occidentale\",\"zh-CN\":\"西欧\",\"ru\":\"Западная Европа\",\"uk\":\"Західна Європа\",\"pl\":\"Europa Zachodnia\"}',4,'2023-08-14 10:41:03','2023-08-24 23:52:12',1,'Q27496'),(18,'Northern Europe','{\"ko\":\"북유럽\",\"pt\":\"Europa Setentrional\",\"nl\":\"Noord-Europa\",\"hr\":\"Sjeverna Europa\",\"fa\":\"شمال اروپا\",\"de\":\"Nordeuropa\",\"es\":\"Europa del Norte\",\"fr\":\"Europe du Nord\",\"ja\":\"北ヨーロッパ\",\"it\":\"Europa settentrionale\",\"zh-CN\":\"北歐\",\"ru\":\"Северная Европа\",\"uk\":\"Північна Європа\",\"pl\":\"Europa Północna\"}',4,'2023-08-14 10:41:03','2023-08-24 23:52:13',1,'Q27479'),(19,'Australia and New Zealand','{\"ko\":\"오스트랄라시아\",\"pt\":\"Australásia\",\"nl\":\"Australazië\",\"hr\":\"Australazija\",\"fa\":\"استرالزی\",\"de\":\"Australasien\",\"es\":\"Australasia\",\"fr\":\"Australasie\",\"ja\":\"オーストララシア\",\"it\":\"Australasia\",\"zh-CN\":\"澳大拉西亞\",\"ru\":\"Австралия и Новая Зеландия\",\"uk\":\"Австралія та Нова Зеландія\",\"pl\":\"Australia i Nowa Zelandia\"}',5,'2023-08-14 10:41:03','2023-08-24 23:52:13',1,'Q45256'),(20,'Melanesia','{\"ko\":\"멜라네시아\",\"pt\":\"Melanésia\",\"nl\":\"Melanesië\",\"hr\":\"Melanezija\",\"fa\":\"ملانزی\",\"de\":\"Melanesien\",\"es\":\"Melanesia\",\"fr\":\"Mélanésie\",\"ja\":\"メラネシア\",\"it\":\"Melanesia\",\"zh-CN\":\"美拉尼西亚\",\"ru\":\"Меланезия\",\"uk\":\"Меланезія\",\"pl\":\"Melanezja\"}',5,'2023-08-14 10:41:03','2023-08-24 23:52:13',1,'Q37394'),(21,'Micronesia','{\"ko\":\"미크로네시아\",\"pt\":\"Micronésia\",\"nl\":\"Micronesië\",\"hr\":\"Mikronezija\",\"fa\":\"میکرونزی\",\"de\":\"Mikronesien\",\"es\":\"Micronesia\",\"fr\":\"Micronésie\",\"ja\":\"ミクロネシア\",\"it\":\"Micronesia\",\"zh-CN\":\"密克罗尼西亚群岛\",\"ru\":\"Микронезия\",\"uk\":\"Мікронезія\",\"pl\":\"Mikronezja\"}',5,'2023-08-14 10:41:03','2023-08-24 23:52:13',1,'Q3359409'),(22,'Polynesia','{\"ko\":\"폴리네시아\",\"pt\":\"Polinésia\",\"nl\":\"Polynesië\",\"hr\":\"Polinezija\",\"fa\":\"پلی‌نزی\",\"de\":\"Polynesien\",\"es\":\"Polinesia\",\"fr\":\"Polynésie\",\"ja\":\"ポリネシア\",\"it\":\"Polinesia\",\"zh-CN\":\"玻里尼西亞\",\"ru\":\"Полинезия\",\"uk\":\"Полінезія\",\"pl\":\"Polinezja\"}',5,'2023-08-14 10:41:03','2023-08-24 23:52:13',1,'Q35942');
/*!40000 ALTER TABLE `subregions` ENABLE KEYS */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-04-01  8:59:15
