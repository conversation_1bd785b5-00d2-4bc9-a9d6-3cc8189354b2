{"private": true, "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.25.9", "@tailwindcss/vite": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.5", "vite": "^6.2.4"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@inertiajs/inertia": "^0.11.1", "@inertiajs/react": "^2.0.8", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@vitejs/plugin-react": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "laravel-echo": "^1.16.1", "lucide-react": "^0.503.0", "pusher-js": "^8.4.0-rc2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0"}}