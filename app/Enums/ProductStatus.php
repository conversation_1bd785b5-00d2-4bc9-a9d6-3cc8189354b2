<?php

namespace App\Enums;

enum ProductStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case DRAFT = 'draft';
    case ARCHIVED = 'archived';
    case OUT_OF_STOCK = 'out_of_stock';
    case DISCONTINUED = 'discontinued';

    /**
     * Get all statuses as array
     */
    public static function toArray(): array
    {
        return [
            self::ACTIVE->value => 'Aktif',
            self::INACTIVE->value => 'Pasif',
            self::DRAFT->value => 'Taslak',
            self::ARCHIVED->value => 'Arşivlendi',
            self::OUT_OF_STOCK->value => 'Stokta Yok',
            self::DISCONTINUED->value => 'Üretimi Durduruldu',
        ];
    }

    /**
     * Get status label
     */
    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Aktif',
            self::INACTIVE => 'Pasif',
            self::DRAFT => 'Taslak',
            self::ARCHIVED => 'Arşivlendi',
            self::OUT_OF_STOCK => 'Stokta Yok',
            self::DISCONTINUED => 'Üretimi Durduruldu',
        };
    }

    /**
     * Get status color
     */
    public function color(): string
    {
        return match($this) {
            self::ACTIVE => 'green',
            self::INACTIVE => 'gray',
            self::DRAFT => 'yellow',
            self::ARCHIVED => 'blue',
            self::OUT_OF_STOCK => 'red',
            self::DISCONTINUED => 'purple',
        };
    }

    /**
     * Check if product is visible to customers
     */
    public function isVisible(): bool
    {
        return match($this) {
            self::ACTIVE => true,
            self::INACTIVE, self::DRAFT, self::ARCHIVED, self::OUT_OF_STOCK, self::DISCONTINUED => false,
        };
    }

    /**
     * Check if product is sellable
     */
    public function isSellable(): bool
    {
        return match($this) {
            self::ACTIVE => true,
            self::INACTIVE, self::DRAFT, self::ARCHIVED, self::OUT_OF_STOCK, self::DISCONTINUED => false,
        };
    }

    /**
     * Check if product can be edited
     */
    public function isEditable(): bool
    {
        return match($this) {
            self::ACTIVE, self::INACTIVE, self::DRAFT => true,
            self::ARCHIVED, self::OUT_OF_STOCK, self::DISCONTINUED => false,
        };
    }

    /**
     * Get available next statuses
     */
    public function nextStatuses(): array
    {
        return match($this) {
            self::DRAFT => [self::ACTIVE, self::INACTIVE, self::ARCHIVED],
            self::ACTIVE => [self::INACTIVE, self::OUT_OF_STOCK, self::DISCONTINUED, self::ARCHIVED],
            self::INACTIVE => [self::ACTIVE, self::ARCHIVED, self::DISCONTINUED],
            self::OUT_OF_STOCK => [self::ACTIVE, self::INACTIVE, self::DISCONTINUED, self::ARCHIVED],
            self::DISCONTINUED => [self::ARCHIVED],
            self::ARCHIVED => [self::ACTIVE, self::INACTIVE], // Can be restored
        };
    }

    /**
     * Get status description
     */
    public function description(): string
    {
        return match($this) {
            self::ACTIVE => 'Ürün aktif ve satışa hazır.',
            self::INACTIVE => 'Ürün geçici olarak pasif.',
            self::DRAFT => 'Ürün henüz taslak aşamasında.',
            self::ARCHIVED => 'Ürün arşivlendi.',
            self::OUT_OF_STOCK => 'Ürün stokta yok.',
            self::DISCONTINUED => 'Ürünün üretimi durduruldu.',
        };
    }

    /**
     * Get customer visible statuses
     */
    public static function customerVisibleStatuses(): array
    {
        return [
            self::ACTIVE->value => 'Mevcut',
            self::OUT_OF_STOCK->value => 'Stokta Yok',
            self::DISCONTINUED->value => 'Üretimi Durduruldu',
        ];
    }

    /**
     * Get admin statuses
     */
    public static function adminStatuses(): array
    {
        return self::toArray();
    }
}
