<?php

namespace App\Core\Domain;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Core\Domain\Contracts\EntityInterface;
use App\Core\Domain\Traits\HasDomainEvents;

/**
 * Base Entity sınıfı
 * Tüm domain entity'leri bu sınıftan türetilmelidir
 */
abstract class Entity extends Model implements EntityInterface
{
    use SoftDeletes, HasDomainEvents;

    /**
     * Domain events
     *
     * @var array
     */
    protected array $domainEvents = [];

    /**
     * Entity'nin benzersiz kimliği
     *
     * @return mixed
     */
    public function getId()
    {
        return $this->getKey();
    }

    /**
     * Entity'nin string temsilini döndür
     *
     * @return string
     */
    public function __toString(): string
    {
        return static::class . ':' . $this->getId();
    }

    /**
     * İki entity'nin eşit olup olmadığını kontrol et
     *
     * @param EntityInterface $entity
     * @return bool
     */
    public function equals(EntityInterface $entity): bool
    {
        return $this->getId() === $entity->getId() 
            && get_class($this) === get_class($entity);
    }

    /**
     * Entity'nin geçerli olup olmadığını kontrol et
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return !empty($this->getId());
    }

    /**
     * Entity'yi array'e çevir
     *
     * @return array
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        
        // Domain events'i array'e dahil etme
        unset($array['domainEvents']);
        
        return $array;
    }
}
