<?php

namespace App\Core\Domain\ValueObjects;

/**
 * Money Value Object
 * Para birimi ve tutarı temsil eder
 */
class Money extends ValueObject
{
    /**
     * Tutar (kuruş cinsinden)
     *
     * @var int
     */
    private int $amount;

    /**
     * Para birimi
     *
     * @var string
     */
    private string $currency;

    /**
     * Constructor
     *
     * @param int $amount Kuruş cinsinden tutar
     * @param string $currency Para birimi (TRY, USD, EUR, vb.)
     */
    public function __construct(int $amount, string $currency = 'TRY')
    {
        $this->amount = $amount;
        $this->currency = strtoupper($currency);
        $this->validate();
    }

    /**
     * Tutarı kuruş cinsinden getir
     *
     * @return int
     */
    public function getAmount(): int
    {
        return $this->amount;
    }

    /**
     * Tutarı lira cinsinden getir
     *
     * @return float
     */
    public function getAmountInMajorUnit(): float
    {
        return $this->amount / 100;
    }

    /**
     * Para birimini getir
     *
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * Para topla
     *
     * @param Money $money
     * @return Money
     * @throws \InvalidArgumentException
     */
    public function add(Money $money): Money
    {
        $this->ensureSameCurrency($money);
        return new Money($this->amount + $money->amount, $this->currency);
    }

    /**
     * Para çıkar
     *
     * @param Money $money
     * @return Money
     * @throws \InvalidArgumentException
     */
    public function subtract(Money $money): Money
    {
        $this->ensureSameCurrency($money);
        return new Money($this->amount - $money->amount, $this->currency);
    }

    /**
     * Para çarp
     *
     * @param float $multiplier
     * @return Money
     */
    public function multiply(float $multiplier): Money
    {
        return new Money((int) round($this->amount * $multiplier), $this->currency);
    }



    /**
     * Aynı para birimi mi kontrol et
     *
     * @param Money $money
     * @throws \InvalidArgumentException
     */
    private function ensureSameCurrency(Money $money): void
    {
        if ($this->currency !== $money->currency) {
            throw new \InvalidArgumentException(
                "Cannot perform operation with different currencies: {$this->currency} and {$money->currency}"
            );
        }
    }

    /**
     * Lira cinsinden Money oluştur
     *
     * @param float $amount
     * @param string $currency
     * @return Money
     */
    public static function fromMajorUnit(float $amount, string $currency = 'TRY'): Money
    {
        return new Money((int) round($amount * 100), $currency);
    }

    /**
     * Float tutardan Money oluştur
     *
     * @param float $amount
     * @param string $currency
     * @return Money
     */
    public static function fromAmount(float $amount, string $currency = 'TRY'): Money
    {
        return self::fromMajorUnit($amount, $currency);
    }

    /**
     * Sıfır Money oluştur
     *
     * @param string $currency
     * @return Money
     */
    public static function zero(string $currency = 'TRY'): Money
    {
        return new Money(0, $currency);
    }

    /**
     * Sıfır mı kontrol et
     *
     * @return bool
     */
    public function isZero(): bool
    {
        return $this->amount === 0;
    }

    /**
     * Pozitif mi kontrol et
     *
     * @return bool
     */
    public function isPositive(): bool
    {
        return $this->amount > 0;
    }

    /**
     * Negatif mi kontrol et
     *
     * @return bool
     */
    public function isNegative(): bool
    {
        return $this->amount < 0;
    }

    /**
     * Eşit mi kontrol et
     *
     * @param ValueObject $other
     * @return bool
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self &&
               $this->amount === $other->amount &&
               $this->currency === $other->currency;
    }

    /**
     * Büyük mü kontrol et
     *
     * @param Money $other
     * @return bool
     */
    public function isGreaterThan(Money $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->amount > $other->amount;
    }

    /**
     * Küçük mü kontrol et
     *
     * @param Money $other
     * @return bool
     */
    public function isLessThan(Money $other): bool
    {
        $this->ensureSameCurrency($other);
        return $this->amount < $other->amount;
    }

    /**
     * Böl
     *
     * @param float $divisor
     * @return Money
     */
    public function divide(float $divisor): Money
    {
        if ($divisor == 0) {
            throw new \InvalidArgumentException('Cannot divide by zero');
        }

        return new Money((int) round($this->amount / $divisor), $this->currency);
    }

    /**
     * Mutlak değer
     *
     * @return Money
     */
    public function abs(): Money
    {
        return new Money(abs($this->amount), $this->currency);
    }

    /**
     * Büyük veya eşit mi kontrol et
     *
     * @param Money $other
     * @return bool
     */
    public function isGreaterThanOrEqualTo(Money $other): bool
    {
        return $this->isGreaterThan($other) || $this->equals($other);
    }

    /**
     * Küçük veya eşit mi kontrol et
     *
     * @param Money $other
     * @return bool
     */
    public function isLessThanOrEqualTo(Money $other): bool
    {
        return $this->isLessThan($other) || $this->equals($other);
    }

    /**
     * Yüzde hesapla
     *
     * @param float $percentage
     * @return Money
     */
    public function percentage(float $percentage): Money
    {
        if ($percentage < 0) {
            throw new \InvalidArgumentException('Percentage cannot be negative');
        }

        return $this->multiply($percentage / 100);
    }

    /**
     * Value object'in geçerli olup olmadığını kontrol et
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return !empty($this->currency); // Amount can be negative for refunds
    }

    /**
     * Value object'i array'e çevir
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'amount' => $this->amount,
            'amount_major' => $this->getAmountInMajorUnit(),
            'currency' => $this->currency,
        ];
    }

    /**
     * Value object'in string temsilini döndür
     *
     * @return string
     */
    public function __toString(): string
    {
        return number_format($this->getAmountInMajorUnit(), 2) . ' ' . $this->currency;
    }
}
