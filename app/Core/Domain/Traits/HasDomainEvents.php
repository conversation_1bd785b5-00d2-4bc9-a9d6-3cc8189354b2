<?php

namespace App\Core\Domain\Traits;

use App\Core\Domain\Events\DomainEvent;

/**
 * Domain Events trait
 * Entity'lerin domain event'leri y<PERSON><PERSON><PERSON> i<PERSON>r
 */
trait HasDomainEvents
{
    /**
     * Domain events
     *
     * @var array
     */
    protected array $domainEvents = [];

    /**
     * Domain event ekle
     *
     * @param DomainEvent $event
     * @return void
     */
    public function addDomainEvent(DomainEvent $event): void
    {
        $this->domainEvents[] = $event;
    }

    /**
     * Tüm domain events'i getir
     *
     * @return array
     */
    public function getDomainEvents(): array
    {
        return $this->domainEvents;
    }

    /**
     * Domain events'i temizle
     *
     * @return void
     */
    public function clearDomainEvents(): void
    {
        $this->domainEvents = [];
    }

    /**
     * Domain events var mı kontrol et
     *
     * @return bool
     */
    public function hasDomainEvents(): bool
    {
        return !empty($this->domainEvents);
    }

    /**
     * Model kaydedildiğinde domain events'i dispatch et
     */
    protected static function bootHasDomainEvents(): void
    {
        static::saved(function ($model) {
            if ($model->hasDomainEvents()) {
                foreach ($model->getDomainEvents() as $event) {
                    event($event);
                }
                $model->clearDomainEvents();
            }
        });

        static::deleted(function ($model) {
            if ($model->hasDomainEvents()) {
                foreach ($model->getDomainEvents() as $event) {
                    event($event);
                }
                $model->clearDomainEvents();
            }
        });
    }
}
