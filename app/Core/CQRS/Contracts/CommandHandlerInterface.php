<?php

namespace App\Core\CQRS\Contracts;

/**
 * Command Handler Interface
 * Tüm command handler'lar bu interface'i implement etmelidir
 */
interface CommandHandlerInterface
{
    /**
     * Command'ı işle
     *
     * @param CommandInterface $command
     * @return mixed
     * @throws \Exception
     */
    public function handle(CommandInterface $command);

    /**
     * Handler'ın desteklediği command sınıfı
     *
     * @return string
     */
    public function getCommandClass(): string;

    /**
     * Handler'ın öncelik seviyesi (düşük sayı = yüksek öncelik)
     *
     * @return int
     */
    public function getPriority(): int;
}
