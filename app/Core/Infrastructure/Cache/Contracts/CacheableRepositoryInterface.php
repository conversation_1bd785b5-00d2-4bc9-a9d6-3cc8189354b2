<?php

namespace App\Core\Infrastructure\Cache\Contracts;

/**
 * CacheableRepositoryInterface
 * Cache'lenebilir repository'ler için interface
 */
interface CacheableRepositoryInterface
{
    /**
     * Cache'i etkinleştir/devre dışı bırak
     */
    public function enableCache(bool $enabled = true): void;

    /**
     * Cache'in etkin olup olmadığını kontrol et
     */
    public function isCacheEnabled(): bool;

    /**
     * Belirli bir entity için cache'i temizle
     */
    public function clearEntityCache($entityId): void;

    /**
     * Repository'nin tüm cache'ini temizle
     */
    public function clearAllCache(): void;

    /**
     * Cache istatistiklerini al
     */
    public function getCacheStats(): array;

    /**
     * Cache key prefix'ini al
     */
    public function getCachePrefix(): string;

    /**
     * Cache TTL değerlerini al
     */
    public function getCacheTtl(): array;
}
