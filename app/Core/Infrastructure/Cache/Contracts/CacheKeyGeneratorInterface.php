<?php

namespace App\Core\Infrastructure\Cache\Contracts;

/**
 * CacheKeyGeneratorInterface
 * Cache key generation için interface
 */
interface CacheKeyGeneratorInterface
{
    /**
     * Entity cache key oluştur
     */
    public function generateEntityKey(string $entityType, $entityId): string;

    /**
     * List cache key oluştur
     */
    public function generateListKey(string $entityType, array $criteria, int $limit = null, int $offset = null): string;

    /**
     * Query cache key oluştur
     */
    public function generateQueryKey(string $entityType, string $method, array $parameters = []): string;

    /**
     * Stats cache key oluştur
     */
    public function generateStatsKey(string $entityType, string $statType, array $parameters = []): string;

    /**
     * Date range cache key oluştur
     */
    public function generateDateRangeKey(string $entityType, \DateTime $startDate, \DateTime $endDate, array $parameters = []): string;

    /**
     * Custom cache key oluştur
     */
    public function generateCustomKey(string $prefix, array $parts): string;

    /**
     * Cache key'in geçerli olup olmadığını kontrol et
     */
    public function isValidKey(string $key): bool;

    /**
     * Cache key'den prefix'i çıkar
     */
    public function extractPrefix(string $key): string;
}
