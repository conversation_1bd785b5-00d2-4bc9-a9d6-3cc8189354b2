<?php

namespace App\Core\Infrastructure\Cache\Services;

use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * CacheTagManager
 * Cache tag management servisi
 */
class CacheTagManager implements CacheTagManagerInterface
{
    private const TAG_SEPARATOR = ':';
    private const GLOBAL_TAG_PREFIX = 'global';

    /**
     * Tag hierarchy mapping
     */
    private array $tagHierarchy = [
        'product' => ['products', 'catalog'],
        'category' => ['categories', 'catalog'],
        'cart' => ['carts', 'user_data'],
        'payment' => ['payments', 'financial'],
        'order' => ['orders', 'financial'],
        'user' => ['users', 'user_data'],
        'inventory' => ['inventory', 'catalog'],
        'shipping' => ['shipping', 'logistics'],
        'notification' => ['notifications', 'communication']
    ];

    /**
     * Tag dependencies mapping
     */
    private array $tagDependencies = [
        'products' => ['categories', 'inventory'],
        'categories' => ['products'],
        'carts' => ['products', 'users'],
        'orders' => ['products', 'users', 'payments', 'shipping'],
        'payments' => ['orders', 'users'],
        'inventory' => ['products'],
        'shipping' => ['orders'],
        'notifications' => ['users', 'orders']
    ];

    /**
     * Entity için tag'leri al
     */
    public function getEntityTags(string $entityType, $entityId): array
    {
        $normalizedType = $this->normalizeEntityType($entityType);

        $tags = [
            $normalizedType,
            $normalizedType . self::TAG_SEPARATOR . $entityId
        ];

        // Hierarchy tag'lerini ekle
        $hierarchyTags = $this->getHierarchyTags($normalizedType);
        $tags = array_merge($tags, $hierarchyTags);

        return array_unique($tags);
    }

    /**
     * List query için tag'leri al
     */
    public function getListTags(string $entityType, array $criteria = []): array
    {
        $normalizedType = $this->normalizeEntityType($entityType);

        $tags = [$normalizedType];

        // Hierarchy tag'lerini ekle
        $hierarchyTags = $this->getHierarchyTags($normalizedType);
        $tags = array_merge($tags, $hierarchyTags);

        // Criteria'ya göre ek tag'ler
        foreach ($criteria as $key => $value) {
            if ($this->isRelationshipCriteria($key)) {
                $relatedType = $this->extractRelatedType($key);
                if ($relatedType) {
                    $tags[] = $relatedType;
                    $tags[] = $relatedType . self::TAG_SEPARATOR . $value;
                }
            }
        }

        return array_unique($tags);
    }

    /**
     * Relationship tag'leri al
     */
    public function getRelationshipTags(string $parentType, $parentId, string $childType): array
    {
        $normalizedParent = $this->normalizeEntityType($parentType);
        $normalizedChild = $this->normalizeEntityType($childType);

        $tags = [
            $normalizedParent,
            $normalizedParent . self::TAG_SEPARATOR . $parentId,
            $normalizedChild,
            'relationship' . self::TAG_SEPARATOR . $normalizedParent . self::TAG_SEPARATOR . $normalizedChild
        ];

        return array_unique($tags);
    }

    /**
     * Global tag'leri al
     */
    public function getGlobalTags(string $entityType): array
    {
        $normalizedType = $this->normalizeEntityType($entityType);

        $tags = [
            self::GLOBAL_TAG_PREFIX,
            self::GLOBAL_TAG_PREFIX . self::TAG_SEPARATOR . $normalizedType
        ];

        // Hierarchy'den global tag'ler
        $hierarchyTags = $this->getHierarchyTags($normalizedType);
        foreach ($hierarchyTags as $hierarchyTag) {
            $tags[] = self::GLOBAL_TAG_PREFIX . self::TAG_SEPARATOR . $hierarchyTag;
        }

        return array_unique($tags);
    }

    /**
     * Tag'lere göre cache'i temizle
     */
    public function clearByTags(array $tags): void
    {
        if (empty($tags)) {
            return;
        }

        try {
            // Laravel'in tag-based cache clearing'ini kullan
            if (method_exists(Cache::getStore(), 'tags')) {
                Cache::tags($tags)->flush();
            } else {
                // Tag desteklemeyen driver'lar için pattern-based clearing
                $this->clearByPattern($tags);
            }
        } catch (\Exception $e) {
            \Log::warning('Cache tag clearing failed', [
                'tags' => $tags,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Entity'ye göre ilgili tüm tag'leri temizle
     */
    public function clearEntityTags(string $entityType, $entityId): void
    {
        $tags = $this->getEntityTags($entityType, $entityId);

        // Dependency tag'lerini de ekle
        $dependencyTags = $this->getDependencyTags($entityType);
        $tags = array_merge($tags, $dependencyTags);

        $this->clearByTags(array_unique($tags));
    }

    /**
     * Tag hierarchy'sini al
     */
    public function getTagHierarchy(string $entityType): array
    {
        $normalizedType = $this->normalizeEntityType($entityType);
        return $this->tagHierarchy[$normalizedType] ?? [];
    }

    /**
     * Tag dependency'lerini al
     */
    public function getTagDependencies(string $tag): array
    {
        $normalizedTag = $this->normalizeEntityType($tag);

        // Önce singular form'u dene
        if (isset($this->tagDependencies[$normalizedTag])) {
            return $this->tagDependencies[$normalizedTag];
        }

        // Sonra plural form'u dene
        $pluralTag = Str::plural($normalizedTag);
        return $this->tagDependencies[$pluralTag] ?? [];
    }

    /**
     * Tag'in geçerli olup olmadığını kontrol et
     */
    public function isValidTag(string $tag): bool
    {
        if (empty($tag)) {
            return false;
        }

        // Geçersiz karakterler kontrolü
        if (preg_match('/[^a-zA-Z0-9:_\-]/', $tag)) {
            return false;
        }

        return true;
    }

    /**
     * Entity type'ı normalize et
     */
    private function normalizeEntityType(string $entityType): string
    {
        return Str::snake(Str::lower(Str::singular($entityType)));
    }

    /**
     * Hierarchy tag'lerini al
     */
    private function getHierarchyTags(string $entityType): array
    {
        return $this->tagHierarchy[$entityType] ?? [];
    }

    /**
     * Dependency tag'lerini al
     */
    private function getDependencyTags(string $entityType): array
    {
        $dependencies = $this->tagDependencies[$entityType] ?? [];
        $tags = [];

        foreach ($dependencies as $dependency) {
            $tags[] = $dependency;
            // Dependency'nin hierarchy tag'lerini de ekle
            $hierarchyTags = $this->getHierarchyTags($dependency);
            $tags = array_merge($tags, $hierarchyTags);
        }

        return array_unique($tags);
    }

    /**
     * Criteria'nın relationship olup olmadığını kontrol et
     */
    private function isRelationshipCriteria(string $key): bool
    {
        return Str::endsWith($key, '_id') || Str::contains($key, '_');
    }

    /**
     * Related type'ı çıkar
     */
    private function extractRelatedType(string $key): ?string
    {
        if (Str::endsWith($key, '_id')) {
            $type = Str::replaceLast('_id', '', $key);
            return $this->normalizeEntityType($type);
        }

        return null;
    }

    /**
     * Pattern-based cache clearing (Redis için)
     */
    private function clearByPattern(array $tags): void
    {
        if (!Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            return;
        }

        $redis = Cache::getRedis();

        foreach ($tags as $tag) {
            $pattern = "*{$tag}*";
            $keys = $redis->keys($pattern);

            if (!empty($keys)) {
                $redis->del($keys);
            }
        }
    }
}
