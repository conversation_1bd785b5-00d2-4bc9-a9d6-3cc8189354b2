<?php

namespace App\Core\Infrastructure\Cache\Decorators;

use App\Core\Infrastructure\Cache\Contracts\CacheableRepositoryInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheKeyGeneratorInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * BaseCacheDecorator
 * Tüm cache decorator'ları için base class
 */
abstract class BaseCacheDecorator implements CacheableRepositoryInterface
{
    protected bool $cacheEnabled = true;
    protected array $cacheStats = [
        'hits' => 0,
        'misses' => 0,
        'writes' => 0,
        'deletes' => 0
    ];

    public function __construct(
        protected object $repository,
        protected CacheKeyGeneratorInterface $keyGenerator,
        protected CacheTagManagerInterface $tagManager,
        protected array $config = []
    ) {
        $this->cacheEnabled = $config['enabled'] ?? true;
    }

    /**
     * Cache'i etkinleştir/devre dışı bırak
     */
    public function enableCache(bool $enabled = true): void
    {
        $this->cacheEnabled = $enabled;
    }

    /**
     * Cache'in etkin olup olmadığını kontrol et
     */
    public function isCacheEnabled(): bool
    {
        return $this->cacheEnabled;
    }

    /**
     * Cache istatistiklerini al
     */
    public function getCacheStats(): array
    {
        return $this->cacheStats;
    }

    /**
     * Cache key prefix'ini al
     */
    abstract public function getCachePrefix(): string;

    /**
     * Cache TTL değerlerini al
     */
    abstract public function getCacheTtl(): array;

    /**
     * Entity type'ını al
     */
    abstract protected function getEntityType(): string;

    /**
     * Cache'den veri al veya callback'i çalıştır
     */
    protected function remember(string $key, int $ttl, callable $callback, array $tags = [])
    {
        if (!$this->cacheEnabled) {
            return $callback();
        }

        try {
            $cacheStore = $this->getCacheStore($tags);
            
            $result = $cacheStore->remember($key, $ttl, function () use ($callback) {
                $this->cacheStats['misses']++;
                return $callback();
            });

            if ($result !== null) {
                $this->cacheStats['hits']++;
            }

            return $result;
        } catch (\Exception $e) {
            Log::warning('Cache remember failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);

            return $callback();
        }
    }

    /**
     * Cache'e veri yaz
     */
    protected function put(string $key, $value, int $ttl, array $tags = []): void
    {
        if (!$this->cacheEnabled) {
            return;
        }

        try {
            $cacheStore = $this->getCacheStore($tags);
            $cacheStore->put($key, $value, $ttl);
            $this->cacheStats['writes']++;
        } catch (\Exception $e) {
            Log::warning('Cache put failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cache'den veri sil
     */
    protected function forget(string $key, array $tags = []): void
    {
        try {
            $cacheStore = $this->getCacheStore($tags);
            $cacheStore->forget($key);
            $this->cacheStats['deletes']++;
        } catch (\Exception $e) {
            Log::warning('Cache forget failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Belirli bir entity için cache'i temizle
     */
    public function clearEntityCache($entityId): void
    {
        $tags = $this->tagManager->getEntityTags($this->getEntityType(), $entityId);
        $this->tagManager->clearByTags($tags);
    }

    /**
     * Repository'nin tüm cache'ini temizle
     */
    public function clearAllCache(): void
    {
        $tags = $this->tagManager->getGlobalTags($this->getEntityType());
        $this->tagManager->clearByTags($tags);
    }

    /**
     * Entity'yi cache'le
     */
    protected function cacheEntity($entity, array $additionalTags = []): void
    {
        if (!$entity || !method_exists($entity, 'getId') || !$entity->getId()) {
            return;
        }

        $entityId = $entity->getId();
        $entityType = $this->getEntityType();
        
        // Entity cache key'i
        $entityKey = $this->keyGenerator->generateEntityKey($entityType, $entityId);
        $entityTags = $this->tagManager->getEntityTags($entityType, $entityId);
        $allTags = array_merge($entityTags, $additionalTags);
        
        $ttl = $this->getCacheTtl()['entity'] ?? 3600;
        $this->put($entityKey, $entity, $ttl, $allTags);

        // Ek cache key'leri (subclass'larda override edilebilir)
        $this->cacheEntityAdditionalKeys($entity, $allTags);
    }

    /**
     * Entity için ek cache key'leri (subclass'larda override edilebilir)
     */
    protected function cacheEntityAdditionalKeys($entity, array $tags): void
    {
        // Override in subclasses for additional caching strategies
    }

    /**
     * List cache'i temizle
     */
    protected function clearListCaches(array $additionalTags = []): void
    {
        $entityType = $this->getEntityType();
        $listTags = $this->tagManager->getListTags($entityType);
        $allTags = array_merge($listTags, $additionalTags);
        
        $this->tagManager->clearByTags($allTags);
    }

    /**
     * Cache store'u al (tag'li veya tag'sız)
     */
    protected function getCacheStore(array $tags = [])
    {
        if (empty($tags)) {
            return Cache::store();
        }

        // Tag desteklemeyen driver'lar için fallback
        if (!method_exists(Cache::getStore(), 'tags')) {
            return Cache::store();
        }

        return Cache::tags($tags);
    }

    /**
     * Default TTL değerini al
     */
    protected function getDefaultTtl(string $type = 'entity'): int
    {
        $ttlConfig = $this->getCacheTtl();
        return $ttlConfig[$type] ?? 3600;
    }

    /**
     * Cache key'in geçerli olup olmadığını kontrol et
     */
    protected function isValidCacheKey(string $key): bool
    {
        return $this->keyGenerator->isValidKey($key);
    }

    /**
     * Repository method'unu proxy et
     */
    protected function proxyToRepository(string $method, array $arguments = [])
    {
        if (!method_exists($this->repository, $method)) {
            throw new \BadMethodCallException("Method {$method} does not exist on repository");
        }

        return call_user_func_array([$this->repository, $method], $arguments);
    }

    /**
     * Magic method for repository method proxying
     */
    public function __call(string $method, array $arguments)
    {
        return $this->proxyToRepository($method, $arguments);
    }
}
