<?php

namespace App\Core\Infrastructure\Api\Documentation\Providers;

use App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface;
use App\Core\Infrastructure\Api\Documentation\Services\OpenApiGenerator;
use App\Core\Infrastructure\Api\Documentation\Commands\GenerateApiDocsCommand;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Console\Scheduling\Schedule;

/**
 * ApiDocumentationServiceProvider
 * API documentation infrastructure service provider
 */
class ApiDocumentationServiceProvider extends ServiceProvider
{
    /**
     * Register services
     */
    public function register(): void
    {
        $this->registerConfiguration();
        $this->registerServices();
        $this->registerCommands();
        $this->registerAliases();
    }

    /**
     * Bootstrap services
     */
    public function boot(): void
    {
        $this->publishConfiguration();
        $this->publishViews();
        $this->publishAssets();
        $this->registerRoutes();
        $this->registerScheduledTasks();
        $this->registerViewComposers();
        $this->bootAdditionalFeatures();
    }

    /**
     * Register configuration
     */
    protected function registerConfiguration(): void
    {
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/api_documentation.php',
            'api_documentation'
        );
    }

    /**
     * Register services
     */
    protected function registerServices(): void
    {
        // Version manager
        $this->app->singleton(\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class, function ($app) {
            return new \App\Core\Infrastructure\Api\Versioning\Services\SimpleApiVersionManager();
        });

        // Documentation generator
        $this->app->singleton(DocumentationGeneratorInterface::class, function ($app) {
            return new OpenApiGenerator(
                $app['router'],
                $app[\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class],
                config('api_documentation.generation', [])
            );
        });

        // Alias for easier access
        $this->app->alias(DocumentationGeneratorInterface::class, 'api.docs.generator');
    }

    /**
     * Register commands
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                GenerateApiDocsCommand::class,
            ]);
        }
    }

    /**
     * Register aliases
     */
    protected function registerAliases(): void
    {
        $this->app->alias(DocumentationGeneratorInterface::class, 'api.docs.generator');
        $this->app->alias(OpenApiGenerator::class, 'api.docs.openapi');
    }

    /**
     * Publish configuration
     */
    protected function publishConfiguration(): void
    {
        $this->publishes([
            __DIR__ . '/../Config/api_documentation.php' => config_path('api_documentation.php'),
        ], 'api-docs-config');
    }

    /**
     * Publish views
     */
    protected function publishViews(): void
    {
        $this->loadViewsFrom(__DIR__ . '/../../../../resources/views/api/docs', 'api-docs');

        $this->publishes([
            __DIR__ . '/../../../../resources/views/api/docs' => resource_path('views/api/docs'),
        ], 'api-docs-views');
    }

    /**
     * Publish assets
     */
    protected function publishAssets(): void
    {
        $this->publishes([
            __DIR__ . '/../Assets' => public_path('docs/assets'),
        ], 'api-docs-assets');
    }

    /**
     * Register routes
     */
    protected function registerRoutes(): void
    {
        if (config('api_documentation.routes.enabled', true)) {
            Route::group([
                'middleware' => config('api_documentation.routes.middleware', 'web'),
                'domain' => config('api_documentation.routes.domain'),
            ], function () {
                $this->loadRoutesFrom(base_path('routes/api/documentation.php'));
            });
        }
    }

    /**
     * Register scheduled tasks
     */
    protected function registerScheduledTasks(): void
    {
        if (config('api_documentation.auto_generate', false)) {
            $this->app->booted(function () {
                $schedule = $this->app->make(Schedule::class);

                // Auto-generate documentation daily
                $schedule->command('api:docs:generate --cache')
                    ->daily()
                    ->withoutOverlapping()
                    ->runInBackground()
                    ->onSuccess(function () {
                        \Log::info('API documentation auto-generated successfully');
                    })
                    ->onFailure(function () {
                        \Log::error('API documentation auto-generation failed');
                    });
            });
        }
    }

    /**
     * Register view composers
     */
    protected function registerViewComposers(): void
    {
        view()->composer('api-docs::*', function ($view) {
            $versionManager = app(\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class);

            $view->with([
                'availableVersions' => $versionManager->getSupportedVersions(),
                'defaultVersion' => $versionManager->getDefaultVersion(),
                'latestVersion' => $versionManager->getLatestVersion(),
                'docsConfig' => config('api_documentation'),
            ]);
        });
    }

    /**
     * Get the services provided by the provider
     */
    public function provides(): array
    {
        return [
            DocumentationGeneratorInterface::class,
            'api.docs.generator',
            'api.docs.openapi',
        ];
    }

    /**
     * Check if documentation is enabled
     */
    public function isEnabled(): bool
    {
        return config('api_documentation.enabled', true);
    }

    /**
     * Get documentation configuration
     */
    public function getConfig(): array
    {
        return config('api_documentation', []);
    }

    /**
     * Get documentation metadata
     */
    public function getMetadata(): array
    {
        return [
            'provider' => 'ApiDocumentationServiceProvider',
            'version' => '1.0.0',
            'enabled' => $this->isEnabled(),
            'routes_registered' => config('api_documentation.routes.enabled', true),
            'auto_generate' => config('api_documentation.auto_generate', false),
            'cache_enabled' => config('api_documentation.cache_enabled', true),
            'supported_formats' => array_keys(array_filter(config('api_documentation.formats', []), function ($format) {
                return $format['enabled'] ?? false;
            })),
            'ui_formats' => array_keys(array_filter(config('api_documentation.ui', []), function ($ui) {
                return $ui['enabled'] ?? false;
            })),
        ];
    }

    /**
     * Register documentation middleware
     */
    protected function registerMiddleware(): void
    {
        // Register documentation-specific middleware if needed
        // $this->app['router']->aliasMiddleware('docs.auth', DocumentationAuthMiddleware::class);
    }

    /**
     * Register documentation events
     */
    protected function registerEvents(): void
    {
        // Listen for documentation generation events
        \Event::listen('api.docs.generated', function ($version, $specification) {
            \Log::info("API documentation generated for version {$version}");
        });

        \Event::listen('api.docs.generation.failed', function ($version, $error) {
            \Log::error("API documentation generation failed for version {$version}: {$error}");
        });
    }

    /**
     * Register documentation health checks
     */
    protected function registerHealthChecks(): void
    {
        if (class_exists('\Spatie\Health\Facades\Health')) {
            \Spatie\Health\Facades\Health::checks([
                \App\Core\Infrastructure\Api\Documentation\HealthChecks\DocumentationHealthCheck::class,
            ]);
        }
    }

    /**
     * Register documentation cache tags
     */
    protected function registerCacheTags(): void
    {
        if (config('api_documentation.cache_enabled', true)) {
            // Register cache tags for documentation
            $this->app->singleton('api.docs.cache.tags', function () {
                return ['api-docs', 'api-specifications'];
            });
        }
    }

    /**
     * Register documentation validation rules
     */
    protected function registerValidationRules(): void
    {
        // Register custom validation rules for documentation
        \Validator::extend('openapi_version', function ($attribute, $value, $parameters, $validator) {
            return in_array($value, ['3.0.0', '3.0.1', '3.0.2', '3.0.3']);
        });

        \Validator::extend('api_version', function ($attribute, $value, $parameters, $validator) {
            return preg_match('/^\d+\.\d+$/', $value);
        });
    }

    /**
     * Register documentation macros
     */
    protected function registerMacros(): void
    {
        // Add route macro for API documentation
        Route::macro('apiDocs', function ($version = null) {
            $version = $version ?? app(\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class)->getLatestVersion();

            return route('api.docs.version', ['version' => $version]);
        });

        // Add response macro for API documentation links
        \Illuminate\Http\Response::macro('withApiDocs', function ($version = null) {
            $docsUrl = Route::apiDocs($version);

            return $this->header('X-API-Docs', $docsUrl);
        });
    }

    /**
     * Register documentation blade directives
     */
    protected function registerBladeDirectives(): void
    {
        \Blade::directive('apiDocs', function ($expression) {
            return "<?php echo route('api.docs.index', {$expression}); ?>";
        });

        \Blade::directive('apiSpec', function ($expression) {
            return "<?php echo route('api.docs.spec', {$expression}); ?>";
        });
    }

    /**
     * Boot additional features
     */
    protected function bootAdditionalFeatures(): void
    {
        $this->registerMiddleware();
        $this->registerEvents();
        $this->registerHealthChecks();
        $this->registerCacheTags();
        $this->registerValidationRules();
        $this->registerMacros();
        $this->registerBladeDirectives();
    }


}
