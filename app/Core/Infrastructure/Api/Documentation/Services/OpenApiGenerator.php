<?php

namespace App\Core\Infrastructure\Api\Documentation\Services;

use App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface;
use App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionMethod;

/**
 * OpenApiGenerator
 * OpenAPI 3.0 specification generator
 */
class OpenApiGenerator implements DocumentationGeneratorInterface
{
    protected Router $router;
    protected ApiVersionManagerInterface $versionManager;
    protected array $config;
    protected array $generatedSpec = [];

    public function __construct(
        Router $router,
        ApiVersionManagerInterface $versionManager,
        array $config = []
    ) {
        $this->router = $router;
        $this->versionManager = $versionManager;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Generate API documentation
     */
    public function generate(array $options = []): array
    {
        $version = $options['version'] ?? $this->versionManager->getLatestVersion();
        return $this->generateForVersion($version, $options);
    }

    /**
     * Generate documentation for specific version
     */
    public function generateForVersion(string $version, array $options = []): array
    {
        $this->generatedSpec = $this->initializeOpenApiSpec($version);
        
        // Get routes for this version
        $routes = $this->getVersionRoutes($version);
        
        // Generate paths
        $this->generatedSpec['paths'] = $this->generatePaths($routes, $version);
        
        // Generate components
        $this->generatedSpec['components'] = $this->generateComponents($routes, $version);
        
        // Add version-specific information
        $this->addVersionInfo($version);
        
        return $this->generatedSpec;
    }

    /**
     * Generate OpenAPI specification
     */
    public function generateOpenApiSpec(string $version = null): array
    {
        $version = $version ?? $this->versionManager->getLatestVersion();
        return $this->generateForVersion($version);
    }

    /**
     * Generate Postman collection
     */
    public function generatePostmanCollection(string $version = null): array
    {
        $openApiSpec = $this->generateOpenApiSpec($version);
        return $this->convertToPostmanCollection($openApiSpec);
    }

    /**
     * Get supported formats
     */
    public function getSupportedFormats(): array
    {
        return ['openapi', 'swagger', 'postman', 'json', 'yaml'];
    }

    /**
     * Export documentation to file
     */
    public function exportToFile(string $format, string $filePath, array $options = []): bool
    {
        $documentation = $this->generate($options);
        
        switch (strtolower($format)) {
            case 'json':
                return file_put_contents($filePath, json_encode($documentation, JSON_PRETTY_PRINT)) !== false;
            case 'yaml':
                return file_put_contents($filePath, yaml_emit($documentation)) !== false;
            default:
                throw new \InvalidArgumentException("Unsupported format: {$format}");
        }
    }

    /**
     * Validate documentation structure
     */
    public function validateDocumentation(array $documentation): array
    {
        $errors = [];
        
        // Required OpenAPI fields
        $requiredFields = ['openapi', 'info', 'paths'];
        foreach ($requiredFields as $field) {
            if (!isset($documentation[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }
        
        // Validate info object
        if (isset($documentation['info'])) {
            $requiredInfoFields = ['title', 'version'];
            foreach ($requiredInfoFields as $field) {
                if (!isset($documentation['info'][$field])) {
                    $errors[] = "Missing required info field: {$field}";
                }
            }
        }
        
        return $errors;
    }

    /**
     * Get documentation metadata
     */
    public function getMetadata(): array
    {
        return [
            'generator' => 'OpenApiGenerator',
            'version' => '1.0.0',
            'generated_at' => now()->toISOString(),
            'supported_formats' => $this->getSupportedFormats(),
            'config' => $this->config,
        ];
    }

    /**
     * Initialize OpenAPI specification structure
     */
    protected function initializeOpenApiSpec(string $version): array
    {
        return [
            'openapi' => '3.0.3',
            'info' => [
                'title' => $this->config['title'] ?? 'API Documentation',
                'description' => $this->config['description'] ?? 'API Documentation',
                'version' => $version,
                'contact' => $this->config['contact'] ?? [],
                'license' => $this->config['license'] ?? [],
            ],
            'servers' => $this->generateServers(),
            'paths' => [],
            'components' => [
                'schemas' => [],
                'responses' => $this->getStandardResponses(),
                'parameters' => $this->getStandardParameters(),
                'securitySchemes' => $this->getSecuritySchemes(),
            ],
            'security' => $this->getGlobalSecurity(),
            'tags' => $this->generateTags(),
        ];
    }

    /**
     * Get routes for specific version
     */
    protected function getVersionRoutes(string $version): array
    {
        $allRoutes = Route::getRoutes();
        $versionRoutes = [];
        
        foreach ($allRoutes as $route) {
            // Check if route belongs to this version
            if ($this->isVersionRoute($route, $version)) {
                $versionRoutes[] = $route;
            }
        }
        
        return $versionRoutes;
    }

    /**
     * Check if route belongs to specific version
     */
    protected function isVersionRoute($route, string $version): bool
    {
        $uri = $route->uri();
        $name = $route->getName();
        
        // Check URI pattern
        if (str_contains($uri, "api/v{$version}") || str_contains($uri, "v{$version}/")) {
            return true;
        }
        
        // Check route name pattern
        if ($name && str_contains($name, "v{$version}.")) {
            return true;
        }
        
        // Check if it's a general API route and version is default
        if (str_contains($uri, 'api/') && $version === $this->versionManager->getDefaultVersion()) {
            return true;
        }
        
        return false;
    }

    /**
     * Generate paths from routes
     */
    protected function generatePaths(array $routes, string $version): array
    {
        $paths = [];
        
        foreach ($routes as $route) {
            $path = $this->normalizeRoutePath($route->uri());
            $methods = $route->methods();
            
            if (!isset($paths[$path])) {
                $paths[$path] = [];
            }
            
            foreach ($methods as $method) {
                if (in_array(strtoupper($method), ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'])) {
                    $paths[$path][strtolower($method)] = $this->generatePathOperation($route, $method, $version);
                }
            }
        }
        
        return $paths;
    }

    /**
     * Generate path operation
     */
    protected function generatePathOperation($route, string $method, string $version): array
    {
        $action = $route->getAction();
        $controller = $action['controller'] ?? null;
        
        $operation = [
            'summary' => $this->generateOperationSummary($route, $method),
            'description' => $this->generateOperationDescription($route, $method),
            'operationId' => $this->generateOperationId($route, $method),
            'tags' => $this->generateOperationTags($route),
            'parameters' => $this->generateOperationParameters($route),
            'responses' => $this->generateOperationResponses($route, $method),
        ];
        
        // Add request body for POST/PUT/PATCH
        if (in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
            $operation['requestBody'] = $this->generateRequestBody($route, $method);
        }
        
        // Add security if route requires authentication
        if ($this->routeRequiresAuth($route)) {
            $operation['security'] = [['bearerAuth' => []]];
        }
        
        return $operation;
    }

    /**
     * Generate components
     */
    protected function generateComponents(array $routes, string $version): array
    {
        return [
            'schemas' => $this->generateSchemas($routes),
            'responses' => $this->getStandardResponses(),
            'parameters' => $this->getStandardParameters(),
            'securitySchemes' => $this->getSecuritySchemes(),
        ];
    }

    /**
     * Generate schemas from models and DTOs
     */
    protected function generateSchemas(array $routes): array
    {
        $schemas = [];
        
        // Standard API response schemas
        $schemas['ApiResponse'] = [
            'type' => 'object',
            'properties' => [
                'success' => ['type' => 'boolean'],
                'data' => ['type' => 'object'],
                'meta' => ['type' => 'object'],
                'message' => ['type' => 'string'],
            ],
            'required' => ['success'],
        ];
        
        $schemas['ApiError'] = [
            'type' => 'object',
            'properties' => [
                'success' => ['type' => 'boolean', 'example' => false],
                'message' => ['type' => 'string'],
                'errors' => ['type' => 'object'],
                'meta' => ['type' => 'object'],
            ],
            'required' => ['success', 'message'],
        ];
        
        $schemas['PaginationMeta'] = [
            'type' => 'object',
            'properties' => [
                'pagination' => [
                    'type' => 'object',
                    'properties' => [
                        'current_page' => ['type' => 'integer'],
                        'per_page' => ['type' => 'integer'],
                        'total' => ['type' => 'integer'],
                        'last_page' => ['type' => 'integer'],
                        'from' => ['type' => 'integer'],
                        'to' => ['type' => 'integer'],
                    ],
                ],
            ],
        ];
        
        return $schemas;
    }

    /**
     * Get default configuration
     */
    protected function getDefaultConfig(): array
    {
        return [
            'title' => config('api_infrastructure.documentation.title', 'API Documentation'),
            'description' => config('api_infrastructure.documentation.description', 'API Documentation'),
            'contact' => config('api_infrastructure.documentation.contact', []),
            'license' => config('api_infrastructure.documentation.license', []),
            'servers' => [
                [
                    'url' => config('app.url') . '/api',
                    'description' => 'Production server',
                ],
            ],
        ];
    }

    /**
     * Generate servers configuration
     */
    protected function generateServers(): array
    {
        return $this->config['servers'] ?? [
            [
                'url' => config('app.url') . '/api',
                'description' => 'API Server',
            ],
        ];
    }

    /**
     * Get standard responses
     */
    protected function getStandardResponses(): array
    {
        return [
            'Success' => [
                'description' => 'Successful response',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiResponse'],
                    ],
                ],
            ],
            'Error' => [
                'description' => 'Error response',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiError'],
                    ],
                ],
            ],
            'ValidationError' => [
                'description' => 'Validation error',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiError'],
                    ],
                ],
            ],
            'Unauthorized' => [
                'description' => 'Unauthorized',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiError'],
                    ],
                ],
            ],
            'NotFound' => [
                'description' => 'Resource not found',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/ApiError'],
                    ],
                ],
            ],
        ];
    }

    /**
     * Get standard parameters
     */
    protected function getStandardParameters(): array
    {
        return [
            'page' => [
                'name' => 'page',
                'in' => 'query',
                'description' => 'Page number',
                'schema' => ['type' => 'integer', 'minimum' => 1, 'default' => 1],
            ],
            'per_page' => [
                'name' => 'per_page',
                'in' => 'query',
                'description' => 'Items per page',
                'schema' => ['type' => 'integer', 'minimum' => 1, 'maximum' => 100, 'default' => 15],
            ],
            'sort' => [
                'name' => 'sort',
                'in' => 'query',
                'description' => 'Sort field',
                'schema' => ['type' => 'string'],
            ],
            'order' => [
                'name' => 'order',
                'in' => 'query',
                'description' => 'Sort order',
                'schema' => ['type' => 'string', 'enum' => ['asc', 'desc'], 'default' => 'asc'],
            ],
        ];
    }

    /**
     * Get security schemes
     */
    protected function getSecuritySchemes(): array
    {
        return [
            'bearerAuth' => [
                'type' => 'http',
                'scheme' => 'bearer',
                'bearerFormat' => 'JWT',
            ],
            'apiKey' => [
                'type' => 'apiKey',
                'in' => 'header',
                'name' => 'X-API-Key',
            ],
        ];
    }

    /**
     * Get global security
     */
    protected function getGlobalSecurity(): array
    {
        return [];
    }

    /**
     * Generate tags
     */
    protected function generateTags(): array
    {
        return [
            ['name' => 'Products', 'description' => 'Product management'],
            ['name' => 'Categories', 'description' => 'Category management'],
            ['name' => 'Orders', 'description' => 'Order management'],
            ['name' => 'Users', 'description' => 'User management'],
            ['name' => 'Authentication', 'description' => 'Authentication endpoints'],
            ['name' => 'Payment', 'description' => 'Payment processing'],
        ];
    }

    // Helper methods for generating specific parts
    protected function normalizeRoutePath(string $path): string
    {
        // Convert Laravel route parameters to OpenAPI format
        return preg_replace('/\{([^}]+)\}/', '{$1}', $path);
    }

    protected function generateOperationSummary($route, string $method): string
    {
        $action = $route->getAction();
        $controller = $action['controller'] ?? '';
        
        if ($controller) {
            [$class, $method] = explode('@', $controller);
            $className = class_basename($class);
            return Str::title(str_replace('Controller', '', $className)) . ' ' . Str::title($method);
        }
        
        return Str::title($method) . ' ' . $route->uri();
    }

    protected function generateOperationDescription($route, string $method): string
    {
        return $this->generateOperationSummary($route, $method);
    }

    protected function generateOperationId($route, string $method): string
    {
        $name = $route->getName();
        if ($name) {
            return str_replace('.', '_', $name);
        }
        
        return strtolower($method) . '_' . str_replace(['/', '{', '}'], ['_', '', ''], $route->uri());
    }

    protected function generateOperationTags($route): array
    {
        $uri = $route->uri();
        
        if (str_contains($uri, 'products')) return ['Products'];
        if (str_contains($uri, 'categories')) return ['Categories'];
        if (str_contains($uri, 'orders')) return ['Orders'];
        if (str_contains($uri, 'users')) return ['Users'];
        if (str_contains($uri, 'auth')) return ['Authentication'];
        if (str_contains($uri, 'payment')) return ['Payment'];
        
        return ['General'];
    }

    protected function generateOperationParameters($route): array
    {
        $parameters = [];
        
        // Extract path parameters
        preg_match_all('/\{([^}]+)\}/', $route->uri(), $matches);
        foreach ($matches[1] as $param) {
            $parameters[] = [
                'name' => $param,
                'in' => 'path',
                'required' => true,
                'schema' => ['type' => 'string'],
            ];
        }
        
        return $parameters;
    }

    protected function generateOperationResponses($route, string $method): array
    {
        $responses = [
            '200' => ['$ref' => '#/components/responses/Success'],
            '400' => ['$ref' => '#/components/responses/Error'],
            '422' => ['$ref' => '#/components/responses/ValidationError'],
        ];
        
        if ($this->routeRequiresAuth($route)) {
            $responses['401'] = ['$ref' => '#/components/responses/Unauthorized'];
        }
        
        if (strtoupper($method) === 'POST') {
            $responses['201'] = ['$ref' => '#/components/responses/Success'];
        }
        
        if (strtoupper($method) === 'DELETE') {
            $responses['204'] = ['description' => 'No content'];
        }
        
        return $responses;
    }

    protected function generateRequestBody($route, string $method): array
    {
        return [
            'required' => true,
            'content' => [
                'application/json' => [
                    'schema' => ['type' => 'object'],
                ],
            ],
        ];
    }

    protected function routeRequiresAuth($route): bool
    {
        $middleware = $route->middleware();
        return in_array('auth:sanctum', $middleware) || in_array('auth', $middleware);
    }

    protected function addVersionInfo(string $version): void
    {
        $versionInfo = $this->versionManager->getVersionInfo($version);
        
        if ($versionInfo) {
            $this->generatedSpec['info']['x-version-info'] = $versionInfo;
        }
    }

    protected function convertToPostmanCollection(array $openApiSpec): array
    {
        // Convert OpenAPI spec to Postman collection format
        // This is a simplified implementation
        return [
            'info' => [
                'name' => $openApiSpec['info']['title'],
                'description' => $openApiSpec['info']['description'],
                'version' => $openApiSpec['info']['version'],
            ],
            'item' => [], // Convert paths to Postman items
        ];
    }
}
