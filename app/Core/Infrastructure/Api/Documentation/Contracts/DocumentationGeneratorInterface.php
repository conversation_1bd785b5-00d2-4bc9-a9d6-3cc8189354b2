<?php

namespace App\Core\Infrastructure\Api\Documentation\Contracts;

/**
 * DocumentationGeneratorInterface
 * API documentation generator contract
 */
interface DocumentationGeneratorInterface
{
    /**
     * Generate API documentation
     */
    public function generate(array $options = []): array;

    /**
     * Generate documentation for specific version
     */
    public function generateForVersion(string $version, array $options = []): array;

    /**
     * Generate OpenAPI specification
     */
    public function generateOpenApiSpec(string $version = null): array;

    /**
     * Generate Postman collection
     */
    public function generatePostmanCollection(string $version = null): array;

    /**
     * Get supported formats
     */
    public function getSupportedFormats(): array;

    /**
     * Export documentation to file
     */
    public function exportToFile(string $format, string $filePath, array $options = []): bool;

    /**
     * Validate documentation structure
     */
    public function validateDocumentation(array $documentation): array;

    /**
     * Get documentation metadata
     */
    public function getMetadata(): array;
}
