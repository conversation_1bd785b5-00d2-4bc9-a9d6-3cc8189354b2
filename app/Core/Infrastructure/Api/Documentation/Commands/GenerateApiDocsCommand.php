<?php

namespace App\Core\Infrastructure\Api\Documentation\Commands;

use App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface;
use App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

/**
 * GenerateApiDocsCommand
 * Generate API documentation command
 */
class GenerateApiDocsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'api:docs:generate
                            {--api-version= : Generate docs for specific API version}
                            {--format=json : Output format (json, yaml, all)}
                            {--output= : Output directory}
                            {--force : Overwrite existing files}
                            {--validate : Validate generated documentation}
                            {--cache : Cache generated documentation}';

    /**
     * The console command description.
     */
    protected $description = 'Generate API documentation';

    protected DocumentationGeneratorInterface $documentationGenerator;
    protected ApiVersionManagerInterface $versionManager;

    public function __construct(
        DocumentationGeneratorInterface $documentationGenerator,
        ApiVersionManagerInterface $versionManager
    ) {
        parent::__construct();
        $this->documentationGenerator = $documentationGenerator;
        $this->versionManager = $versionManager;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Generating API Documentation...');
        $this->newLine();

        try {
            $version = $this->option('api-version');
            $format = $this->option('format');
            $outputDir = $this->option('output') ?? storage_path('api-docs');
            $force = $this->option('force');
            $validate = $this->option('validate');
            $cache = $this->option('cache');

            // Ensure output directory exists
            if (!File::exists($outputDir)) {
                File::makeDirectory($outputDir, 0755, true);
                $this->info("📁 Created output directory: {$outputDir}");
            }

            // Get versions to generate
            $versions = $version ? [$version] : $this->versionManager->getSupportedVersions();

            if (empty($versions)) {
                $this->error('❌ No API versions found');
                return self::FAILURE;
            }

            $this->info("📋 Generating documentation for versions: " . implode(', ', $versions));
            $this->newLine();

            $progressBar = $this->output->createProgressBar(count($versions));
            $progressBar->setFormat('verbose');

            $results = [];

            foreach ($versions as $ver) {
                $progressBar->setMessage("Generating v{$ver}...");

                try {
                    $result = $this->generateForVersion($ver, $format, $outputDir, $force, $validate);
                    $results[$ver] = $result;

                    if ($cache) {
                        $this->cacheDocumentation($ver, $result['specification']);
                    }

                } catch (\Exception $e) {
                    $results[$ver] = [
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];
                }

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            // Display results
            $this->displayResults($results);

            // Generate index file
            $this->generateIndexFile($outputDir, $results);

            $this->newLine();
            $this->info('✅ API documentation generation completed!');
            $this->info("📂 Output directory: {$outputDir}");

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Failed to generate API documentation: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    /**
     * Generate documentation for specific version
     */
    protected function generateForVersion(
        string $version,
        string $format,
        string $outputDir,
        bool $force,
        bool $validate
    ): array {
        $specification = $this->documentationGenerator->generateForVersion($version);

        // Validate if requested
        if ($validate) {
            $errors = $this->documentationGenerator->validateDocumentation($specification);
            if (!empty($errors)) {
                throw new \Exception("Validation failed for v{$version}: " . implode(', ', $errors));
            }
        }

        $result = [
            'success' => true,
            'specification' => $specification,
            'files' => [],
            'validation_errors' => [],
        ];

        // Generate files based on format
        $formats = $format === 'all' ? ['json', 'yaml'] : [$format];

        foreach ($formats as $fmt) {
            $filename = "api-docs-v{$version}.{$fmt}";
            $filepath = "{$outputDir}/{$filename}";

            // Check if file exists and force is not set
            if (File::exists($filepath) && !$force) {
                $this->warn("⚠️  File exists, skipping: {$filename} (use --force to overwrite)");
                continue;
            }

            // Generate content
            $content = $this->formatContent($specification, $fmt);

            // Write file
            File::put($filepath, $content);
            $result['files'][] = $filepath;
        }

        return $result;
    }

    /**
     * Format content based on format
     */
    protected function formatContent(array $specification, string $format): string
    {
        switch ($format) {
            case 'yaml':
                return yaml_emit($specification);
            case 'json':
            default:
                return json_encode($specification, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        }
    }

    /**
     * Cache documentation
     */
    protected function cacheDocumentation(string $version, array $specification): void
    {
        $cacheKey = "api_docs_spec_{$version}_json";
        $cacheTtl = config('api_infrastructure.documentation.cache_ttl', 3600);

        cache()->put($cacheKey, $specification, $cacheTtl);

        $this->line("💾 Cached documentation for v{$version}");
    }

    /**
     * Display generation results
     */
    protected function displayResults(array $results): void
    {
        $this->info('📊 Generation Results:');
        $this->newLine();

        $headers = ['Version', 'Status', 'Files', 'Paths', 'Schemas', 'Details'];
        $rows = [];

        foreach ($results as $version => $result) {
            if ($result['success']) {
                $spec = $result['specification'];
                $pathsCount = count($spec['paths'] ?? []);
                $schemasCount = count($spec['components']['schemas'] ?? []);
                $filesCount = count($result['files']);

                $rows[] = [
                    "v{$version}",
                    '✅ Success',
                    $filesCount,
                    $pathsCount,
                    $schemasCount,
                    "Generated {$filesCount} file(s)",
                ];
            } else {
                $rows[] = [
                    "v{$version}",
                    '❌ Failed',
                    '0',
                    '-',
                    '-',
                    $result['error'] ?? 'Unknown error',
                ];
            }
        }

        $this->table($headers, $rows);
    }

    /**
     * Generate index HTML file
     */
    protected function generateIndexFile(string $outputDir, array $results): void
    {
        $html = $this->buildIndexHtml($results);
        $indexPath = "{$outputDir}/index.html";

        File::put($indexPath, $html);
        $this->info("📄 Generated index file: {$indexPath}");
    }

    /**
     * Build index HTML content
     */
    protected function buildIndexHtml(array $results): string
    {
        $title = config('api_infrastructure.documentation.title', 'API Documentation');
        $description = config('api_infrastructure.documentation.description', 'API Documentation');

        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; margin-bottom: 10px; }
        .description { color: #7f8c8d; margin-bottom: 30px; }
        .version-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .version-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
        .version-card.error { border-left-color: #e74c3c; }
        .version-title { font-size: 1.2em; font-weight: bold; margin-bottom: 10px; color: #2c3e50; }
        .version-stats { margin: 10px 0; }
        .version-files { margin-top: 15px; }
        .version-files a { display: inline-block; margin: 5px 10px 5px 0; padding: 8px 15px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; font-size: 0.9em; }
        .version-files a:hover { background: #2980b9; }
        .error-message { color: #e74c3c; font-style: italic; }
        .footer { margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>' . $title . '</h1>
        <p class="description">' . $description . '</p>
        <p><strong>Generated:</strong> ' . now()->format('Y-m-d H:i:s') . '</p>

        <div class="version-grid">';

        foreach ($results as $version => $result) {
            $cardClass = $result['success'] ? 'version-card' : 'version-card error';

            $html .= '<div class="' . $cardClass . '">
                <div class="version-title">API v' . $version . '</div>';

            if ($result['success']) {
                $spec = $result['specification'];
                $pathsCount = count($spec['paths'] ?? []);
                $schemasCount = count($spec['components']['schemas'] ?? []);

                $html .= '<div class="version-stats">
                    <div>📊 <strong>' . $pathsCount . '</strong> API endpoints</div>
                    <div>🏗️ <strong>' . $schemasCount . '</strong> data schemas</div>
                    <div>📁 <strong>' . count($result['files']) . '</strong> generated files</div>
                </div>
                <div class="version-files">';

                foreach ($result['files'] as $file) {
                    $filename = basename($file);
                    $html .= '<a href="' . $filename . '">' . $filename . '</a>';
                }

                $html .= '</div>';
            } else {
                $html .= '<div class="error-message">❌ ' . ($result['error'] ?? 'Generation failed') . '</div>';
            }

            $html .= '</div>';
        }

        $html .= '</div>
        <div class="footer">
            <p>Generated by ModularEcommerce API Documentation Generator</p>
        </div>
    </div>
</body>
</html>';

        return $html;
    }
}
