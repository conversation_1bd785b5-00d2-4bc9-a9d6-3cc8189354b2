<?php

namespace App\Core\Infrastructure\Api\Contracts;

use Illuminate\Http\JsonResponse;

/**
 * ApiResponseInterface
 * Standardize API response interface
 */
interface ApiResponseInterface
{
    /**
     * Başarılı response oluştur
     */
    public function success($data = null, string $message = '', int $statusCode = 200, array $meta = []): JsonResponse;

    /**
     * Hata response'u oluştur
     */
    public function error(string $message, int $statusCode = 400, array $errors = [], array $meta = []): JsonResponse;

    /**
     * Validation error response'u oluştur
     */
    public function validationError(array $errors, string $message = 'Validation failed', int $statusCode = 422): JsonResponse;

    /**
     * Not found response'u oluştur
     */
    public function notFound(string $message = 'Resource not found'): JsonResponse;

    /**
     * Unauthorized response'u oluştur
     */
    public function unauthorized(string $message = 'Unauthorized'): JsonResponse;

    /**
     * Forbidden response'u oluştur
     */
    public function forbidden(string $message = 'Forbidden'): JsonResponse;

    /**
     * Server error response'u oluştur
     */
    public function serverError(string $message = 'Internal server error'): JsonResponse;

    /**
     * Paginated response oluştur
     */
    public function paginated($data, array $pagination, string $message = '', array $meta = []): JsonResponse;

    /**
     * Collection response oluştur
     */
    public function collection($data, string $message = '', array $meta = []): JsonResponse;

    /**
     * Resource response oluştur
     */
    public function resource($data, string $message = '', array $meta = []): JsonResponse;

    /**
     * Created response oluştur
     */
    public function created($data = null, string $message = 'Resource created successfully'): JsonResponse;

    /**
     * Updated response oluştur
     */
    public function updated($data = null, string $message = 'Resource updated successfully'): JsonResponse;

    /**
     * Deleted response oluştur
     */
    public function deleted(string $message = 'Resource deleted successfully'): JsonResponse;

    /**
     * No content response oluştur
     */
    public function noContent(): JsonResponse;

    /**
     * Custom response oluştur
     */
    public function custom(array $data, int $statusCode = 200): JsonResponse;

    /**
     * Response format'ını ayarla
     */
    public function setFormat(string $format): self;

    /**
     * Response version'ını ayarla
     */
    public function setVersion(string $version): self;

    /**
     * Response locale'ini ayarla
     */
    public function setLocale(string $locale): self;

    /**
     * Debug mode'u ayarla
     */
    public function setDebugMode(bool $debug): self;
}
