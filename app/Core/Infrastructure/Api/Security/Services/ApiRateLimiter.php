<?php

namespace App\Core\Infrastructure\Api\Security\Services;

use App\Core\Infrastructure\Api\Security\Contracts\RateLimiterInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

/**
 * ApiRateLimiter
 * API rate limiting service
 */
class ApiRateLimiter implements RateLimiterInterface
{
    private array $config;
    private string $driver;
    private string $prefix;

    public function __construct()
    {
        // Config helper'ı mevcut değilse default değ<PERSON>ler kullan
        try {
            $this->config = config('api_rate_limiting', []);
        } catch (\Exception $e) {
            $this->config = $this->getDefaultConfig();
        }

        $this->driver = $this->config['general']['driver'] ?? 'redis';
        $this->prefix = $this->config['cache']['prefix'] ?? 'api_rate_limit';
    }

    /**
     * Default config'i al
     */
    private function getDefaultConfig(): array
    {
        return [
            'general' => [
                'enabled' => true,
                'driver' => 'redis',
                'key_generator' => 'ip',
            ],
            'cache' => [
                'prefix' => 'api_rate_limit',
            ],
            'bypass' => [
                'enabled' => false,
                'ips' => [],
                'user_agents' => [],
                'api_keys' => [],
                'routes' => [],
            ],
        ];
    }

    /**
     * Request'in rate limit'ini kontrol et
     */
    public function tooManyAttempts(Request $request, string $key, int $maxAttempts, int $decayMinutes = 1): bool
    {
        if (!$this->config['general']['enabled']) {
            return false;
        }

        // Bypass kontrolü
        if ($this->shouldBypass($request)) {
            return false;
        }

        $attempts = $this->getAttempts($key);

        return $attempts >= $maxAttempts;
    }

    /**
     * Request'i rate limit counter'a ekle
     */
    public function hit(Request $request, string $key, int $decayMinutes = 1): int
    {
        $cacheKey = $this->getCacheKey($key);
        $ttl = $decayMinutes * 60;

        if ($this->driver === 'redis') {
            return $this->hitRedis($cacheKey, $ttl);
        }

        return $this->hitCache($cacheKey, $ttl);
    }

    /**
     * Kalan attempt sayısını al
     */
    public function retriesLeft(string $key, int $maxAttempts): int
    {
        $attempts = $this->getAttempts($key);
        return max(0, $maxAttempts - $attempts);
    }

    /**
     * Rate limit'i temizle
     */
    public function clear(string $key): void
    {
        $cacheKey = $this->getCacheKey($key);

        if ($this->driver === 'redis') {
            Redis::del($cacheKey);
        } else {
            Cache::forget($cacheKey);
        }
    }

    /**
     * Rate limit key'ini oluştur
     */
    public function resolveRequestSignature(Request $request, string $prefix = ''): string
    {
        $keyGenerator = $this->config['general']['key_generator'] ?? 'ip';

        switch ($keyGenerator) {
            case 'user':
                return $prefix . 'user:' . ($request->user()?->id ?? 'guest') . ':' . $request->ip();
            case 'api_key':
                return $prefix . 'api_key:' . ($request->header('X-API-Key') ?? 'none') . ':' . $request->ip();
            case 'ip':
            default:
                return $prefix . 'ip:' . $request->ip();
        }
    }

    /**
     * Rate limit bilgilerini al
     */
    public function getLimitInfo(string $key): array
    {
        $cacheKey = $this->getCacheKey($key);
        $attempts = $this->getAttempts($key);
        $ttl = $this->getTtl($cacheKey);

        return [
            'key' => $key,
            'attempts' => $attempts,
            'ttl' => $ttl,
            'reset_at' => now()->addSeconds($ttl),
        ];
    }

    /**
     * Retry-After header değerini hesapla
     */
    public function getRetryAfter(string $key): int
    {
        $cacheKey = $this->getCacheKey($key);
        return $this->getTtl($cacheKey);
    }

    /**
     * Rate limit'e takılan IP'leri al
     */
    public function getBlockedIps(): array
    {
        $pattern = $this->getCacheKey('ip:*');

        if ($this->driver === 'redis') {
            $keys = Redis::keys($pattern);
            $blockedIps = [];

            foreach ($keys as $key) {
                $ip = str_replace($this->getCacheKey('ip:'), '', $key);
                $attempts = Redis::get($key);
                $ttl = Redis::ttl($key);

                $blockedIps[] = [
                    'ip' => $ip,
                    'attempts' => $attempts,
                    'ttl' => $ttl,
                    'blocked_until' => now()->addSeconds($ttl),
                ];
            }

            return $blockedIps;
        }

        // Cache driver için basit implementation
        return [];
    }

    /**
     * IP'yi rate limit'ten çıkar
     */
    public function unblockIp(string $ip): void
    {
        $key = 'ip:' . $ip;
        $this->clear($key);
    }

    /**
     * Rate limit istatistiklerini al
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_blocked_ips' => count($this->getBlockedIps()),
            'driver' => $this->driver,
            'enabled' => $this->config['general']['enabled'],
            'limits' => $this->config['limits'] ?? [],
        ];

        if ($this->driver === 'redis') {
            $pattern = $this->getCacheKey('*');
            $keys = Redis::keys($pattern);
            $stats['total_rate_limit_keys'] = count($keys);
        }

        return $stats;
    }

    /**
     * Bypass kontrolü
     */
    private function shouldBypass(Request $request): bool
    {
        if (!$this->config['bypass']['enabled']) {
            return false;
        }

        // IP bypass
        $bypassIps = $this->config['bypass']['ips'] ?? [];
        if (in_array($request->ip(), $bypassIps)) {
            return true;
        }

        // User agent bypass
        $bypassUserAgents = $this->config['bypass']['user_agents'] ?? [];
        $userAgent = $request->userAgent();
        foreach ($bypassUserAgents as $pattern) {
            if (str_contains($userAgent, $pattern)) {
                return true;
            }
        }

        // API key bypass
        $bypassApiKeys = $this->config['bypass']['api_keys'] ?? [];
        $apiKey = $request->header('X-API-Key');
        if ($apiKey && in_array($apiKey, $bypassApiKeys)) {
            return true;
        }

        // Route bypass
        $bypassRoutes = $this->config['bypass']['routes'] ?? [];
        $path = $request->path();
        foreach ($bypassRoutes as $pattern) {
            if ($this->matchesPattern($path, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Attempt sayısını al
     */
    private function getAttempts(string $key): int
    {
        $cacheKey = $this->getCacheKey($key);

        if ($this->driver === 'redis') {
            return (int) Redis::get($cacheKey) ?: 0;
        }

        return (int) Cache::get($cacheKey, 0);
    }

    /**
     * Redis ile hit
     */
    private function hitRedis(string $cacheKey, int $ttl): int
    {
        $current = Redis::incr($cacheKey);

        if ($current === 1) {
            Redis::expire($cacheKey, $ttl);
        }

        return $current;
    }

    /**
     * Cache ile hit
     */
    private function hitCache(string $cacheKey, int $ttl): int
    {
        $current = Cache::get($cacheKey, 0) + 1;
        Cache::put($cacheKey, $current, $ttl);

        return $current;
    }

    /**
     * TTL al
     */
    private function getTtl(string $cacheKey): int
    {
        if ($this->driver === 'redis') {
            return Redis::ttl($cacheKey) ?: 0;
        }

        // Cache driver için approximate TTL
        return 60; // Default 1 minute
    }

    /**
     * Cache key oluştur
     */
    private function getCacheKey(string $key): string
    {
        return $this->prefix . ':' . $key;
    }

    /**
     * Pattern matching
     */
    private function matchesPattern(string $path, string $pattern): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        $pattern = '/^' . str_replace('/', '\/', $pattern) . '$/';

        return preg_match($pattern, $path);
    }
}
