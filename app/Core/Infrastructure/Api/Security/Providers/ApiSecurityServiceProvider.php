<?php

namespace App\Core\Infrastructure\Api\Security\Providers;

use App\Core\Infrastructure\Api\Security\Contracts\RateLimiterInterface;
use App\Core\Infrastructure\Api\Security\Contracts\SecurityManagerInterface;
use App\Core\Infrastructure\Api\Security\Contracts\ApiKeyManagerInterface;
use App\Core\Infrastructure\Api\Security\Services\ApiRateLimiter;
use App\Core\Infrastructure\Api\Security\Services\SecurityManager;
use App\Core\Infrastructure\Api\Security\Services\ApiKeyManager;
use App\Core\Infrastructure\Api\Security\Middleware\ApiRateLimitMiddleware;
use App\Core\Infrastructure\Api\Security\Middleware\ApiSecurityMiddleware;
use App\Core\Infrastructure\Api\Security\Middleware\ApiKeyMiddleware;
use Illuminate\Support\ServiceProvider;
use Illuminate\Console\Scheduling\Schedule;

/**
 * ApiSecurityServiceProvider
 * API security infrastructure service provider
 */
class ApiSecurityServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerSecurityServices();
        $this->registerSecurityConfiguration();
        $this->registerSecurityAliases();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->publishConfiguration();
        $this->registerSecurityMiddleware();
        $this->registerScheduledTasks();
        $this->loadMigrations();
    }

    /**
     * Security servislerini kaydet
     */
    private function registerSecurityServices(): void
    {
        // Rate Limiter
        $this->app->singleton(RateLimiterInterface::class, function ($app) {
            return new ApiRateLimiter();
        });

        // Security Manager
        $this->app->singleton(SecurityManagerInterface::class, function ($app) {
            return new SecurityManager();
        });

        // API Key Manager
        $this->app->singleton(ApiKeyManagerInterface::class, function ($app) {
            return new ApiKeyManager();
        });

        // Concrete implementations
        $this->app->singleton(ApiRateLimiter::class, function ($app) {
            return $app->make(RateLimiterInterface::class);
        });

        $this->app->singleton(SecurityManager::class, function ($app) {
            return $app->make(SecurityManagerInterface::class);
        });

        $this->app->singleton(ApiKeyManager::class, function ($app) {
            return $app->make(ApiKeyManagerInterface::class);
        });
    }

    /**
     * Security konfigürasyonunu kaydet
     */
    private function registerSecurityConfiguration(): void
    {
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/api_rate_limiting.php',
            'api_rate_limiting'
        );
    }

    /**
     * Security alias'larını kaydet
     */
    private function registerSecurityAliases(): void
    {
        $this->app->alias(RateLimiterInterface::class, 'api.rate_limiter');
        $this->app->alias(SecurityManagerInterface::class, 'api.security_manager');
        $this->app->alias(ApiKeyManagerInterface::class, 'api.key_manager');
        $this->app->alias(ApiRateLimiter::class, 'api.rate_limiter.service');
        $this->app->alias(SecurityManager::class, 'api.security_manager.service');
        $this->app->alias(ApiKeyManager::class, 'api.key_manager.service');
    }

    /**
     * Konfigürasyonu publish et
     */
    private function publishConfiguration(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../Config/api_rate_limiting.php' => config_path('api_rate_limiting.php'),
            ], 'api-security-config');
        }
    }

    /**
     * Security middleware'lerini kaydet
     */
    private function registerSecurityMiddleware(): void
    {
        $router = $this->app['router'];

        // Rate limiting middleware
        $router->aliasMiddleware('api.rate_limit', ApiRateLimitMiddleware::class);
        
        // Security middleware
        $router->aliasMiddleware('api.security', ApiSecurityMiddleware::class);
        
        // API key middleware
        $router->aliasMiddleware('api.key', ApiKeyMiddleware::class);

        // API middleware group'una ekle
        if (config('api_rate_limiting.general.enabled', true)) {
            $router->pushMiddlewareToGroup('api', ApiRateLimitMiddleware::class);
        }

        if (config('api_rate_limiting.security.enabled', true)) {
            $router->pushMiddlewareToGroup('api', ApiSecurityMiddleware::class);
        }
    }

    /**
     * Scheduled task'ları kaydet
     */
    private function registerScheduledTasks(): void
    {
        if ($this->app->runningInConsole()) {
            $this->app->booted(function () {
                $schedule = $this->app->make(Schedule::class);
                
                // API key cleanup task
                $schedule->call(function () {
                    $apiKeyManager = $this->app->make(ApiKeyManagerInterface::class);
                    $cleaned = $apiKeyManager->cleanupExpiredKeys();
                    
                    if ($cleaned > 0) {
                        \Log::info("Cleaned up {$cleaned} expired API keys");
                    }
                })->daily()->at('02:00');

                // Rate limit cache cleanup
                $schedule->call(function () {
                    $this->cleanupRateLimitCache();
                })->hourly();
            });
        }
    }

    /**
     * Migration'ları yükle
     */
    private function loadMigrations(): void
    {
        if ($this->app->runningInConsole()) {
            $this->loadMigrationsFrom(__DIR__ . '/../../../../../../database/migrations');
        }
    }

    /**
     * Rate limit cache'ini temizle
     */
    private function cleanupRateLimitCache(): void
    {
        $cacheConfig = config('api_rate_limiting.cache', []);
        
        if (!($cacheConfig['cleanup_enabled'] ?? true)) {
            return;
        }

        try {
            $rateLimiter = $this->app->make(RateLimiterInterface::class);
            
            // Bu method rate limiter'da implement edilebilir
            if (method_exists($rateLimiter, 'cleanup')) {
                $rateLimiter->cleanup();
            }
        } catch (\Exception $e) {
            \Log::error('Failed to cleanup rate limit cache', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            RateLimiterInterface::class,
            SecurityManagerInterface::class,
            ApiKeyManagerInterface::class,
            ApiRateLimiter::class,
            SecurityManager::class,
            ApiKeyManager::class,
            'api.rate_limiter',
            'api.security_manager',
            'api.key_manager',
            'api.rate_limiter.service',
            'api.security_manager.service',
            'api.key_manager.service',
        ];
    }

    /**
     * API security infrastructure'ın yüklenip yüklenmediğini kontrol et
     */
    public function isLoaded(): bool
    {
        return $this->app->bound(RateLimiterInterface::class) &&
               $this->app->bound(SecurityManagerInterface::class) &&
               $this->app->bound(ApiKeyManagerInterface::class);
    }

    /**
     * API security infrastructure version'ını al
     */
    public function getVersion(): string
    {
        return '1.0.0';
    }

    /**
     * Enabled feature'ları al
     */
    public function getEnabledFeatures(): array
    {
        return [
            'rate_limiting' => config('api_rate_limiting.general.enabled', true),
            'security_filtering' => config('api_rate_limiting.security.enabled', true),
            'ip_filtering' => config('api_rate_limiting.ip_filtering.enabled', false),
            'api_keys' => config('api_rate_limiting.api_keys.enabled', true),
            'cors' => config('api_rate_limiting.cors.enabled', true),
            'security_headers' => config('api_rate_limiting.security_headers.enabled', true),
            'monitoring' => config('api_rate_limiting.monitoring.enabled', true),
        ];
    }

    /**
     * API security infrastructure health check
     */
    public function healthCheck(): array
    {
        $services = [
            'rate_limiter' => $this->app->bound(RateLimiterInterface::class),
            'security_manager' => $this->app->bound(SecurityManagerInterface::class),
            'api_key_manager' => $this->app->bound(ApiKeyManagerInterface::class),
        ];

        $middleware = [
            'rate_limit' => $this->app['router']->hasMiddlewareGroup('api'),
            'security' => config('api_rate_limiting.security.enabled', true),
            'api_key' => config('api_rate_limiting.api_keys.enabled', true),
        ];

        return [
            'status' => 'healthy',
            'version' => $this->getVersion(),
            'features' => $this->getEnabledFeatures(),
            'services' => $services,
            'middleware' => $middleware,
            'driver' => config('api_rate_limiting.general.driver', 'redis'),
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Security statistics'leri al
     */
    public function getSecurityStatistics(): array
    {
        try {
            $rateLimiter = $this->app->make(RateLimiterInterface::class);
            $securityManager = $this->app->make(SecurityManagerInterface::class);

            return [
                'rate_limiting' => $rateLimiter->getStatistics(),
                'security' => $securityManager->getSecurityStatistics(),
                'timestamp' => now()->toISOString(),
            ];
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ];
        }
    }
}
