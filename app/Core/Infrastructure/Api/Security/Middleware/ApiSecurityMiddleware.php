<?php

namespace App\Core\Infrastructure\Api\Security\Middleware;

use App\Core\Infrastructure\Api\Security\Contracts\SecurityManagerInterface;
use App\Core\Infrastructure\Api\Contracts\ApiResponseInterface;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * ApiSecurityMiddleware
 * API security middleware
 */
class ApiSecurityMiddleware
{
    private SecurityManagerInterface $securityManager;
    private ApiResponseInterface $apiResponse;
    private array $config;

    public function __construct(
        SecurityManagerInterface $securityManager,
        ApiResponseInterface $apiResponse
    ) {
        $this->securityManager = $securityManager;
        $this->apiResponse = $apiResponse;
        $this->config = config('api_rate_limiting', []);
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Security enabled kontrolü
        if (!($this->config['security']['enabled'] ?? true)) {
            return $this->addSecurityHeaders($next($request));
        }

        // IP filtering kontrolü
        if (!$this->checkIpFiltering($request)) {
            return $this->buildSecurityResponse('IP address not allowed', 403);
        }

        // Request security kontrolü
        if (!$this->securityManager->isRequestSecure($request)) {
            $this->securityManager->logSecurityEvent('insecure_request', [
                'ip' => $request->ip(),
                'path' => $request->path(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
            ]);

            if ($this->config['security']['block_suspicious_requests'] ?? true) {
                return $this->buildSecurityResponse('Request blocked for security reasons', 403);
            }
        }

        // CORS preflight kontrolü
        if ($request->isMethod('OPTIONS')) {
            return $this->handlePreflightRequest($request);
        }

        // Request'i işle
        $response = $next($request);

        // Security header'larını ekle
        return $this->addSecurityHeaders($response, $request);
    }

    /**
     * IP filtering kontrolü
     */
    private function checkIpFiltering(Request $request): bool
    {
        $ipFiltering = $this->config['ip_filtering'] ?? [];
        
        if (!($ipFiltering['enabled'] ?? false)) {
            return true;
        }

        $ip = $request->ip();

        // Blacklist kontrolü
        if ($this->securityManager->isIpBlacklisted($ip)) {
            $this->securityManager->logSecurityEvent('ip_blocked', [
                'ip' => $ip,
                'reason' => 'blacklisted',
            ]);
            return false;
        }

        // Whitelist kontrolü
        if ($ipFiltering['mode'] === 'whitelist') {
            if (!$this->securityManager->isIpWhitelisted($ip)) {
                $this->securityManager->logSecurityEvent('ip_blocked', [
                    'ip' => $ip,
                    'reason' => 'not_whitelisted',
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * CORS preflight request'ini handle et
     */
    private function handlePreflightRequest(Request $request): Response
    {
        $corsHeaders = $this->securityManager->getCorsHeaders($request);
        
        $response = response('', 200);
        
        foreach ($corsHeaders as $header => $value) {
            $response->header($header, $value);
        }

        return $response;
    }

    /**
     * Security header'larını ekle
     */
    private function addSecurityHeaders(Response $response, Request $request = null): Response
    {
        // Security headers
        $securityHeaders = $this->securityManager->getSecurityHeaders();
        foreach ($securityHeaders as $header => $value) {
            $response->headers->set($header, $value);
        }

        // CORS headers
        if ($request) {
            $corsHeaders = $this->securityManager->getCorsHeaders($request);
            foreach ($corsHeaders as $header => $value) {
                $response->headers->set($header, $value);
            }
        }

        return $response;
    }

    /**
     * Security response'unu oluştur
     */
    private function buildSecurityResponse(string $message, int $statusCode): Response
    {
        return $this->apiResponse->error($message, $statusCode, [], [
            'security_event' => true,
            'timestamp' => now()->toISOString(),
        ]);
    }
}
