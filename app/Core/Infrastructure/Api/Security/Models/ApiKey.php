<?php

namespace App\Core\Infrastructure\Api\Security\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * ApiKey Model
 * API key management için model
 */
class ApiKey extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'api_keys';

    protected $fillable = [
        'name',
        'key',
        'secret',
        'user_id',
        'permissions',
        'rate_limit_per_minute',
        'rate_limit_per_hour',
        'rate_limit_per_day',
        'allowed_ips',
        'allowed_domains',
        'expires_at',
        'last_used_at',
        'usage_count',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'permissions' => 'array',
        'allowed_ips' => 'array',
        'allowed_domains' => 'array',
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'is_active' => 'boolean',
        'metadata' => 'array',
        'usage_count' => 'integer',
        'rate_limit_per_minute' => 'integer',
        'rate_limit_per_hour' => 'integer',
        'rate_limit_per_day' => 'integer',
    ];

    protected $hidden = [
        'secret',
    ];

    protected $dates = [
        'expires_at',
        'last_used_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * User relationship
     */
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * API key usage logs
     */
    public function usageLogs()
    {
        return $this->hasMany(ApiKeyUsageLog::class);
    }

    /**
     * Generate a new API key
     */
    public static function generateKey(string $prefix = 'ak_'): string
    {
        return $prefix . Str::random(32);
    }

    /**
     * Generate a new API secret
     */
    public static function generateSecret(): string
    {
        return Str::random(64);
    }

    /**
     * Check if API key is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if API key is active
     */
    public function isActive(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Check if IP is allowed
     */
    public function isIpAllowed(string $ip): bool
    {
        if (empty($this->allowed_ips)) {
            return true;
        }

        foreach ($this->allowed_ips as $allowedIp) {
            if ($this->matchesIpPattern($ip, $allowedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if domain is allowed
     */
    public function isDomainAllowed(string $domain): bool
    {
        if (empty($this->allowed_domains)) {
            return true;
        }

        return in_array($domain, $this->allowed_domains);
    }

    /**
     * Check if permission is granted
     */
    public function hasPermission(string $permission): bool
    {
        if (empty($this->permissions)) {
            return true; // No restrictions
        }

        return in_array($permission, $this->permissions) || in_array('*', $this->permissions);
    }

    /**
     * Record usage
     */
    public function recordUsage(array $data = []): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);

        // Create usage log
        $this->usageLogs()->create([
            'ip_address' => $data['ip'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
            'endpoint' => $data['endpoint'] ?? request()->path(),
            'method' => $data['method'] ?? request()->method(),
            'response_status' => $data['status'] ?? null,
            'response_time' => $data['response_time'] ?? null,
            'metadata' => $data['metadata'] ?? [],
        ]);
    }

    /**
     * Get rate limits
     */
    public function getRateLimits(): array
    {
        return [
            'per_minute' => $this->rate_limit_per_minute ?: config('api_rate_limiting.limits.api_key.requests', 2000),
            'per_hour' => $this->rate_limit_per_hour ?: ($this->rate_limit_per_minute * 60),
            'per_day' => $this->rate_limit_per_day ?: ($this->rate_limit_per_minute * 60 * 24),
        ];
    }

    /**
     * Get usage statistics
     */
    public function getUsageStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'total_requests' => $this->usage_count,
            'requests_last_30_days' => $this->usageLogs()
                ->where('created_at', '>=', $startDate)
                ->count(),
            'last_used' => $this->last_used_at,
            'average_response_time' => $this->usageLogs()
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('response_time')
                ->avg('response_time'),
            'error_rate' => $this->calculateErrorRate($startDate),
            'top_endpoints' => $this->getTopEndpoints($startDate),
        ];
    }

    /**
     * Scope for active keys
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope for expired keys
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Match IP pattern (supports CIDR notation)
     */
    private function matchesIpPattern(string $ip, string $pattern): bool
    {
        if ($ip === $pattern) {
            return true;
        }

        // CIDR notation support
        if (strpos($pattern, '/') !== false) {
            [$subnet, $mask] = explode('/', $pattern);
            return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
        }

        // Wildcard support
        if (strpos($pattern, '*') !== false) {
            $pattern = str_replace('*', '.*', $pattern);
            return preg_match('/^' . $pattern . '$/', $ip);
        }

        return false;
    }

    /**
     * Calculate error rate
     */
    private function calculateErrorRate($startDate): float
    {
        $totalRequests = $this->usageLogs()
            ->where('created_at', '>=', $startDate)
            ->count();

        if ($totalRequests === 0) {
            return 0;
        }

        $errorRequests = $this->usageLogs()
            ->where('created_at', '>=', $startDate)
            ->where('response_status', '>=', 400)
            ->count();

        return round(($errorRequests / $totalRequests) * 100, 2);
    }

    /**
     * Get top endpoints
     */
    private function getTopEndpoints($startDate, int $limit = 10): array
    {
        return $this->usageLogs()
            ->where('created_at', '>=', $startDate)
            ->selectRaw('endpoint, method, COUNT(*) as count')
            ->groupBy('endpoint', 'method')
            ->orderByDesc('count')
            ->limit($limit)
            ->get()
            ->toArray();
    }
}
