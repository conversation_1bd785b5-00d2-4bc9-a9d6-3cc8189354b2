<?php

namespace App\Core\Infrastructure\Api\Validation\Services;

use App\Core\Infrastructure\Api\Validation\Contracts\ValidationMessageInterface;
use Illuminate\Support\Facades\Cache;

/**
 * ValidationMessageManager
 * Validation message management service
 */
class ValidationMessageManager implements ValidationMessageInterface
{
    private array $config;
    private string $locale;
    private string $fallbackLocale;
    private array $messages = [];
    private array $fieldLabels = [];

    public function __construct()
    {
        $this->config = config('api_validation.messages', []);
        $this->locale = $this->config['default_locale'] ?? app()->getLocale();
        $this->fallbackLocale = $this->config['fallback_locale'] ?? 'en';

        $this->loadMessages();
        $this->loadFieldLabels();
    }

    /**
     * Message'ı al
     */
    public function get(string $key, array $parameters = [], string $locale = null): string
    {
        $locale = $locale ?: $this->locale;

        // Önce belirtilen locale'de ara
        if (isset($this->messages[$locale][$key])) {
            $message = $this->messages[$locale][$key];
        }
        // Fallback locale'de ara
        elseif (isset($this->messages[$this->fallbackLocale][$key])) {
            $message = $this->messages[$this->fallbackLocale][$key];
        }
        // Default message
        else {
            $message = $this->getDefaultMessage($key);
        }

        return $this->format($message, $parameters);
    }

    /**
     * Tüm message'ları al
     */
    public function all(string $locale = null): array
    {
        $locale = $locale ?: $this->locale;

        $messages = $this->messages[$locale] ?? [];

        // Fallback locale'den eksik message'ları ekle
        if ($locale !== $this->fallbackLocale) {
            $fallbackMessages = $this->messages[$this->fallbackLocale] ?? [];
            $messages = array_merge($fallbackMessages, $messages);
        }

        return $messages;
    }

    /**
     * Message'ı set et
     */
    public function set(string $key, string $message, string $locale = null): void
    {
        $locale = $locale ?: $this->locale;

        if (!isset($this->messages[$locale])) {
            $this->messages[$locale] = [];
        }

        $this->messages[$locale][$key] = $message;

        // Cache'i temizle
        $this->clearCache($locale);
    }

    /**
     * Message'ları toplu set et
     */
    public function setMany(array $messages, string $locale = null): void
    {
        $locale = $locale ?: $this->locale;

        if (!isset($this->messages[$locale])) {
            $this->messages[$locale] = [];
        }

        $this->messages[$locale] = array_merge($this->messages[$locale], $messages);

        // Cache'i temizle
        $this->clearCache($locale);
    }

    /**
     * Message var mı kontrol et
     */
    public function has(string $key, string $locale = null): bool
    {
        $locale = $locale ?: $this->locale;

        return isset($this->messages[$locale][$key]) ||
               isset($this->messages[$this->fallbackLocale][$key]);
    }

    /**
     * Message'ı sil
     */
    public function forget(string $key, string $locale = null): void
    {
        $locale = $locale ?: $this->locale;

        if (isset($this->messages[$locale][$key])) {
            unset($this->messages[$locale][$key]);
            $this->clearCache($locale);
        }
    }

    /**
     * Locale'i set et
     */
    public function setLocale(string $locale): void
    {
        $this->locale = $locale;
    }

    /**
     * Mevcut locale'i al
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * Fallback locale'i set et
     */
    public function setFallbackLocale(string $locale): void
    {
        $this->fallbackLocale = $locale;
    }

    /**
     * Message'ı format et
     */
    public function format(string $message, array $parameters = []): string
    {
        if (empty($parameters)) {
            return $message;
        }

        foreach ($parameters as $key => $value) {
            $placeholder = ':' . $key;
            $message = str_replace($placeholder, $value, $message);
        }

        return $message;
    }

    /**
     * Field label'ını al
     */
    public function getFieldLabel(string $field, string $locale = null): string
    {
        $locale = $locale ?: $this->locale;

        // Önce belirtilen locale'de ara
        if (isset($this->fieldLabels[$locale][$field])) {
            return $this->fieldLabels[$locale][$field];
        }

        // Fallback locale'de ara
        if (isset($this->fieldLabels[$this->fallbackLocale][$field])) {
            return $this->fieldLabels[$this->fallbackLocale][$field];
        }

        // Field name'i human readable yap
        return $this->humanizeFieldName($field);
    }

    /**
     * Field label'ını set et
     */
    public function setFieldLabel(string $field, string $label, string $locale = null): void
    {
        $locale = $locale ?: $this->locale;

        if (!isset($this->fieldLabels[$locale])) {
            $this->fieldLabels[$locale] = [];
        }

        $this->fieldLabels[$locale][$field] = $label;
    }

    /**
     * Message'ları yükle
     */
    private function loadMessages(): void
    {
        $cacheKey = 'api_validation_messages';

        if (config('api_validation.performance.cache_enabled', true) && $this->isCacheAvailable()) {
            try {
                if (Cache::has($cacheKey)) {
                    $this->messages = Cache::get($cacheKey);
                    return;
                }
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }

        // Turkish messages
        $this->messages['tr'] = [
            // Required rules
            'required' => ':attribute alanı zorunludur.',
            'required_if' => ':other :value olduğunda :attribute alanı zorunludur.',
            'required_unless' => ':other :values değerlerinden biri olmadığında :attribute alanı zorunludur.',
            'required_with' => ':values alanlarından biri mevcut olduğunda :attribute alanı zorunludur.',
            'required_with_all' => ':values alanlarının tümü mevcut olduğunda :attribute alanı zorunludur.',
            'required_without' => ':values alanlarından biri mevcut olmadığında :attribute alanı zorunludur.',
            'required_without_all' => ':values alanlarının hiçbiri mevcut olmadığında :attribute alanı zorunludur.',

            // Type rules
            'string' => ':attribute bir metin olmalıdır.',
            'integer' => ':attribute bir tam sayı olmalıdır.',
            'numeric' => ':attribute bir sayı olmalıdır.',
            'boolean' => ':attribute true veya false olmalıdır.',
            'array' => ':attribute bir dizi olmalıdır.',
            'email' => ':attribute geçerli bir e-posta adresi olmalıdır.',
            'url' => ':attribute geçerli bir URL olmalıdır.',
            'date' => ':attribute geçerli bir tarih olmalıdır.',
            'uuid' => ':attribute geçerli bir UUID olmalıdır.',

            // Size rules
            'min' => [
                'numeric' => ':attribute en az :min olmalıdır.',
                'string' => ':attribute en az :min karakter olmalıdır.',
                'array' => ':attribute en az :min öğe içermelidir.',
            ],
            'max' => [
                'numeric' => ':attribute en fazla :max olmalıdır.',
                'string' => ':attribute en fazla :max karakter olmalıdır.',
                'array' => ':attribute en fazla :max öğe içermelidir.',
            ],
            'between' => [
                'numeric' => ':attribute :min ile :max arasında olmalıdır.',
                'string' => ':attribute :min ile :max karakter arasında olmalıdır.',
                'array' => ':attribute :min ile :max öğe arasında olmalıdır.',
            ],
            'size' => [
                'numeric' => ':attribute :size olmalıdır.',
                'string' => ':attribute :size karakter olmalıdır.',
                'array' => ':attribute :size öğe içermelidir.',
            ],

            // Comparison rules
            'same' => ':attribute ile :other aynı olmalıdır.',
            'different' => ':attribute ile :other farklı olmalıdır.',
            'confirmed' => ':attribute doğrulaması eşleşmiyor.',
            'in' => 'Seçili :attribute geçersiz.',
            'not_in' => 'Seçili :attribute geçersiz.',

            // Database rules
            'unique' => ':attribute daha önce alınmış.',
            'exists' => 'Seçili :attribute geçersiz.',

            // Date rules
            'after' => ':attribute :date tarihinden sonra olmalıdır.',
            'after_or_equal' => ':attribute :date tarihinden sonra veya aynı tarihte olmalıdır.',
            'before' => ':attribute :date tarihinden önce olmalıdır.',
            'before_or_equal' => ':attribute :date tarihinden önce veya aynı tarihte olmalıdır.',

            // Custom rules
            'turkish_phone' => ':attribute geçerli bir Türkiye telefon numarası olmalıdır.',
            'turkish_identity' => ':attribute geçerli bir TC kimlik numarası olmalıdır.',
            'iban' => ':attribute geçerli bir IBAN numarası olmalıdır.',
            'credit_card' => ':attribute geçerli bir kredi kartı numarası olmalıdır.',
            'slug' => ':attribute geçerli bir slug formatında olmalıdır.',
            'currency_code' => ':attribute geçerli bir para birimi kodu olmalıdır.',
            'price_format' => ':attribute geçerli bir fiyat formatında olmalıdır.',
            'stock_quantity' => ':attribute geçerli bir stok miktarı olmalıdır.',
            'discount_percentage' => ':attribute 0 ile 100 arasında bir indirim yüzdesi olmalıdır.',
            'product_sku' => ':attribute geçerli bir ürün SKU formatında olmalıdır.',
        ];

        // English messages (fallback)
        $this->messages['en'] = [
            'required' => 'The :attribute field is required.',
            'string' => 'The :attribute must be a string.',
            'integer' => 'The :attribute must be an integer.',
            'numeric' => 'The :attribute must be a number.',
            'boolean' => 'The :attribute field must be true or false.',
            'array' => 'The :attribute must be an array.',
            'email' => 'The :attribute must be a valid email address.',
            'url' => 'The :attribute must be a valid URL.',
            'date' => 'The :attribute must be a valid date.',
            'uuid' => 'The :attribute must be a valid UUID.',
            'min' => [
                'numeric' => 'The :attribute must be at least :min.',
                'string' => 'The :attribute must be at least :min characters.',
                'array' => 'The :attribute must have at least :min items.',
            ],
            'max' => [
                'numeric' => 'The :attribute may not be greater than :max.',
                'string' => 'The :attribute may not be greater than :max characters.',
                'array' => 'The :attribute may not have more than :max items.',
            ],
            'unique' => 'The :attribute has already been taken.',
            'exists' => 'The selected :attribute is invalid.',
            'turkish_phone' => 'The :attribute must be a valid Turkish phone number.',
            'turkish_identity' => 'The :attribute must be a valid Turkish identity number.',
            'iban' => 'The :attribute must be a valid IBAN number.',
            'credit_card' => 'The :attribute must be a valid credit card number.',
            'slug' => 'The :attribute must be a valid slug format.',
            'currency_code' => 'The :attribute must be a valid currency code.',
            'price_format' => 'The :attribute must be a valid price format.',
            'stock_quantity' => 'The :attribute must be a valid stock quantity.',
            'discount_percentage' => 'The :attribute must be a discount percentage between 0 and 100.',
            'product_sku' => 'The :attribute must be a valid product SKU format.',
        ];

        if (config('api_validation.performance.cache_enabled', true) && $this->isCacheAvailable()) {
            try {
                Cache::put($cacheKey, $this->messages, 3600);
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }
    }

    /**
     * Field label'larını yükle
     */
    private function loadFieldLabels(): void
    {
        $cacheKey = 'api_validation_field_labels';

        if (config('api_validation.performance.cache_enabled', true) && $this->isCacheAvailable()) {
            try {
                if (Cache::has($cacheKey)) {
                    $this->fieldLabels = Cache::get($cacheKey);
                    return;
                }
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }

        // Turkish field labels
        $this->fieldLabels['tr'] = [
            'name' => 'Ad',
            'email' => 'E-posta',
            'password' => 'Şifre',
            'phone' => 'Telefon',
            'address' => 'Adres',
            'city' => 'Şehir',
            'country' => 'Ülke',
            'price' => 'Fiyat',
            'quantity' => 'Miktar',
            'description' => 'Açıklama',
            'category_id' => 'Kategori',
            'product_id' => 'Ürün',
            'order_id' => 'Sipariş',
            'customer_id' => 'Müşteri',
            'status' => 'Durum',
            'created_at' => 'Oluşturulma Tarihi',
            'updated_at' => 'Güncellenme Tarihi',
            'sku' => 'Ürün Kodu',
            'stock' => 'Stok',
            'weight' => 'Ağırlık',
            'dimensions' => 'Boyutlar',
            'currency' => 'Para Birimi',
            'amount' => 'Tutar',
            'gateway' => 'Ödeme Ağ Geçidi',
            'payment_method' => 'Ödeme Yöntemi',
            'code' => 'Kod',
            'type' => 'Tip',
            'value' => 'Değer',
            'expires_at' => 'Son Kullanma Tarihi',
        ];

        // English field labels
        $this->fieldLabels['en'] = [
            'name' => 'Name',
            'email' => 'Email',
            'password' => 'Password',
            'phone' => 'Phone',
            'address' => 'Address',
            'city' => 'City',
            'country' => 'Country',
            'price' => 'Price',
            'quantity' => 'Quantity',
            'description' => 'Description',
            'category_id' => 'Category',
            'product_id' => 'Product',
            'order_id' => 'Order',
            'customer_id' => 'Customer',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'sku' => 'SKU',
            'stock' => 'Stock',
            'weight' => 'Weight',
            'dimensions' => 'Dimensions',
            'currency' => 'Currency',
            'amount' => 'Amount',
            'gateway' => 'Gateway',
            'payment_method' => 'Payment Method',
            'code' => 'Code',
            'type' => 'Type',
            'value' => 'Value',
            'expires_at' => 'Expires At',
        ];

        if (config('api_validation.performance.cache_enabled', true) && $this->isCacheAvailable()) {
            try {
                Cache::put($cacheKey, $this->fieldLabels, 3600);
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }
    }

    /**
     * Default message al
     */
    private function getDefaultMessage(string $key): string
    {
        return "Validation failed for {$key}";
    }

    /**
     * Field name'i human readable yap
     */
    private function humanizeFieldName(string $field): string
    {
        return ucfirst(str_replace(['_', '.'], ' ', $field));
    }

    /**
     * Cache'i temizle
     */
    private function clearCache(string $locale): void
    {
        if (config('api_validation.performance.cache_enabled', true) && $this->isCacheAvailable()) {
            try {
                Cache::forget('api_validation_messages');
                Cache::forget('api_validation_field_labels');
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }
    }

    /**
     * Cache'in kullanılabilir olup olmadığını kontrol et
     */
    private function isCacheAvailable(): bool
    {
        try {
            Cache::put('cache_test', 'test', 1);
            Cache::forget('cache_test');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
