<?php

namespace App\Core\Infrastructure\Api\Validation\Middleware;

use App\Core\Infrastructure\Api\Validation\Contracts\ApiValidatorInterface;
use App\Core\Infrastructure\Api\Contracts\ApiResponseInterface;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

/**
 * ApiValidationMiddleware
 * API request validation middleware
 */
class ApiValidationMiddleware
{
    private ApiValidatorInterface $validator;
    private ApiResponseInterface $apiResponse;
    private array $config;

    public function __construct(
        ApiValidatorInterface $validator,
        ApiResponseInterface $apiResponse
    ) {
        $this->validator = $validator;
        $this->apiResponse = $apiResponse;
        $this->config = config('api_validation.middleware', []);
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$parameters): Response
    {
        // Middleware enabled kontrolü
        if (!($this->config['enabled'] ?? true)) {
            return $next($request);
        }

        // Auto apply kontrolü
        if (!$this->shouldApplyValidation($request)) {
            return $next($request);
        }

        // Validation rule'larını belirle
        $rules = $this->determineValidationRules($request, $parameters);
        
        if (empty($rules)) {
            return $next($request);
        }

        try {
            // Request'i validate et
            $validatedData = $this->validator->validate($request, $rules);
            
            // Validated data'yı request'e ekle
            $request->merge(['validated_data' => $validatedData]);
            
            // Performance logging
            if (config('api_validation.logging.log_performance', false)) {
                $this->logValidationPerformance($request, $rules);
            }
            
        } catch (ValidationException $e) {
            return $this->handleValidationException($e, $request);
        } catch (\Exception $e) {
            return $this->handleGeneralException($e, $request);
        }

        return $next($request);
    }

    /**
     * Validation'ın uygulanıp uygulanmayacağını kontrol et
     */
    private function shouldApplyValidation(Request $request): bool
    {
        // Method kontrolü
        $allowedMethods = $this->config['methods'] ?? ['POST', 'PUT', 'PATCH'];
        if (!in_array($request->method(), $allowedMethods)) {
            return false;
        }

        // Route pattern kontrolü
        $path = $request->path();
        
        // Exclude pattern'ları kontrol et
        $excludePatterns = $this->config['exclude_patterns'] ?? [];
        foreach ($excludePatterns as $pattern) {
            if ($this->matchesPattern($path, $pattern)) {
                return false;
            }
        }

        // Include pattern'ları kontrol et
        $includePatterns = $this->config['route_patterns'] ?? [];
        if (empty($includePatterns)) {
            return true;
        }

        foreach ($includePatterns as $pattern) {
            if ($this->matchesPattern($path, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validation rule'larını belirle
     */
    private function determineValidationRules(Request $request, array $parameters): array
    {
        // Parameter'lardan rule'ları al
        if (!empty($parameters)) {
            return $this->parseParameterRules($parameters);
        }

        // Route'dan module ve action'ı çıkar
        $routeInfo = $this->extractRouteInfo($request);
        
        if ($routeInfo['module'] && $routeInfo['action']) {
            return $this->validator->getModuleRules($routeInfo['module'], $routeInfo['action']);
        }

        // Auto-detection
        return $this->autoDetectRules($request);
    }

    /**
     * Parameter rule'larını parse et
     */
    private function parseParameterRules(array $parameters): array
    {
        $rules = [];
        
        foreach ($parameters as $parameter) {
            if (strpos($parameter, ':') !== false) {
                [$field, $rule] = explode(':', $parameter, 2);
                $rules[$field] = explode('|', $rule);
            } elseif (strpos($parameter, '=') !== false) {
                [$field, $pattern] = explode('=', $parameter, 2);
                $rules[$field] = $this->validator->getFieldPattern($pattern);
            }
        }
        
        return $rules;
    }

    /**
     * Route bilgilerini çıkar
     */
    private function extractRouteInfo(Request $request): array
    {
        $path = $request->path();
        $segments = explode('/', $path);
        
        $module = null;
        $action = null;
        
        // API path pattern: api/v1/module/resource
        if (count($segments) >= 3 && $segments[0] === 'api') {
            if (preg_match('/^v\d+(\.\d+)?$/', $segments[1])) {
                // Versioned API
                $module = $segments[2] ?? null;
            } else {
                // Non-versioned API
                $module = $segments[1] ?? null;
            }
            
            // Action'ı HTTP method'dan belirle
            $action = $this->mapHttpMethodToAction($request->method());
        }
        
        return [
            'module' => $module,
            'action' => $action,
            'segments' => $segments,
        ];
    }

    /**
     * HTTP method'u action'a map et
     */
    private function mapHttpMethodToAction(string $method): string
    {
        $mapping = [
            'POST' => 'store',
            'PUT' => 'update',
            'PATCH' => 'update',
            'DELETE' => 'destroy',
            'GET' => 'index',
        ];
        
        return $mapping[$method] ?? 'store';
    }

    /**
     * Rule'ları otomatik detect et
     */
    private function autoDetectRules(Request $request): array
    {
        $rules = [];
        $data = $request->all();
        
        // Common field pattern'larını kontrol et
        foreach ($data as $field => $value) {
            if ($pattern = $this->detectFieldPattern($field, $value)) {
                $rules[$field] = $this->validator->getFieldPattern($pattern);
            }
        }
        
        return $rules;
    }

    /**
     * Field pattern'ını detect et
     */
    private function detectFieldPattern(string $field, $value): ?string
    {
        $patterns = [
            'email' => '/^.+@.+\..+$/',
            'phone' => '/^[\+]?[0-9\s\-\(\)]+$/',
            'price' => '/^\d+(\.\d{1,2})?$/',
            'uuid' => '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i',
        ];
        
        // Field name'e göre pattern detect et
        if (str_contains($field, 'email')) return 'email';
        if (str_contains($field, 'phone')) return 'phone';
        if (str_contains($field, 'price') || str_contains($field, 'amount')) return 'price';
        if (str_contains($field, 'id') && is_numeric($value)) return 'id';
        if (str_contains($field, 'uuid')) return 'uuid';
        if (str_contains($field, 'password')) return 'password';
        if (str_contains($field, 'name')) return 'name';
        if (str_contains($field, 'description')) return 'description';
        if (str_contains($field, 'quantity') || str_contains($field, 'stock')) return 'quantity';
        if (str_contains($field, 'percentage')) return 'percentage';
        if (str_contains($field, 'date')) return 'date';
        if (str_contains($field, 'status')) return 'status';
        
        // Value'ya göre pattern detect et
        if (is_string($value)) {
            foreach ($patterns as $pattern => $regex) {
                if (preg_match($regex, $value)) {
                    return $pattern;
                }
            }
        }
        
        return null;
    }

    /**
     * Pattern matching
     */
    private function matchesPattern(string $path, string $pattern): bool
    {
        // Wildcard pattern'ları destekle
        $pattern = str_replace('*', '.*', $pattern);
        $pattern = '/^' . str_replace('/', '\/', $pattern) . '$/';
        
        return preg_match($pattern, $path);
    }

    /**
     * Validation exception'ını handle et
     */
    private function handleValidationException(ValidationException $e, Request $request): Response
    {
        $config = config('api_validation.error_response', []);
        
        $errors = $this->validator->formatErrors(
            $e->errors(),
            $config['format'] ?? 'structured'
        );
        
        // Logging
        if (config('api_validation.logging.log_failed_validations', true)) {
            $this->logValidationFailure($request, $e->errors());
        }
        
        return $this->apiResponse->validationError(
            $errors,
            'Validation failed',
            $config['status_code'] ?? 422
        );
    }

    /**
     * Genel exception'ı handle et
     */
    private function handleGeneralException(\Exception $e, Request $request): Response
    {
        // Logging
        if (config('api_validation.logging.enabled', false)) {
            \Log::channel(config('api_validation.logging.log_channel', 'api'))
                ->error('API Validation Error', [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'request_path' => $request->path(),
                    'request_method' => $request->method(),
                ]);
        }
        
        return $this->apiResponse->error(
            'Validation system error',
            500,
            [],
            ['error_id' => uniqid()]
        );
    }

    /**
     * Validation performance'ını logla
     */
    private function logValidationPerformance(Request $request, array $rules): void
    {
        $memory = memory_get_peak_usage(true);
        $time = microtime(true) - LARAVEL_START;
        
        \Log::channel(config('api_validation.logging.log_channel', 'api'))
            ->info('API Validation Performance', [
                'path' => $request->path(),
                'method' => $request->method(),
                'rules_count' => count($rules),
                'memory_usage' => $memory,
                'execution_time' => $time,
            ]);
    }

    /**
     * Validation failure'ını logla
     */
    private function logValidationFailure(Request $request, array $errors): void
    {
        \Log::channel(config('api_validation.logging.log_channel', 'api'))
            ->warning('API Validation Failed', [
                'path' => $request->path(),
                'method' => $request->method(),
                'errors' => $errors,
                'input' => $request->except(['password', 'password_confirmation']),
            ]);
    }
}
