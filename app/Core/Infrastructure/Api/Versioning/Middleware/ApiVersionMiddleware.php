<?php

namespace App\Core\Infrastructure\Api\Versioning\Middleware;

use App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface;
use App\Core\Infrastructure\Api\Contracts\ApiResponseInterface;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

/**
 * ApiVersionMiddleware
 * API version detection ve validation middleware
 */
class ApiVersionMiddleware
{
    private ApiVersionManagerInterface $versionManager;
    private ApiResponseInterface $apiResponse;

    public function __construct(
        ApiVersionManagerInterface $versionManager,
        ApiResponseInterface $apiResponse
    ) {
        $this->versionManager = $versionManager;
        $this->apiResponse = $apiResponse;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$parameters): SymfonyResponse
    {
        // Version'ı detect et
        $detectedVersion = $this->versionManager->detectVersion($request);
        
        // Request'e version'ı set et
        $request->attributes->set('api_version', $detectedVersion);
        
        // Version validation
        if (!$this->versionManager->isVersionSupported($detectedVersion)) {
            return $this->handleUnsupportedVersion($detectedVersion, $request);
        }
        
        // Sunset version kontrolü
        if ($this->versionManager->isVersionSunset($detectedVersion)) {
            return $this->handleSunsetVersion($detectedVersion, $request);
        }
        
        // Version usage'ını track et
        $this->versionManager->trackVersionUsage($detectedVersion, $request);
        
        // Request'i işle
        $response = $next($request);
        
        // Response'a version header'larını ekle
        $this->addVersionHeaders($response, $detectedVersion, $request);
        
        return $response;
    }

    /**
     * Desteklenmeyen version için error response
     */
    private function handleUnsupportedVersion(string $version, Request $request): SymfonyResponse
    {
        $supportedVersions = $this->versionManager->getSupportedVersions();
        $config = config('api_versioning.error_handling', []);
        
        $errorData = [
            'requested_version' => $version,
            'supported_versions' => $supportedVersions,
            'latest_version' => $this->versionManager->getLatestVersion(),
        ];
        
        $message = $config['detailed_error_messages'] ?? true
            ? "API version '{$version}' is not supported. Supported versions: " . implode(', ', $supportedVersions)
            : 'Unsupported API version';
        
        $statusCode = $config['unsupported_version_code'] ?? 400;
        
        return response()->json([
            'error' => 'unsupported_version',
            'message' => $message,
            'data' => $errorData,
            'meta' => [
                'version' => $this->versionManager->getDefaultVersion(),
                'timestamp' => now()->toISOString(),
            ]
        ], $statusCode);
    }

    /**
     * Sunset version için error response
     */
    private function handleSunsetVersion(string $version, Request $request): SymfonyResponse
    {
        $config = config('api_versioning.error_handling', []);
        $sunsetWarning = $this->versionManager->getSunsetWarning($version);
        
        $errorData = [
            'sunset_version' => $version,
            'latest_version' => $this->versionManager->getLatestVersion(),
            'sunset_warning' => $sunsetWarning,
        ];
        
        $message = $config['detailed_error_messages'] ?? true
            ? $sunsetWarning
            : 'API version has been sunset';
        
        $statusCode = $config['sunset_version_code'] ?? 410;
        
        return response()->json([
            'error' => 'version_sunset',
            'message' => $message,
            'data' => $errorData,
            'meta' => [
                'version' => $version,
                'timestamp' => now()->toISOString(),
            ]
        ], $statusCode);
    }

    /**
     * Response'a version header'larını ekle
     */
    private function addVersionHeaders(SymfonyResponse $response, string $version, Request $request): void
    {
        $headers = config('api_versioning.headers', []);
        
        // Version header'ı
        if (isset($headers['version'])) {
            $response->headers->set($headers['version'], $version);
        }
        
        // Supported versions header'ı
        if (isset($headers['supported_versions'])) {
            $supportedVersions = implode(', ', $this->versionManager->getSupportedVersions());
            $response->headers->set($headers['supported_versions'], $supportedVersions);
        }
        
        // Deprecation warning
        if ($this->versionManager->isVersionDeprecated($version)) {
            $deprecationWarning = $this->versionManager->getDeprecationWarning($version);
            
            if ($deprecationWarning && isset($headers['deprecation_warning'])) {
                $response->headers->set($headers['deprecation_warning'], $deprecationWarning);
            }
        }
        
        // API infrastructure header'ları
        $response->headers->set('X-API-Infrastructure', 'ModularEcommerce');
        $response->headers->set('X-API-Response-Time', $this->getResponseTime($request));
    }

    /**
     * Response time'ı hesapla
     */
    private function getResponseTime(Request $request): string
    {
        $startTime = $request->server('REQUEST_TIME_FLOAT', microtime(true));
        $responseTime = (microtime(true) - $startTime) * 1000; // milliseconds
        
        return number_format($responseTime, 2) . 'ms';
    }
}
