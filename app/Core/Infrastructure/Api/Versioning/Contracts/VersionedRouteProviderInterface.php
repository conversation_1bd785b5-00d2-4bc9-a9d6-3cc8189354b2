<?php

namespace App\Core\Infrastructure\Api\Versioning\Contracts;

use Illuminate\Routing\Router;

/**
 * VersionedRouteProviderInterface
 * Version-specific route management için contract
 */
interface VersionedRouteProviderInterface
{
    /**
     * Version-specific route'ları register et
     */
    public function registerVersionedRoutes(Router $router, string $version): void;

    /**
     * Version için controller namespace'ini al
     */
    public function getControllerNamespace(string $version): string;

    /**
     * Version için route prefix'ini al
     */
    public function getRoutePrefix(string $version): string;

    /**
     * Version için middleware'leri al
     */
    public function getVersionMiddleware(string $version): array;

    /**
     * Version-specific route dosyasının var olup olmadığını kontrol et
     */
    public function hasVersionRoutes(string $version): bool;

    /**
     * Version route dosyasının path'ini al
     */
    public function getVersionRoutePath(string $version): string;

    /**
     * Auto-discovery ile version route'larını bul
     */
    public function discoverVersionRoutes(): array;

    /**
     * Version için fallback route'ları al
     */
    public function getFallbackRoutes(string $version): array;

    /**
     * Route versioning strategy'sini al
     */
    public function getVersioningStrategy(): string;

    /**
     * Version-specific route cache key'ini al
     */
    public function getRouteCacheKey(string $version): string;
}
