<?php

namespace App\Core\Infrastructure\Api\Versioning\Strategies;

use App\Core\Infrastructure\Api\Versioning\Contracts\VersionStrategyInterface;
use Illuminate\Http\Request;

/**
 * UrlVersionStrategy
 * URL path-based version detection strategy
 */
class UrlVersionStrategy implements VersionStrategyInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'prefix' => 'api',
            'version_prefix' => 'v',
            'pattern' => '/^\/api\/v(\d+(?:\.\d+)?)\//',
            'position' => 2, // /api/v1/... -> position 2
        ], $config);
    }

    /**
     * Request'ten version'ı extract et
     */
    public function extractVersion(Request $request): ?string
    {
        $path = $request->getPathInfo();
        
        // Regex pattern ile version'ı extract et
        if (preg_match($this->config['pattern'], $path, $matches)) {
            return $this->normalizeVersion($matches[1]);
        }

        // Fallback: path segment'lerini manuel parse et
        $segments = explode('/', trim($path, '/'));
        
        if (count($segments) >= $this->config['position']) {
            $versionSegment = $segments[$this->config['position'] - 1];
            
            // Version prefix'ini kontrol et
            if (str_starts_with($versionSegment, $this->config['version_prefix'])) {
                $version = substr($versionSegment, strlen($this->config['version_prefix']));
                return $this->normalizeVersion($version);
            }
        }

        return null;
    }

    /**
     * Strategy'nin request'i support edip etmediğini kontrol et
     */
    public function supports(Request $request): bool
    {
        $path = $request->getPathInfo();
        return preg_match($this->config['pattern'], $path) === 1;
    }

    /**
     * Strategy'nin priority'sini al
     */
    public function getPriority(): int
    {
        return 80; // Orta priority
    }

    /**
     * Strategy'nin adını al
     */
    public function getName(): string
    {
        return 'url';
    }

    /**
     * Strategy'nin açıklamasını al
     */
    public function getDescription(): string
    {
        return 'Detects API version from URL path segments';
    }

    /**
     * Version'ı request'e set et
     */
    public function setVersion(Request $request, string $version): void
    {
        // URL-based strategy için set etme işlemi route level'da yapılır
        // Bu method response header'ları için kullanılabilir
        $request->attributes->set('api_version', $version);
    }

    /**
     * Strategy configuration'ını validate et
     */
    public function validateConfiguration(array $config): bool
    {
        return isset($config['pattern']) && 
               is_string($config['pattern']) && 
               !empty($config['pattern']) &&
               isset($config['position']) &&
               is_int($config['position']) &&
               $config['position'] > 0;
    }

    /**
     * Version'ı normalize et
     */
    private function normalizeVersion(string $version): string
    {
        // Whitespace'leri temizle
        $version = trim($version);
        
        // Numeric version kontrolü
        if (is_numeric($version)) {
            // 1 -> 1.0
            if (!str_contains($version, '.')) {
                $version .= '.0';
            }
        }

        return $version;
    }

    /**
     * Version için URL pattern'ini generate et
     */
    public function generateVersionUrl(string $version, string $path = ''): string
    {
        $prefix = $this->config['prefix'];
        $versionPrefix = $this->config['version_prefix'];
        
        return "/{$prefix}/{$versionPrefix}{$version}" . ($path ? "/{$path}" : '');
    }

    /**
     * Path'ten version'ı kaldır
     */
    public function removeVersionFromPath(string $path): string
    {
        return preg_replace($this->config['pattern'], "/{$this->config['prefix']}/", $path);
    }
}
