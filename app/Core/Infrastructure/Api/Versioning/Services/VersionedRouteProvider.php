<?php

namespace App\Core\Infrastructure\Api\Versioning\Services;

use App\Core\Infrastructure\Api\Versioning\Contracts\VersionedRouteProviderInterface;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

/**
 * VersionedRouteProvider
 * Version-specific route management service
 */
class VersionedRouteProvider implements VersionedRouteProviderInterface
{
    private array $config;
    private array $discoveredRoutes = [];

    public function __construct(array $config = [])
    {
        $this->config = array_merge(config('api_versioning', []), $config);
    }

    /**
     * Version-specific route'ları register et
     */
    public function registerVersionedRoutes(Router $router, string $version): void
    {
        $routePath = $this->getVersionRoutePath($version);
        
        // Version-specific route dosyası varsa yükle
        if ($this->hasVersionRoutes($version)) {
            $this->loadVersionRoutes($router, $version, $routePath);
        } else {
            // Fallback route'ları yükle
            $this->loadFallbackRoutes($router, $version);
        }
    }

    /**
     * Version için controller namespace'ini al
     */
    public function getControllerNamespace(string $version): string
    {
        $pattern = $this->config['routes']['version_namespace_pattern'] ?? 'App\\Http\\Controllers\\Api\\V{version}';
        $namespace = str_replace('{version}', $this->formatVersionForNamespace($version), $pattern);
        
        // Namespace'in var olup olmadığını kontrol et
        if (!$this->namespaceExists($namespace)) {
            return $this->config['routes']['fallback_namespace'] ?? 'App\\Http\\Controllers\\Api\\V1';
        }
        
        return $namespace;
    }

    /**
     * Version için route prefix'ini al
     */
    public function getRoutePrefix(string $version): string
    {
        $urlConfig = $this->config['url_patterns'] ?? [];
        $prefix = $urlConfig['prefix'] ?? 'api';
        $versionPrefix = $urlConfig['version_prefix'] ?? 'v';
        
        return "{$prefix}/{$versionPrefix}{$version}";
    }

    /**
     * Version için middleware'leri al
     */
    public function getVersionMiddleware(string $version): array
    {
        $baseMiddleware = ['api'];
        
        // Version-specific middleware'ler
        $versionMiddleware = [
            'api.version',
        ];
        
        // Deprecated version için ek middleware
        if (app('api.version.manager')->isVersionDeprecated($version)) {
            $versionMiddleware[] = 'api.version.deprecation';
        }
        
        return array_merge($baseMiddleware, $versionMiddleware);
    }

    /**
     * Version-specific route dosyasının var olup olmadığını kontrol et
     */
    public function hasVersionRoutes(string $version): bool
    {
        return File::exists($this->getVersionRoutePath($version));
    }

    /**
     * Version route dosyasının path'ini al
     */
    public function getVersionRoutePath(string $version): string
    {
        $formattedVersion = $this->formatVersionForPath($version);
        return base_path("routes/api/v{$formattedVersion}.php");
    }

    /**
     * Auto-discovery ile version route'larını bul
     */
    public function discoverVersionRoutes(): array
    {
        if (!empty($this->discoveredRoutes)) {
            return $this->discoveredRoutes;
        }
        
        // Cache'den kontrol et (Redis bağlantısı varsa)
        if (($this->config['cache']['enabled'] ?? false) && $this->isCacheAvailable()) {
            $cacheKey = $this->getRouteCacheKey('discovery');
            try {
                $cached = Cache::tags($this->config['cache']['tags'] ?? [])
                    ->get($cacheKey);

                if ($cached) {
                    $this->discoveredRoutes = $cached;
                    return $cached;
                }
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }
        
        $routesPath = base_path('routes/api');
        $discovered = [];
        
        if (File::isDirectory($routesPath)) {
            $files = File::glob($routesPath . '/v*.php');
            
            foreach ($files as $file) {
                $filename = basename($file, '.php');
                if (preg_match('/^v(\d+(?:\.\d+)?)$/', $filename, $matches)) {
                    $version = $matches[1];
                    $discovered[$version] = $file;
                }
            }
        }
        
        // Module route'larını da kontrol et
        $discovered = array_merge($discovered, $this->discoverModuleRoutes());
        
        // Cache'e kaydet (Redis bağlantısı varsa)
        if (($this->config['cache']['enabled'] ?? false) && $this->isCacheAvailable()) {
            try {
                Cache::tags($this->config['cache']['tags'] ?? [])
                    ->put($cacheKey, $discovered, $this->config['cache']['ttl'] ?? 3600);
            } catch (\Exception $e) {
                // Cache hatası durumunda devam et
            }
        }
        
        $this->discoveredRoutes = $discovered;
        return $discovered;
    }

    /**
     * Version için fallback route'ları al
     */
    public function getFallbackRoutes(string $version): array
    {
        // En yakın desteklenen version'ı bul
        $supportedVersions = app('api.version.manager')->getSupportedVersions();
        $fallbackVersion = $this->findClosestVersion($version, $supportedVersions);
        
        if ($fallbackVersion && $this->hasVersionRoutes($fallbackVersion)) {
            return [$this->getVersionRoutePath($fallbackVersion)];
        }
        
        // Default fallback
        $defaultPath = base_path('routes/api/v1.php');
        return File::exists($defaultPath) ? [$defaultPath] : [];
    }

    /**
     * Route versioning strategy'sini al
     */
    public function getVersioningStrategy(): string
    {
        return $this->config['detection_strategy'] ?? 'header';
    }

    /**
     * Version-specific route cache key'ini al
     */
    public function getRouteCacheKey(string $version): string
    {
        $prefix = $this->config['cache']['key_prefix'] ?? 'api_version';
        return "{$prefix}:routes:{$version}";
    }

    /**
     * Version route'larını yükle
     */
    private function loadVersionRoutes(Router $router, string $version, string $routePath): void
    {
        $router->group([
            'prefix' => $this->getRoutePrefix($version),
            'namespace' => $this->getControllerNamespace($version),
            'middleware' => $this->getVersionMiddleware($version),
            'as' => "api.v{$version}.",
        ], function () use ($routePath) {
            require $routePath;
        });
    }

    /**
     * Fallback route'ları yükle
     */
    private function loadFallbackRoutes(Router $router, string $version): void
    {
        $fallbackRoutes = $this->getFallbackRoutes($version);
        
        foreach ($fallbackRoutes as $routePath) {
            $this->loadVersionRoutes($router, $version, $routePath);
        }
    }

    /**
     * Module route'larını discover et
     */
    private function discoverModuleRoutes(): array
    {
        $discovered = [];
        $modulesPath = app_path('Modules');
        
        if (File::isDirectory($modulesPath)) {
            $modules = File::directories($modulesPath);
            
            foreach ($modules as $modulePath) {
                $routesPath = $modulePath . '/Routes';
                
                if (File::isDirectory($routesPath)) {
                    $files = File::glob($routesPath . '/api_v*.php');
                    
                    foreach ($files as $file) {
                        $filename = basename($file, '.php');
                        if (preg_match('/^api_v(\d+(?:\.\d+)?)$/', $filename, $matches)) {
                            $version = $matches[1];
                            $discovered[$version][] = $file;
                        }
                    }
                }
            }
        }
        
        return $discovered;
    }

    /**
     * Version'ı namespace için formatla
     */
    private function formatVersionForNamespace(string $version): string
    {
        return str_replace('.', '', $version); // 1.0 -> 10
    }

    /**
     * Version'ı path için formatla
     */
    private function formatVersionForPath(string $version): string
    {
        return $version; // 1.0 -> 1.0
    }

    /**
     * Namespace'in var olup olmadığını kontrol et
     */
    private function namespaceExists(string $namespace): bool
    {
        // Bu basit bir kontrol, daha gelişmiş reflection kullanılabilir
        $path = str_replace('\\', '/', str_replace('App\\', app_path() . '/', $namespace));
        return File::isDirectory($path);
    }

    /**
     * En yakın version'ı bul
     */
    private function findClosestVersion(string $targetVersion, array $availableVersions): ?string
    {
        if (empty($availableVersions)) {
            return null;
        }
        
        usort($availableVersions, 'version_compare');
        
        foreach (array_reverse($availableVersions) as $version) {
            if (version_compare($version, $targetVersion, '<=')) {
                return $version;
            }
        }
        
        return $availableVersions[0]; // En eski version
    }

    /**
     * Cache'in kullanılabilir olup olmadığını kontrol et
     */
    private function isCacheAvailable(): bool
    {
        try {
            Cache::put('cache_test', 'test', 1);
            Cache::forget('cache_test');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
