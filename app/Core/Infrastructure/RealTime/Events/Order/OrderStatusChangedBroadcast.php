<?php

namespace App\Core\Infrastructure\RealTime\Events\Order;

use App\Core\Infrastructure\RealTime\Events\BaseBroadcastEvent;

/**
 * Order status değişikliği real-time broadcast event'i
 */
class OrderStatusChangedBroadcast extends BaseBroadcastEvent
{
    public function __construct(
        private int $orderId,
        private string $newStatus,
        private string $oldStatus,
        private int $userId,
        private ?string $statusMessage = null,
        private ?array $trackingInfo = null
    ) {
        parent::__construct([
            'order_id' => $this->orderId,
            'new_status' => $this->newStatus,
            'old_status' => $this->oldStatus,
            'user_id' => $this->userId,
            'status_message' => $this->statusMessage,
            'tracking_info' => $this->trackingInfo,
            'status_progress' => $this->getStatusProgress(),
            'is_final_status' => $this->isFinalStatus(),
            'requires_action' => $this->requiresUserAction(),
        ]);
    }

    /**
     * Default channel'ları döndür
     */
    protected function getDefaultChannels(): array
    {
        $channels = [];

        // User-specific channel - sipariş sahibi için
        $channels[] = $this->createUserChannel($this->userId);

        // Order-specific channel
        $orderPrefix = config('broadcasting.channel_prefixes.order', 'order');
        $channels[] = $this->createPrivateChannel("{$orderPrefix}.{$this->orderId}");

        // Admin channel - tüm sipariş güncellemeleri için
        $channels[] = $this->createAdminChannel();

        // Public order tracking channel (tracking number ile)
        if ($this->trackingInfo && isset($this->trackingInfo['tracking_number'])) {
            $channels[] = $this->createPublicChannel("tracking.{$this->trackingInfo['tracking_number']}");
        }

        return $channels;
    }

    /**
     * Default event adını döndür
     */
    protected function getDefaultEventName(): string
    {
        return 'order.status.changed';
    }

    /**
     * Event'in aktif olup olmadığını kontrol et
     */
    protected function isEventEnabled(): bool
    {
        return config('broadcasting.features.broadcast_events.order_status_changed', true);
    }

    /**
     * Authorization için gerekli veriyi döndür
     */
    protected function getAuthData(): array
    {
        return [
            'order_id' => $this->orderId,
            'user_id' => $this->userId,
            'new_status' => $this->newStatus,
        ];
    }

    /**
     * Priority döndür - önemli status'ler yüksek priority
     */
    protected function getDefaultPriority(): int
    {
        $highPriorityStatuses = ['delivered', 'cancelled', 'refunded', 'failed'];
        $mediumPriorityStatuses = ['shipped', 'processing', 'confirmed'];

        if (in_array($this->newStatus, $highPriorityStatuses)) {
            return 9; // Yüksek priority
        }

        if (in_array($this->newStatus, $mediumPriorityStatuses)) {
            return 7; // Orta-yüksek priority
        }

        return 5; // Normal priority
    }

    /**
     * Cache ayarı - order status güncellemeleri cache'lenir
     */
    protected function getDefaultShouldCache(): bool
    {
        return true;
    }

    /**
     * Cache TTL - 5 dakika
     */
    protected function getDefaultCacheTtl(): int
    {
        return 300;
    }

    /**
     * Status progress yüzdesini hesapla
     */
    private function getStatusProgress(): int
    {
        $statusMap = [
            'pending' => 10,
            'confirmed' => 25,
            'processing' => 40,
            'shipped' => 70,
            'delivered' => 100,
            'cancelled' => 0,
            'refunded' => 0,
            'failed' => 0,
        ];

        return $statusMap[$this->newStatus] ?? 0;
    }

    /**
     * Final status kontrolü
     */
    private function isFinalStatus(): bool
    {
        $finalStatuses = ['delivered', 'cancelled', 'refunded', 'failed'];
        return in_array($this->newStatus, $finalStatuses);
    }

    /**
     * Kullanıcı aksiyonu gerekip gerekmediğini kontrol et
     */
    private function requiresUserAction(): bool
    {
        $actionRequiredStatuses = ['payment_required', 'address_confirmation', 'pickup_ready'];
        return in_array($this->newStatus, $actionRequiredStatuses);
    }

    /**
     * Broadcast edilecek veriyi genişlet
     */
    public function broadcastWith(): array
    {
        $data = parent::broadcastWith();

        // Status bilgileri
        $data['status_info'] = [
            'display_name' => $this->getStatusDisplayName(),
            'description' => $this->getStatusDescription(),
            'color' => $this->getStatusColor(),
            'icon' => $this->getStatusIcon(),
        ];

        // Timeline bilgisi
        $data['timeline'] = [
            'current_step' => $this->getCurrentStep(),
            'total_steps' => $this->getTotalSteps(),
            'progress_percentage' => $this->getStatusProgress(),
        ];

        // Kullanıcı için mesaj
        $data['user_message'] = [
            'title' => $this->getUserMessageTitle(),
            'message' => $this->getUserMessage(),
            'action_button' => $this->getActionButton(),
        ];

        // Admin için ek bilgiler
        $data['admin_info'] = [
            'requires_attention' => $this->requiresAdminAttention(),
            'automated_action' => $this->getAutomatedAction(),
        ];

        return $data;
    }

    /**
     * Status görünen adını döndür
     */
    private function getStatusDisplayName(): string
    {
        $statusNames = [
            'pending' => 'Beklemede',
            'confirmed' => 'Onaylandı',
            'processing' => 'Hazırlanıyor',
            'shipped' => 'Kargoya Verildi',
            'delivered' => 'Teslim Edildi',
            'cancelled' => 'İptal Edildi',
            'refunded' => 'İade Edildi',
            'failed' => 'Başarısız',
        ];

        return $statusNames[$this->newStatus] ?? ucfirst($this->newStatus);
    }

    /**
     * Status açıklamasını döndür
     */
    private function getStatusDescription(): string
    {
        $descriptions = [
            'pending' => 'Siparişiniz alındı ve işleme alınmayı bekliyor.',
            'confirmed' => 'Siparişiniz onaylandı ve hazırlık aşamasına geçti.',
            'processing' => 'Siparişiniz hazırlanıyor.',
            'shipped' => 'Siparişiniz kargoya verildi.',
            'delivered' => 'Siparişiniz başarıyla teslim edildi.',
            'cancelled' => 'Siparişiniz iptal edildi.',
            'refunded' => 'Siparişiniz için iade işlemi tamamlandı.',
            'failed' => 'Siparişinizde bir sorun oluştu.',
        ];

        return $descriptions[$this->newStatus] ?? '';
    }

    /**
     * Status rengini döndür
     */
    private function getStatusColor(): string
    {
        $colors = [
            'pending' => 'orange',
            'confirmed' => 'blue',
            'processing' => 'purple',
            'shipped' => 'indigo',
            'delivered' => 'green',
            'cancelled' => 'red',
            'refunded' => 'gray',
            'failed' => 'red',
        ];

        return $colors[$this->newStatus] ?? 'gray';
    }

    /**
     * Status ikonunu döndür
     */
    private function getStatusIcon(): string
    {
        $icons = [
            'pending' => 'clock',
            'confirmed' => 'check-circle',
            'processing' => 'cog',
            'shipped' => 'truck',
            'delivered' => 'check-circle-2',
            'cancelled' => 'x-circle',
            'refunded' => 'arrow-left-circle',
            'failed' => 'alert-circle',
        ];

        return $icons[$this->newStatus] ?? 'circle';
    }

    /**
     * Mevcut adımı döndür
     */
    private function getCurrentStep(): int
    {
        $steps = [
            'pending' => 1,
            'confirmed' => 2,
            'processing' => 3,
            'shipped' => 4,
            'delivered' => 5,
        ];

        return $steps[$this->newStatus] ?? 1;
    }

    /**
     * Toplam adım sayısını döndür
     */
    private function getTotalSteps(): int
    {
        return 5;
    }

    /**
     * Kullanıcı mesaj başlığını döndür
     */
    private function getUserMessageTitle(): string
    {
        return "Sipariş #{$this->orderId} Güncellendi";
    }

    /**
     * Kullanıcı mesajını döndür
     */
    private function getUserMessage(): string
    {
        return $this->statusMessage ?? $this->getStatusDescription();
    }

    /**
     * Aksiyon butonunu döndür
     */
    private function getActionButton(): ?array
    {
        if ($this->requiresUserAction()) {
            return [
                'text' => 'Detayları Görüntüle',
                'url' => "/orders/{$this->orderId}",
            ];
        }

        if ($this->newStatus === 'shipped' && $this->trackingInfo) {
            return [
                'text' => 'Kargo Takip',
                'url' => "/tracking/{$this->trackingInfo['tracking_number']}",
            ];
        }

        return null;
    }

    /**
     * Admin dikkat gerektirip gerektirmediğini kontrol et
     */
    private function requiresAdminAttention(): bool
    {
        return in_array($this->newStatus, ['failed', 'cancelled']);
    }

    /**
     * Otomatik aksiyon döndür
     */
    private function getAutomatedAction(): ?string
    {
        if ($this->newStatus === 'delivered') {
            return 'send_review_request';
        }

        if ($this->newStatus === 'shipped') {
            return 'start_tracking_updates';
        }

        return null;
    }
}
