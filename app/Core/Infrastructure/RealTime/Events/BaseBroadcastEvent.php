<?php

namespace App\Core\Infrastructure\RealTime\Events;

use App\Core\Infrastructure\RealTime\Contracts\BroadcastEventInterface;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Base broadcast event sınıfı
 */
abstract class BaseBroadcastEvent implements ShouldBroadcast, BroadcastEventInterface
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected array $data;
    protected array $channels;
    protected string $eventName;
    protected int $priority;
    protected bool $shouldCache;
    protected int $cacheTtl;
    protected string $queue;

    public function __construct(array $data = [])
    {
        $this->data = $data;
        $this->channels = $this->getDefaultChannels();
        $this->eventName = $this->getDefaultEventName();
        $this->priority = $this->getDefaultPriority();
        $this->shouldCache = $this->getDefaultShouldCache();
        $this->cacheTtl = $this->getDefaultCacheTtl();
        $this->queue = $this->getDefaultQueue();
    }

    /**
     * Event'in broadcast edileceği channel'ları döndür
     */
    public function broadcastOn(): array|Channel
    {
        return $this->channels;
    }

    /**
     * Broadcast event adını döndür
     */
    public function broadcastAs(): string
    {
        return $this->eventName;
    }

    /**
     * Broadcast edilecek veriyi döndür
     */
    public function broadcastWith(): array
    {
        return array_merge($this->data, [
            'timestamp' => now()->toISOString(),
            'event_id' => uniqid('event_', true),
            'priority' => $this->priority,
        ]);
    }

    /**
     * Event'in broadcast edilip edilmeyeceğini belirle
     */
    public function shouldBroadcast(): bool
    {
        // Global real-time feature kontrolü
        if (!config('broadcasting.features.enabled', true)) {
            return false;
        }

        // Event-specific kontrol
        return $this->isEventEnabled();
    }

    /**
     * Broadcast queue'sunu belirle
     */
    public function broadcastQueue(): string
    {
        return $this->queue;
    }

    /**
     * Channel authorization için gerekli veriyi döndür
     */
    public function getAuthorizationData(): array
    {
        return [
            'event_type' => $this->eventName,
            'timestamp' => now()->toISOString(),
            'data' => $this->getAuthData(),
        ];
    }

    /**
     * Event priority'sini döndür
     */
    public function getPriority(): int
    {
        return $this->priority;
    }

    /**
     * Event'in cache'lenip cache'lenmeyeceğini belirle
     */
    public function shouldCache(): bool
    {
        return $this->shouldCache;
    }

    /**
     * Cache TTL'ini döndür
     */
    public function getCacheTtl(): int
    {
        return $this->cacheTtl;
    }

    /**
     * Default channel'ları döndür (subclass'larda override edilecek)
     */
    abstract protected function getDefaultChannels(): array;

    /**
     * Default event adını döndür (subclass'larda override edilecek)
     */
    abstract protected function getDefaultEventName(): string;

    /**
     * Event'in aktif olup olmadığını kontrol et (subclass'larda override edilecek)
     */
    abstract protected function isEventEnabled(): bool;

    /**
     * Authorization için gerekli veriyi döndür (subclass'larda override edilebilir)
     */
    protected function getAuthData(): array
    {
        return [];
    }

    /**
     * Default priority döndür
     */
    protected function getDefaultPriority(): int
    {
        return 5; // Orta priority
    }

    /**
     * Default cache ayarını döndür
     */
    protected function getDefaultShouldCache(): bool
    {
        return false;
    }

    /**
     * Default cache TTL döndür
     */
    protected function getDefaultCacheTtl(): int
    {
        return 300; // 5 dakika
    }

    /**
     * Default queue döndür
     */
    protected function getDefaultQueue(): string
    {
        return 'realtime';
    }

    /**
     * Private channel oluştur
     */
    protected function createPrivateChannel(string $name): PrivateChannel
    {
        return new PrivateChannel($name);
    }

    /**
     * Public channel oluştur
     */
    protected function createPublicChannel(string $name): Channel
    {
        return new Channel($name);
    }

    /**
     * User-specific channel oluştur
     */
    protected function createUserChannel(int $userId): PrivateChannel
    {
        $prefix = config('broadcasting.channel_prefixes.user', 'user');
        return new PrivateChannel("{$prefix}.{$userId}");
    }

    /**
     * Admin channel oluştur
     */
    protected function createAdminChannel(): PrivateChannel
    {
        $prefix = config('broadcasting.channel_prefixes.admin', 'admin');
        return new PrivateChannel($prefix);
    }
}
