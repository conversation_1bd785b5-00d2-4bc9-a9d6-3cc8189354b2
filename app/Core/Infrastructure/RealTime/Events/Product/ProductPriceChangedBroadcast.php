<?php

namespace App\Core\Infrastructure\RealTime\Events\Product;

use App\Core\Infrastructure\RealTime\Events\BaseBroadcastEvent;

/**
 * Product fiyat değişikliği real-time broadcast event'i
 */
class ProductPriceChangedBroadcast extends BaseBroadcastEvent
{
    public function __construct(
        private int $productId,
        private float $newPrice,
        private float $oldPrice,
        private string $currency = 'TRY',
        private ?int $variantId = null,
        private ?string $priceType = 'regular' // regular, sale, special
    ) {
        parent::__construct([
            'product_id' => $this->productId,
            'new_price' => $this->newPrice,
            'old_price' => $this->oldPrice,
            'currency' => $this->currency,
            'variant_id' => $this->variantId,
            'price_type' => $this->priceType,
            'price_change' => $this->newPrice - $this->oldPrice,
            'price_change_percentage' => $this->calculatePriceChangePercentage(),
            'is_price_increase' => $this->newPrice > $this->oldPrice,
            'is_price_decrease' => $this->newPrice < $this->oldPrice,
            'is_sale_price' => $this->priceType === 'sale',
        ]);
    }

    /**
     * Default channel'ları döndür
     */
    protected function getDefaultChannels(): array
    {
        $channels = [];

        // Public product channel
        $productPrefix = config('broadcasting.channel_prefixes.product', 'product');
        $channels[] = $this->createPublicChannel("{$productPrefix}.{$this->productId}.price");

        // Variant-specific channel
        if ($this->variantId) {
            $channels[] = $this->createPublicChannel("{$productPrefix}.{$this->productId}.variant.{$this->variantId}.price");
        }

        // Admin channel - önemli fiyat değişiklikleri için
        if ($this->isSignificantPriceChange()) {
            $channels[] = $this->createAdminChannel();
        }

        // Cart channel - sepetinde bu ürün olan kullanıcılar için
        $cartPrefix = config('broadcasting.channel_prefixes.cart', 'cart');
        $channels[] = $this->createPublicChannel("{$cartPrefix}.product.{$this->productId}");

        // Wishlist channel - favorilerinde bu ürün olan kullanıcılar için
        $channels[] = $this->createPublicChannel("wishlist.product.{$this->productId}");

        return $channels;
    }

    /**
     * Default event adını döndür
     */
    protected function getDefaultEventName(): string
    {
        return 'product.price.changed';
    }

    /**
     * Event'in aktif olup olmadığını kontrol et
     */
    protected function isEventEnabled(): bool
    {
        return config('broadcasting.features.broadcast_events.product_price_changed', true);
    }

    /**
     * Authorization için gerekli veriyi döndür
     */
    protected function getAuthData(): array
    {
        return [
            'product_id' => $this->productId,
            'variant_id' => $this->variantId,
            'price_type' => $this->priceType,
        ];
    }

    /**
     * Priority döndür - önemli fiyat değişiklikleri yüksek priority
     */
    protected function getDefaultPriority(): int
    {
        if ($this->isSignificantPriceChange()) {
            return 8; // Önemli fiyat değişikliği
        }

        if ($this->priceType === 'sale') {
            return 7; // İndirim fiyatı
        }

        return 6; // Normal fiyat değişikliği
    }

    /**
     * Cache ayarı - fiyat değişiklikleri kısa süre cache'lenir
     */
    protected function getDefaultShouldCache(): bool
    {
        return true;
    }

    /**
     * Cache TTL - 1 dakika
     */
    protected function getDefaultCacheTtl(): int
    {
        return 60;
    }

    /**
     * Fiyat değişikliği yüzdesini hesapla
     */
    private function calculatePriceChangePercentage(): float
    {
        if ($this->oldPrice == 0) {
            return 0;
        }

        return round((($this->newPrice - $this->oldPrice) / $this->oldPrice) * 100, 2);
    }

    /**
     * Önemli fiyat değişikliği kontrolü
     */
    private function isSignificantPriceChange(): bool
    {
        $significantThreshold = config('products.price_change_threshold', 20); // %20
        return abs($this->calculatePriceChangePercentage()) >= $significantThreshold;
    }

    /**
     * Broadcast edilecek veriyi genişlet
     */
    public function broadcastWith(): array
    {
        $data = parent::broadcastWith();

        // Formatlanmış fiyat bilgileri
        $data['formatted_prices'] = [
            'new_price_formatted' => number_format($this->newPrice, 2) . ' ' . $this->currency,
            'old_price_formatted' => number_format($this->oldPrice, 2) . ' ' . $this->currency,
            'savings' => $this->oldPrice > $this->newPrice ? 
                number_format($this->oldPrice - $this->newPrice, 2) . ' ' . $this->currency : null,
        ];

        // Fiyat değişikliği analizi
        $data['price_analysis'] = [
            'is_significant_change' => $this->isSignificantPriceChange(),
            'change_type' => $this->getPriceChangeType(),
            'recommendation' => $this->getPriceRecommendation(),
        ];

        // Admin için ek bilgiler
        if ($this->isSignificantPriceChange()) {
            $data['admin_alert'] = [
                'type' => 'significant_price_change',
                'message' => $this->getAlertMessage(),
                'action_required' => false,
            ];
        }

        return $data;
    }

    /**
     * Fiyat değişikliği tipini belirle
     */
    private function getPriceChangeType(): string
    {
        if ($this->newPrice > $this->oldPrice) {
            return 'increase';
        } elseif ($this->newPrice < $this->oldPrice) {
            return 'decrease';
        }

        return 'no_change';
    }

    /**
     * Fiyat önerisi oluştur
     */
    private function getPriceRecommendation(): string
    {
        if ($this->priceType === 'sale' && $this->newPrice < $this->oldPrice) {
            return 'Şimdi satın almak için iyi bir fırsat!';
        }

        if ($this->newPrice > $this->oldPrice && $this->calculatePriceChangePercentage() > 10) {
            return 'Fiyat artışı öncesi satın almayı düşünün.';
        }

        return 'Fiyat güncellendi.';
    }

    /**
     * Alert mesajı oluştur
     */
    private function getAlertMessage(): string
    {
        $changePercentage = $this->calculatePriceChangePercentage();
        $changeType = $changePercentage > 0 ? 'arttı' : 'azaldı';
        
        return "Ürün #{$this->productId} fiyatı %{$changePercentage} {$changeType}!";
    }
}
