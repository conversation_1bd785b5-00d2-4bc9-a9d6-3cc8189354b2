<?php

namespace App\Core\Infrastructure\RealTime\Events\Product;

use App\Core\Infrastructure\RealTime\Events\BaseBroadcastEvent;
use Illuminate\Broadcasting\Channel;

/**
 * Product stock güncellemesi real-time broadcast event'i
 */
class ProductStockUpdatedBroadcast extends BaseBroadcastEvent
{
    public function __construct(
        private int $productId,
        private int $newStock,
        private int $oldStock,
        private ?int $variantId = null
    ) {
        parent::__construct([
            'product_id' => $this->productId,
            'new_stock' => $this->newStock,
            'old_stock' => $this->oldStock,
            'variant_id' => $this->variantId,
            'stock_change' => $this->newStock - $this->oldStock,
            'is_in_stock' => $this->newStock > 0,
            'is_low_stock' => $this->isLowStock(),
            'is_out_of_stock' => $this->newStock <= 0,
        ]);
    }

    /**
     * Default channel'ları döndür
     */
    protected function getDefaultChannels(): array
    {
        $channels = [];

        // Public product channel - tüm kullanıcılar için
        $productPrefix = config('broadcasting.channel_prefixes.product', 'product');
        $channels[] = $this->createPublicChannel("{$productPrefix}.{$this->productId}.stock");

        // Variant-specific channel (eğer variant varsa)
        if ($this->variantId) {
            $channels[] = $this->createPublicChannel("{$productPrefix}.{$this->productId}.variant.{$this->variantId}.stock");
        }

        // Admin channel - stok uyarıları için
        if ($this->isLowStock() || $this->newStock <= 0) {
            $channels[] = $this->createAdminChannel();
        }

        // Cart channel - sepetinde bu ürün olan kullanıcılar için
        $cartPrefix = config('broadcasting.channel_prefixes.cart', 'cart');
        $channels[] = $this->createPublicChannel("{$cartPrefix}.product.{$this->productId}");

        return $channels;
    }

    /**
     * Default event adını döndür
     */
    protected function getDefaultEventName(): string
    {
        return 'product.stock.updated';
    }

    /**
     * Event'in aktif olup olmadığını kontrol et
     */
    protected function isEventEnabled(): bool
    {
        return config('broadcasting.features.broadcast_events.product_stock_updated', true);
    }

    /**
     * Authorization için gerekli veriyi döndür
     */
    protected function getAuthData(): array
    {
        return [
            'product_id' => $this->productId,
            'variant_id' => $this->variantId,
        ];
    }

    /**
     * Priority döndür - stok kritik olduğunda yüksek priority
     */
    protected function getDefaultPriority(): int
    {
        if ($this->newStock <= 0) {
            return 9; // Stok bitti - yüksek priority
        }

        if ($this->isLowStock()) {
            return 7; // Düşük stok - orta-yüksek priority
        }

        return 5; // Normal stok güncellemesi
    }

    /**
     * Cache ayarı - stok güncellemeleri cache'lenmez
     */
    protected function getDefaultShouldCache(): bool
    {
        return false;
    }

    /**
     * Queue ayarı - kritik güncellemeler için hızlı queue
     */
    protected function getDefaultQueue(): string
    {
        if ($this->newStock <= 0 || $this->isLowStock()) {
            return 'realtime-critical';
        }

        return 'realtime';
    }

    /**
     * Düşük stok kontrolü
     */
    private function isLowStock(): bool
    {
        $lowStockThreshold = config('inventory.alerts.low_stock_threshold', 10);
        return $this->newStock > 0 && $this->newStock <= $lowStockThreshold;
    }

    /**
     * Broadcast edilecek veriyi genişlet
     */
    public function broadcastWith(): array
    {
        $data = parent::broadcastWith();

        // Ek bilgiler ekle
        $data['alerts'] = [
            'low_stock' => $this->isLowStock(),
            'out_of_stock' => $this->newStock <= 0,
            'stock_increased' => $this->newStock > $this->oldStock,
            'stock_decreased' => $this->newStock < $this->oldStock,
        ];

        // Admin için ek bilgiler
        if ($this->isLowStock() || $this->newStock <= 0) {
            $data['admin_alert'] = [
                'type' => $this->newStock <= 0 ? 'out_of_stock' : 'low_stock',
                'message' => $this->getAlertMessage(),
                'action_required' => true,
            ];
        }

        return $data;
    }

    /**
     * Alert mesajı oluştur
     */
    private function getAlertMessage(): string
    {
        if ($this->newStock <= 0) {
            return "Ürün #{$this->productId} stokta kalmadı!";
        }

        if ($this->isLowStock()) {
            return "Ürün #{$this->productId} stoku azaldı! Kalan: {$this->newStock}";
        }

        return "Ürün #{$this->productId} stoku güncellendi.";
    }
}
