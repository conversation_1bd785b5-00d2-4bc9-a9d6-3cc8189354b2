<?php

namespace App\Core\Infrastructure\RealTime\Commands;

use App\Core\Infrastructure\RealTime\Events\Product\ProductPriceChangedBroadcast;
use App\Core\Infrastructure\RealTime\Events\Product\ProductStockUpdatedBroadcast;
use App\Core\Infrastructure\RealTime\Events\Order\OrderStatusChangedBroadcast;
use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use Illuminate\Console\Command;

/**
 * Real-time system test komutu
 */
class TestRealTimeCommand extends Command
{
    protected $signature = 'realtime:test 
                           {--type=all : Test tipi (all, stock, price, order)}
                           {--count=1 : Test event sayısı}
                           {--delay=1 : Event'ler arası bekleme süresi (saniye)}';

    protected $description = 'Real-time system test event\'leri gönder';

    public function handle(RealTimeEventDispatcher $dispatcher): int
    {
        $type = $this->option('type');
        $count = (int) $this->option('count');
        $delay = (int) $this->option('delay');

        $this->info('🧪 Real-Time System Test');
        $this->info("Test Type: {$type}");
        $this->info("Event Count: {$count}");
        $this->info("Delay: {$delay}s");
        $this->newLine();

        $successCount = 0;
        $failureCount = 0;

        for ($i = 1; $i <= $count; $i++) {
            $this->info("Sending test event {$i}/{$count}...");

            try {
                $success = $this->sendTestEvent($dispatcher, $type, $i);
                
                if ($success) {
                    $successCount++;
                    $this->line("✅ Event {$i} sent successfully");
                } else {
                    $failureCount++;
                    $this->error("❌ Event {$i} failed to send");
                }

            } catch (\Exception $e) {
                $failureCount++;
                $this->error("❌ Event {$i} error: " . $e->getMessage());
            }

            // Delay (son event'te bekleme yok)
            if ($i < $count && $delay > 0) {
                sleep($delay);
            }
        }

        $this->newLine();
        $this->info('📊 Test Results:');
        $this->line("✅ Successful: {$successCount}");
        $this->line("❌ Failed: {$failureCount}");
        $this->line("📈 Success Rate: " . round(($successCount / $count) * 100, 2) . "%");

        return $failureCount === 0 ? 0 : 1;
    }

    /**
     * Test event gönder
     */
    private function sendTestEvent(RealTimeEventDispatcher $dispatcher, string $type, int $index): bool
    {
        switch ($type) {
            case 'stock':
                return $this->sendStockTestEvent($dispatcher, $index);
                
            case 'price':
                return $this->sendPriceTestEvent($dispatcher, $index);
                
            case 'order':
                return $this->sendOrderTestEvent($dispatcher, $index);
                
            case 'all':
                $results = [
                    $this->sendStockTestEvent($dispatcher, $index),
                    $this->sendPriceTestEvent($dispatcher, $index),
                    $this->sendOrderTestEvent($dispatcher, $index),
                ];
                return !in_array(false, $results);
                
            default:
                $this->error("Unknown test type: {$type}");
                return false;
        }
    }

    /**
     * Stock test event gönder
     */
    private function sendStockTestEvent(RealTimeEventDispatcher $dispatcher, int $index): bool
    {
        $productId = 1000 + $index;
        $newStock = rand(0, 100);
        $oldStock = $newStock + rand(1, 20);

        $event = new ProductStockUpdatedBroadcast(
            productId: $productId,
            newStock: $newStock,
            oldStock: $oldStock
        );

        return $dispatcher->dispatch($event);
    }

    /**
     * Price test event gönder
     */
    private function sendPriceTestEvent(RealTimeEventDispatcher $dispatcher, int $index): bool
    {
        $productId = 2000 + $index;
        $oldPrice = rand(100, 1000);
        $newPrice = $oldPrice + rand(-50, 50);

        $event = new ProductPriceChangedBroadcast(
            productId: $productId,
            newPrice: $newPrice,
            oldPrice: $oldPrice,
            currency: 'TRY',
            priceType: rand(0, 1) ? 'regular' : 'sale'
        );

        return $dispatcher->dispatch($event);
    }

    /**
     * Order test event gönder
     */
    private function sendOrderTestEvent(RealTimeEventDispatcher $dispatcher, int $index): bool
    {
        $orderId = 3000 + $index;
        $userId = rand(1, 100);
        
        $statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered'];
        $oldStatus = $statuses[array_rand($statuses)];
        $newStatus = $statuses[array_rand($statuses)];

        $event = new OrderStatusChangedBroadcast(
            orderId: $orderId,
            newStatus: $newStatus,
            oldStatus: $oldStatus,
            userId: $userId,
            statusMessage: "Test order status change from {$oldStatus} to {$newStatus}"
        );

        return $dispatcher->dispatch($event);
    }
}
