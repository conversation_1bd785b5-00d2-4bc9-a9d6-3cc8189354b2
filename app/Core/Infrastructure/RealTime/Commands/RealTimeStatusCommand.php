<?php

namespace App\Core\Infrastructure\RealTime\Commands;

use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

/**
 * Real-time system status kontrolü komutu
 */
class RealTimeStatusCommand extends Command
{
    protected $signature = 'realtime:status 
                           {--detailed : Detaylı status bilgisi göster}
                           {--json : JSON formatında çıktı ver}';

    protected $description = 'Real-time system status ve health check';

    public function handle(RealTimeEventDispatcher $dispatcher): int
    {
        $this->info('🚀 Real-Time System Status Check');
        $this->newLine();

        $status = $this->gatherSystemStatus($dispatcher);

        if ($this->option('json')) {
            $this->line(json_encode($status, JSON_PRETTY_PRINT));
            return 0;
        }

        $this->displayStatus($status);

        return $status['overall_health'] === 'healthy' ? 0 : 1;
    }

    /**
     * System status bilgilerini topla
     */
    private function gatherSystemStatus(RealTimeEventDispatcher $dispatcher): array
    {
        $status = [
            'timestamp' => now()->toISOString(),
            'overall_health' => 'healthy',
            'components' => [],
            'statistics' => [],
            'configuration' => [],
        ];

        // Broadcasting driver kontrolü
        $status['components']['broadcasting'] = $this->checkBroadcastingDriver();

        // Cache driver kontrolü
        $status['components']['cache'] = $this->checkCacheDriver();

        // Queue system kontrolü
        $status['components']['queue'] = $this->checkQueueSystem();

        // Redis connection kontrolü
        $status['components']['redis'] = $this->checkRedisConnection();

        // Event dispatcher statistics
        $status['statistics'] = $dispatcher->getStatistics();

        // Configuration summary
        $status['configuration'] = $this->getConfigurationSummary();

        // Overall health hesapla
        $status['overall_health'] = $this->calculateOverallHealth($status['components']);

        return $status;
    }

    /**
     * Broadcasting driver kontrolü
     */
    private function checkBroadcastingDriver(): array
    {
        try {
            $driver = config('broadcasting.default');
            $connection = config("broadcasting.connections.{$driver}");

            return [
                'status' => 'healthy',
                'driver' => $driver,
                'configuration' => $connection ? 'configured' : 'missing',
                'enabled' => config('broadcasting.features.enabled', true),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Cache driver kontrolü
     */
    private function checkCacheDriver(): array
    {
        try {
            $testKey = 'realtime:health:' . uniqid();
            $testValue = 'test_' . time();

            Cache::put($testKey, $testValue, 10);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);

            return [
                'status' => $retrieved === $testValue ? 'healthy' : 'unhealthy',
                'driver' => config('cache.default'),
                'test_result' => $retrieved === $testValue ? 'passed' : 'failed',
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Queue system kontrolü
     */
    private function checkQueueSystem(): array
    {
        try {
            $queueDriver = config('queue.default');
            $realtimeQueue = config('realtime.queues.default', 'realtime');

            return [
                'status' => 'healthy',
                'driver' => $queueDriver,
                'realtime_queue' => $realtimeQueue,
                'horizon_status' => $this->checkHorizonStatus(),
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Redis connection kontrolü
     */
    private function checkRedisConnection(): array
    {
        try {
            $redis = Redis::connection();
            $redis->ping();

            return [
                'status' => 'healthy',
                'connection' => 'active',
                'info' => $redis->info('server')['redis_version'] ?? 'unknown',
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Horizon status kontrolü
     */
    private function checkHorizonStatus(): string
    {
        try {
            if (class_exists(\Laravel\Horizon\Horizon::class)) {
                return 'installed';
            }
            return 'not_installed';

        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Configuration summary
     */
    private function getConfigurationSummary(): array
    {
        return [
            'realtime_enabled' => config('realtime.enabled', true),
            'debug_mode' => config('realtime.debug', false),
            'rate_limiting' => config('realtime.rate_limiting.enabled', true),
            'caching' => config('realtime.caching.enabled', true),
            'monitoring' => config('realtime.monitoring.enabled', true),
            'enabled_events' => array_filter(config('realtime.events', []), function ($event) {
                return $event['enabled'] ?? false;
            }),
        ];
    }

    /**
     * Overall health hesapla
     */
    private function calculateOverallHealth(array $components): string
    {
        $unhealthyCount = 0;
        $totalCount = count($components);

        foreach ($components as $component) {
            if (($component['status'] ?? 'unhealthy') !== 'healthy') {
                $unhealthyCount++;
            }
        }

        if ($unhealthyCount === 0) {
            return 'healthy';
        } elseif ($unhealthyCount < $totalCount / 2) {
            return 'degraded';
        } else {
            return 'unhealthy';
        }
    }

    /**
     * Status'u görsel olarak göster
     */
    private function displayStatus(array $status): void
    {
        // Overall health
        $healthIcon = match ($status['overall_health']) {
            'healthy' => '✅',
            'degraded' => '⚠️',
            'unhealthy' => '❌',
            default => '❓',
        };

        $this->info("Overall Health: {$healthIcon} " . ucfirst($status['overall_health']));
        $this->newLine();

        // Components
        $this->info('📊 Components Status:');
        foreach ($status['components'] as $name => $component) {
            $icon = $component['status'] === 'healthy' ? '✅' : '❌';
            $this->line("  {$icon} " . ucfirst($name) . ": " . $component['status']);
            
            if ($this->option('detailed') && isset($component['error'])) {
                $this->error("    Error: " . $component['error']);
            }
        }
        $this->newLine();

        // Statistics
        if (!empty($status['statistics'])) {
            $this->info('📈 Statistics:');
            $stats = $status['statistics'];
            $this->line("  Dispatched: " . ($stats['dispatched'] ?? 0));
            $this->line("  Cached: " . ($stats['cached'] ?? 0));
            $this->line("  Failed: " . ($stats['failed'] ?? 0));
            $this->line("  Filtered: " . ($stats['filtered'] ?? 0));
            $this->newLine();
        }

        // Configuration (detailed mode)
        if ($this->option('detailed')) {
            $this->info('⚙️ Configuration:');
            $config = $status['configuration'];
            $this->line("  Real-time Enabled: " . ($config['realtime_enabled'] ? 'Yes' : 'No'));
            $this->line("  Debug Mode: " . ($config['debug_mode'] ? 'Yes' : 'No'));
            $this->line("  Rate Limiting: " . ($config['rate_limiting'] ? 'Yes' : 'No'));
            $this->line("  Caching: " . ($config['caching'] ? 'Yes' : 'No'));
            $this->line("  Monitoring: " . ($config['monitoring'] ? 'Yes' : 'No'));
            $this->line("  Enabled Events: " . count($config['enabled_events']));
        }
    }
}
