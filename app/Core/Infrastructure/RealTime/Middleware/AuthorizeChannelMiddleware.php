<?php

namespace App\Core\Infrastructure\RealTime\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * Real-time channel authorization middleware
 */
class AuthorizeChannelMiddleware
{
    /**
     * Channel authorization'ı handle et
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $channelName = $request->route('channel');
            $user = Auth::user();

            Log::debug('Channel authorization request', [
                'channel' => $channelName,
                'user_id' => $user?->id,
                'ip' => $request->ip(),
            ]);

            // Channel authorization kontrolü
            if (!$this->authorizeChannel($channelName, $user, $request)) {
                Log::warning('Channel authorization failed', [
                    'channel' => $channelName,
                    'user_id' => $user?->id,
                    'reason' => 'unauthorized',
                ]);

                return response()->json([
                    'error' => 'Unauthorized',
                    'message' => 'Bu kanala erişim yetkiniz bulunmamaktadır.',
                ], 403);
            }

            // Rate limiting kontrolü
            if (!$this->checkRateLimit($channelName, $user, $request)) {
                Log::warning('Channel rate limit exceeded', [
                    'channel' => $channelName,
                    'user_id' => $user?->id,
                ]);

                return response()->json([
                    'error' => 'Rate Limit Exceeded',
                    'message' => 'Çok fazla bağlantı denemesi.',
                ], 429);
            }

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Channel authorization error', [
                'channel' => $request->route('channel'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'error' => 'Authorization Error',
                'message' => 'Yetkilendirme sırasında bir hata oluştu.',
            ], 500);
        }
    }

    /**
     * Channel authorization kontrolü
     */
    private function authorizeChannel(string $channelName, $user, Request $request): bool
    {
        // Public channel'lar için authorization gerekmez
        if ($this->isPublicChannel($channelName)) {
            return true;
        }

        // Private channel'lar için authentication gerekli
        if ($this->isPrivateChannel($channelName)) {
            if (!$user) {
                return false;
            }

            return $this->authorizePrivateChannel($channelName, $user, $request);
        }

        // Presence channel'lar için authentication gerekli
        if ($this->isPresenceChannel($channelName)) {
            if (!$user) {
                return false;
            }

            return $this->authorizePresenceChannel($channelName, $user, $request);
        }

        // Admin channel'lar için admin yetkisi gerekli
        if ($this->isAdminChannel($channelName)) {
            if (!$user || !$user->hasRole('admin')) {
                return false;
            }

            return true;
        }

        // Bilinmeyen channel tipi
        return false;
    }

    /**
     * Public channel kontrolü
     */
    private function isPublicChannel(string $channelName): bool
    {
        $publicPrefixes = [
            'product.',
            'cart.product.',
            'tracking.',
            'public.',
        ];

        foreach ($publicPrefixes as $prefix) {
            if (str_starts_with($channelName, $prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Private channel kontrolü
     */
    private function isPrivateChannel(string $channelName): bool
    {
        $privatePrefixes = [
            'private-user.',
            'private-order.',
            'private-cart.',
            'private-notification.',
        ];

        foreach ($privatePrefixes as $prefix) {
            if (str_starts_with($channelName, $prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Presence channel kontrolü
     */
    private function isPresenceChannel(string $channelName): bool
    {
        return str_starts_with($channelName, 'presence-');
    }

    /**
     * Admin channel kontrolü
     */
    private function isAdminChannel(string $channelName): bool
    {
        $adminPrefixes = [
            'private-admin',
            'admin.',
        ];

        foreach ($adminPrefixes as $prefix) {
            if (str_starts_with($channelName, $prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Private channel authorization
     */
    private function authorizePrivateChannel(string $channelName, $user, Request $request): bool
    {
        // User channel - sadece kendi channel'ına erişebilir
        if (str_starts_with($channelName, 'private-user.')) {
            $userId = (int) str_replace('private-user.', '', $channelName);
            return $user->id === $userId;
        }

        // Order channel - sadece kendi siparişlerine erişebilir
        if (str_starts_with($channelName, 'private-order.')) {
            $orderId = (int) str_replace('private-order.', '', $channelName);
            return $this->userOwnsOrder($user, $orderId);
        }

        // Cart channel - sadece kendi sepetine erişebilir
        if (str_starts_with($channelName, 'private-cart.')) {
            $userId = (int) str_replace('private-cart.', '', $channelName);
            return $user->id === $userId;
        }

        // Notification channel - sadece kendi bildirimlerine erişebilir
        if (str_starts_with($channelName, 'private-notification.')) {
            $userId = (int) str_replace('private-notification.', '', $channelName);
            return $user->id === $userId;
        }

        return false;
    }

    /**
     * Presence channel authorization
     */
    private function authorizePresenceChannel(string $channelName, $user, Request $request): bool
    {
        // Presence channel'lar için özel authorization logic'i
        // Örneğin: presence-chat.room.123 -> room 123'e erişim kontrolü
        
        return true; // Şimdilik tüm authenticated user'lara izin ver
    }

    /**
     * Rate limiting kontrolü
     */
    private function checkRateLimit(string $channelName, $user, Request $request): bool
    {
        if (!config('broadcasting.features.rate_limiting.enabled', true)) {
            return true;
        }

        $key = $this->getRateLimitKey($channelName, $user, $request);
        $maxConnections = config('broadcasting.features.performance.max_connections_per_user', 5);

        $cache = app('cache');
        $current = $cache->get($key, 0);

        if ($current >= $maxConnections) {
            return false;
        }

        $cache->put($key, $current + 1, 300); // 5 dakika
        return true;
    }

    /**
     * Rate limit key oluştur
     */
    private function getRateLimitKey(string $channelName, $user, Request $request): string
    {
        $identifier = $user ? "user:{$user->id}" : "ip:{$request->ip()}";
        return "realtime:connections:{$identifier}";
    }

    /**
     * User'ın order'a sahip olup olmadığını kontrol et
     */
    private function userOwnsOrder($user, int $orderId): bool
    {
        try {
            // Order repository'den kontrol et
            $orderRepository = app(\App\Infrastructure\Orders\Repositories\EloquentOrderRepository::class);
            $order = $orderRepository->findById($orderId);
            
            return $order && $order->getUserId() === $user->id;

        } catch (\Exception $e) {
            Log::error('Order ownership check failed', [
                'user_id' => $user->id,
                'order_id' => $orderId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
