<?php

namespace App\Core\Infrastructure\RealTime\Services;

use App\Core\Infrastructure\RealTime\Contracts\BroadcastEventInterface;
use Illuminate\Broadcasting\BroadcastManager;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

/**
 * Real-time event dispatcher servisi
 */
class RealTimeEventDispatcher
{
    private array $stats = [
        'dispatched' => 0,
        'cached' => 0,
        'failed' => 0,
        'filtered' => 0,
    ];

    public function __construct(
        private BroadcastManager $broadcastManager,
        private CacheRepository $cache,
        private array $config = []
    ) {
        $this->config = array_merge([
            'enabled' => true,
            'debug' => false,
            'rate_limiting' => true,
            'max_events_per_minute' => 60,
            'cache_enabled' => true,
        ], $config);
    }

    /**
     * Real-time event dispatch et
     */
    public function dispatch(BroadcastEventInterface $event): bool
    {
        try {
            // Global kontrol
            if (!$this->isEnabled()) {
                $this->stats['filtered']++;
                return false;
            }

            // Event-specific kontrol
            if (!$event->shouldBroadcast()) {
                $this->stats['filtered']++;
                return false;
            }

            // Rate limiting kontrolü
            if (!$this->checkRateLimit($event)) {
                $this->stats['filtered']++;
                Log::warning('Real-time event rate limited', [
                    'event' => $event->broadcastAs(),
                    'priority' => $event->getPriority(),
                ]);
                return false;
            }

            // Cache kontrolü
            if ($this->shouldSkipDueToCaching($event)) {
                $this->stats['cached']++;
                return true;
            }

            // Event'i dispatch et
            $this->performDispatch($event);

            // Cache'e kaydet (eğer gerekiyorsa)
            $this->cacheEvent($event);

            $this->stats['dispatched']++;

            if ($this->config['debug']) {
                Log::debug('Real-time event dispatched', [
                    'event' => $event->broadcastAs(),
                    'channels' => $this->getChannelNames($event),
                    'priority' => $event->getPriority(),
                ]);
            }

            return true;

        } catch (\Exception $e) {
            $this->stats['failed']++;
            Log::error('Real-time event dispatch failed', [
                'event' => $event->broadcastAs(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Birden fazla event'i batch olarak dispatch et
     */
    public function dispatchBatch(array $events): array
    {
        $results = [];

        foreach ($events as $event) {
            if ($event instanceof BroadcastEventInterface) {
                $results[] = $this->dispatch($event);
            } else {
                $results[] = false;
                Log::warning('Invalid event in batch dispatch', [
                    'event_type' => get_class($event),
                ]);
            }
        }

        return $results;
    }

    /**
     * Event'i queue'ya ekle (async dispatch için)
     */
    public function dispatchAsync(BroadcastEventInterface $event): bool
    {
        try {
            $queue = $event->broadcastQueue();
            
            Queue::pushOn($queue, new \App\Core\Infrastructure\RealTime\Jobs\DispatchRealTimeEvent($event));

            Log::debug('Real-time event queued for async dispatch', [
                'event' => $event->broadcastAs(),
                'queue' => $queue,
                'priority' => $event->getPriority(),
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Real-time event async dispatch failed', [
                'event' => $event->broadcastAs(),
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Statistics'leri al
     */
    public function getStatistics(): array
    {
        return array_merge($this->stats, [
            'timestamp' => now()->toISOString(),
            'rate_limit_remaining' => $this->getRateLimitRemaining(),
        ]);
    }

    /**
     * Statistics'leri sıfırla
     */
    public function resetStatistics(): void
    {
        $this->stats = [
            'dispatched' => 0,
            'cached' => 0,
            'failed' => 0,
            'filtered' => 0,
        ];
    }

    /**
     * Real-time system'in aktif olup olmadığını kontrol et
     */
    private function isEnabled(): bool
    {
        return $this->config['enabled'] && 
               config('broadcasting.features.enabled', true);
    }

    /**
     * Rate limit kontrolü
     */
    private function checkRateLimit(BroadcastEventInterface $event): bool
    {
        if (!$this->config['rate_limiting']) {
            return true;
        }

        $key = 'realtime:rate_limit:' . now()->format('Y-m-d-H-i');
        $current = $this->cache->get($key, 0);
        $limit = $this->config['max_events_per_minute'];

        if ($current >= $limit) {
            return false;
        }

        $this->cache->put($key, $current + 1, 60);
        return true;
    }

    /**
     * Cache nedeniyle skip edilip edilmeyeceğini kontrol et
     */
    private function shouldSkipDueToCaching(BroadcastEventInterface $event): bool
    {
        if (!$this->config['cache_enabled'] || !$event->shouldCache()) {
            return false;
        }

        $cacheKey = $this->generateEventCacheKey($event);
        return $this->cache->has($cacheKey);
    }

    /**
     * Event'i gerçekten dispatch et
     */
    private function performDispatch(BroadcastEventInterface $event): void
    {
        // Priority'ye göre farklı broadcast driver'ları kullanılabilir
        $driver = $this->selectBroadcastDriver($event);
        
        $this->broadcastManager->connection($driver)->broadcast(
            $event->broadcastOn(),
            $event->broadcastAs(),
            $event->broadcastWith()
        );
    }

    /**
     * Event'i cache'e kaydet
     */
    private function cacheEvent(BroadcastEventInterface $event): void
    {
        if (!$this->config['cache_enabled'] || !$event->shouldCache()) {
            return;
        }

        $cacheKey = $this->generateEventCacheKey($event);
        $ttl = $event->getCacheTtl();

        $this->cache->put($cacheKey, [
            'event' => $event->broadcastAs(),
            'data' => $event->broadcastWith(),
            'timestamp' => now()->toISOString(),
        ], $ttl);
    }

    /**
     * Event için cache key oluştur
     */
    private function generateEventCacheKey(BroadcastEventInterface $event): string
    {
        $data = $event->broadcastWith();
        $dataHash = md5(serialize($data));
        
        return "realtime:event:{$event->broadcastAs()}:{$dataHash}";
    }

    /**
     * Priority'ye göre broadcast driver seç
     */
    private function selectBroadcastDriver(BroadcastEventInterface $event): ?string
    {
        $priority = $event->getPriority();

        // Yüksek priority event'ler için daha hızlı driver
        if ($priority >= 8) {
            return config('broadcasting.high_priority_driver', 'reverb');
        }

        // Normal priority için default driver
        return null; // Default driver kullanılacak
    }

    /**
     * Channel isimlerini al (debug için)
     */
    private function getChannelNames(BroadcastEventInterface $event): array
    {
        $channels = $event->broadcastOn();
        
        if (!is_array($channels)) {
            $channels = [$channels];
        }

        return array_map(function ($channel) {
            return method_exists($channel, 'name') ? $channel->name : (string) $channel;
        }, $channels);
    }

    /**
     * Kalan rate limit sayısını al
     */
    private function getRateLimitRemaining(): int
    {
        if (!$this->config['rate_limiting']) {
            return -1; // Unlimited
        }

        $key = 'realtime:rate_limit:' . now()->format('Y-m-d-H-i');
        $current = $this->cache->get($key, 0);
        $limit = $this->config['max_events_per_minute'];

        return max(0, $limit - $current);
    }
}
