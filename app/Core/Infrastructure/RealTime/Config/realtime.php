<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Real-Time Features
    |--------------------------------------------------------------------------
    |
    | ModularEcommerce real-time özellik konfigürasyonu
    |
    */

    'enabled' => env('REALTIME_ENABLED', true),
    'debug' => env('REALTIME_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Event Broadcasting Configuration
    |--------------------------------------------------------------------------
    |
    | Hangi event'lerin real-time broadcast edileceği
    |
    */

    'events' => [
        'product_stock_updated' => [
            'enabled' => env('REALTIME_PRODUCT_STOCK', true),
            'priority' => 8,
            'cache_enabled' => false,
            'queue' => 'realtime-critical',
        ],
        
        'product_price_changed' => [
            'enabled' => env('REALTIME_PRODUCT_PRICE', true),
            'priority' => 6,
            'cache_enabled' => true,
            'cache_ttl' => 60,
            'queue' => 'realtime',
        ],
        
        'order_status_changed' => [
            'enabled' => env('REALTIME_ORDER_STATUS', true),
            'priority' => 9,
            'cache_enabled' => true,
            'cache_ttl' => 300,
            'queue' => 'realtime-critical',
        ],
        
        'cart_updated' => [
            'enabled' => env('REALTIME_CART_UPDATES', true),
            'priority' => 5,
            'cache_enabled' => false,
            'queue' => 'realtime',
        ],
        
        'notifications' => [
            'enabled' => env('REALTIME_NOTIFICATIONS', true),
            'priority' => 7,
            'cache_enabled' => false,
            'queue' => 'realtime',
        ],
        
        'admin_alerts' => [
            'enabled' => env('REALTIME_ADMIN_ALERTS', true),
            'priority' => 10,
            'cache_enabled' => false,
            'queue' => 'realtime-critical',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Channel Configuration
    |--------------------------------------------------------------------------
    |
    | Channel prefix'leri ve authorization ayarları
    |
    */

    'channels' => [
        'prefixes' => [
            'public' => 'public',
            'private' => 'private',
            'presence' => 'presence',
            'admin' => 'admin',
            'user' => 'user',
            'product' => 'product',
            'order' => 'order',
            'cart' => 'cart',
            'notification' => 'notification',
        ],

        'authorization' => [
            'enabled' => env('REALTIME_AUTH_ENABLED', true),
            'middleware' => ['auth:sanctum'],
            'guards' => ['web', 'api'],
            'timeout' => env('REALTIME_AUTH_TIMEOUT', 30),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Performance ve resource management ayarları
    |
    */

    'performance' => [
        'max_connections_per_user' => env('REALTIME_MAX_CONNECTIONS', 5),
        'connection_timeout' => env('REALTIME_CONNECTION_TIMEOUT', 60),
        'heartbeat_interval' => env('REALTIME_HEARTBEAT_INTERVAL', 30),
        'max_message_size' => env('REALTIME_MAX_MESSAGE_SIZE', 1024), // KB
        'memory_limit' => env('REALTIME_MEMORY_LIMIT', '256M'),
        'max_execution_time' => env('REALTIME_MAX_EXECUTION_TIME', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | Rate limiting ayarları
    |
    */

    'rate_limiting' => [
        'enabled' => env('REALTIME_RATE_LIMITING', true),
        'max_events_per_minute' => env('REALTIME_MAX_EVENTS_PER_MINUTE', 60),
        'max_events_per_hour' => env('REALTIME_MAX_EVENTS_PER_HOUR', 1000),
        'max_connections_per_ip' => env('REALTIME_MAX_CONNECTIONS_PER_IP', 10),
        'ban_duration' => env('REALTIME_BAN_DURATION', 300), // saniye
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Real-time event'ler için queue ayarları
    |
    */

    'queues' => [
        'default' => env('REALTIME_DEFAULT_QUEUE', 'realtime'),
        'critical' => env('REALTIME_CRITICAL_QUEUE', 'realtime-critical'),
        'low_priority' => env('REALTIME_LOW_PRIORITY_QUEUE', 'realtime-low'),
        
        'retry_attempts' => env('REALTIME_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('REALTIME_RETRY_DELAY', 5), // saniye
        'timeout' => env('REALTIME_QUEUE_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching Configuration
    |--------------------------------------------------------------------------
    |
    | Real-time event caching ayarları
    |
    */

    'caching' => [
        'enabled' => env('REALTIME_CACHE_ENABLED', true),
        'driver' => env('REALTIME_CACHE_DRIVER', 'redis'),
        'prefix' => env('REALTIME_CACHE_PREFIX', 'realtime'),
        'default_ttl' => env('REALTIME_CACHE_TTL', 300), // saniye
        
        'tags' => [
            'enabled' => env('REALTIME_CACHE_TAGS', true),
            'product_events' => 'realtime:products',
            'order_events' => 'realtime:orders',
            'user_events' => 'realtime:users',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Monitoring ve logging ayarları
    |
    */

    'monitoring' => [
        'enabled' => env('REALTIME_MONITORING', true),
        'log_connections' => env('REALTIME_LOG_CONNECTIONS', false),
        'log_events' => env('REALTIME_LOG_EVENTS', false),
        'log_errors' => env('REALTIME_LOG_ERRORS', true),
        
        'metrics' => [
            'enabled' => env('REALTIME_METRICS', true),
            'driver' => env('REALTIME_METRICS_DRIVER', 'redis'),
            'retention_days' => env('REALTIME_METRICS_RETENTION', 7),
        ],
        
        'alerts' => [
            'enabled' => env('REALTIME_ALERTS', true),
            'high_error_rate_threshold' => env('REALTIME_ERROR_THRESHOLD', 10), // %
            'high_latency_threshold' => env('REALTIME_LATENCY_THRESHOLD', 1000), // ms
            'connection_limit_threshold' => env('REALTIME_CONNECTION_THRESHOLD', 80), // %
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Güvenlik ayarları
    |
    */

    'security' => [
        'csrf_protection' => env('REALTIME_CSRF_PROTECTION', true),
        'origin_check' => env('REALTIME_ORIGIN_CHECK', true),
        'allowed_origins' => env('REALTIME_ALLOWED_ORIGINS', '*'),
        
        'encryption' => [
            'enabled' => env('REALTIME_ENCRYPTION', false),
            'algorithm' => env('REALTIME_ENCRYPTION_ALGORITHM', 'AES-256-CBC'),
        ],
        
        'ip_filtering' => [
            'enabled' => env('REALTIME_IP_FILTERING', false),
            'whitelist' => env('REALTIME_IP_WHITELIST', ''),
            'blacklist' => env('REALTIME_IP_BLACKLIST', ''),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Configuration
    |--------------------------------------------------------------------------
    |
    | Development ve testing ayarları
    |
    */

    'development' => [
        'fake_events' => env('REALTIME_FAKE_EVENTS', false),
        'test_mode' => env('REALTIME_TEST_MODE', false),
        'debug_toolbar' => env('REALTIME_DEBUG_TOOLBAR', false),
        
        'mock_drivers' => [
            'enabled' => env('REALTIME_MOCK_DRIVERS', false),
            'log_to_file' => env('REALTIME_MOCK_LOG_FILE', false),
        ],
    ],

];
