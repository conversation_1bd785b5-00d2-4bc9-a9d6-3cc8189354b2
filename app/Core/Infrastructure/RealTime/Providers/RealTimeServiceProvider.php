<?php

namespace App\Core\Infrastructure\RealTime\Providers;

use App\Core\Infrastructure\RealTime\Listeners\OrderEventBroadcastListener;
use App\Core\Infrastructure\RealTime\Listeners\ProductEventBroadcastListener;
use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use App\Domain\Orders\Events\OrderStatusChanged;
use App\Domain\Products\Events\PriceChanged;
use App\Domain\Products\Events\StockUpdated;
use Illuminate\Broadcasting\BroadcastManager;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

/**
 * Real-Time Infrastructure Service Provider
 */
class RealTimeServiceProvider extends ServiceProvider
{
    /**
     * Register services
     */
    public function register(): void
    {
        // Real-Time Event Dispatcher'ı register et
        $this->app->singleton(RealTimeEventDispatcher::class, function ($app) {
            return new RealTimeEventDispatcher(
                $app->make(BroadcastManager::class),
                $app->make(CacheRepository::class),
                config('broadcasting.features', [])
            );
        });

        // Config'i merge et
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/realtime.php',
            'realtime'
        );
    }

    /**
     * Bootstrap services
     */
    public function boot(): void
    {
        // Real-time features aktifse event listener'ları register et
        if (config('broadcasting.features.enabled', true)) {
            $this->registerEventListeners();
        }

        // Broadcasting routes'ları register et
        $this->registerBroadcastingRoutes();

        // Commands'ları register et
        if ($this->app->runningInConsole()) {
            $this->registerCommands();
        }

        // Config'i publish et
        $this->publishes([
            __DIR__ . '/../Config/realtime.php' => config_path('realtime.php'),
        ], 'realtime-config');

        // Middleware'leri register et
        $this->registerMiddleware();
    }

    /**
     * Event listener'ları register et
     */
    private function registerEventListeners(): void
    {
        // Product event'leri için real-time broadcast listener
        if (config('broadcasting.features.broadcast_events.product_stock_updated', true) ||
            config('broadcasting.features.broadcast_events.product_price_changed', true)) {
            
            Event::listen([
                StockUpdated::class,
                PriceChanged::class,
            ], ProductEventBroadcastListener::class);
        }

        // Order event'leri için real-time broadcast listener
        if (config('broadcasting.features.broadcast_events.order_status_changed', true)) {
            Event::listen([
                OrderStatusChanged::class,
            ], OrderEventBroadcastListener::class);
        }
    }

    /**
     * Broadcasting routes'ları register et
     */
    private function registerBroadcastingRoutes(): void
    {
        if (config('broadcasting.features.auth.enabled', true)) {
            // Channel authorization route'ları
            \Illuminate\Support\Facades\Broadcast::routes([
                'middleware' => config('broadcasting.features.auth.middleware', ['auth:sanctum']),
                'prefix' => 'api/broadcasting',
            ]);

            // Custom channel authorization'ları tanımla
            $this->defineChannelAuthorizations();
        }
    }

    /**
     * Channel authorization'ları tanımla
     */
    private function defineChannelAuthorizations(): void
    {
        // User-specific private channels
        \Illuminate\Support\Facades\Broadcast::channel('user.{userId}', function ($user, $userId) {
            return (int) $user->id === (int) $userId;
        });

        // Order-specific private channels
        \Illuminate\Support\Facades\Broadcast::channel('order.{orderId}', function ($user, $orderId) {
            return $this->userOwnsOrder($user, $orderId);
        });

        // Admin channels
        \Illuminate\Support\Facades\Broadcast::channel('admin', function ($user) {
            return $user->hasRole('admin');
        });

        // Cart channels
        \Illuminate\Support\Facades\Broadcast::channel('cart.{userId}', function ($user, $userId) {
            return (int) $user->id === (int) $userId;
        });

        // Notification channels
        \Illuminate\Support\Facades\Broadcast::channel('notification.{userId}', function ($user, $userId) {
            return (int) $user->id === (int) $userId;
        });
    }

    /**
     * Commands'ları register et
     */
    private function registerCommands(): void
    {
        $this->commands([
            \App\Core\Infrastructure\RealTime\Commands\RealTimeStatusCommand::class,
            \App\Core\Infrastructure\RealTime\Commands\TestRealTimeCommand::class,
        ]);
    }

    /**
     * Middleware'leri register et
     */
    private function registerMiddleware(): void
    {
        $router = $this->app['router'];
        
        $router->aliasMiddleware(
            'authorize.channel',
            \App\Core\Infrastructure\RealTime\Middleware\AuthorizeChannelMiddleware::class
        );
    }

    /**
     * User'ın order'a sahip olup olmadığını kontrol et
     */
    private function userOwnsOrder($user, int $orderId): bool
    {
        try {
            $orderRepository = $this->app->make(\App\Infrastructure\Orders\Repositories\EloquentOrderRepository::class);
            $order = $orderRepository->findById($orderId);
            
            return $order && $order->getUserId() === $user->id;

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Order ownership check failed in channel authorization', [
                'user_id' => $user->id,
                'order_id' => $orderId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Provider'ın sağladığı service'leri döndür
     */
    public function provides(): array
    {
        return [
            RealTimeEventDispatcher::class,
        ];
    }
}
