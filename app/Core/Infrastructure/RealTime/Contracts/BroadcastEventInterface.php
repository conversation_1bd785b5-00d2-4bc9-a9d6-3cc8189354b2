<?php

namespace App\Core\Infrastructure\RealTime\Contracts;

use Illuminate\Broadcasting\Channel;

/**
 * Real-time broadcast edilebilir event interface'i
 */
interface BroadcastEventInterface
{
    /**
     * Event'in broadcast edileceği channel'ları döndür
     */
    public function broadcastOn(): array|Channel;

    /**
     * Broadcast event adını döndür
     */
    public function broadcastAs(): string;

    /**
     * Broadcast edilecek veriyi döndür
     */
    public function broadcastWith(): array;

    /**
     * Event'in broadcast edilip edilmeyeceğini belirle
     */
    public function shouldBroadcast(): bool;

    /**
     * Broadcast queue'sunu belirle
     */
    public function broadcastQueue(): string;

    /**
     * Channel authorization için gerekli veriyi döndür
     */
    public function getAuthorizationData(): array;

    /**
     * Event priority'sini döndür (1-10, 10 en yüksek)
     */
    public function getPriority(): int;

    /**
     * Event'in cache'lenip cache'lenmeyeceğini belirle
     */
    public function shouldCache(): bool;

    /**
     * Cache TTL'ini döndür (saniye)
     */
    public function getCacheTtl(): int;
}
