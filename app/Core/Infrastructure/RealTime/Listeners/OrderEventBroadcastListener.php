<?php

namespace App\Core\Infrastructure\RealTime\Listeners;

use App\Core\Infrastructure\Events\Listeners\BaseEventListener;
use App\Core\Infrastructure\RealTime\Events\Order\OrderStatusChangedBroadcast;
use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use App\Domain\Orders\Events\OrderStatusChanged;
use App\Domain\Shared\Events\DomainEvent;
use Illuminate\Support\Facades\Log;

/**
 * Order domain event'lerini real-time broadcast'e çeviren listener
 */
class OrderEventBroadcastListener extends BaseEventListener
{
    public function __construct(
        private RealTimeEventDispatcher $realTimeDispatcher
    ) {
        parent::__construct();
    }

    /**
     * Event'i handle et
     */
    protected function doHandle(DomainEvent $event): void
    {
        try {
            $broadcastEvent = $this->createBroadcastEvent($event);
            
            if ($broadcastEvent) {
                // Sync dispatch (order status değişiklikleri kritik)
                $this->realTimeDispatcher->dispatch($broadcastEvent);
                
                Log::debug('Order event converted to real-time broadcast', [
                    'domain_event' => $event->getEventName(),
                    'broadcast_event' => $broadcastEvent->broadcastAs(),
                    'order_id' => $this->getOrderId($event),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to convert order event to real-time broadcast', [
                'domain_event' => $event->getEventName(),
                'order_id' => $this->getOrderId($event),
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Domain event'ten broadcast event oluştur
     */
    private function createBroadcastEvent(DomainEvent $event): ?object
    {
        switch (get_class($event)) {
            case OrderStatusChanged::class:
                return $this->createOrderStatusChangedBroadcast($event);
                
            default:
                return null;
        }
    }

    /**
     * Order status changed broadcast event oluştur
     */
    private function createOrderStatusChangedBroadcast(OrderStatusChanged $event): OrderStatusChangedBroadcast
    {
        $eventData = $event->toArray();
        
        return new OrderStatusChangedBroadcast(
            orderId: $eventData['order_id'],
            newStatus: $eventData['new_status'],
            oldStatus: $eventData['old_status'],
            userId: $eventData['user_id'],
            statusMessage: $eventData['status_message'] ?? null,
            trackingInfo: $eventData['tracking_info'] ?? null
        );
    }

    /**
     * Event'ten order ID'sini al
     */
    private function getOrderId(DomainEvent $event): ?int
    {
        $eventData = $event->toArray();
        return $eventData['order_id'] ?? null;
    }

    /**
     * Bu listener'ın handle edebileceği event'leri kontrol et
     */
    public function canHandle(DomainEvent $event): bool
    {
        $supportedEvents = [
            OrderStatusChanged::class,
        ];

        return in_array(get_class($event), $supportedEvents);
    }

    /**
     * Listener adını döndür
     */
    public function getName(): string
    {
        return 'OrderEventBroadcastListener';
    }

    /**
     * Listener'ın aktif olup olmadığını kontrol et
     */
    public function isEnabled(): bool
    {
        return config('broadcasting.features.enabled', true) &&
               config('broadcasting.features.broadcast_events.order_status_changed', true);
    }
}
