<?php

namespace App\Core\Infrastructure\RealTime\Listeners;

use App\Core\Infrastructure\Events\Listeners\BaseEventListener;
use App\Core\Infrastructure\RealTime\Events\Product\ProductPriceChangedBroadcast;
use App\Core\Infrastructure\RealTime\Events\Product\ProductStockUpdatedBroadcast;
use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use App\Domain\Products\Events\PriceChanged;
use App\Domain\Products\Events\StockUpdated;
use App\Domain\Shared\Events\DomainEvent;
use Illuminate\Support\Facades\Log;

/**
 * Product domain event'lerini real-time broadcast'e çeviren listener
 */
class ProductEventBroadcastListener extends BaseEventListener
{
    public function __construct(
        private RealTimeEventDispatcher $realTimeDispatcher
    ) {
        parent::__construct();
    }

    /**
     * Event'i handle et
     */
    protected function doHandle(DomainEvent $event): void
    {
        try {
            $broadcastEvent = $this->createBroadcastEvent($event);
            
            if ($broadcastEvent) {
                // Async dispatch (queue'ya ekle)
                $this->realTimeDispatcher->dispatchAsync($broadcastEvent);
                
                Log::debug('Product event converted to real-time broadcast', [
                    'domain_event' => $event->getEventName(),
                    'broadcast_event' => $broadcastEvent->broadcastAs(),
                    'product_id' => $this->getProductId($event),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to convert product event to real-time broadcast', [
                'domain_event' => $event->getEventName(),
                'product_id' => $this->getProductId($event),
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Domain event'ten broadcast event oluştur
     */
    private function createBroadcastEvent(DomainEvent $event): ?object
    {
        switch (get_class($event)) {
            case StockUpdated::class:
                return $this->createStockUpdatedBroadcast($event);
                
            case PriceChanged::class:
                return $this->createPriceChangedBroadcast($event);
                
            default:
                return null;
        }
    }

    /**
     * Stock updated broadcast event oluştur
     */
    private function createStockUpdatedBroadcast(StockUpdated $event): ProductStockUpdatedBroadcast
    {
        $eventData = $event->toArray();
        
        return new ProductStockUpdatedBroadcast(
            productId: $eventData['product_id'],
            newStock: $eventData['new_stock'],
            oldStock: $eventData['old_stock'],
            variantId: $eventData['variant_id'] ?? null
        );
    }

    /**
     * Price changed broadcast event oluştur
     */
    private function createPriceChangedBroadcast(PriceChanged $event): ProductPriceChangedBroadcast
    {
        $eventData = $event->toArray();
        
        return new ProductPriceChangedBroadcast(
            productId: $eventData['product_id'],
            newPrice: $eventData['new_price'],
            oldPrice: $eventData['old_price'],
            currency: $eventData['currency'] ?? 'TRY',
            variantId: $eventData['variant_id'] ?? null,
            priceType: $eventData['price_type'] ?? 'regular'
        );
    }

    /**
     * Event'ten product ID'sini al
     */
    private function getProductId(DomainEvent $event): ?int
    {
        $eventData = $event->toArray();
        return $eventData['product_id'] ?? null;
    }

    /**
     * Bu listener'ın handle edebileceği event'leri kontrol et
     */
    public function canHandle(DomainEvent $event): bool
    {
        $supportedEvents = [
            StockUpdated::class,
            PriceChanged::class,
        ];

        return in_array(get_class($event), $supportedEvents);
    }

    /**
     * Listener adını döndür
     */
    public function getName(): string
    {
        return 'ProductEventBroadcastListener';
    }

    /**
     * Listener'ın aktif olup olmadığını kontrol et
     */
    public function isEnabled(): bool
    {
        return config('broadcasting.features.enabled', true) &&
               (config('broadcasting.features.broadcast_events.product_stock_updated', true) ||
                config('broadcasting.features.broadcast_events.product_price_changed', true));
    }
}
