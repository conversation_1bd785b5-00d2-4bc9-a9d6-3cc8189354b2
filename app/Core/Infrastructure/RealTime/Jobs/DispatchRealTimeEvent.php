<?php

namespace App\Core\Infrastructure\RealTime\Jobs;

use App\Core\Infrastructure\RealTime\Contracts\BroadcastEventInterface;
use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * Real-time event dispatch job'ı
 */
class DispatchRealTimeEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $maxExceptions = 2;
    public int $timeout = 30;
    public int $backoff = 5;

    public function __construct(
        private BroadcastEventInterface $event
    ) {
        // Priority'ye göre queue ayarla
        $this->onQueue($this->event->broadcastQueue());
        
        // Yüksek priority event'ler için daha az bekleme
        if ($this->event->getPriority() >= 8) {
            $this->tries = 5;
            $this->timeout = 60;
            $this->backoff = 2;
        }
    }

    /**
     * Job'ı execute et
     */
    public function handle(RealTimeEventDispatcher $dispatcher): void
    {
        try {
            Log::debug('Processing real-time event job', [
                'event' => $this->event->broadcastAs(),
                'priority' => $this->event->getPriority(),
                'attempt' => $this->attempts(),
            ]);

            $success = $dispatcher->dispatch($this->event);

            if (!$success) {
                throw new \Exception('Real-time event dispatch failed');
            }

            Log::debug('Real-time event job completed successfully', [
                'event' => $this->event->broadcastAs(),
                'attempt' => $this->attempts(),
            ]);

        } catch (\Exception $e) {
            Log::error('Real-time event job failed', [
                'event' => $this->event->broadcastAs(),
                'attempt' => $this->attempts(),
                'error' => $e->getMessage(),
            ]);

            // Yeniden deneme
            if ($this->attempts() < $this->tries) {
                $this->release($this->backoff * $this->attempts());
                return;
            }

            // Son deneme de başarısız oldu
            $this->fail($e);
        }
    }

    /**
     * Job başarısız olduğunda çalışır
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Real-time event job permanently failed', [
            'event' => $this->event->broadcastAs(),
            'priority' => $this->event->getPriority(),
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
        ]);

        // Kritik event'ler için alert gönder
        if ($this->event->getPriority() >= 8) {
            $this->sendCriticalEventFailureAlert($exception);
        }
    }

    /**
     * Job retry edilmeden önce çalışır
     */
    public function retryUntil(): \DateTime
    {
        // Yüksek priority event'ler için daha uzun retry süresi
        $minutes = $this->event->getPriority() >= 8 ? 10 : 5;
        
        return now()->addMinutes($minutes);
    }

    /**
     * Kritik event başarısızlığı için alert gönder
     */
    private function sendCriticalEventFailureAlert(\Throwable $exception): void
    {
        try {
            // Admin'lere bildirim gönder
            \Illuminate\Support\Facades\Notification::route('mail', config('app.admin_email'))
                ->notify(new \App\Core\Infrastructure\RealTime\Notifications\CriticalEventFailureNotification(
                    $this->event,
                    $exception
                ));

        } catch (\Exception $e) {
            Log::error('Failed to send critical event failure alert', [
                'original_event' => $this->event->broadcastAs(),
                'alert_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Job unique ID'sini döndür (duplicate prevention için)
     */
    public function uniqueId(): string
    {
        $data = $this->event->broadcastWith();
        $dataHash = md5(serialize($data));
        
        return "realtime_event:{$this->event->broadcastAs()}:{$dataHash}";
    }

    /**
     * Job'ın unique olma süresini döndür
     */
    public function uniqueFor(): int
    {
        // Cache'lenen event'ler için daha uzun unique süresi
        if ($this->event->shouldCache()) {
            return $this->event->getCacheTtl();
        }

        return 60; // 1 dakika
    }
}
