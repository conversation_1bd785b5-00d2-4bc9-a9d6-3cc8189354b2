<?php

namespace App\Core\Infrastructure\Repository;

use App\Core\Domain\Contracts\EntityInterface;
use App\Core\Domain\Contracts\RepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Base Eloquent Repository sınıfı
 * Tüm Eloquent repository'leri bu sınıftan türetilmelidir
 */
abstract class EloquentRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var Model
     */
    protected Model $model;

    /**
     * Constructor
     *
     * @param Model $model
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /**
     * Tüm kayıtları getir
     *
     * @param array $columns
     * @return Collection
     */
    public function all(array $columns = ['*']): Collection
    {
        return $this->model->all($columns);
    }

    /**
     * ID'ye göre kayıt getir
     *
     * @param mixed $id
     * @param array $columns
     * @return EntityInterface|null
     */
    public function findById($id, array $columns = ['*']): ?EntityInterface
    {
        return $this->model->find($id, $columns);
    }

    /**
     * ID'ye göre kayıt getir veya exception fırlat
     *
     * @param mixed $id
     * @param array $columns
     * @return EntityInterface
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function findByIdOrFail($id, array $columns = ['*']): EntityInterface
    {
        return $this->model->findOrFail($id, $columns);
    }

    /**
     * Koşula göre kayıt getir
     *
     * @param array $criteria
     * @param array $columns
     * @return EntityInterface|null
     */
    public function findBy(array $criteria, array $columns = ['*']): ?EntityInterface
    {
        $query = $this->model->newQuery();
        
        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }
        
        return $query->first($columns);
    }

    /**
     * Koşula göre kayıtları getir
     *
     * @param array $criteria
     * @param array $columns
     * @return Collection
     */
    public function findAllBy(array $criteria, array $columns = ['*']): Collection
    {
        $query = $this->model->newQuery();
        
        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }
        
        return $query->get($columns);
    }

    /**
     * Kayıt oluştur
     *
     * @param array $data
     * @return EntityInterface
     */
    public function create(array $data): EntityInterface
    {
        return $this->model->create($data);
    }

    /**
     * Kayıt güncelle
     *
     * @param mixed $id
     * @param array $data
     * @return EntityInterface
     */
    public function update($id, array $data): EntityInterface
    {
        $entity = $this->findByIdOrFail($id);
        $entity->update($data);
        return $entity->fresh();
    }

    /**
     * Kayıt sil
     *
     * @param mixed $id
     * @return bool
     */
    public function delete($id): bool
    {
        $entity = $this->findByIdOrFail($id);
        return $entity->delete();
    }

    /**
     * Sayfalı kayıtları getir
     *
     * @param int $perPage
     * @param array $columns
     * @return LengthAwarePaginator
     */
    public function paginate(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->model->paginate($perPage, $columns);
    }

    /**
     * Kayıt sayısını getir
     *
     * @return int
     */
    public function count(): int
    {
        return $this->model->count();
    }

    /**
     * Kayıt var mı kontrol et
     *
     * @param array $criteria
     * @return bool
     */
    public function exists(array $criteria): bool
    {
        $query = $this->model->newQuery();
        
        foreach ($criteria as $field => $value) {
            $query->where($field, $value);
        }
        
        return $query->exists();
    }
}
