<?php

namespace App\Core\Infrastructure\Events\Listeners;

use App\Core\Infrastructure\Events\Contracts\CacheInvalidationListenerInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheKeyGeneratorInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use App\Domain\Shared\Events\DomainEvent;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * CacheInvalidationEventListener
 * Ana cache invalidation listener
 */
class CacheInvalidationEventListener extends BaseEventListener implements CacheInvalidationListenerInterface
{
    protected int $priority = 10; // Yüksek priority - cache invalidation öncelikli

    private array $invalidationStrategies = [
        'immediate' => 'immediate',
        'async' => 'async',
        'batch' => 'batch',
        'lazy' => 'lazy'
    ];

    private array $entityCacheConfig = [];

    public function __construct(
        private CacheKeyGeneratorInterface $keyGenerator,
        private CacheTagManagerInterface $tagManager
    ) {
        $this->loadEntityCacheConfig();
    }

    /**
     * Event'i handle et
     */
    protected function doHandle(DomainEvent $event): void
    {
        $strategy = $this->getInvalidationStrategy($event);

        switch ($strategy) {
            case 'immediate':
                $this->invalidateImmediate($event);
                break;
            case 'async':
                $this->invalidateAsync($event);
                break;
            case 'batch':
                $this->invalidateBatch($event);
                break;
            case 'lazy':
                $this->invalidateLazy($event);
                break;
            default:
                $this->invalidateImmediate($event);
        }

        // Cache warming gerekiyorsa
        if ($this->shouldWarmCache($event)) {
            $this->warmCache($event);
        }
    }

    /**
     * Event'e göre invalidate edilecek cache tag'lerini döndür
     */
    public function getCacheTagsToInvalidate(DomainEvent $event): array
    {
        $entityType = $this->extractEntityType($event);
        $entityId = $this->extractEntityId($event);

        $tags = [];

        // Entity-specific tags
        if ($entityId) {
            $tags = array_merge($tags, $this->tagManager->getEntityTags($entityType, $entityId));
        }

        // Global entity tags
        $tags = array_merge($tags, $this->tagManager->getGlobalTags($entityType));

        // Event-specific tags
        $tags = array_merge($tags, $this->getEventSpecificTags($event));

        // Related entity tags
        if ($this->shouldInvalidateRelatedEntities($event)) {
            $tags = array_merge($tags, $this->getRelatedEntityTags($event));
        }

        return array_unique($tags);
    }

    /**
     * Event'e göre invalidate edilecek cache key'lerini döndür
     */
    public function getCacheKeysToInvalidate(DomainEvent $event): array
    {
        $entityType = $this->extractEntityType($event);
        $entityId = $this->extractEntityId($event);

        $keys = [];

        if ($entityId) {
            // Entity cache key
            $keys[] = $this->keyGenerator->generateEntityKey($entityType, $entityId);

            // Entity-specific query keys
            $keys = array_merge($keys, $this->getEntityQueryKeys($event));
        }

        // List cache keys
        $keys = array_merge($keys, $this->getListCacheKeys($event));

        // Stats cache keys
        $keys = array_merge($keys, $this->getStatsCacheKeys($event));

        return array_unique($keys);
    }

    /**
     * Bulk invalidation gerekip gerekmediğini kontrol et
     */
    public function requiresBulkInvalidation(DomainEvent $event): bool
    {
        $eventName = $event->getEventName();
        $entityType = $this->extractEntityType($event);

        // Bulk invalidation gerektiren event'ler
        $bulkEvents = [
            'category.updated',
            'product.price_changed',
            'inventory.bulk_updated',
            'system.cache_clear'
        ];

        return in_array($eventName, $bulkEvents) ||
               $this->getEntityCacheConfig($entityType, 'bulk_invalidation', false);
    }

    /**
     * Related entity'lerin cache'lerini de invalidate et
     */
    public function shouldInvalidateRelatedEntities(DomainEvent $event): bool
    {
        $entityType = $this->extractEntityType($event);
        return $this->getEntityCacheConfig($entityType, 'invalidate_related', true);
    }

    /**
     * Cache invalidation'ın asenkron olup olmayacağını belirle
     */
    public function shouldInvalidateAsync(DomainEvent $event): bool
    {
        return $this->requiresBulkInvalidation($event) ||
               $this->getEntityCacheConfig($this->extractEntityType($event), 'async_invalidation', false);
    }

    /**
     * Cache invalidation stratejisini döndür
     */
    public function getInvalidationStrategy(DomainEvent $event): string
    {
        if ($this->shouldInvalidateAsync($event)) {
            return 'async';
        }

        if ($this->requiresBulkInvalidation($event)) {
            return 'batch';
        }

        $entityType = $this->extractEntityType($event);
        return $this->getEntityCacheConfig($entityType, 'invalidation_strategy', 'immediate');
    }

    /**
     * Cache invalidation'dan sonra warming gerekip gerekmediğini kontrol et
     */
    public function shouldWarmCache(DomainEvent $event): bool
    {
        $entityType = $this->extractEntityType($event);
        return $this->getEntityCacheConfig($entityType, 'warm_after_invalidation', false);
    }

    /**
     * Immediate cache invalidation
     */
    private function invalidateImmediate(DomainEvent $event): void
    {
        $tags = $this->getCacheTagsToInvalidate($event);
        $keys = $this->getCacheKeysToInvalidate($event);

        // Tag-based invalidation
        if (!empty($tags)) {
            $this->tagManager->clearByTags($tags);
        }

        // Key-based invalidation
        foreach ($keys as $key) {
            Cache::forget($key);
        }

        Log::info('Cache invalidated immediately', [
            'event' => $event->getEventName(),
            'tags_count' => count($tags),
            'keys_count' => count($keys),
            'aggregate_id' => $event->getAggregateId()
        ]);
    }

    /**
     * Async cache invalidation
     */
    private function invalidateAsync(DomainEvent $event): void
    {
        // Queue job olarak cache invalidation'ı çalıştır
        // Bu implementation'da basit bir şekilde immediate yapıyoruz
        // Gerçek async implementation için job queue kullanılabilir
        $this->invalidateImmediate($event);
    }

    /**
     * Batch cache invalidation
     */
    private function invalidateBatch(DomainEvent $event): void
    {
        // Batch invalidation için event'leri topla ve belirli aralıklarla işle
        // Bu implementation'da immediate yapıyoruz
        $this->invalidateImmediate($event);
    }

    /**
     * Lazy cache invalidation
     */
    private function invalidateLazy(DomainEvent $event): void
    {
        // Cache'i hemen invalidate etme, sadece mark et
        // Cache read sırasında kontrol edilecek
        $this->invalidateImmediate($event);
    }

    /**
     * Cache warming
     */
    private function warmCache(DomainEvent $event): void
    {
        // Cache warming logic'i
        // Bu basit implementation'da warming yapmıyoruz
        Log::info('Cache warming requested', [
            'event' => $event->getEventName(),
            'aggregate_id' => $event->getAggregateId()
        ]);
    }

    /**
     * Event-specific cache tag'lerini al
     */
    private function getEventSpecificTags(DomainEvent $event): array
    {
        $eventName = $event->getEventName();
        $tags = [];

        // Event tipine göre özel tag'ler
        switch ($eventName) {
            case 'product.created':
            case 'product.updated':
            case 'product.deleted':
                $tags[] = 'products_list';
                $tags[] = 'featured_products';
                $tags[] = 'search_results';
                break;

            case 'category.created':
            case 'category.updated':
            case 'category.deleted':
                $tags[] = 'categories_list';
                $tags[] = 'navigation';
                $tags[] = 'category_tree';
                break;

            case 'order.created':
            case 'order.updated':
                $tags[] = 'order_stats';
                $tags[] = 'sales_reports';
                break;

            case 'payment.processed':
            case 'payment.failed':
                $tags[] = 'payment_stats';
                $tags[] = 'financial_reports';
                break;
        }

        return $tags;
    }

    /**
     * Related entity tag'lerini al
     */
    private function getRelatedEntityTags(DomainEvent $event): array
    {
        $entityType = $this->extractEntityType($event);
        $tags = [];

        switch ($entityType) {
            case 'product':
                // Product değiştiğinde category cache'i de invalidate et
                $tags = array_merge($tags, $this->tagManager->getGlobalTags('category'));
                $tags[] = 'inventory';
                break;

            case 'category':
                // Category değiştiğinde product cache'i de invalidate et
                $tags = array_merge($tags, $this->tagManager->getGlobalTags('product'));
                break;

            case 'order':
                // Order değiştiğinde inventory ve payment cache'i invalidate et
                $tags[] = 'inventory';
                $tags[] = 'payments';
                break;
        }

        return $tags;
    }

    /**
     * Entity query cache key'lerini al
     */
    private function getEntityQueryKeys(DomainEvent $event): array
    {
        $entityType = $this->extractEntityType($event);
        $entityId = $this->extractEntityId($event);
        $keys = [];

        if (!$entityId) {
            return $keys;
        }

        // Common query patterns
        $queryMethods = ['findById', 'findBySlug', 'findWithRelations'];

        foreach ($queryMethods as $method) {
            $keys[] = $this->keyGenerator->generateQueryKey($entityType, $method, ['id' => $entityId]);
        }

        return $keys;
    }

    /**
     * List cache key'lerini al
     */
    private function getListCacheKeys(DomainEvent $event): array
    {
        $entityType = $this->extractEntityType($event);
        $keys = [];

        // Common list patterns
        $listPatterns = [
            ['criteria' => [], 'limit' => 10],
            ['criteria' => [], 'limit' => 20],
            ['criteria' => ['status' => 'active'], 'limit' => 10],
            ['criteria' => ['featured' => true], 'limit' => 5]
        ];

        foreach ($listPatterns as $pattern) {
            $keys[] = $this->keyGenerator->generateListKey(
                $entityType,
                $pattern['criteria'],
                $pattern['limit']
            );
        }

        return $keys;
    }

    /**
     * Stats cache key'lerini al
     */
    private function getStatsCacheKeys(DomainEvent $event): array
    {
        $entityType = $this->extractEntityType($event);
        $keys = [];

        // Common stats patterns
        $statsTypes = ['count', 'total', 'average', 'daily', 'monthly'];

        foreach ($statsTypes as $statType) {
            $keys[] = $this->keyGenerator->generateStatsKey($entityType, $statType);
        }

        return $keys;
    }

    /**
     * Entity cache konfigürasyonunu yükle
     */
    private function loadEntityCacheConfig(): void
    {
        $this->entityCacheConfig = config('cache_infrastructure.entities', []);
    }

    /**
     * Entity cache konfigürasyonunu al
     */
    private function getEntityCacheConfig(string $entityType, string $key, $default = null)
    {
        return $this->entityCacheConfig[$entityType][$key] ?? $default;
    }
}
