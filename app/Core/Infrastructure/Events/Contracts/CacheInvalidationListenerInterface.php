<?php

namespace App\Core\Infrastructure\Events\Contracts;

use App\Domain\Shared\Events\DomainEvent;

/**
 * CacheInvalidationListenerInterface
 * Cache invalidation için özel interface
 */
interface CacheInvalidationListenerInterface extends EventListenerInterface
{
    /**
     * Event'e göre invalidate edilecek cache tag'lerini döndür
     */
    public function getCacheTagsToInvalidate(DomainEvent $event): array;

    /**
     * Event'e göre invalidate edilecek cache key'lerini döndür
     */
    public function getCacheKeysToInvalidate(DomainEvent $event): array;

    /**
     * Bulk invalidation gerekip gerekmediğini kontrol et
     */
    public function requiresBulkInvalidation(DomainEvent $event): bool;

    /**
     * Related entity'lerin cache'lerini de invalidate et
     */
    public function shouldInvalidateRelatedEntities(DomainEvent $event): bool;

    /**
     * Cache invalidation'ın asenkron olup olmaya<PERSON>ğını belirle
     */
    public function shouldInvalidateAsync(DomainEvent $event): bool;

    /**
     * Cache invalidation stratejisini döndür
     */
    public function getInvalidationStrategy(DomainEvent $event): string;

    /**
     * Cache invalidation'dan sonra warming gerekip gerekmediğini kontrol et
     */
    public function shouldWarmCache(DomainEvent $event): bool;
}
