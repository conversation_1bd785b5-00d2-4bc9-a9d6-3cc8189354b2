<?php

namespace App\Core\Infrastructure\Events\Contracts;

use App\Domain\Shared\Events\DomainEvent;

/**
 * EventListenerInterface
 * Standardize event listener interface
 */
interface EventListenerInterface
{
    /**
     * Event'i handle et
     */
    public function handle(DomainEvent $event): void;

    /**
     * Bu listener'ın handle edebileceği event tiplerini döndür
     */
    public function getSupportedEvents(): array;

    /**
     * Listener'ın prioritysini döndür (düşük sayı = yüksek priority)
     */
    public function getPriority(): int;

    /**
     * Listener'ın aktif olup olmadığını kontrol et
     */
    public function isEnabled(): bool;

    /**
     * Listener'ı etkinleştir/devre dışı bırak
     */
    public function setEnabled(bool $enabled): void;

    /**
     * Event'i handle edebilir mi kontrol et
     */
    public function canHandle(DomainEvent $event): bool;

    /**
     * Listener'ın adını döndür
     */
    public function getName(): string;
}
