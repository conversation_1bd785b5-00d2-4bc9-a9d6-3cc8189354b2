<?php

namespace App\Core\Http\Controllers;

use App\Core\Infrastructure\Api\Controllers\BaseApiController;
use App\Core\Infrastructure\Api\Contracts\ApiResponseInterface;

/**
 * ApiController
 * Enhanced API controller using new infrastructure
 *
 * @deprecated Use BaseApiController directly or extend it
 */
class ApiController extends BaseApiController
{
    public function __construct(ApiResponseInterface $apiResponse)
    {
        parent::__construct($apiResponse);
    }

    /**
     * Başarılı API yanıtı oluştur
     *
     * @deprecated Use successResponse() method from BaseApiController
     */
    protected function successResponse($data = null, string $message = '', int $statusCode = 200, array $meta = []): \Illuminate\Http\JsonResponse
    {
        return parent::successResponse($data, $message, $statusCode, $meta);
    }

    /**
     * Hata API yanıtı oluştur
     *
     * @deprecated Use errorResponse() method from BaseApiController
     */
    protected function errorResponse(string $message, int $statusCode = 400, array $errors = [], array $meta = []): \Illuminate\Http\JsonResponse
    {
        return parent::errorResponse($message, $statusCode, $errors, $meta);
    }
}
