<?php

namespace App\Core\Providers;

use Illuminate\Support\ServiceProvider;

/**
 * Core Service Provider
 * Core katmanının servis sa<PERSON>ısı
 */
class CoreServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Core services'leri register et
        $this->registerCoreServices();
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Core services'leri boot et
        $this->bootCoreServices();
    }

    /**
     * Core services'leri register et
     *
     * @return void
     */
    protected function registerCoreServices(): void
    {
        // Domain event dispatcher
        $this->app->singleton('domain.event.dispatcher', function ($app) {
            return $app['events'];
        });

        // Repository factory
        $this->app->singleton('repository.factory', function ($app) {
            return new \App\Core\Infrastructure\Repository\RepositoryFactory($app);
        });
    }

    /**
     * Core services'leri boot et
     *
     * @return void
     */
    protected function bootCoreServices(): void
    {
        // Domain events'i register et
        $this->registerDomainEventListeners();
    }

    /**
     * Domain event listener'larını register et
     *
     * @return void
     */
    protected function registerDomainEventListeners(): void
    {
        // Domain event listener'ları burada register edilecek
        // Örnek:
        // Event::listen(ProductCreated::class, ProductCreatedListener::class);
    }
}
