<?php

namespace App\Core\Application;

/**
 * Base Use Case sınıfı
 * Tüm use case'ler bu sınıftan türetilmelidir
 */
abstract class UseCase
{
    /**
     * Use case'i çalıştır
     *
     * @param mixed $input
     * @return mixed
     */
    abstract public function execute($input = null);

    /**
     * Input'u validate et
     *
     * @param mixed $input
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateInput($input): void
    {
        // Override edilebilir validation logic
    }

    /**
     * Use case'i çalıştır ve validate et
     *
     * @param mixed $input
     * @return mixed
     */
    public function handle($input = null)
    {
        $this->validateInput($input);
        return $this->execute($input);
    }
}
