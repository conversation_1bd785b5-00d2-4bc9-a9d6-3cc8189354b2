<?php

namespace App\Observers;

use App\Models\Product;
use App\Services\Cache\ProductCacheService;
use App\Services\Cache\CategoryCacheService;

class ProductObserver
{
    /**
     * @var ProductCacheService
     */
    protected $productCacheService;

    /**
     * @var CategoryCacheService
     */
    protected $categoryCacheService;

    /**
     * ProductObserver constructor.
     *
     * @param ProductCacheService $productCacheService
     * @param CategoryCacheService $categoryCacheService
     */
    public function __construct(
        ProductCacheService $productCacheService,
        CategoryCacheService $categoryCacheService
    ) {
        $this->productCacheService = $productCacheService;
        $this->categoryCacheService = $categoryCacheService;
    }

    /**
     * Ürün oluşturulduğunda
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function created(Product $product)
    {
        // Tüm ürün listesi cache'ini temizle
        $this->productCacheService->invalidateAllProducts();

        // Ürünün kategorisinin cache'ini temizle
        if ($product->category_id) {
            $this->categoryCacheService->invalidateCategory($product->category_id);
        }
    }

    /**
     * Ürün güncellendiğinde
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function updated(Product $product)
    {
        // Ürün cache'ini temizle
        $this->productCacheService->invalidateProduct($product->id);

        // Kategori değiştiyse eski ve yeni kategorilerin cache'ini temizle
        if ($product->isDirty('category_id')) {
            $oldCategoryId = $product->getOriginal('category_id');
            if ($oldCategoryId) {
                $this->categoryCacheService->invalidateCategory($oldCategoryId);
            }
            if ($product->category_id) {
                $this->categoryCacheService->invalidateCategory($product->category_id);
            }
        }
    }

    /**
     * Ürün silindiğinde
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function deleted(Product $product)
    {
        // Ürün cache'ini temizle
        $this->productCacheService->invalidateProduct($product->id);

        // Ürünün kategorisinin cache'ini temizle
        if ($product->category_id) {
            $this->categoryCacheService->invalidateCategory($product->category_id);
        }

        // Tüm ürün listesi cache'ini temizle
        $this->productCacheService->invalidateAllProducts();
    }

    /**
     * Ürün geri yüklendiğinde (soft delete)
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function restored(Product $product)
    {
        // Ürün cache'ini temizle
        $this->productCacheService->invalidateProduct($product->id);

        // Ürünün kategorisinin cache'ini temizle
        if ($product->category_id) {
            $this->categoryCacheService->invalidateCategory($product->category_id);
        }

        // Tüm ürün listesi cache'ini temizle
        $this->productCacheService->invalidateAllProducts();
    }

    /**
     * Ürün kalıcı olarak silindiğinde (force delete)
     *
     * @param  \App\Models\Product  $product
     * @return void
     */
    public function forceDeleted(Product $product)
    {
        // Ürün cache'ini temizle
        $this->productCacheService->invalidateProduct($product->id);

        // Ürünün kategorisinin cache'ini temizle
        if ($product->category_id) {
            $this->categoryCacheService->invalidateCategory($product->category_id);
        }

        // Tüm ürün listesi cache'ini temizle
        $this->productCacheService->invalidateAllProducts();
    }
}
