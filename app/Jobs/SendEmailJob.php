<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Alıcı e-posta adresi
     *
     * @var string
     */
    protected $email;

    /**
     * E-posta konusu
     *
     * @var string
     */
    protected $subject;

    /**
     * E-posta içeriği
     *
     * @var string
     */
    protected $content;

    /**
     * Create a new job instance.
     */
    public function __construct(string $email, string $subject, string $content)
    {
        $this->email = $email;
        $this->subject = $subject;
        $this->content = $content;
        $this->onQueue('emails');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // E-posta gönderimi simülasyonu
        Log::info("E-posta gönderiliyor: {$this->email}, <PERSON><PERSON>: {$this->subject}");

        // İşlem simülasyonu için 1 saniye bekle
        sleep(1);

        // İşlem tamamlandı
        Log::info("E-posta başarıyla gönderildi: {$this->email}");
    }
}
