<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Attribute;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ProductFilterService
{
    /**
     * Ürünleri filtrele ve sırala
     *
     * @param Request $request
     * @param int|null $categoryId
     * @return Builder
     */
    public function filter(Request $request, ?int $categoryId = null): Builder
    {
        // Debug için tüm request parametrelerini logla
        \Illuminate\Support\Facades\Log::info('Filter request parameters:', $request->all());

        // Özellikle brand parametresini kontrol et
        if ($request->has('filtreler')) {
            \Illuminate\Support\Facades\Log::info('Filtreler parametresi:', ['filtreler' => $request->filtreler]);

            if (strpos($request->filtreler, 'brand:') !== false) {
                \Illuminate\Support\Facades\Log::info('Brand filtresi bulundu!');
            }
        }

        // Temel sorgu
        $query = Product::query()->where('status', true);

        // Kategori filtresi
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        // Arama filtresi
        if ($request->has('q') && !empty($request->q)) {
            $searchTerm = $request->q;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('stock_code', 'like', "%{$searchTerm}%");
            });
        }

        // Fiyat aralığı filtresi
        if ($request->has('price_min') && is_numeric($request->price_min)) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->has('price_max') && is_numeric($request->price_max)) {
            $query->where('price', '<=', $request->price_max);
        }

        // Stok durumu filtresi
        if ($request->has('in_stock') && $request->in_stock) {
            $query->where('stock', '>', 0);
        }

        // Özellik filtreleri - Laravel'in dizi formatını işle
        $attributes = [];

        // Debug için tüm request parametrelerini logla
        \Illuminate\Support\Facades\Log::info('All request parameters:', $request->all());

        foreach ($request->all() as $key => $value) {
            // Daha esnek bir regex kullan - attributes[brand][][]=apple formatını da destekle
            if (preg_match('/^attributes\[(.*?)\](?:\[\])*(?:\[\])?$/', $key, $matches)) {
                $attributeCode = $matches[1];

                \Illuminate\Support\Facades\Log::info("Found attribute parameter: {$key}", [
                    'code' => $attributeCode,
                    'value' => $value
                ]);

                // Değer bir dizi değilse, diziye çevir
                $attributeValues = is_array($value) ? $value : [$value];

                // Boş olmayan değerleri filtrele
                $attributeValues = array_filter($attributeValues, function($val) {
                    return $val !== null && $val !== '';
                });

                if (!empty($attributeValues)) {
                    // Eğer bu özellik zaten eklenmiş ise, değerleri birleştir
                    if (isset($attributes[$attributeCode])) {
                        $attributes[$attributeCode] = array_merge($attributes[$attributeCode], $attributeValues);
                    } else {
                        $attributes[$attributeCode] = $attributeValues;
                    }
                }
            }
        }

        // Standart attributes parametresi de kontrol et
        if ($request->has('attributes') && is_array($request->attributes)) {
            foreach ($request->attributes as $code => $values) {
                if (!empty($values)) {
                    $attributes[$code] = is_array($values) ? $values : [$values];
                    // Debug için log ekle
                    \Illuminate\Support\Facades\Log::info("Found standard attribute: {$code}", ['values' => $values]);
                }
            }
        }

        // Ayrıca filtreler parametresinden gelen değerleri de kontrol et
        if ($request->has('filtreler')) {
            $filterString = $request->filtreler;
            $filterParts = explode(';', $filterString);

            foreach ($filterParts as $part) {
                $keyValue = explode(':', $part);
                if (count($keyValue) == 2) {
                    $key = $keyValue[0];
                    $values = explode(',', $keyValue[1]);

                    // Özellik mi varyant mı kontrol et
                    $attribute = \App\Models\Attribute::where('code', $key)->first();

                    if ($attribute) {
                        // Bu bir özellik
                        $attributes[$key] = $values;
                        // Debug için log ekle
                        \Illuminate\Support\Facades\Log::info("Found attribute from filtreler: {$key}", ['values' => $values]);
                    }
                }
            }
        }

        // Özellik filtreleri uygula
        if (!empty($attributes)) {
            \Illuminate\Support\Facades\Log::info('Applying attribute filters:', $attributes);

            foreach ($attributes as $code => $values) {
                if (!empty($values)) {
                    // Özellikle brand özelliği için log ekle
                    if ($code === 'brand') {
                        \Illuminate\Support\Facades\Log::info('Brand filtresi uygulanıyor:', ['values' => $values]);
                    }

                    // Özellik filtreleme sorgusunu oluştur
                    $query->whereHas('attributes', function ($q) use ($code, $values) {
                        $q->where('code', $code)
                          ->whereHas('values', function ($q) use ($values) {
                              // Değerleri array olarak cast et ve boş değerleri filtrele
                              $filteredValues = array_filter((array) $values, function($value) {
                                  return $value !== null && $value !== '';
                              });

                              if (!empty($filteredValues)) {
                                  $q->whereIn('value', $filteredValues);

                                  // Debug için log ekle
                                  \Illuminate\Support\Facades\Log::info("whereIn('value', filteredValues) uygulandı", ['filteredValues' => $filteredValues]);
                              }
                          });
                    });

                    // Debug için log ekle
                    \Illuminate\Support\Facades\Log::info("Filtering by attribute: {$code}", ['values' => $values]);
                }
            }
        }

        // Varyant filtreleri - Laravel'in dizi formatını işle
        $variants = [];
        foreach ($request->all() as $key => $value) {
            // Daha esnek bir regex kullan - variants[color][][]=red formatını da destekle
            if (preg_match('/^variants\[(.*?)\](?:\[\])*(?:\[\])?$/', $key, $matches)) {
                $variantCode = $matches[1];

                \Illuminate\Support\Facades\Log::info("Found variant parameter: {$key}", [
                    'code' => $variantCode,
                    'value' => $value
                ]);

                // Değer bir dizi değilse, diziye çevir
                $variantValues = is_array($value) ? $value : [$value];

                // Boş olmayan değerleri filtrele
                $variantValues = array_filter($variantValues, function($val) {
                    return $val !== null && $val !== '';
                });

                if (!empty($variantValues)) {
                    // Eğer bu varyant zaten eklenmiş ise, değerleri birleştir
                    if (isset($variants[$variantCode])) {
                        $variants[$variantCode] = array_merge($variants[$variantCode], $variantValues);
                    } else {
                        $variants[$variantCode] = $variantValues;
                    }
                }
            }
        }

        // Standart variants parametresi de kontrol et
        if ($request->has('variants') && is_array($request->variants)) {
            foreach ($request->variants as $code => $values) {
                if (!empty($values)) {
                    $variants[$code] = is_array($values) ? $values : [$values];
                    // Debug için log ekle
                    \Illuminate\Support\Facades\Log::info("Found standard variant: {$code}", ['values' => $values]);
                }
            }
        }

        // Ayrıca filtreler parametresinden gelen değerleri de kontrol et
        if ($request->has('filtreler')) {
            $filterString = $request->filtreler;
            $filterParts = explode(';', $filterString);

            foreach ($filterParts as $part) {
                $keyValue = explode(':', $part);
                if (count($keyValue) == 2) {
                    $key = $keyValue[0];
                    $values = explode(',', $keyValue[1]);

                    // Özellik mi varyant mı kontrol et
                    $attribute = \App\Models\Attribute::where('code', $key)->first();

                    if (!$attribute) {
                        // Bu bir varyant olabilir
                        $variants[$key] = $values;
                        // Debug için log ekle
                        \Illuminate\Support\Facades\Log::info("Found variant from filtreler: {$key}", ['values' => $values]);
                    }
                }
            }
        }

        // Varyant filtreleri uygula
        if (!empty($variants)) {
            \Illuminate\Support\Facades\Log::info('Applying variant filters:', $variants);

            foreach ($variants as $code => $values) {
                if (!empty($values)) {
                    // Varyant filtreleme sorgusunu oluştur
                    $query->whereHas('variants', function ($q) use ($code, $values) {
                        // JSON içinde arama yapmak için whereJsonContains kullan
                        $q->where(function($subQuery) use ($code, $values) {
                            // Değerleri array olarak cast et ve boş değerleri filtrele
                            $filteredValues = array_filter((array) $values, function($value) {
                                return $value !== null && $value !== '';
                            });

                            if (!empty($filteredValues)) {
                                foreach ($filteredValues as $value) {
                                    $subQuery->orWhereJsonContains('attribute_values->' . $code, $value);
                                }
                            }
                        });
                    });

                    // Debug için log ekle
                    \Illuminate\Support\Facades\Log::info("Filtering by variant: {$code}", ['values' => $values]);
                }
            }
        }

        // Sıralama
        $sortField = 'created_at';
        $sortDirection = 'desc';

        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'price_asc':
                    $sortField = 'price';
                    $sortDirection = 'asc';
                    break;
                case 'price_desc':
                    $sortField = 'price';
                    $sortDirection = 'desc';
                    break;
                case 'name_asc':
                    $sortField = 'name';
                    $sortDirection = 'asc';
                    break;
                case 'name_desc':
                    $sortField = 'name';
                    $sortDirection = 'desc';
                    break;
                case 'newest':
                    $sortField = 'created_at';
                    $sortDirection = 'desc';
                    break;
                case 'oldest':
                    $sortField = 'created_at';
                    $sortDirection = 'asc';
                    break;
                case 'popularity':
                    // Popülerlik sıralaması için özel bir mantık eklenebilir
                    // Örneğin, görüntülenme sayısı veya satış sayısı
                    $sortField = 'view_count';
                    $sortDirection = 'desc';
                    break;
                case 'discount':
                    // İndirim oranına göre sıralama - geçici olarak fiyata göre sıralama yapıyoruz
                    $sortField = 'price';
                    $sortDirection = 'asc';
                    break;
                default:
                    // Varsayılan sıralama
                    break;
            }
        }

        $query->orderBy($sortField, $sortDirection);

        return $query;
    }

    /**
     * Kategori için mevcut filtreleri al
     *
     * @param int|null $categoryId
     * @return array
     */
    public function getAvailableFilters(?int $categoryId = null): array
    {
        // Kategori ID'si yoksa, tüm ürünler için filtreleri getir
        if (!$categoryId) {
            return $this->getAllProductFilters();
        }

        $cacheKey = "category_filters_{$categoryId}";

        // Önbelleği temizle (geçici olarak, sorun çözüldükten sonra kaldırılabilir)
        Cache::forget($cacheKey);

        return Cache::remember($cacheKey, now()->addHours(24), function () use ($categoryId) {
            $filters = [];

            // Fiyat aralığı filtresi için min ve max değerleri
            $priceRange = Product::where('category_id', $categoryId)
                ->where('status', true)
                ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                ->first();

            $filters['price_range'] = [
                'min' => round($priceRange->min_price ?? 0),
                'max' => round($priceRange->max_price ?? 1000),
            ];

            // Kategorideki ürünleri al
            $products = Product::where('category_id', $categoryId)
                ->where('status', true)
                ->with(['attributes', 'variants'])
                ->get();

            // Ürünlerin sahip olduğu özellikleri topla
            $attributeValues = [];
            $variantAttributes = [];

            foreach ($products as $product) {
                // Ürün özelliklerini topla
                foreach ($product->attributes as $attribute) {
                    if ($attribute->is_filterable) {
                        $code = $attribute->code;

                        // Özellik değerlerini al
                        $attributeObj = Attribute::where('code', $code)->first();
                        if (!$attributeObj) continue;

                        $attributeValues[$code] = [
                            'name' => $attribute->name,
                            'type' => $attribute->type,
                            'values' => []
                        ];

                        // Özellik değerlerini ekle
                        foreach ($attributeObj->values as $value) {
                            if (!in_array($value->value, $attributeValues[$code]['values'])) {
                                $attributeValues[$code]['values'][] = $value->value;
                            }
                        }
                    }
                }

                // Varyant özelliklerini topla
                foreach ($product->variants as $variant) {
                    if ($variant->attribute_values) {
                        foreach ($variant->attribute_values as $code => $value) {
                            // Özellik mi kontrol et
                            $attribute = \App\Models\Attribute::where('code', $code)->first();

                            if ($attribute && $attribute->is_filterable) {
                                // Bu bir özellik
                                if (!isset($attributeValues[$code])) {
                                    $attributeValues[$code] = [
                                        'name' => $attribute->name,
                                        'type' => $attribute->type,
                                        'values' => []
                                    ];
                                }

                                if (!in_array($value, $attributeValues[$code]['values'])) {
                                    $attributeValues[$code]['values'][] = $value;
                                }
                            } else {
                                // Bu bir varyant
                                if (!isset($variantAttributes[$code])) {
                                    // Özellik adını bul
                                    $attributeName = $code;
                                    if ($attribute) {
                                        $attributeName = $attribute->name;
                                    }

                                    $variantAttributes[$code] = [
                                        'name' => $attributeName,
                                        'values' => []
                                    ];
                                }

                                if (!in_array($value, $variantAttributes[$code]['values'])) {
                                    $variantAttributes[$code]['values'][] = $value;
                                }
                            }
                        }
                    }
                }
            }

            // Özellikleri ve varyantları filtrelere ekle
            $filters['attributes'] = $attributeValues;
            $filters['variants'] = $variantAttributes;

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('Available filters for category ' . $categoryId . ':', $filters);

            return $filters;
        });
    }

    /**
     * Tüm ürünler için mevcut filtreleri al
     *
     * @return array
     */
    public function getAllProductFilters(): array
    {
        // Önbelleği temizle (geçici olarak, sorun çözüldükten sonra kaldırılabilir)
        Cache::forget("all_product_filters");

        $cacheKey = "all_product_filters";

        return Cache::remember($cacheKey, now()->addHours(24), function () {
            $filters = [];

            // Fiyat aralığı filtresi için min ve max değerleri
            $priceRange = Product::where('status', true)
                ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                ->first();

            $filters['price_range'] = [
                'min' => round($priceRange->min_price ?? 0),
                'max' => round($priceRange->max_price ?? 1000),
            ];

            // Ürünleri al (performans için sınırlı sayıda)
            $products = Product::where('status', true)
                ->with(['attributes', 'variants'])
                ->limit(100) // Performans için sınırla
                ->get();

            // Ürünlerin sahip olduğu özellikleri topla
            $attributeValues = [];
            $variantAttributes = [];

            foreach ($products as $product) {
                // Ürün özelliklerini topla
                foreach ($product->attributes as $attribute) {
                    if ($attribute->is_filterable) {
                        $code = $attribute->code;

                        // Özellik değerlerini al
                        $attributeObj = Attribute::where('code', $code)->first();
                        if (!$attributeObj) continue;

                        $attributeValues[$code] = [
                            'name' => $attribute->name,
                            'type' => $attribute->type,
                            'values' => []
                        ];

                        // Özellik değerlerini ekle
                        foreach ($attributeObj->values as $value) {
                            if (!in_array($value->value, $attributeValues[$code]['values'])) {
                                $attributeValues[$code]['values'][] = $value->value;
                            }
                        }
                    }
                }

                // Varyant özelliklerini topla
                foreach ($product->variants as $variant) {
                    if ($variant->attribute_values) {
                        foreach ($variant->attribute_values as $code => $value) {
                            // Özellik mi kontrol et
                            $attribute = \App\Models\Attribute::where('code', $code)->first();

                            if ($attribute && $attribute->is_filterable) {
                                // Bu bir özellik
                                if (!isset($attributeValues[$code])) {
                                    $attributeValues[$code] = [
                                        'name' => $attribute->name,
                                        'type' => $attribute->type,
                                        'values' => []
                                    ];
                                }

                                if (!in_array($value, $attributeValues[$code]['values'])) {
                                    $attributeValues[$code]['values'][] = $value;
                                }
                            } else {
                                // Bu bir varyant
                                if (!isset($variantAttributes[$code])) {
                                    // Özellik adını bul
                                    $attributeName = $code;
                                    if ($attribute) {
                                        $attributeName = $attribute->name;
                                    }

                                    $variantAttributes[$code] = [
                                        'name' => $attributeName,
                                        'values' => []
                                    ];
                                }

                                if (!in_array($value, $variantAttributes[$code]['values'])) {
                                    $variantAttributes[$code]['values'][] = $value;
                                }
                            }
                        }
                    }
                }
            }

            // Özellikleri ve varyantları filtrelere ekle
            $filters['attributes'] = $attributeValues;
            $filters['variants'] = $variantAttributes;

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('Available filters for all products:', $filters);

            return $filters;
        });
    }

    /**
     * Aktif filtreleri al
     *
     * @param Request $request
     * @return array
     */
    public function getActiveFilters(Request $request): array
    {
        $activeFilters = [];

        // Filtreler parametresini kontrol et
        if ($request->has('filtreler') && !empty($request->filtreler)) {
            $filterString = $request->filtreler;
            $filterParts = explode(';', $filterString);

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('Processing filter string:', ['filterString' => $filterString]);

            foreach ($filterParts as $part) {
                $keyValue = explode(':', $part);
                if (count($keyValue) == 2) {
                    $key = $keyValue[0];
                    $values = explode(',', $keyValue[1]);

                    // Özellik mi varyant mı kontrol et
                    $attribute = \App\Models\Attribute::where('code', $key)->first();

                    if ($attribute) {
                        // Bu bir özellik
                        if (!isset($activeFilters['attributes'])) {
                            $activeFilters['attributes'] = [];
                        }
                        $activeFilters['attributes'][$key] = $values;

                        // Ayrıca Laravel'in dizi formatında da ekle
                        foreach ($values as $value) {
                            $paramKey = "attributes[{$key}][]";
                            // Doğrudan request nesnesine ekle
                            $request->merge([$paramKey => $value]);
                            // Debug için log ekle
                            \Illuminate\Support\Facades\Log::info("Added attribute to request: {$paramKey} = {$value}");
                        }
                    } else {
                        // Bu bir varyant olabilir
                        if (!isset($activeFilters['variants'])) {
                            $activeFilters['variants'] = [];
                        }
                        $activeFilters['variants'][$key] = $values;

                        // Ayrıca Laravel'in dizi formatında da ekle
                        foreach ($values as $value) {
                            $paramKey = "variants[{$key}][]";
                            // Doğrudan request nesnesine ekle
                            $request->merge([$paramKey => $value]);
                            // Debug için log ekle
                            \Illuminate\Support\Facades\Log::info("Added variant to request: {$paramKey} = {$value}");
                        }
                    }
                }
            }

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('Processed filter parts:', $activeFilters);
        }

        // Arama filtresi
        if ($request->has('q') && !empty($request->q)) {
            $activeFilters['q'] = $request->q;
        }

        // Fiyat aralığı filtresi
        if ($request->has('price_min') && is_numeric($request->price_min)) {
            $activeFilters['price_min'] = $request->price_min;
        }

        if ($request->has('price_max') && is_numeric($request->price_max)) {
            $activeFilters['price_max'] = $request->price_max;
        }

        // Stok durumu filtresi
        if ($request->has('in_stock') && $request->in_stock) {
            $activeFilters['in_stock'] = true;
        }

        // Özellik filtreleri - Laravel'in dizi formatını işle
        $attributes = [];
        foreach ($request->all() as $key => $value) {
            // Daha esnek bir regex kullan - attributes[brand][][]=apple formatını da destekle
            if (preg_match('/^attributes\[(.*?)\](?:\[\])*(?:\[\])?$/', $key, $matches)) {
                $attributeCode = $matches[1];

                \Illuminate\Support\Facades\Log::info("Found active attribute parameter: {$key}", [
                    'code' => $attributeCode,
                    'value' => $value
                ]);

                // Değer bir dizi değilse, diziye çevir
                $attributeValues = is_array($value) ? $value : [$value];

                // Boş olmayan değerleri filtrele
                $attributeValues = array_filter($attributeValues, function($val) {
                    return $val !== null && $val !== '';
                });

                if (!empty($attributeValues)) {
                    // Eğer bu özellik zaten eklenmiş ise, değerleri birleştir
                    if (isset($attributes[$attributeCode])) {
                        $attributes[$attributeCode] = array_merge($attributes[$attributeCode], $attributeValues);
                    } else {
                        $attributes[$attributeCode] = $attributeValues;
                    }
                }
            }
        }

        if (!empty($attributes)) {
            $activeFilters['attributes'] = $attributes;
            \Illuminate\Support\Facades\Log::info('Active attribute filters:', $attributes);
        }

        // Varyant filtreleri - Laravel'in dizi formatını işle
        $variants = [];
        foreach ($request->all() as $key => $value) {
            // Daha esnek bir regex kullan - variants[color][][]=red formatını da destekle
            if (preg_match('/^variants\[(.*?)\](?:\[\])*(?:\[\])?$/', $key, $matches)) {
                $variantCode = $matches[1];

                \Illuminate\Support\Facades\Log::info("Found active variant parameter: {$key}", [
                    'code' => $variantCode,
                    'value' => $value
                ]);

                // Değer bir dizi değilse, diziye çevir
                $variantValues = is_array($value) ? $value : [$value];

                // Boş olmayan değerleri filtrele
                $variantValues = array_filter($variantValues, function($val) {
                    return $val !== null && $val !== '';
                });

                if (!empty($variantValues)) {
                    // Eğer bu varyant zaten eklenmiş ise, değerleri birleştir
                    if (isset($variants[$variantCode])) {
                        $variants[$variantCode] = array_merge($variants[$variantCode], $variantValues);
                    } else {
                        $variants[$variantCode] = $variantValues;
                    }
                }
            }
        }

        if (!empty($variants)) {
            $activeFilters['variants'] = $variants;
            \Illuminate\Support\Facades\Log::info('Active variant filters:', $variants);
        }

        // Sıralama - sort adını değiştirerek JavaScript'teki sort() metodu ile karışmasını önle
        if ($request->has('sort') && !empty($request->sort)) {
            $activeFilters['sorting'] = $request->sort;
        }

        // Kategori filtresi
        if ($request->has('category') && !empty($request->category)) {
            $activeFilters['category'] = $request->category;
        }

        // Debug için tüm filtreleri logla
        \Illuminate\Support\Facades\Log::info('All active filters before URL generation:', $activeFilters);

        try {
            // Filtre URL'sini oluştur (SEO dostu)
            $activeFilters['filter_url'] = $this->buildSeoFriendlyFilterUrl($activeFilters);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error building SEO friendly filter URL: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error($e->getTraceAsString());
            $activeFilters['filter_url'] = '/products';
        }

        return $activeFilters;
    }

    /**
     * SEO dostu filtre URL'si oluştur
     *
     * @param array $filters
     * @return string
     */
    private function buildSeoFriendlyFilterUrl(array $filters): string
    {
        $url = '/products';

        // Debug için log ekle
        \Illuminate\Support\Facades\Log::info('Building SEO friendly filter URL with filters:', $filters);

        // Kategori varsa ekle
        if (isset($filters['category'])) {
            $category = \App\Models\Category::find($filters['category']);
            if ($category) {
                $url = '/' . $category->slug . '-c-' . $category->id;
            }
        }

        // Özellik filtreleri varsa ekle
        $filterParts = [];

        try {
            if (isset($filters['attributes']) && is_array($filters['attributes'])) {
                foreach ($filters['attributes'] as $code => $values) {
                    if (!empty($values)) {
                        // Değerlerin dizi olduğundan emin ol
                        $valuesArray = is_array($values) ? $values : [$values];

                        // Boş olmayan değerleri filtrele
                        $valuesArray = array_filter($valuesArray, function($value) {
                            return $value !== null && $value !== '';
                        });

                        // Değer varsa ekle
                        if (!empty($valuesArray)) {
                            // Her bir değerin string olduğundan emin ol
                            $stringValues = array_map(function($value) {
                                return (string)$value;
                            }, $valuesArray);

                            $filterParts[] = $code . ':' . implode(',', $stringValues);
                        }
                    }
                }
            }

            // Varyant filtreleri varsa ekle
            if (isset($filters['variants']) && is_array($filters['variants'])) {
                foreach ($filters['variants'] as $code => $values) {
                    if (!empty($values)) {
                        // Değerlerin dizi olduğundan emin ol
                        $valuesArray = is_array($values) ? $values : [$values];

                        // Boş olmayan değerleri filtrele
                        $valuesArray = array_filter($valuesArray, function($value) {
                            return $value !== null && $value !== '';
                        });

                        // Değer varsa ekle
                        if (!empty($valuesArray)) {
                            // Her bir değerin string olduğundan emin ol
                            $stringValues = array_map(function($value) {
                                return (string)$value;
                            }, $valuesArray);

                            $filterParts[] = $code . ':' . implode(',', $stringValues);
                        }
                    }
                }
            }

            // Filtre parçalarını URL'ye ekle
            if (!empty($filterParts)) {
                // Debug için log ekle
                \Illuminate\Support\Facades\Log::info('Filter parts for URL:', $filterParts);

                $url .= '?filtreler=' . implode(';', $filterParts);
            }
        } catch (\Exception $e) {
            // Hata durumunda log ekle
            \Illuminate\Support\Facades\Log::error('Error building filter parts: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error($e->getTraceAsString());
        }

        return $url;
    }

    /**
     * Filtre URL'si oluştur
     *
     * @param array $filters
     * @param string $baseUrl
     * @return string
     */
    public function buildFilterUrl(array $filters, string $baseUrl): string
    {
        $queryParams = http_build_query($filters);

        if (empty($queryParams)) {
            return $baseUrl;
        }

        return $baseUrl . '?' . $queryParams;
    }
}
