<?php

namespace App\Services\Cache;

class CacheTags
{
    // Ana tag'ler
    const PRODUCTS = 'products';
    const CATEGORIES = 'categories';
    const VARIANTS = 'variants';
    const ATTRIBUTES = 'attributes';
    const CART = 'cart';
    const ORDERS = 'orders';
    const USERS = 'users';
    const SEARCH = 'search';
    const HOMEPAGE = 'homepage';
    const NAVIGATION = 'navigation';
    const FEATURED = 'featured';
    const ON_SALE = 'on_sale';
    const MOST_VIEWED = 'most_viewed';
    const RECOMMENDATIONS = 'recommendations';
    const FAVORITES = 'favorites';

    /**
     * Ürün ile ilgili tag'leri döndürür
     *
     * @param int $productId
     * @return array
     */
    public static function forProduct(int $productId): array
    {
        return [
            self::PRODUCTS,
            "product_{$productId}"
        ];
    }

    /**
     * <PERSON>gori ile ilgili tag'leri döndürür
     *
     * @param int $categoryId
     * @return array
     */
    public static function forCategory(int $categoryId): array
    {
        return [
            self::CATEGORIES,
            "category_{$categoryId}"
        ];
    }

    /**
     * Ürün varyantı ile ilgili tag'leri döndürür
     *
     * @param int $variantId
     * @param int $productId
     * @return array
     */
    public static function forVariant(int $variantId, int $productId): array
    {
        return [
            self::VARIANTS,
            "variant_{$variantId}",
            "product_{$productId}"
        ];
    }

    /**
     * Özellik ile ilgili tag'leri döndürür
     *
     * @param int $attributeId
     * @return array
     */
    public static function forAttribute(int $attributeId): array
    {
        return [
            self::ATTRIBUTES,
            "attribute_{$attributeId}"
        ];
    }

    /**
     * Kategori ağacı için tag'leri döndürür
     *
     * @return array
     */
    public static function forCategoryTree(): array
    {
        return [
            self::CATEGORIES,
            self::NAVIGATION,
            'category_tree'
        ];
    }

    /**
     * Ana sayfa için tag'leri döndürür
     *
     * @return array
     */
    public static function forHomepage(): array
    {
        return [
            self::HOMEPAGE,
            self::PRODUCTS,
            self::CATEGORIES
        ];
    }

    /**
     * Vitrin ürünleri için tag'leri döndürür
     *
     * @return array
     */
    public static function forFeaturedProducts(): array
    {
        return [
            self::HOMEPAGE,
            self::PRODUCTS,
            self::FEATURED
        ];
    }

    /**
     * İndirimli ürünler için tag'leri döndürür
     *
     * @return array
     */
    public static function forOnSaleProducts(): array
    {
        return [
            self::HOMEPAGE,
            self::PRODUCTS,
            self::ON_SALE
        ];
    }

    /**
     * En çok görüntülenen ürünler için tag'leri döndürür
     *
     * @return array
     */
    public static function forMostViewedProducts(): array
    {
        return [
            self::HOMEPAGE,
            self::PRODUCTS,
            self::MOST_VIEWED
        ];
    }

    /**
     * Kullanıcı önerileri için tag'leri döndürür
     *
     * @param int $userId
     * @return array
     */
    public static function forUserRecommendations(int $userId): array
    {
        return [
            self::USERS,
            self::RECOMMENDATIONS,
            "user_{$userId}"
        ];
    }

    /**
     * Kullanıcı favorileri için tag'leri döndürür
     *
     * @param int $userId
     * @return array
     */
    public static function forUserFavorites(int $userId): array
    {
        return [
            self::USERS,
            self::FAVORITES,
            "user_{$userId}"
        ];
    }
}
