<?php

namespace App\Services;

use App\Models\City;
use App\Models\Country;
use App\Models\State;
use Illuminate\Support\Facades\Cache;

class LocationService
{
    /**
     * Tüm konum verilerini önbelleğe al
     */
    public static function cacheAll()
    {
        try {
            // Ülkeleri önbelleğe al
            $countries = Country::where('is_active', true)->get();
            Cache::forever('countries', $countries);

            // Ülkeleri ID'ye göre önbelleğe al
            $countriesById = $countries->keyBy('id');
            Cache::forever('countries_by_id', $countriesById);

            // Değişkenleri başlangıçta tanımla
            $turkeyStates = collect();
            $turkeyCities = collect();

            // Türkiye'yi bul (ID: 225)
            $turkey = $countries->where('id', 225)->first();

            if ($turkey) {
                // Türkiye'nin illerini önbelleğe al
                $turkeyStates = State::where('country_id', $turkey->id)
                    ->where('is_active', true)
                    ->orderBy('name')
                    ->get();
                Cache::forever('turkey_states', $turkeyStates);

                // Türkiye'nin illerini ID'ye göre önbelleğe al
                $turkeyStatesById = $turkeyStates->keyBy('id');
                Cache::forever('turkey_states_by_id', $turkeyStatesById);

                // Türkiye'nin ilçelerini önbelleğe al
                $turkeyStateIds = $turkeyStates->pluck('id')->toArray();
                $turkeyCities = City::whereIn('state_id', $turkeyStateIds)
                    ->where('is_active', true)
                    ->orderBy('name')
                    ->get();

                // İlçeleri il ID'sine göre grupla
                $turkeyCitiesByState = $turkeyCities->groupBy('state_id');
                Cache::forever('turkey_cities_by_state', $turkeyCitiesByState);
            }

            // Tüm illeri ülke ID'sine göre grupla
            $states = State::where('is_active', true)->orderBy('name')->get();
            $statesByCountry = $states->groupBy('country_id');
            Cache::forever('states_by_country', $statesByCountry);

            // Tüm ilçeleri önbelleğe almak yerine, sadece Türkiye'nin ilçelerini önbelleğe alıyoruz
            // Diğer ülkelerin ilçeleri talep edildiğinde ayrı ayrı yüklenecek

            return [
                'countries' => $countries->count(),
                'states' => $states->count(),
                'turkey_states' => $turkeyStates->count(),
                'turkey_cities' => $turkeyCities->count(),
            ];
        } catch (\Exception $e) {
            // Veritabanı bağlantı sorunu varsa boş collection'ları önbelleğe al
            Cache::forever('countries', collect());
            Cache::forever('turkey_states', collect());
            Cache::forever('turkey_states_by_id', collect());
            Cache::forever('turkey_cities_by_state', collect());
            Cache::forever('states_by_country', collect());

            return [
                'countries' => 0,
                'states' => 0,
                'turkey_states' => 0,
                'turkey_cities' => 0,
            ];
        }
    }

    /**
     * Tüm ülkeleri getir
     */
    public static function getCountries()
    {
        try {
            if (!Cache::has('countries')) {
                self::cacheAll();
            }

            return Cache::get('countries', collect());
        } catch (\Exception $e) {
            return collect();
        }
    }

    /**
     * Belirli bir ülkeyi getir
     */
    public static function getCountry($id)
    {
        if (!Cache::has('countries_by_id')) {
            self::cacheAll();
        }

        $countriesById = Cache::get('countries_by_id');
        return $countriesById[$id] ?? null;
    }

    /**
     * Belirli bir ülkenin illerini getir
     */
    public static function getStatesByCountry($countryId)
    {
        if (!Cache::has('states_by_country')) {
            self::cacheAll();
        }

        $statesByCountry = Cache::get('states_by_country');
        return $statesByCountry[$countryId] ?? collect();
    }

    /**
     * Türkiye'nin illerini getir
     */
    public static function getTurkeyStates()
    {
        try {
            if (!Cache::has('turkey_states')) {
                self::cacheAll();
            }

            return Cache::get('turkey_states', collect());
        } catch (\Exception $e) {
            return collect();
        }
    }

    /**
     * Belirli bir ili getir
     */
    public static function getState($id)
    {
        if (!Cache::has('turkey_states_by_id')) {
            self::cacheAll();
        }

        $statesById = Cache::get('turkey_states_by_id');
        return $statesById[$id] ?? null;
    }

    /**
     * Belirli bir ilin ilçelerini getir
     *
     * @param int $stateId İl ID'si
     * @param int $page Sayfa numarası
     * @param int $perPage Sayfa başına kayıt sayısı
     * @param string|null $search Arama metni
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getCitiesByState($stateId, $page = 1, $perPage = 100, $search = null)
    {
        $cacheKey = "cities_by_state_{$stateId}_page_{$page}_perpage_{$perPage}" . ($search ? "_search_" . md5($search) : "");

        // Önbellekte varsa, önbellekten getir
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Sorguyu oluştur
        $query = City::where('state_id', $stateId)
            ->where('is_active', true);

        // Arama metni varsa filtrele
        if ($search) {
            $query->where('name', 'LIKE', "%{$search}%");
        }

        // Sadece gerekli alanları seç ve sayfalama yap
        $cities = $query->orderBy('name')
            ->select(['id', 'name', 'state_id'])
            ->paginate($perPage, ['*'], 'page', $page);

        // Ülkeye göre önbellek süresini belirle
        $state = State::find($stateId);
        $cacheDuration = now()->addDay(); // Varsayılan: 1 gün

        if ($state) {
            if ($state->country_id == 225) { // Türkiye
                $cacheDuration = now()->addWeek();
            } else if ($state->country_id == 233) { // ABD
                $cacheDuration = now()->addDays(3);
            }
        }

        Cache::put($cacheKey, $cities, $cacheDuration);

        return $cities;
    }

    /**
     * Türkiye'nin belirli bir ilinin ilçelerini getir
     *
     * @param int $stateId İl ID'si
     * @param int $page Sayfa numarası
     * @param int $perPage Sayfa başına kayıt sayısı
     * @param string|null $search Arama metni
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getTurkeyCitiesByState($stateId, $page = 1, $perPage = 100, $search = null)
    {
        $cacheKey = "turkey_cities_by_state_{$stateId}_page_{$page}_perpage_{$perPage}" . ($search ? "_search_" . md5($search) : "");

        // Önbellekte varsa, önbellekten getir
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Türkiye'nin ilçelerini önbellekten al
        if (!Cache::has('turkey_cities_by_state')) {
            self::cacheAll();
        }

        $turkeyCitiesByState = Cache::get('turkey_cities_by_state');
        $allCities = $turkeyCitiesByState[$stateId] ?? collect();

        // Arama metni varsa filtrele
        if ($search) {
            $allCities = $allCities->filter(function ($city) use ($search) {
                return stripos($city->name, $search) !== false;
            });
        }

        // Sayfalama yap
        $total = $allCities->count();
        $offset = ($page - 1) * $perPage;
        $items = $allCities->slice($offset, $perPage)->values();

        // Sayfalama nesnesi oluştur
        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        // 1 hafta süreyle önbelleğe al (Türkiye verileri daha uzun süre önbellekte kalabilir)
        Cache::put($cacheKey, $paginator, now()->addWeek());

        return $paginator;
    }

    /**
     * Önbelleği temizle ve yeniden yükle
     */
    public static function refreshCache()
    {
        // Önbelleği temizle
        Cache::forget('countries');
        Cache::forget('countries_by_id');
        Cache::forget('turkey_states');
        Cache::forget('turkey_states_by_id');
        Cache::forget('turkey_cities_by_state');
        Cache::forget('states_by_country');

        // İl bazlı ilçe önbelleklerini temizle
        // Tüm illerin önbelleklerini temizle
        $states = State::all();
        foreach ($states as $state) {
            // Temel önbellek anahtarını temizle
            Cache::forget("cities_by_state_{$state->id}");

            // Sayfalama ve arama için kullanılan önbellekleri de temizle
            // Bu, tüm önbellekleri temizlemez ama en çok kullanılanları temizler
            for ($page = 1; $page <= 5; $page++) {
                Cache::forget("cities_by_state_{$state->id}_page_{$page}_perpage_100");
                Cache::forget("cities_by_state_{$state->id}_page_{$page}_perpage_50");

                // Türkiye ilçeleri için önbellek anahtarlarını da temizle
                if ($state->country_id == 225) {
                    Cache::forget("turkey_cities_by_state_{$state->id}_page_{$page}_perpage_100");
                    Cache::forget("turkey_cities_by_state_{$state->id}_page_{$page}_perpage_50");
                }
            }
        }

        // Yeniden yükle
        return self::cacheAll();
    }
}
