<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BulkProductService
{
    /**
     * Bulk update product status
     *
     * @param array $productIds
     * @param bool $status
     * @return int
     */
    public function updateStatus(array $productIds, bool $status)
    {
        return Product::whereIn('id', $productIds)->update(['status' => $status]);
    }

    /**
     * Bulk update product category
     *
     * @param array $productIds
     * @param int $categoryId
     * @return int
     */
    public function updateCategory(array $productIds, int $categoryId)
    {
        // Verify category exists
        $category = Category::findOrFail($categoryId);

        return Product::whereIn('id', $productIds)->update(['category_id' => $categoryId]);
    }

    /**
     * Bulk update product stock
     *
     * @param array $productStockData
     * @return array
     */
    public function updateStock(array $productStockData)
    {
        $updated = [];

        DB::beginTransaction();

        try {
            foreach ($productStockData as $data) {
                $product = Product::findOrFail($data['id']);
                $product->stock = $data['stock'];
                $product->save();

                $updated[] = $product->id;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk stock update failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk update product stock with operation
     *
     * @param array $productIds
     * @param int $stockValue
     * @param string $operation
     * @return int
     */
    public function updateStockWithOperation(array $productIds, int $stockValue, string $operation = 'set')
    {
        $updated = 0;

        DB::beginTransaction();

        try {
            foreach ($productIds as $id) {
                $product = Product::findOrFail($id);

                switch ($operation) {
                    case 'set':
                        $product->stock = $stockValue;
                        break;
                    case 'increase':
                        $product->stock += $stockValue;
                        break;
                    case 'decrease':
                        $product->stock = max(0, $product->stock - $stockValue); // Prevent negative stock
                        break;
                }

                $product->save();
                $updated++;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk stock update with operation failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk update variant stock
     *
     * @param array $productIds
     * @param int $stockValue
     * @param string $operation
     * @return int
     */
    public function updateVariantStock(array $productIds, int $stockValue, string $operation = 'set')
    {
        $updated = 0;

        DB::beginTransaction();

        try {
            // Get all variants for the selected products
            $variants = \App\Models\ProductVariant::whereIn('product_id', $productIds)->get();

            foreach ($variants as $variant) {
                switch ($operation) {
                    case 'set':
                        $variant->stock = $stockValue;
                        break;
                    case 'increase':
                        $variant->stock += $stockValue;
                        break;
                    case 'decrease':
                        $variant->stock = max(0, $variant->stock - $stockValue); // Prevent negative stock
                        break;
                }

                $variant->save();
                $updated++;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk variant stock update failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk update default variant
     *
     * @param array $productIds
     * @param string $defaultVariantStrategy
     * @return int
     */
    public function updateDefaultVariant(array $productIds, string $defaultVariantStrategy = 'first')
    {
        $updated = 0;

        DB::beginTransaction();

        try {
            foreach ($productIds as $id) {
                $product = Product::with('variants')->findOrFail($id);

                if ($product->variants->isEmpty()) {
                    continue;
                }

                // Reset all variants to non-default
                foreach ($product->variants as $variant) {
                    $variant->is_default = false;
                    $variant->save();
                }

                // Set new default variant based on strategy
                $defaultVariant = null;

                switch ($defaultVariantStrategy) {
                    case 'first':
                        $defaultVariant = $product->variants->first();
                        break;
                    case 'highest_stock':
                        $defaultVariant = $product->variants->sortByDesc('stock')->first();
                        break;
                    case 'lowest_price':
                        $defaultVariant = $product->variants->sortBy('additional_price')->first();
                        break;
                    case 'highest_price':
                        $defaultVariant = $product->variants->sortByDesc('additional_price')->first();
                        break;
                }

                if ($defaultVariant) {
                    $defaultVariant->is_default = true;
                    $defaultVariant->save();
                    $updated++;
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk default variant update failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk update product prices
     *
     * @param array $productIds
     * @param float $percentageChange
     * @param string $operation
     * @return int
     */
    public function updatePrices(array $productIds, float $percentageChange, string $operation = 'increase')
    {
        $updated = 0;

        DB::beginTransaction();

        try {
            foreach ($productIds as $id) {
                $product = Product::findOrFail($id);

                if ($operation === 'increase') {
                    $product->price = $product->price * (1 + $percentageChange / 100);
                } else {
                    $product->price = $product->price * (1 - $percentageChange / 100);
                }

                $product->save();
                $updated++;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk price update failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk update product prices to a fixed value
     *
     * @param array $productIds
     * @param float $fixedPrice
     * @return int
     */
    public function updateFixedPrice(array $productIds, float $fixedPrice)
    {
        $updated = 0;

        DB::beginTransaction();

        try {
            foreach ($productIds as $id) {
                $product = Product::findOrFail($id);
                $product->price = $fixedPrice;
                $product->save();
                $updated++;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk fixed price update failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk update product prices by a fixed amount
     *
     * @param array $productIds
     * @param float $amount
     * @param string $operation
     * @return int
     */
    public function updatePriceByAmount(array $productIds, float $amount, string $operation = 'increase')
    {
        $updated = 0;

        DB::beginTransaction();

        try {
            foreach ($productIds as $id) {
                $product = Product::findOrFail($id);

                if ($operation === 'increase') {
                    $product->price = $product->price + $amount;
                } else {
                    $product->price = max(0, $product->price - $amount); // Prevent negative prices
                }

                $product->save();
                $updated++;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk price update by amount failed: ' . $e->getMessage());
            throw $e;
        }

        return $updated;
    }

    /**
     * Bulk delete products
     *
     * @param array $productIds
     * @return int
     */
    public function delete(array $productIds)
    {
        return Product::whereIn('id', $productIds)->delete();
    }

    /**
     * Import products from CSV
     *
     * @param string $filePath
     * @param array $columnMapping
     * @return array
     */
    public function importFromCsv(string $filePath, array $columnMapping)
    {
        $imported = [];
        $errors = [];
        $row = 1;

        if (($handle = fopen($filePath, "r")) !== FALSE) {
            DB::beginTransaction();

            try {
                // Skip header row
                fgetcsv($handle, 1000, ",");
                $row++;

                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    try {
                        $productData = $this->mapCsvRowToProductData($data, $columnMapping);

                        // Generate slug if not provided
                        if (!isset($productData['slug']) || empty($productData['slug'])) {
                            $productData['slug'] = Str::slug($productData['name']);
                        }

                        // Find category by name if provided
                        if (isset($productData['category_name']) && !empty($productData['category_name'])) {
                            $category = Category::where('name', $productData['category_name'])->first();
                            if ($category) {
                                $productData['category_id'] = $category->id;
                            }
                            unset($productData['category_name']);
                        }

                        $product = Product::create($productData);
                        $imported[] = $product->id;
                    } catch (\Exception $e) {
                        $errors[] = "Row {$row}: " . $e->getMessage();
                    }

                    $row++;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('CSV import failed: ' . $e->getMessage());
                throw $e;
            }

            fclose($handle);
        }

        return [
            'imported' => $imported,
            'errors' => $errors
        ];
    }

    /**
     * Map CSV row to product data
     *
     * @param array $row
     * @param array $columnMapping
     * @return array
     */
    private function mapCsvRowToProductData(array $row, array $columnMapping)
    {
        $productData = [];

        foreach ($columnMapping as $field => $columnIndex) {
            if (isset($row[$columnIndex])) {
                $productData[$field] = $row[$columnIndex];
            }
        }

        return $productData;
    }
}
