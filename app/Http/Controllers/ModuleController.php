<?php

namespace App\Http\Controllers;

use App\Core\Services\ModuleManager;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ModuleController extends Controller
{
    protected $moduleManager;

    public function __construct(ModuleManager $moduleManager)
    {
        $this->moduleManager = $moduleManager;
    }

    /**
     * Modül yönetim sayfasını göster
     */
    public function index()
    {
        $modules = config('modules.register', []);
        $enabledModules = config('modules.enabled', []);
        $dependencies = config('modules.dependencies', []);
        
        $moduleData = [];
        
        foreach ($modules as $module) {
            $moduleData[] = [
                'name' => $module,
                'enabled' => in_array($module, $enabledModules),
                'dependencies' => $dependencies[$module] ?? [],
                'dependents' => $this->getDependents($module, $dependencies),
                'description' => $this->getModuleDescription($module),
            ];
        }
        
        return Inertia::render('Admin/Modules/Index', [
            'modules' => $moduleData
        ]);
    }
    
    /**
     * Modül durumunu güncelle
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'modules' => 'required|array',
            'modules.*' => 'boolean',
        ]);
        
        $enabledModules = [];
        foreach ($validated['modules'] as $module => $enabled) {
            if ($enabled) {
                $enabledModules[] = $module;
                
                // Bağımlılıkları da etkinleştir
                $dependencies = config("modules.dependencies.{$module}", []);
                $enabledModules = array_merge($enabledModules, $dependencies);
            }
        }
        
        // Tekrarlanan modülleri kaldır
        $enabledModules = array_unique($enabledModules);
        
        // Modül konfigürasyonunu güncelle
        $this->updateModuleConfig($enabledModules);
        
        return redirect()->back()->with('success', 'Modül ayarları güncellendi.');
    }
    
    /**
     * Bir modüle bağımlı olan modülleri bul
     */
    protected function getDependents($module, $dependencies)
    {
        $dependents = [];
        
        foreach ($dependencies as $dependentModule => $moduleDependencies) {
            if (in_array($module, $moduleDependencies)) {
                $dependents[] = $dependentModule;
            }
        }
        
        return $dependents;
    }
    
    /**
     * Modül açıklamasını getir
     */
    protected function getModuleDescription($module)
    {
        $descriptions = [
            'Products' => 'Ürün yönetimi, kategoriler, özellikler ve varyantlar',
            'Categories' => 'Kategori yönetimi, ağaç yapısı ve özellikler',
            'Orders' => 'Sipariş yönetimi, durum takibi ve raporlama',
            'Users' => 'Kullanıcı yönetimi, roller ve izinler',
            'Payments' => 'Ödeme yöntemleri, entegrasyonlar ve işlem geçmişi',
            'CurrencyPricing' => 'Çoklu para birimi desteği ve otomatik kur güncellemeleri',
        ];
        
        return $descriptions[$module] ?? 'Bu modül için açıklama bulunmamaktadır.';
    }
    
    /**
     * Modül konfigürasyonunu güncelle
     */
    protected function updateModuleConfig($enabledModules)
    {
        $configPath = config_path('modules.php');
        $configContent = file_get_contents($configPath);
        
        // Etkin modüller dizisini güncelle
        $pattern = "/('enabled'\s*=>\s*\[)[^\]]*(\])/";
        $replacement = "$1\n        '" . implode("',\n        '", $enabledModules) . "',\n    $2";
        
        $updatedContent = preg_replace($pattern, $replacement, $configContent);
        
        file_put_contents($configPath, $updatedContent);
    }
}
