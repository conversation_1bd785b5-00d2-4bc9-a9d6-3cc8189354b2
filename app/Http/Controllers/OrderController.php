<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderNote;
use App\Models\Product;
use App\Models\User;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use App\Events\OrderCreated;
use App\Services\EmailService;
use App\Services\ShippingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $status = $request->input('status', '');

        $orders = Order::with('user') // withoutTrashed() kaldırıldı
            ->when($search, function($query, $search) {
                return $query->where('order_number', 'like', "%{$search}%")
                    ->orWhere('billing_name', 'like', "%{$search}%")
                    ->orWhere('billing_email', 'like', "%{$search}%")
                    ->orWhere('billing_phone', 'like', "%{$search}%");
            })
            ->when($status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage)
            ->withQueryString();

        return Inertia::render('Admin/Orders/Index', [
            'orders' => $orders,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'per_page' => $perPage
            ],
            'statuses' => OrderStatus::toArray(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        $order->load(['items.product', 'notes.user', 'user']);

        // Sipariş durumu için renk ve açıklama ekle
        $order->status_color = $order->status->color();
        $order->status_description = $order->status->description();

        // Kargo takip URL'i ekle
        $order->tracking_url = $order->getTrackingUrl();

        // Fatura PDF URL'i ekle
        $order->invoice_pdf_url = $order->getInvoicePdfUrl();

        // Notlar için ek bilgiler ekle
        if ($order->notes && is_iterable($order->notes)) {
            foreach ($order->notes as $note) {
                $note->type_label = $note->getTypeLabel();
                $note->type_color = $note->getTypeColor();
                $note->type_icon = $note->getTypeIcon();

                if ($note->status_before) {
                    $note->status_before_label = $note->status_before->label();
                    $note->status_before_color = $note->status_before->color();
                }

                if ($note->status_after) {
                    $note->status_after_label = $note->status_after->label();
                    $note->status_after_color = $note->status_after->color();
                }
            }
        }

        // Sonraki olası durumları OrderStatus nesneleri olarak al
        $nextStatusObjects = $order->status ? $order->status->nextStatuses() : [];
        $nextStatusesFormatted = [];

        foreach ($nextStatusObjects as $status) {
            $nextStatusesFormatted[] = [
                'value' => $status->value,
                'label' => $status->label(),
                'color' => $status->color(),
                'description' => $status->description(),
            ];
        }

        return Inertia::render('Admin/Orders/Show', [
            'order' => $order,
            'statuses' => OrderStatus::toArray(),
            'paymentStatuses' => PaymentStatus::toArray(),
            'nextStatuses' => $nextStatusesFormatted,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Order $order)
    {
        $order->load(['items.product', 'user']);
        $users = User::all();
        $statuses = [
            'pending' => 'Beklemede',
            'processing' => 'İşlemde',
            'completed' => 'Tamamlandı',
            'cancelled' => 'İptal Edildi'
        ];

        $paymentStatuses = [
            'pending' => 'Ödeme Bekliyor',
            'paid' => 'Ödendi',
            'failed' => 'Başarısız'
        ];

        return Inertia::render('Admin/Orders/Edit', [
            'order' => $order,
            'users' => $users,
            'statuses' => $statuses,
            'paymentStatuses' => $paymentStatuses
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Order $order)
    {
        $validated = $request->validate([
            'status' => 'sometimes|string|in:' . implode(',', array_column(OrderStatus::cases(), 'value')),
            'payment_status' => 'sometimes|string|in:' . implode(',', array_column(PaymentStatus::cases(), 'value')),
            'tracking_number' => 'nullable|string|max:255',
            'estimated_delivery_date' => 'nullable|date',
            'admin_notes' => 'nullable|string',
        ]);

        // Update order status if provided
        if (isset($validated['status']) && $validated['status'] !== $order->status->value) {
            $newStatus = OrderStatus::from($validated['status']);
            $order->updateStatus($newStatus, null, Auth::id());
            unset($validated['status']); // Remove from validated data since we've already updated it
        }

        // Update other fields
        if (!empty($validated)) {
            $order->update($validated);
        }

        return redirect()->back()
            ->with('success', 'Sipariş başarıyla güncellendi.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Order $order)
    {
        // Siparişleri tamamen silmek yerine iptal etmek daha doğru olabilir
        // Bu nedenle silme işlemi yerine iptal etme işlemi yapıyoruz
        $order->updateStatus(OrderStatus::CANCELLED, 'Sipariş admin tarafından iptal edildi.', Auth::id());

        return redirect()->route('admin.orders.index')
            ->with('success', 'Sipariş başarıyla iptal edildi.');
    }

    /**
     * Add a note to the order.
     */
    public function addNote(Request $request, Order $order)
    {
        $validated = $request->validate([
            'note' => 'required|string',
            'is_private' => 'boolean',
        ]);

        $order->addNote(
            $validated['note'],
            Auth::id(),
            $validated['is_private'] ?? true
        );

        return redirect()->back()
            ->with('success', 'Not başarıyla eklendi.');
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $validated = $request->validate([
            'status' => 'required|string|in:' . implode(',', array_column(OrderStatus::cases(), 'value')),
            'note' => 'nullable|string',
            'notify_customer' => 'boolean',
        ]);

        $newStatus = OrderStatus::from($validated['status']);
        $order->updateStatus($newStatus, $validated['note'], Auth::id(), !($validated['notify_customer'] ?? false));

        return redirect()->back()
            ->with('success', 'Sipariş durumu başarıyla güncellendi.');
    }

    /**
     * Update payment status.
     */
    public function updatePaymentStatus(Request $request, Order $order)
    {
        $validated = $request->validate([
            'payment_status' => 'required|string|in:' . implode(',', array_column(PaymentStatus::cases(), 'value')),
            'note' => 'nullable|string',
        ]);

        $oldStatus = $order->payment_status;
        $newStatus = PaymentStatus::from($validated['payment_status']);

        $order->payment_status = $newStatus;
        $order->save();

        // Add note if provided or status changed
        if (isset($validated['note']) || $oldStatus !== $newStatus) {
            $note = $validated['note'] ?? 'Ödeme durumu değiştirildi: ' .
                   ($oldStatus ? $oldStatus->label() : 'Bilinmiyor') . ' -> ' .
                   $newStatus->label();

            $order->notes()->create([
                'user_id' => Auth::id(),
                'note' => $note,
                'is_private' => true,
            ]);
        }

        return redirect()->back()
            ->with('success', 'Ödeme durumu başarıyla güncellendi.');
    }

    /**
     * Update shipping information.
     */
    public function updateShipping(Request $request, Order $order)
    {
        $validated = $request->validate([
            'tracking_number' => 'nullable|string|max:255',
            'estimated_delivery_date' => 'nullable|date',
            'shipping_method' => 'nullable|string|max:255',
            'shipping_cost' => 'nullable|numeric|min:0',
        ]);

        $order->update($validated);

        // If tracking number is provided and order is in processing status, update to shipped
        if (!empty($validated['tracking_number']) && $order->status === OrderStatus::PROCESSING) {
            $order->updateStatus(
                OrderStatus::SHIPPED,
                'Kargo takip numarası eklendi: ' . $validated['tracking_number'],
                Auth::id()
            );
        }

        return redirect()->back()
            ->with('success', 'Kargo bilgileri başarıyla güncellendi.');
    }

    /**
     * Delete a note.
     */
    public function deleteNote(Order $order, OrderNote $note)
    {
        // Check if the note belongs to the order
        if ($note->order_id !== $order->id) {
            return redirect()->back()
                ->with('error', 'Bu not bu siparişe ait değil.');
        }

        $note->delete();

        return redirect()->back()
            ->with('success', 'Not başarıyla silindi.');
    }

    /**
     * Generate invoice.
     */
    public function generateInvoice(Order $order)
    {
        // This would be implemented with a PDF generation library
        // For now, just return a placeholder response

        return redirect()->back()
            ->with('success', 'Fatura oluşturma özelliği henüz uygulanmadı.');
    }

    /**
     * Send order confirmation email.
     */
    public function sendConfirmationEmail(Order $order)
    {
        // E-posta servisini kullanarak sipariş onay e-postası gönder
        $emailService = app(EmailService::class);
        $result = $emailService->sendOrderConfirmationEmail($order);

        if ($result) {
            return redirect()->back()
                ->with('success', 'Sipariş onay e-postası başarıyla gönderildi.');
        } else {
            return redirect()->back()
                ->with('error', 'Sipariş onay e-postası gönderilirken bir hata oluştu.');
        }
    }

    /**
     * Update shipping information with tracking.
     */
    public function updateShippingInfo(Request $request, Order $order)
    {
        $validated = $request->validate([
            'tracking_number' => 'required|string|max:255',
            'shipping_company' => 'required|string|max:255',
            'shipping_date' => 'nullable|date',
            'estimated_delivery_date' => 'nullable|date',
            'note' => 'nullable|string',
            'notify_customer' => 'boolean',
        ]);

        // Kargo servisini kullan
        $shippingService = app(ShippingService::class);

        try {
            // Siparişi kargoya ver
            $shippingService->shipOrder(
                $order,
                $validated['shipping_company'],
                $validated['tracking_number'],
                $validated['shipping_date'] ?? now(),
                $validated['note'],
                Auth::id()
            );

            // Tahmini teslimat tarihi
            if (isset($validated['estimated_delivery_date'])) {
                $order->estimated_delivery_date = $validated['estimated_delivery_date'];
                $order->save();
            }

            return redirect()->back()
                ->with('success', 'Kargo bilgileri başarıyla güncellendi.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Kargo bilgileri güncellenirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Update invoice information.
     */
    public function updateInvoiceInfo(Request $request, Order $order)
    {
        $validated = $request->validate([
            'invoice_number' => 'required|string|max:255',
            'invoice_date' => 'required|date',
            'invoice_pdf' => 'nullable|file|mimes:pdf|max:10240',
            'note' => 'nullable|string',
            'notify_customer' => 'boolean',
        ]);

        // Fatura PDF'i yüklendiyse
        $invoicePdfPath = null;
        if ($request->hasFile('invoice_pdf')) {
            $invoicePdfPath = $request->file('invoice_pdf')->store('invoices', 'public');
        }

        // Fatura bilgilerini güncelle
        $order->updateInvoiceInfo(
            $validated['invoice_number'],
            $validated['invoice_date'],
            $invoicePdfPath,
            $validated['note'],
            Auth::id()
        );

        return redirect()->back()
            ->with('success', 'Fatura bilgileri başarıyla güncellendi.');
    }

    /**
     * Mark order as delivered.
     */
    public function markAsDelivered(Request $request, Order $order)
    {
        $validated = $request->validate([
            'delivery_date' => 'nullable|date',
            'note' => 'nullable|string',
            'notify_customer' => 'boolean',
        ]);

        // Teslimat bilgilerini güncelle
        $order->markAsDelivered(
            $validated['delivery_date'] ?? now(),
            $validated['note'],
            Auth::id()
        );

        return redirect()->back()
            ->with('success', 'Sipariş teslim edildi olarak işaretlendi.');
    }

    /**
     * Add a note with type.
     */
    public function addNoteWithType(Request $request, Order $order)
    {
        $validated = $request->validate([
            'note' => 'required|string',
            'is_private' => 'boolean',
            'note_type' => 'required|string|in:general,status_change,payment,shipping,customer,system',
            'notify_customer' => 'boolean',
        ]);

        $order->addNote(
            $validated['note'],
            Auth::id(),
            $validated['is_private'] ?? true,
            $validated['note_type'],
            $validated['notify_customer'] ?? false
        );

        return redirect()->back()
            ->with('success', 'Not başarıyla eklendi.');
    }
}
