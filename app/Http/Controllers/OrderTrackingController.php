<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Inertia\Inertia;

class OrderTrackingController extends Controller
{
    /**
     * Sipariş takip sayfasını göster
     */
    public function index(Request $request)
    {
        $orderNumber = $request->query('order_number');
        $email = $request->query('email');

        $order = null;
        $error = null;

        // Sipariş numarası ve e-posta varsa siparişi bul
        if ($orderNumber && $email) {
            $order = Order::where('order_number', $orderNumber)
                ->where(function ($query) use ($email) {
                    $query->where('billing_email', $email)
                        ->orWhere('shipping_email', $email);
                })
                ->with(['items.product', 'notes' => function ($query) {
                    $query->where('is_private', false)->orderBy('created_at', 'desc');
                }, 'bankAccount'])
                ->first();

            if (!$order) {
                $error = 'Belirtilen sipariş numarası ve e-posta adresi ile eşleşen bir sipariş bulunamadı.';
            }
        }

        if ($order) {
            return Inertia::render('Orders/Track', [
                'order' => [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'status' => $order->status,
                    'payment_status' => $order->payment_status,
                    'payment_method' => $order->payment_method,
                    'shipping_method' => $order->shipping_method,
                    'total_amount' => $order->total_amount,
                    'shipping_cost' => $order->shipping_cost,
                    'tax_amount' => $order->tax_amount,
                    'discount_amount' => $order->discount_amount,
                    'coupon_code' => $order->coupon_code,
                    'created_at' => $order->created_at,
                    'updated_at' => $order->updated_at,
                    'tracking_number' => $order->tracking_number,
                    'shipping_company' => $order->shipping_company,
                    'shipping_date' => $order->shipping_date,
                    'estimated_delivery_date' => $order->estimated_delivery_date,
                    'actual_delivery_date' => $order->actual_delivery_date,
                    'tracking_url' => $order->getTrackingUrl(),
                    'billing_name' => $order->billing_name,
                    'billing_email' => $order->billing_email,
                    'billing_phone' => $order->billing_phone,
                    'billing_address' => $order->billing_address,
                    'billing_city' => $order->billing_city,
                    'billing_state' => $order->billing_state,
                    'billing_zipcode' => $order->billing_zipcode,
                    'billing_country' => $order->billing_country,
                    'shipping_name' => $order->shipping_name,
                    'shipping_email' => $order->shipping_email,
                    'shipping_phone' => $order->shipping_phone,
                    'shipping_address' => $order->shipping_address,
                    'shipping_city' => $order->shipping_city,
                    'shipping_state' => $order->shipping_state,
                    'shipping_zipcode' => $order->shipping_zipcode,
                    'shipping_country' => $order->shipping_country,
                    'items' => $order->items && $order->items->count() > 0 ? $order->items->map(function($item) {
                        return [
                            'id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_name' => $item->product_name,
                            'price' => $item->price,
                            'quantity' => $item->quantity,
                            'subtotal' => $item->subtotal,
                            'options' => $item->options,
                            'product' => $item->product ? [
                                'id' => $item->product->id,
                                'name' => $item->product->name,
                                'image' => $item->product->image,
                            ] : null,
                        ];
                    })->toArray() : [],
                    'notes' => $order->notes && $order->notes->count() > 0 ? $order->notes->map(function($note) {
                        return [
                            'id' => $note->id,
                            'note' => $note->note,
                            'note_type' => $note->note_type,
                            'created_at' => $note->created_at,
                        ];
                    })->toArray() : [],
                    'bank_account' => $order->bankAccount ? [
                        'bank_name' => $order->bankAccount->bank_name,
                        'account_name' => $order->bankAccount->account_name,
                        'iban' => $order->bankAccount->iban,
                        'description' => $order->bankAccount->description,
                    ] : null,
                ],
                'orderNumber' => $orderNumber,
                'email' => $email,
            ]);
        }

        return Inertia::render('Orders/Track', [
            'order' => null,
            'error' => $error,
            'orderNumber' => $orderNumber,
            'email' => $email,
        ]);
    }

    /**
     * Sipariş numarası ile sipariş detaylarını göster
     */
    public function track(Request $request)
    {
        $validated = $request->validate([
            'order_number' => 'required|string',
            'email' => 'required|email',
        ]);

        return redirect()->route('orders.track', [
            'order_number' => $validated['order_number'],
            'email' => $validated['email'],
        ]);
    }
}
