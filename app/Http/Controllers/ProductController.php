<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');

        $products = Product::with('category')
            ->when($search, function($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->withQueryString();

        // Sayfalama bağlantılarını düzelt
        $products->through(function ($product) {
            return $product;
        });

        $categories = Category::all();

        return Inertia::render('Admin/Products', [
            'products' => $products,
            'categories' => $categories,
            'filters' => [
                'search' => $search,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('status', true)->get();

        return Inertia::render('Admin/Products/Create', [
            'categories' => $categories
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'status' => 'boolean',
            'image' => 'nullable|string',
            'weight' => 'nullable|numeric|min:0',
            'desi' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'is_on_sale' => 'boolean',
            'sale_price' => 'nullable|numeric|min:0',
            'sale_starts_at' => 'nullable|date',
            'sale_ends_at' => 'nullable|date|after_or_equal:sale_starts_at',
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        $product = Product::create($validated);

        // Redirect türüne göre yönlendirme yap
        if ($request->has('redirect_type') && $request->redirect_type === 'create') {
            return redirect()->route('admin.products.create')
                ->with('success', 'Ürün başarıyla eklendi. Yeni bir ürün ekleyebilirsiniz.');
        }

        return redirect()->route('admin.products.index')
            ->with('success', 'Ürün başarıyla eklendi.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        // Inertia isteği ise Inertia yanıtı döndür
        if (request()->header('X-Inertia')) {
            return Inertia::render('Admin/Products/Show', [
                'product' => $product->load('category')
            ]);
        }

        // API isteği ise JSON yanıtı döndür
        return response()->json($product->load('category'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $categories = Category::where('status', true)->get();

        return Inertia::render('Admin/Products/Edit', [
            'product' => $product,
            'categories' => $categories
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'status' => 'boolean',
            'image' => 'nullable|string',
            'weight' => 'nullable|numeric|min:0',
            'desi' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'is_on_sale' => 'boolean',
            'sale_price' => 'nullable|numeric|min:0',
            'sale_starts_at' => 'nullable|date',
            'sale_ends_at' => 'nullable|date|after_or_equal:sale_starts_at',
        ]);

        if ($request->has('name') && $product->name !== $request->name) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $product->update($validated);

        // Redirect türüne göre yönlendirme yap
        if ($request->has('redirect_type')) {
            if ($request->redirect_type === 'create') {
                return redirect()->route('admin.products.create')
                    ->with('success', 'Ürün başarıyla güncellendi. Yeni bir ürün ekleyebilirsiniz.');
            }
        }

        return redirect()->route('admin.products.index')
            ->with('success', 'Ürün başarıyla güncellendi.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        $product->delete();
        return redirect()->route('admin.products.index')
            ->with('success', 'Ürün başarıyla silindi.');
    }

    /**
     * Search products for API
     */
    public function search(Request $request)
    {
        $query = $request->input('q', '');

        $products = Product::where('name', 'like', "%{$query}%")
            ->orWhere('stock_code', 'like', "%{$query}%") // sku yerine stock_code kullanıldı
            ->orWhere('description', 'like', "%{$query}%")
            ->limit(50)
            ->get();

        return response()->json($products);
    }
}
