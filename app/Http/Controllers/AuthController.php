<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class AuthController extends Controller
{
    /**
     * Giriş formunu göster
     */
    public function showLoginForm()
    {
        return Inertia::render('Auth/Login');
    }

    /**
     * <PERSON>iri<PERSON> işlemini gerçekleştir
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            return redirect()->intended('/admin/dashboard');
        }

        throw ValidationException::withMessages([
            'email' => ['Verilen bilgiler kayıtlar<PERSON><PERSON><PERSON><PERSON><PERSON> eşleşmiyor.'],
        ]);
    }

    /**
     * Kayıt formunu göster
     */
    public function showRegisterForm()
    {
        return Inertia::render('Auth/Register');
    }

    /**
     * <PERSON><PERSON> kullanıcı kaydı oluştur
     */
    public function register(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
        ]);

        // Yeni kullanıcıya 'customer' rolünü ata
        $user->assignRole('customer');

        Auth::login($user);

        return redirect('/admin/dashboard');
    }

    /**
     * Kullanıcıyı çıkış yaptır
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/admin/login');
    }

    /**
     * Kullanıcı profilini göster
     */
    public function profile()
    {
        return Inertia::render('Auth/Profile', [
            'user' => Auth::user(),
        ]);
    }

    /**
     * Kullanıcı profilini güncelle
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Şifre doğrulaması
        if (isset($validated['current_password'])) {
            if (!Hash::check($validated['current_password'], $user->password)) {
                throw ValidationException::withMessages([
                    'current_password' => ['Mevcut şifre doğru değil.'],
                ]);
            }
        }

        // Kullanıcı bilgilerini güncelle
        $user->name = $validated['name'];
        $user->email = $validated['email'];

        if (isset($validated['password'])) {
            $user->password = Hash::make($validated['password']);
        }

        $user->save();

        return redirect()->route('profile')->with('success', 'Profil başarıyla güncellendi.');
    }
}
