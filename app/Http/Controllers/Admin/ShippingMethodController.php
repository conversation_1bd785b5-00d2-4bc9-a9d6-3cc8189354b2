<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShippingMethod;
use App\Models\ShippingCompany;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ShippingMethodController extends Controller
{
    /**
     * Kargo metodları listesini göster
     */
    public function index()
    {
        $methods = ShippingMethod::with('company')
            ->orderBy('name')
            ->withCount('zoneMethods')
            ->get();

        // Kargo şirketlerine göre grupla
        $methodsByCompany = $methods->groupBy('shipping_company_id');

        // Kargo şirketlerini al
        $companies = ShippingCompany::where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Shipping/Methods/Index', [
            'methods' => $methods,
            'methodsByCompany' => $methodsByCompany,
            'companies' => $companies,
        ]);
    }

    /**
     * Yeni kargo metodu oluşturma formunu göster
     */
    public function create()
    {
        $companies = ShippingCompany::where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Shipping/Methods/Create', [
            'companies' => $companies,
        ]);
    }

    /**
     * Yeni kargo metodu kaydet
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:shipping_methods,code',
            'description' => 'nullable|string',
            'shipping_company_id' => 'required|exists:shipping_companies,id',
            'delivery_time' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'requires_address' => 'boolean',
        ]);

        ShippingMethod::create($validated);

        return redirect()->route('admin.shipping.methods.index')
            ->with('success', 'Kargo metodu başarıyla oluşturuldu.');
    }

    /**
     * Kargo metodu düzenleme formunu göster
     */
    public function edit(ShippingMethod $method)
    {
        // Kargo şirketini yükle
        $method->load('company');

        $companies = ShippingCompany::where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('Admin/Shipping/Methods/Edit', [
            'method' => $method,
            'companies' => $companies,
        ]);
    }

    /**
     * Kargo metodunu güncelle
     */
    public function update(Request $request, ShippingMethod $method)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:shipping_methods,code,' . $method->id,
            'description' => 'nullable|string',
            'shipping_company_id' => 'required|exists:shipping_companies,id',
            'delivery_time' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'requires_address' => 'boolean',
        ]);

        $method->update($validated);

        return redirect()->route('admin.shipping.methods.index')
            ->with('success', 'Kargo metodu başarıyla güncellendi.');
    }

    /**
     * Kargo metodunu sil
     */
    public function destroy(ShippingMethod $method)
    {
        // Kargo metodunun kullanımda olup olmadığını kontrol et
        if ($method->zoneMethods()->count() > 0) {
            if (request()->wantsJson() || request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bu kargo metodu bölgelerde kullanıldığı için silinemez.'
                ], 422);
            }

            return redirect()->route('admin.shipping.methods.index')
                ->with('error', 'Bu kargo metodu bölgelerde kullanıldığı için silinemez.');
        }

        $method->delete();

        if (request()->wantsJson() || request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Kargo metodu başarıyla silindi.'
            ]);
        }

        return redirect()->route('admin.shipping.methods.index')
            ->with('success', 'Kargo metodu başarıyla silindi.');
    }
}
