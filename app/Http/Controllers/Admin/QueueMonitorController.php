<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessOrderJob;
use App\Jobs\SendEmailJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Inertia\Inertia;

class QueueMonitorController extends Controller
{
    /**
     * Queue monitör sayfasını göster
     */
    public function index()
    {
        // Queue istatistiklerini al
        $stats = $this->getQueueStats();

        return Inertia::render('Admin/QueueMonitor/Index', [
            'stats' => $stats,
        ]);
    }

    /**
     * Test job'ları gönder
     */
    public function sendTestJobs(Request $request)
    {
        $count = $request->input('count', 10);

        // Sipariş işleme job'ları
        for ($i = 1; $i <= $count; $i++) {
            ProcessOrderJob::dispatch($i);
        }

        // E-posta gönderme job'ları
        for ($i = 1; $i <= $count; $i++) {
            SendEmailJob::dispatch(
                "user{$i}@example.com",
                "Test E-posta #{$i}",
                "Bu bir test e-postasıdır. Numara: {$i}"
            );
        }

        return redirect()->back()->with('success', "Toplam " . ($count * 2) . " adet job başarıyla kuyruğa eklendi.");
    }

    /**
     * Başarısız job'ları temizle
     */
    public function clearFailedJobs()
    {
        Artisan::call('queue:flush');
        return redirect()->back()->with('success', "Başarısız job'lar temizlendi.");
    }

    /**
     * Queue istatistiklerini al
     */
    private function getQueueStats()
    {
        // Redis bağlantısı kontrolü
        $redisAvailable = false;
        try {
            Redis::ping();
            $redisAvailable = true;
        } catch (\Exception $e) {
            // Redis bağlantısı yok
        }

        // Queue istatistikleri
        $stats = [
            'redis_available' => $redisAvailable,
            'queues' => [],
            'failed_count' => DB::table('failed_jobs')->count(),
        ];

        // Redis bağlantısı varsa kuyruk bilgilerini al
        if ($redisAvailable) {
            $queues = ['default', 'emails', 'orders'];

            foreach ($queues as $queue) {
                $stats['queues'][$queue] = [
                    'count' => Redis::llen("queues:{$queue}"),
                    'processed' => 0, // Bu bilgiyi Redis'ten alamıyoruz, Horizon olmadan
                    'failed' => 0,    // Bu bilgiyi Redis'ten alamıyoruz, Horizon olmadan
                ];
            }
        }

        return $stats;
    }
}
