<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Cache\CategoryCacheService;
use App\Services\Cache\ProductCacheService;
use App\Services\Cache\HomepageCacheWarmer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;

class CacheController extends Controller
{
    /**
     * @var ProductCacheService
     */
    protected $productCacheService;

    /**
     * @var CategoryCacheService
     */
    protected $categoryCacheService;

    /**
     * @var HomepageCacheWarmer
     */
    protected $homepageCacheWarmer;

    /**
     * CacheController constructor.
     *
     * @param ProductCacheService $productCacheService
     * @param CategoryCacheService $categoryCacheService
     * @param HomepageCacheWarmer $homepageCacheWarmer
     */
    public function __construct(
        ProductCacheService $productCacheService,
        CategoryCacheService $categoryCacheService,
        HomepageCacheWarmer $homepageCacheWarmer
    ) {
        $this->productCacheService = $productCacheService;
        $this->categoryCacheService = $categoryCacheService;
        $this->homepageCacheWarmer = $homepageCacheWarmer;
    }

    /**
     * Cache yönetim panelini göster
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        $cacheStats = $this->getCacheStats();

        return Inertia::render('Admin/CacheManagement/Index', [
            'cacheStats' => $cacheStats,
        ]);
    }

    /**
     * Belirli bir tür için cache'i temizle
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clear(Request $request)
    {
        $type = $request->input('type');

        switch ($type) {
            case 'products':
                $this->productCacheService->invalidateAllProducts();
                $message = 'Ürün cache\'leri temizlendi.';
                break;
            case 'categories':
                $this->categoryCacheService->invalidateAllCategories();
                $message = 'Kategori cache\'leri temizlendi.';
                break;
            case 'category-tree':
                $this->categoryCacheService->invalidateCategoryTree();
                $message = 'Kategori ağacı cache\'i temizlendi.';
                break;
            case 'routes':
                Artisan::call('route:clear');
                $message = 'Rota cache\'i temizlendi.';
                break;
            case 'views':
                Artisan::call('view:clear');
                $message = 'Görünüm cache\'i temizlendi.';
                break;
            case 'config':
                Artisan::call('config:clear');
                $message = 'Konfigürasyon cache\'i temizlendi.';
                break;
            case 'application':
                Artisan::call('cache:clear');
                $message = 'Uygulama cache\'i temizlendi.';
                break;
            case 'all':
                Cache::flush();
                Artisan::call('route:clear');
                Artisan::call('view:clear');
                Artisan::call('config:clear');
                $message = 'Tüm cache\'ler temizlendi.';
                break;
            default:
                return redirect()->route('admin.cache.index')
                    ->with('error', 'Geçersiz cache türü.');
        }

        return redirect()->route('admin.cache.index')
            ->with('success', $message);
    }

    /**
     * Belirli bir tür için cache'i ısıt
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function warm(Request $request)
    {
        $type = $request->input('type');

        switch ($type) {
            case 'products':
                Artisan::call('cache:warm products');
                $message = 'Ürün cache\'leri ısıtıldı.';
                break;
            case 'categories':
                Artisan::call('cache:warm categories');
                $message = 'Kategori cache\'leri ısıtıldı.';
                break;
            case 'homepage':
                $result = $this->homepageCacheWarmer->warm();
                $message = $result['message'];
                break;
            case 'all':
                Artisan::call('cache:warm all');
                $this->homepageCacheWarmer->warm();
                $message = 'Tüm cache\'ler ısıtıldı.';
                break;
            default:
                return redirect()->route('admin.cache.index')
                    ->with('error', 'Geçersiz cache türü.');
        }

        return redirect()->route('admin.cache.index')
            ->with('success', $message);
    }

    /**
     * Cache istatistiklerini al
     *
     * @return array
     */
    protected function getCacheStats()
    {
        $driver = config('cache.default');
        $prefix = config('cache.prefix');

        // Ürün sayısı
        $productCount = \App\Models\Product::count();

        // Kategori sayısı
        $categoryCount = \App\Models\Category::count();

        // Cache süresi ayarları
        $ttlSettings = config('cache.ttl');

        // Cache türleri
        $cacheTypes = [
            'products' => [
                'name' => 'Ürün Cache',
                'description' => 'Ürünlerle ilgili tüm önbelleği temizler',
                'color' => 'blue',
            ],
            'categories' => [
                'name' => 'Kategori Cache',
                'description' => 'Kategorilerle ilgili tüm önbelleği temizler',
                'color' => 'green',
            ],
            'category-tree' => [
                'name' => 'Kategori Ağacı Cache',
                'description' => 'Kategori ağacı önbelleğini temizler',
                'color' => 'emerald',
            ],
            'routes' => [
                'name' => 'Rota Cache',
                'description' => 'Rota önbelleğini temizler',
                'color' => 'purple',
            ],
            'views' => [
                'name' => 'Görünüm Cache',
                'description' => 'Blade şablonları önbelleğini temizler',
                'color' => 'amber',
            ],
            'config' => [
                'name' => 'Konfigürasyon Cache',
                'description' => 'Konfigürasyon önbelleğini temizler',
                'color' => 'pink',
            ],
            'application' => [
                'name' => 'Uygulama Cache',
                'description' => 'Genel uygulama önbelleğini temizler',
                'color' => 'indigo',
            ],
            'all' => [
                'name' => 'Tüm Cache',
                'description' => 'Tüm önbellek türlerini temizler',
                'color' => 'red',
            ],
        ];

        // Isıtılabilir cache türleri
        $warmableTypes = [
            'products' => [
                'name' => 'Ürün Cache',
                'description' => 'Ürünlerle ilgili tüm önbelleği ısıtır',
                'color' => 'blue',
            ],
            'categories' => [
                'name' => 'Kategori Cache',
                'description' => 'Kategorilerle ilgili tüm önbelleği ısıtır',
                'color' => 'green',
            ],
            'homepage' => [
                'name' => 'Anasayfa Cache',
                'description' => 'Anasayfa için tüm önbelleği ısıtır (vitrin ürünleri, indirimli ürünler, vb.)',
                'color' => 'amber',
            ],
            'all' => [
                'name' => 'Tüm Cache',
                'description' => 'Tüm önbellek türlerini ısıtır',
                'color' => 'red',
            ],
        ];

        // Cache durumu
        $cacheStatus = [
            'driver' => $driver,
            'prefix' => $prefix,
            'product_count' => $productCount,
            'category_count' => $categoryCount,
            'ttl_settings' => $ttlSettings,
            'cache_types' => $cacheTypes,
            'warmable_types' => $warmableTypes,
        ];

        return $cacheStatus;
    }
}
