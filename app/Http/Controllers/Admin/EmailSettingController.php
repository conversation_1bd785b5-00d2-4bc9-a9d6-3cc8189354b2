<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailSetting;
use App\Services\MailchimpService;
use App\Services\MailtrapService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EmailSettingController extends Controller
{
    /**
     * E-posta ayarları sayfasını göster
     */
    public function index()
    {
        $settings = EmailSetting::getDefault();

        return Inertia::render('Admin/EmailSettings/Index', [
            'settings' => $settings,
        ]);
    }

    /**
     * E-posta ayarlarını güncelle
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'mail_driver' => 'required|string|in:smtp,sendmail,mailgun,ses,postmark,log,array',
            'mail_host' => 'nullable|string|max:255',
            'mail_port' => 'nullable|integer',
            'mail_username' => 'nullable|string|max:255',
            'mail_password' => 'nullable|string|max:255',
            'mail_encryption' => 'nullable|string|in:tls,ssl,null',
            'mail_from_address' => 'nullable|email|max:255',
            'mail_from_name' => 'nullable|string|max:255',
            'queue_emails' => 'boolean',
            'emails_per_batch' => 'integer|min:1|max:1000',
            'track_emails' => 'boolean',
            'notification_settings' => 'array',
            // Mailchimp ayarları
            'mailchimp_api_key' => 'nullable|string|max:255',
            'mailchimp_list_id' => 'nullable|string|max:255',
            'mailchimp_enabled' => 'boolean',
            // Mailtrap ayarları
            'mailtrap_enabled' => 'boolean',
            'mailtrap_inbox_id' => 'nullable|string|max:255',
            'mailtrap_api_token' => 'nullable|string|max:255',
        ]);

        // Eğer şifre alanı boşsa, mevcut şifreyi koru
        if (empty($validated['mail_password']) || $validated['mail_password'] === '********') {
            $settings = EmailSetting::getDefault();
            $validated['mail_password'] = $settings->mail_password;
        }

        // Ayarları güncelle
        $settings = EmailSetting::getDefault();
        $settings->update($validated);

        // Ayarları Laravel config'e uygula
        $settings->applyToConfig();

        // Mailchimp ayarlarını kontrol et
        if ($settings->mailchimp_enabled) {
            $mailchimpService = app(MailchimpService::class);
            if (!$mailchimpService->isEnabled()) {
                return redirect()->back()
                    ->with('warning', 'E-posta ayarları güncellendi, ancak Mailchimp entegrasyonu için gerekli bilgiler eksik veya hatalı.');
            }
        }

        // Mailtrap ayarlarını kontrol et
        if ($settings->mailtrap_enabled) {
            $mailtrapService = app(MailtrapService::class);
            if (!$mailtrapService->isEnabled()) {
                return redirect()->back()
                    ->with('warning', 'E-posta ayarları güncellendi, ancak Mailtrap entegrasyonu için gerekli bilgiler eksik veya hatalı.');
            }
        }

        return redirect()->back()
            ->with('success', 'E-posta ayarları başarıyla güncellendi.');
    }

    /**
     * Test e-postası gönder
     */
    public function sendTest(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
        ]);

        // Ayarları yükle
        $settings = EmailSetting::getDefault();
        $settings->applyToConfig();

        // E-posta servisini kullanarak test e-postası gönder
        $emailService = app(\App\Services\EmailService::class);

        // Mailtrap etkinse, test e-postasını Mailtrap üzerinden gönder
        if ($settings->mailtrap_enabled) {
            $mailtrapService = app(MailtrapService::class);
            $mailtrapService->applyMailtrapSettings();
        }

        $result = $emailService->sendRawEmail(
            $validated['email'],
            'Test E-postası',
            '<h1>Test E-postası</h1><p>Bu bir test e-postasıdır. E-posta ayarlarınız doğru çalışıyor.</p>',
            'Test E-postası. Bu bir test e-postasıdır. E-posta ayarlarınız doğru çalışıyor.',
            'Test Kullanıcı',
            false // Kuyruğa almadan doğrudan gönder
        );

        if ($result) {
            // Mailchimp etkinse, test e-postasını Mailchimp listesine ekle
            if ($settings->mailchimp_enabled) {
                $mailchimpService = app(MailchimpService::class);
                if ($mailchimpService->isEnabled()) {
                    $mailchimpService->subscribe(
                        $validated['email'],
                        ['FNAME' => 'Test', 'LNAME' => 'Kullanıcı']
                    );
                }
            }

            return redirect()->back()
                ->with('success', 'Test e-postası başarıyla gönderildi.');
        } else {
            return redirect()->back()
                ->with('error', 'Test e-postası gönderilirken bir hata oluştu.');
        }
    }

    /**
     * Mailchimp listesini test et
     */
    public function testMailchimp(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
        ]);

        $mailchimpService = app(MailchimpService::class);

        if (!$mailchimpService->isEnabled()) {
            return redirect()->back()
                ->with('error', 'Mailchimp entegrasyonu etkin değil veya ayarları eksik.');
        }

        $result = $mailchimpService->subscribe(
            $validated['email'],
            ['FNAME' => 'Test', 'LNAME' => 'Kullanıcı']
        );

        if ($result) {
            return redirect()->back()
                ->with('success', 'E-posta adresi Mailchimp listesine başarıyla eklendi.');
        } else {
            return redirect()->back()
                ->with('error', 'E-posta adresi Mailchimp listesine eklenirken bir hata oluştu.');
        }
    }

    /**
     * Mailtrap bağlantısını test et
     */
    public function testMailtrap()
    {
        $mailtrapService = app(MailtrapService::class);

        if (!$mailtrapService->isEnabled()) {
            return redirect()->back()
                ->with('error', 'Mailtrap entegrasyonu etkin değil veya ayarları eksik.');
        }

        $inboxInfo = $mailtrapService->getInboxInfo();

        if ($inboxInfo) {
            return redirect()->back()
                ->with('success', 'Mailtrap bağlantısı başarılı. Inbox bilgileri alındı.');
        } else {
            return redirect()->back()
                ->with('error', 'Mailtrap bağlantısı kurulamadı. Lütfen ayarlarınızı kontrol edin.');
        }
    }
}
