<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailLog;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EmailLogController extends Controller
{
    /**
     * E-posta loglarını listele
     */
    public function index(Request $request)
    {
        $query = EmailLog::with('template')
            ->orderBy('created_at', 'desc');

        // Filtreleme
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('email_type')) {
            $query->where('email_type', $request->email_type);
        }

        if ($request->has('recipient')) {
            $query->where('recipient_email', 'like', '%' . $request->recipient . '%');
        }

        // Sayfalama
        $logs = $query->paginate(20)
            ->withQueryString();

        // E-posta türleri ve durumları için filtreleme seçenekleri
        $emailTypes = EmailLog::select('email_type')
            ->distinct()
            ->pluck('email_type');

        $statuses = EmailLog::select('status')
            ->distinct()
            ->pluck('status');

        return Inertia::render('Admin/EmailLogs/Index', [
            'logs' => $logs,
            'filters' => [
                'status' => $request->status,
                'email_type' => $request->email_type,
                'recipient' => $request->recipient,
            ],
            'emailTypes' => $emailTypes,
            'statuses' => $statuses,
        ]);
    }

    /**
     * E-posta log detayını göster
     */
    public function show(EmailLog $emailLog)
    {
        $emailLog->load('template');

        return Inertia::render('Admin/EmailLogs/Show', [
            'log' => $emailLog,
        ]);
    }

    /**
     * E-posta loglarını temizle
     */
    public function clear(Request $request)
    {
        // Belirli bir tarihten önceki logları temizle
        $date = now()->subDays($request->days ?? 30);

        $count = EmailLog::where('created_at', '<', $date)->delete();

        return redirect()->back()
            ->with('success', $count . ' adet e-posta logu temizlendi.');
    }

    /**
     * Başarısız e-postaları yeniden gönder
     */
    public function resend(EmailLog $emailLog)
    {
        // E-posta servisini kullanarak e-postayı yeniden gönder
        $emailService = app(\App\Services\EmailService::class);

        $result = $emailService->send(
            $emailLog->email_type,
            $emailLog->recipient_email,
            $emailLog->data ?? [],
            $emailLog->recipient_name
        );

        if ($result) {
            return redirect()->back()
                ->with('success', 'E-posta başarıyla yeniden gönderildi.');
        } else {
            return redirect()->back()
                ->with('error', 'E-posta yeniden gönderilirken bir hata oluştu.');
        }
    }
}
