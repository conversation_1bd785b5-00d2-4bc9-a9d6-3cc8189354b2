<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Inertia\Inertia;

class LocationController extends Controller
{
    /**
     * Konum yönetimi sayfasını göster
     */
    public function index()
    {
        try {
            // Konum verilerinin istatistiklerini al
            $stats = [
                'countries' => LocationService::getCountries()->count(),
                'turkey_states' => LocationService::getTurkeyStates()->count(),
            ];

            // Verilerin import edilip edilmediğini kontrol et
            $isDataImported = $stats['countries'] > 0 && $stats['turkey_states'] > 0;
        } catch (\Exception $e) {
            // Veritabanı bağlantı sorunu varsa varsayılan değerler kullan
            $stats = [
                'countries' => 0,
                'turkey_states' => 0,
            ];
            $isDataImported = false;
        }

        return Inertia::render('Admin/Locations/Index', [
            'stats' => $stats,
            'isDataImported' => $isDataImported,
        ]);
    }

    /**
     * Konum verilerini önbelleğe al
     */
    public function refreshCache(Request $request)
    {
        // Konum verilerini önbelleğe al
        $stats = LocationService::refreshCache();

        return redirect()->back()->with('success', 'Konum verileri başarıyla önbelleğe alındı.');
    }

    /**
     * Konum verilerini import et
     */
    public function importData(Request $request)
    {
        try {
            // JSON dosyalarının varlığını kontrol et
            $countriesJsonPath = storage_path('app/data/countries.json');
            $statesJsonPath = storage_path('app/data/states.json');
            $citiesJsonPath = storage_path('app/data/cities.json');

            if (!file_exists($countriesJsonPath)) {
                return redirect()->back()->with('error', 'Ülkeler JSON dosyası bulunamadı. Lütfen storage/app/data/countries.json dosyasını yükleyin.');
            }

            if (!file_exists($statesJsonPath)) {
                return redirect()->back()->with('error', 'İller JSON dosyası bulunamadı. Lütfen storage/app/data/states.json dosyasını yükleyin.');
            }

            if (!file_exists($citiesJsonPath)) {
                return redirect()->back()->with('error', 'İlçeler JSON dosyası bulunamadı. Lütfen storage/app/data/cities.json dosyasını yükleyin.');
            }

            // Önce ülkeleri import et
            Artisan::call('app:import-countries');

            // Sonra Türkiye'nin illerini ve ilçelerini import et (ID: 225)
            Artisan::call('app:import-states-and-cities', [
                '--country' => [225]
            ]);

            // Önbelleği yenile
            LocationService::refreshCache();

            return redirect()->back()->with('success', 'Konum verileri başarıyla import edildi ve önbelleğe alındı.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Konum verileri import edilirken bir hata oluştu: ' . $e->getMessage());
        }
    }
}
