<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use App\Services\SearchAnalyticsService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;

class SearchController extends Controller
{
    /**
     * Arama analitiği servisi
     */
    protected $searchAnalyticsService;

    /**
     * Yeni bir controller örneği oluştur
     */
    public function __construct(SearchAnalyticsService $searchAnalyticsService)
    {
        $this->searchAnalyticsService = $searchAnalyticsService;
    }
    /**
     * Arama sonuçlarını göster
     */
    public function index(Request $request)
    {
        // Hata ayıklama bilgilerini ekle
        Log::info('SearchController@index called');
        Log::info('Request parameters:', $request->all());

        $query = $request->input('q', '');
        $categoryId = $request->input('category', '');
        $sort = $request->input('sort', 'relevance');
        $perPage = $request->input('per_page', 12);

        // Boş arama sorgusu kontrolü
        if (empty($query)) {
            return redirect()->route('products.index');
        }

        // Ürünleri ara
        $productsQuery = Product::search($query);

        // Kategorileri ara
        $categoriesQuery = Category::search($query);

        // Kategori filtresi
        if (!empty($categoryId)) {
            $productsQuery->where('category_id', $categoryId);
        }

        // Sıralama
        switch ($sort) {
            case 'price_asc':
                $productsQuery->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $productsQuery->orderBy('price', 'desc');
                break;
            case 'name_asc':
                $productsQuery->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $productsQuery->orderBy('name', 'desc');
                break;
            case 'newest':
                $productsQuery->orderBy('created_at', 'desc');
                break;
            case 'relevance':
            default:
                // Varsayılan sıralama (ilgililik)
                break;
        }

        // Ürünleri getir
        $products = $productsQuery->paginate($perPage);

        // Kategorileri getir (en fazla 5 kategori)
        $categories = $categoriesQuery->take(5)->get();

        // Tüm kategorileri getir (filtre için)
        $allCategories = Category::where('status', true)->get();

        // Ürünlere varyant bilgilerini ekle
        $products->getCollection()->transform(function ($product) {
            $product->has_variants = $product->hasVariants();
            $product->default_variant_id = $product->variants->where('is_default', true)->first()?->id;
            $product->seo_url = $product->seo_url;
            $product->url = $product->url;
            return $product;
        });

        // Hata ayıklama bilgilerini ekle
        Log::info('Search results:', [
            'query' => $query,
            'products_count' => $products->count(),
            'categories_count' => $categories->count(),
        ]);

        // Arama sorgusunu kaydet
        $this->searchAnalyticsService->logSearchQuery($request, $products->total());

        // Inertia ile sayfayı render et
        return Inertia::render('Search/Index', [
            'query' => $query,
            'products' => $products,
            'categories' => $categories,
            'allCategories' => $allCategories,
            'filters' => [
                'q' => $query,
                'category' => $categoryId,
                'sort' => $sort,
                'per_page' => $perPage,
            ],
        ]);
    }

    /**
     * Otomatik tamamlama önerileri
     */
    public function autocomplete(Request $request)
    {
        $query = $request->input('q', '');

        if (strlen($query) < 2) {
            return response()->json([
                'products' => [],
                'categories' => [],
            ]);
        }

        // Arama sorgusunu kaydet (otomatik tamamlama için)
        $this->searchAnalyticsService->logSearchQuery($request, 0);

        // Ürünleri ara (en fazla 5 ürün)
        $products = Product::search($query)
            ->take(5)
            ->get(['id', 'name', 'price', 'stock_code', 'slug']);

        // Kategorileri ara (en fazla 3 kategori)
        $categories = Category::search($query)
            ->take(3)
            ->get(['id', 'name', 'slug']);

        // Ürünlere URL ekle
        $products->transform(function ($product) {
            $product->url = $product->url;
            return $product;
        });

        // Kategorilere URL ekle
        $categories->transform(function ($category) {
            $category->url = $category->url;
            return $category;
        });

        return response()->json([
            'products' => $products,
            'categories' => $categories,
        ]);
    }
}
