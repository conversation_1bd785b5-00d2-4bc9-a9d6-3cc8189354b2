<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Services\ProductShowcaseService;
use App\Services\UserRecommendationService;
use App\Services\SeoService;

class HomeController extends Controller
{
    /**
     * Ana sayfayı göster
     */
    public function index(
        Request $request,
        ProductShowcaseService $showcaseService,
        UserRecommendationService $recommendationService,
        SeoService $seoService
    ) {
        // Vitrin ürünleri
        $featuredProducts = $showcaseService->getFeaturedProducts(8);
        
        // İndirimli ürünler
        $onSaleProducts = $showcaseService->getOnSaleProducts(8);
        
        // En çok görüntülenen ürünler
        $mostViewedProducts = $showcaseService->getMostViewedProducts(8);
        
        // Popüler kategoriler
        $popularCategories = $showcaseService->getPopularCategories(4);
        
        // Kullanıc<PERSON> giriş ya<PERSON>ı<PERSON>sa kişiselleştirilmiş içerikler
        $recentlyViewedProducts = collect();
        $favoriteProducts = collect();
        $personalizedRecommendations = collect();
        
        if ($request->user()) {
            // Son görüntülenen ürünler
            $recentlyViewedProducts = $recommendationService->getRecentlyViewedProducts(8);
            
            // Favori ürünler
            $favoriteProducts = $recommendationService->getFavoriteProducts(8);
            
            // Kişiselleştirilmiş öneriler
            $personalizedRecommendations = $recommendationService->getPersonalizedRecommendations(8);
        }
        
        // SEO meta etiketleri
        $meta = $seoService->getHomeMeta();
        
        return Inertia::render('Home', [
            'auth' => [
                'user' => $request->user(),
            ],
            'featuredProducts' => $featuredProducts,
            'onSaleProducts' => $onSaleProducts,
            'mostViewedProducts' => $mostViewedProducts,
            'popularCategories' => $popularCategories,
            'recentlyViewedProducts' => $recentlyViewedProducts,
            'favoriteProducts' => $favoriteProducts,
            'personalizedRecommendations' => $personalizedRecommendations,
            'meta' => $meta,
        ]);
    }
}
