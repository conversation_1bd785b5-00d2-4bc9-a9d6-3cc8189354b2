<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CartController extends Controller
{
    /**
     * Sepeti görüntüle
     */
    public function index(Request $request)
    {
        $cart = $this->getCart($request);

        // Sepeti ve içindeki ürünleri yükle (varyantlar dahil)
        $cart->load(['items.product.variants']);

        // Varyant bilgilerini ekleyelim
        foreach ($cart->items as $item) {
            if (!empty($item->options['variant_id'])) {
                $variantId = $item->options['variant_id'];
                $variant = $item->product->variants->where('id', $variantId)->first();

                if ($variant) {
                    // Varyant özelliklerini ekleyelim
                    $variant->makeVisible(['attribute_values', 'desi']);
                }
            }

            // Ürün desi değeri<PERSON> görünür yap
            $item->product->makeVisible(['desi']);
        }

        return Inertia::render('Cart/Index', [
            'cart' => $cart,
            'subtotal' => $cart->getSubtotalAttribute(),
            'total' => $cart->getTotalAttribute(),
            'totalItems' => $cart->getTotalItemsAttribute(),
        ]);
    }

    /**
     * Sepete ürün ekle
     */
    public function addItem(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'options' => 'nullable|array',
            'variant_id' => 'nullable|exists:product_variants,id',
        ]);

        $cart = $this->getCart($request);
        $product = Product::findOrFail($validated['product_id']);

        // Varyant seçilmiş mi kontrol et
        if (!empty($validated['variant_id'])) {
            // Varyantı bul
            $variant = $product->variants()->findOrFail($validated['variant_id']);

            // Varyant stokta var mı kontrol et
            if ($variant->stock < $validated['quantity'] || $variant->status !== 'in_stock') {
                return back()->with('error', 'Seçtiğiniz varyant stokta yeterli miktarda bulunmamaktadır.');
            }

            // Sepette aynı varyant var mı kontrol et
            $options = ['variant_id' => $variant->id];
            if (!empty($validated['options'])) {
                $options = array_merge($options, $validated['options']);
            }

            $optionsJson = json_encode($options);

            $cartItem = CartItem::where('cart_id', $cart->id)
                ->where('product_id', $product->id)
                ->where('options', $optionsJson)
                ->first();

            if ($cartItem) {
                // Varsa miktarı güncelle
                $newQuantity = $cartItem->quantity + $validated['quantity'];

                // Yeni miktar stoktan fazla mı kontrol et
                if ($variant->stock < $newQuantity) {
                    return back()->with('error', 'Sepetinize eklemek istediğiniz miktar stok miktarını aşıyor.');
                }

                $cartItem->quantity = $newQuantity;
                $cartItem->total = $variant->price * $newQuantity;
                $cartItem->save();
            } else {
                // Yoksa yeni öğe oluştur
                CartItem::create([
                    'cart_id' => $cart->id,
                    'product_id' => $product->id,
                    'product_variant_id' => $variant->id,
                    'quantity' => $validated['quantity'],
                    'price' => $variant->price,
                    'options' => $options,
                    'total' => $variant->price * $validated['quantity'],
                ]);
            }
        } else {
            // Normal ürün için stok kontrolü
            if ($product->stock < $validated['quantity']) {
                return back()->with('error', 'Ürün stokta yeterli miktarda bulunmamaktadır.');
            }

            // Sepette aynı ürün var mı kontrol et
            $options = $validated['options'] ?? null;
            $optionsJson = $options ? json_encode($options) : null;

            $cartItem = CartItem::where('cart_id', $cart->id)
                ->where('product_id', $product->id)
                ->where('options', $optionsJson)
                ->first();

            if ($cartItem) {
                // Varsa miktarı güncelle
                $newQuantity = $cartItem->quantity + $validated['quantity'];

                // Yeni miktar stoktan fazla mı kontrol et
                if ($product->stock < $newQuantity) {
                    return back()->with('error', 'Sepetinize eklemek istediğiniz miktar stok miktarını aşıyor.');
                }

                $cartItem->quantity = $newQuantity;
                $cartItem->total = $product->price * $newQuantity;
                $cartItem->save();
            } else {
                // Yoksa yeni öğe oluştur
                CartItem::create([
                    'cart_id' => $cart->id,
                    'product_id' => $product->id,
                    'quantity' => $validated['quantity'],
                    'price' => $product->price,
                    'options' => $options,
                    'total' => $product->price * $validated['quantity'],
                ]);
            }
        }

        // Kupon varsa yeniden hesapla
        if ($cart->coupon_code) {
            $cart->applyCoupon($cart->coupon_code);
        }

        // Sepet sayısını güncelle
        $count = $cart->getTotalItemsAttribute();

        // Sepet verilerini hazırla
        $cartData = [
            'success' => true,
            'message' => 'Ürün sepete eklendi.',
            'cartCount' => $count,
            'cart' => [
                'items' => $cart->items->map(function($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'subtotal' => $item->price * $item->quantity,
                        'options' => $item->options,
                        'product' => [
                            'id' => $item->product->id,
                            'name' => $item->product->name,
                            'image' => $item->product->image,
                            'desi' => $item->product->desi ?? 1,
                            'variants' => $item->product->variants->map(function($variant) {
                                return [
                                    'id' => $variant->id,
                                    'attribute_values' => $variant->attribute_values,
                                    'formatted_attributes' => $variant->formatted_attributes,
                                    'desi' => $variant->desi ?? 1,
                                ];
                            })->values()->all(),
                        ]
                    ];
                }),
                'subtotal' => $cart->getSubtotalAttribute(),
                'total' => $cart->getTotalAttribute(),
                'discount_amount' => $cart->discount_amount,
                'coupon_code' => $cart->coupon_code,
            ]
        ];

        if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
            return response()->json($cartData);
        }

        return back()->with([
            'success' => 'Ürün sepete eklendi.',
            'cartCount' => $count
        ]);
    }

    /**
     * Sepetteki ürünün miktarını güncelle
     */
    public function updateItem(Request $request, CartItem $cartItem)
    {
        $validated = $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        // Kullanıcının kendi sepetindeki ürünü mü kontrol et
        $cart = $this->getCart($request);
        if ($cartItem->cart_id !== $cart->id) {
            return back()->with('error', 'Bu ürünü güncelleme yetkiniz yok.');
        }

        // Ürün stokta var mı kontrol et
        $product = $cartItem->product;
        if ($product->stock < $validated['quantity']) {
            return back()->with('error', 'Ürün stokta yeterli miktarda bulunmamaktadır.');
        }

        $cartItem->quantity = $validated['quantity'];
        $cartItem->total = $cartItem->price * $validated['quantity'];
        $cartItem->save();

        // Kupon varsa yeniden hesapla
        if ($cart->coupon_code) {
            $cart->applyCoupon($cart->coupon_code);
        }

        // Sepet sayısını güncelle
        $count = $cart->getTotalItemsAttribute();

        // Sepet verilerini hazırla
        $cartData = [
            'success' => true,
            'message' => 'Sepet güncellendi.',
            'cartCount' => $count,
            'cart' => [
                'items' => $cart->items->map(function($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'subtotal' => $item->price * $item->quantity,
                        'options' => $item->options,
                        'product' => [
                            'id' => $item->product->id,
                            'name' => $item->product->name,
                            'image' => $item->product->image,
                            'desi' => $item->product->desi ?? 1,
                            'variants' => $item->product->variants->map(function($variant) {
                                return [
                                    'id' => $variant->id,
                                    'attribute_values' => $variant->attribute_values,
                                    'formatted_attributes' => $variant->formatted_attributes,
                                    'desi' => $variant->desi ?? 1,
                                ];
                            })->values()->all(),
                        ]
                    ];
                }),
                'subtotal' => $cart->getSubtotalAttribute(),
                'total' => $cart->getTotalAttribute(),
                'discount_amount' => $cart->discount_amount,
                'coupon_code' => $cart->coupon_code,
            ]
        ];

        if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
            return response()->json($cartData);
        }

        return back()->with([
            'success' => 'Sepet güncellendi.',
            'cartCount' => $count
        ]);
    }

    /**
     * Sepetten ürün çıkar
     */
    public function removeItem(Request $request, CartItem $cartItem)
    {
        // Kullanıcının kendi sepetindeki ürünü mü kontrol et
        $cart = $this->getCart($request);
        if ($cartItem->cart_id !== $cart->id) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => 'Bu ürünü silme yetkiniz yok.'], 403);
            }
            return back()->with('error', 'Bu ürünü silme yetkiniz yok.');
        }

        $cartItem->delete();

        // Kupon varsa yeniden hesapla
        if ($cart->coupon_code) {
            $cart->applyCoupon($cart->coupon_code);
        }

        // Sepet sayısını güncelle
        $count = $cart->getTotalItemsAttribute();

        // Sepet verilerini hazırla
        $cartData = [
            'success' => true,
            'message' => 'Ürün sepetten çıkarıldı.',
            'cartCount' => $count,
            'cart' => [
                'items' => $cart->items->map(function($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'subtotal' => $item->price * $item->quantity,
                        'options' => $item->options,
                        'product' => [
                            'id' => $item->product->id,
                            'name' => $item->product->name,
                            'image' => $item->product->image,
                            'desi' => $item->product->desi ?? 1,
                            'variants' => $item->product->variants->map(function($variant) {
                                return [
                                    'id' => $variant->id,
                                    'attribute_values' => $variant->attribute_values,
                                    'formatted_attributes' => $variant->formatted_attributes,
                                    'desi' => $variant->desi ?? 1,
                                ];
                            })->values()->all(),
                        ]
                    ];
                }),
                'subtotal' => $cart->getSubtotalAttribute(),
                'total' => $cart->getTotalAttribute(),
                'discount_amount' => $cart->discount_amount,
                'coupon_code' => $cart->coupon_code,
            ]
        ];

        if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
            return response()->json($cartData);
        }

        return back()->with([
            'success' => 'Ürün sepetten çıkarıldı.',
            'cartCount' => $count
        ]);
    }

    /**
     * Sepeti boşalt
     */
    public function clear(Request $request)
    {
        $cart = $this->getCart($request);

        // Tüm ürünleri sil
        $cart->items()->delete();

        // Kuponu kaldır
        $cart->removeCoupon();

        // Sepet verilerini hazırla
        $cartData = [
            'success' => true,
            'message' => 'Sepetiniz boşaltıldı.',
            'cartCount' => 0,
            'cart' => [
                'items' => [],
                'subtotal' => 0,
                'total' => 0,
                'discount_amount' => 0,
                'coupon_code' => null,
            ]
        ];

        if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
            return response()->json($cartData);
        }

        return back()->with([
            'success' => 'Sepetiniz boşaltıldı.',
            'cartCount' => 0
        ]);
    }

    /**
     * Kuponu uygula
     */
    public function applyCoupon(Request $request)
    {
        $validated = $request->validate([
            'coupon_code' => 'required|string|max:50',
        ]);

        $cart = $this->getCart($request);

        // Sepet boş mu kontrol et
        if ($cart->items->isEmpty()) {
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Kupon uygulamak için sepetinizde ürün olmalıdır.'], 400);
            }
            return back()->with('error', 'Kupon uygulamak için sepetinizde ürün olmalıdır.');
        }

        $result = $cart->applyCoupon($validated['coupon_code']);

        if ($result) {
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Kupon başarıyla uygulandı.',
                    'cart' => [
                        'subtotal' => $cart->getSubtotalAttribute(),
                        'total' => $cart->getTotalAttribute(),
                        'discount_amount' => $cart->discount_amount,
                        'coupon_code' => $cart->coupon_code,
                    ]
                ]);
            }
            return back()->with('success', 'Kupon başarıyla uygulandı.');
        } else {
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Geçersiz veya süresi dolmuş kupon.'], 400);
            }
            return back()->with('error', 'Geçersiz veya süresi dolmuş kupon.');
        }
    }

    /**
     * Kuponu kaldır
     */
    public function removeCoupon(Request $request)
    {
        $cart = $this->getCart($request);
        $cart->removeCoupon();

        // Sepet sayısını güncelle
        $count = $cart->getTotalItemsAttribute();

        if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Kupon kaldırıldı.',
                'cartCount' => $count,
                'cart' => [
                    'subtotal' => $cart->getSubtotalAttribute(),
                    'total' => $cart->getTotalAttribute(),
                    'discount_amount' => 0,
                    'coupon_code' => null,
                ]
            ]);
        }

        return back()->with([
            'success' => 'Kupon kaldırıldı.',
            'cartCount' => $count
        ]);
    }

    /**
     * Sepet ürün sayısını döndür
     */
    public function getCartCount(Request $request)
    {
        $cart = $this->getCart($request);
        $count = $cart->getTotalItemsAttribute();

        return response()->json(['count' => $count]);
    }

    /**
     * Kullanıcının sepetini getir veya oluştur
     */
    protected function getCart(Request $request)
    {
        if (Auth::check()) {
            // Giriş yapmış kullanıcı için sepeti bul veya oluştur
            $cart = Cart::firstOrCreate(
                ['user_id' => Auth::id()],
                ['session_id' => null]
            );

            // Oturum sepeti varsa, kullanıcı sepetine taşı
            $sessionId = $request->session()->getId();
            $sessionCart = Cart::where('session_id', $sessionId)->first();

            if ($sessionCart && $sessionCart->id !== $cart->id) {
                // Oturum sepetindeki ürünleri kullanıcı sepetine taşı
                foreach ($sessionCart->items as $item) {
                    $existingItem = CartItem::where('cart_id', $cart->id)
                        ->where('product_id', $item->product_id)
                        ->where('options', $item->options)
                        ->first();

                    if ($existingItem) {
                        // Aynı ürün varsa miktarı güncelle
                        $existingItem->quantity += $item->quantity;
                        $existingItem->save();
                    } else {
                        // Yoksa yeni öğe oluştur
                        CartItem::create([
                            'cart_id' => $cart->id,
                            'product_id' => $item->product_id,
                            'quantity' => $item->quantity,
                            'price' => $item->price,
                            'options' => $item->options,
                        ]);
                    }
                }

                // Oturum sepetini sil
                $sessionCart->items()->delete();
                $sessionCart->delete();
            }

            return $cart;
        } else {
            // Giriş yapmamış kullanıcı için oturum sepetini bul veya oluştur
            $sessionId = $request->session()->getId();
            return Cart::firstOrCreate(
                ['session_id' => $sessionId],
                ['user_id' => null]
            );
        }
    }
}
