<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class LocationController extends Controller
{
    /**
     * Tüm ülkeleri getir
     */
    public function countries()
    {
        $countries = LocationService::getCountries();

        if ($countries->isEmpty()) {
            // Önbellek boşsa, yeniden yükle
            LocationService::refreshCache();
            $countries = LocationService::getCountries();
        }

        return response()->json($countries);
    }

    /**
     * Belirli bir ülkenin illerini getir
     */
    public function states($countryId)
    {
        $states = LocationService::getStatesByCountry($countryId);

        if ($states->isEmpty()) {
            // Önbellek boşsa, yeniden yükle
            LocationService::refreshCache();
            $states = LocationService::getStatesByCountry($countryId);
        }

        return response()->json($states);
    }

    /**
     * Türkiye'nin illerini getir
     */
    public function turkeyStates()
    {
        $states = LocationService::getTurkeyStates();

        if ($states->isEmpty()) {
            // Önbellek boşsa, yeniden yükle
            LocationService::refreshCache();
            $states = LocationService::getTurkeyStates();
        }

        return response()->json($states);
    }

    /**
     * Belirli bir ilin ilçelerini getir
     *
     * @param int $stateId İl ID'si
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cities($stateId, Request $request)
    {
        // Parametreleri al
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 100);
        $search = $request->input('search');

        // Sayfa başına kayıt sayısını sınırla
        $perPage = min($perPage, 500);

        // İlçeleri getir
        $cities = LocationService::getCitiesByState($stateId, $page, $perPage, $search);

        if ($cities->isEmpty() && $page == 1 && !$search) {
            // Önbellek boşsa ve ilk sayfa isteniyorsa, yeniden yükle
            LocationService::refreshCache();
            $cities = LocationService::getCitiesByState($stateId, $page, $perPage, $search);
        }

        return response()->json($cities);
    }

    /**
     * Türkiye'nin belirli bir ilinin ilçelerini getir
     *
     * @param int $stateId İl ID'si
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function turkeyCities($stateId, Request $request)
    {
        // Parametreleri al
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 100);
        $search = $request->input('search');

        // Sayfa başına kayıt sayısını sınırla
        $perPage = min($perPage, 500);

        // İlçeleri getir - Türkiye için özel metot kullanıyoruz
        $cities = LocationService::getTurkeyCitiesByState($stateId, $page, $perPage, $search);

        if ($cities->isEmpty() && $page == 1 && !$search) {
            // Önbellek boşsa ve ilk sayfa isteniyorsa, yeniden yükle
            LocationService::refreshCache();
            $cities = LocationService::getTurkeyCitiesByState($stateId, $page, $perPage, $search);
        }

        return response()->json($cities);
    }

    /**
     * Konum verilerini önbelleğe al (sadece yöneticiler için)
     */
    public function refreshCache()
    {
        // Yetkilendirme kontrolü
        if (!Gate::allows('manage-settings')) {
            return response()->json(['error' => 'Bu işlem için yetkiniz yok.'], 403);
        }

        $stats = LocationService::refreshCache();

        return response()->json([
            'message' => 'Konum verileri başarıyla güncellendi',
            'stats' => $stats
        ]);
    }
}
