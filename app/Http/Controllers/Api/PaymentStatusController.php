<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentStatusController extends Controller
{
    /**
     * Ödeme durumunu kontrol et
     */
    public function checkStatus(Request $request)
    {
        try {
            $orderNumber = $request->input('order_number');
            
            if (!$orderNumber) {
                return response()->json([
                    'success' => false,
                    'message' => 'Sipariş numarası gerekli'
                ], 400);
            }
            
            // Siparişi bul
            $order = Order::where('order_number', $orderNumber)->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Sipariş bulunamadı'
                ], 404);
            }
            
            // Ödeme durumunu kontrol et
            $paymentStatus = $order->payment_status;
            
            Log::info('Ödeme durumu kontrol edildi', [
                'order_number' => $orderNumber,
                'payment_status' => $paymentStatus
            ]);
            
            return response()->json([
                'success' => true,
                'payment_status' => $paymentStatus,
                'is_paid' => $paymentStatus === 'paid',
                'order_number' => $orderNumber
            ]);
        } catch (\Exception $e) {
            Log::error('Ödeme durumu kontrol edilirken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Ödeme durumu kontrol edilirken bir hata oluştu'
            ], 500);
        }
    }
}
