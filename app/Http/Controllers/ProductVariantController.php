<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Attribute;
use App\Services\VariantGeneratorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProductVariantController extends Controller
{
    protected $variantGenerator;

    public function __construct(VariantGeneratorService $variantGenerator)
    {
        $this->variantGenerator = $variantGenerator;
    }

    /**
     * Display variants for a product
     */
    public function index(Product $product)
    {
        $product->load('variants', 'attributes');

        $attributes = Attribute::where('is_variant', true)
            ->with('values')
            ->orderBy('position')
            ->get();

        return Inertia::render('Admin/Products/Variants/Index', [
            'product' => $product,
            'attributes' => $attributes,
        ]);
    }

    /**
     * Show the form for creating variants
     */
    public function create(Product $product)
    {
        $product->load('attributes');

        $attributes = Attribute::where('is_variant', true)
            ->with('values')
            ->orderBy('position')
            ->get();

        return Inertia::render('Admin/Products/Variants/Create', [
            'product' => $product,
            'attributes' => $attributes,
            'productAttributes' => $product->attributes->pluck('id')->toArray(),
        ]);
    }

    /**
     * Store product attributes and generate variants
     */
    public function store(Request $request, Product $product)
    {
        $validated = $request->validate([
            'attributes' => 'required|array',
            'attributes.*' => 'exists:attributes,id',
            'variant_attributes' => 'required|array',
            'variant_attributes.*' => 'exists:attributes,id',
            'attribute_values' => 'required|array',
            'weight' => 'nullable|numeric|min:0',
            'desi' => 'nullable|numeric|min:0',
            'additional_price' => 'nullable|numeric|min:0',
            'stock' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_on_sale' => 'boolean',
            'sale_price' => 'nullable|numeric|min:0',
            'sale_starts_at' => 'nullable|date',
            'sale_ends_at' => 'nullable|date|after_or_equal:sale_starts_at',
        ]);

        DB::beginTransaction();

        try {
            // Sync product attributes
            $attributeData = [];
            foreach ($validated['attributes'] as $attributeId) {
                $isVariantGenerator = in_array($attributeId, $validated['variant_attributes']);
                $attributeData[$attributeId] = [
                    'is_variant_generator' => $isVariantGenerator,
                    'is_required' => true,
                ];
            }

            $product->attributes()->sync($attributeData);

            // Generate variants
            if (!empty($validated['variant_attributes'])) {
                // Seçilen değerleri kontrol et
                $selectedValues = [];
                foreach ($validated['attribute_values'] as $attributeCode => $values) {
                    $selectedValues[$attributeCode] = array_keys($values);
                }

                $this->variantGenerator->generateVariants($product, $validated['attribute_values'], $selectedValues);
            }

            DB::commit();

            return redirect()->route('admin.products.variants.index', $product)
                ->with('success', 'Ürün varyantları başarıyla oluşturuldu.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'Varyant oluşturma sırasında bir hata oluştu: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show the form for editing a variant
     */
    public function edit(Product $product, ProductVariant $variant)
    {
        return Inertia::render('Admin/Products/Variants/Edit', [
            'product' => $product,
            'variant' => $variant,
            'formattedAttributes' => $variant->formatted_attributes,
        ]);
    }

    /**
     * Update a variant
     */
    public function update(Request $request, Product $product, ProductVariant $variant)
    {
        $validated = $request->validate([
            'sku' => 'required|string|max:255|unique:product_variants,sku,' . $variant->id,
            'additional_price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'status' => 'required|in:in_stock,out_of_stock,disabled',
            'is_default' => 'boolean',
            'image' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:255',
            'slug' => 'nullable|string|max:255',
            'weight' => 'nullable|numeric|min:0',
            'desi' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'is_on_sale' => 'boolean',
            'sale_price' => 'nullable|numeric|min:0',
            'sale_starts_at' => 'nullable|date',
            'sale_ends_at' => 'nullable|date|after_or_equal:sale_starts_at',
        ]);

        // Slug oluşturma
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($product->name . '-' . $validated['sku']);
        } else {
            $validated['slug'] = Str::slug($validated['slug']);
        }

        DB::beginTransaction();

        try {
            // Eğer bu varyant varsayılan olarak işaretlenmişse, diğer varyantların varsayılan durumunu kaldır
            if (isset($validated['is_default']) && $validated['is_default']) {
                ProductVariant::where('product_id', $product->id)
                    ->where('id', '!=', $variant->id)
                    ->update(['is_default' => false]);
            }

            $variant->update($validated);

            DB::commit();

            return redirect()->route('admin.products.variants.index', $product)
                ->with('success', 'Varyant başarıyla güncellendi.');
        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'Varyant güncellenirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Update variant stock
     */
    public function updateStock(Request $request, Product $product, ProductVariant $variant)
    {
        $validated = $request->validate([
            'stock' => 'required|integer|min:0',
        ]);

        // Eski stok değerini kaydet
        $oldStock = $variant->stock;

        // Stok güncelleme
        $variant->stock = $validated['stock'];

        // Stok durumunu güncelle
        if ($variant->stock > 0) {
            $variant->status = 'in_stock';
        } else {
            $variant->status = 'out_of_stock';
        }

        $variant->save();

        return redirect()->back()
            ->with('success', 'Stok miktarı başarıyla güncellendi.');
    }

    /**
     * Bulk update variant stock
     */
    public function bulkUpdateStock(Request $request, Product $product)
    {
        Log::info('Bulk stock update request received', [
            'product_id' => $product->id,
            'request_data' => $request->all(),
            'request_method' => $request->method(),
            'request_path' => $request->path(),
            'request_url' => $request->url(),
        ]);

        try {
            // JSON olarak gönderilmiş varyantları işle
            $variantsJson = $request->input('variants');
            $variants = is_string($variantsJson) ? json_decode($variantsJson, true) : $request->input('variants');

            Log::info('Parsed variants data', ['variants' => $variants]);

            if (!is_array($variants)) {
                throw new \Exception('Geçersiz varyant verisi');
            }

            // Varyantları doğrula
            foreach ($variants as $variant) {
                if (!isset($variant['id']) || !isset($variant['stock'])) {
                    throw new \Exception('Eksik varyant verisi');
                }

                if (!is_numeric($variant['stock']) || $variant['stock'] < 0) {
                    throw new \Exception('Geçersiz stok miktarı');
                }
            }

            DB::beginTransaction();

            foreach ($variants as $variantData) {
                // Sadece bu ürüne ait varyantları güncelle
                $variant = ProductVariant::where('id', $variantData['id'])
                    ->where('product_id', $product->id)
                    ->first();

                if (!$variant) {
                    Log::warning('Variant not found or not belongs to this product', [
                        'variant_id' => $variantData['id'],
                        'product_id' => $product->id
                    ]);
                    continue; // Bu varyantı atla
                }

                Log::info('Updating variant stock', [
                    'variant_id' => $variant->id,
                    'old_stock' => $variant->stock,
                    'new_stock' => $variantData['stock']
                ]);

                // Stok güncelleme
                $variant->stock = $variantData['stock'];

                // Stok durumunu güncelle
                if ($variant->stock > 0) {
                    $variant->status = 'in_stock';
                } else {
                    $variant->status = 'out_of_stock';
                }

                $variant->save();
            }

            DB::commit();
            Log::info('Bulk stock update completed successfully');

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Stok miktarları başarıyla güncellendi.']);
            }

            return redirect()->back()
                ->with('success', 'Stok miktarları başarıyla güncellendi.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error during bulk stock update', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Stok güncelleme sırasında bir hata oluştu: ' . $e->getMessage()], 500);
            }

            return redirect()->back()
                ->with('error', 'Stok güncelleme sırasında bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Bulk update variant status
     */
    public function bulkUpdateStatus(Request $request, Product $product)
    {
        Log::info('Bulk status update request received', [
            'product_id' => $product->id,
            'request_data' => $request->all(),
            'request_method' => $request->method(),
            'request_path' => $request->path(),
        ]);

        try {
            // JSON olarak gönderilmiş varyant ID'lerini işle
            $variantIdsJson = $request->input('variant_ids');
            $variantIds = is_string($variantIdsJson) ? json_decode($variantIdsJson, true) : $request->input('variant_ids');

            if (!is_array($variantIds)) {
                throw new \Exception('Geçersiz varyant ID listesi');
            }

            $status = $request->input('status');
            if (!in_array($status, ['in_stock', 'out_of_stock', 'disabled'])) {
                throw new \Exception('Geçersiz durum değeri');
            }

            DB::beginTransaction();

            foreach ($variantIds as $variantId) {
                $variant = ProductVariant::where('id', $variantId)
                    ->where('product_id', $product->id)
                    ->first();

                if ($variant) {
                    $variant->status = $status;
                    $variant->save();
                }
            }

            DB::commit();

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Varyant durumları başarıyla güncellendi.']);
            }

            return redirect()->back()
                ->with('success', 'Varyant durumları başarıyla güncellendi.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error during bulk status update', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Varyant durumları güncellenirken bir hata oluştu: ' . $e->getMessage()], 500);
            }

            return redirect()->back()
                ->with('error', 'Varyant durumları güncellenirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Bulk update variant price
     */
    public function bulkUpdatePrice(Request $request, Product $product)
    {
        Log::info('Bulk price update request received', [
            'product_id' => $product->id,
            'request_data' => $request->all(),
            'request_method' => $request->method(),
            'request_path' => $request->path(),
        ]);

        try {
            // JSON olarak gönderilmiş varyant ID'lerini işle
            $variantIdsJson = $request->input('variant_ids');
            $variantIds = is_string($variantIdsJson) ? json_decode($variantIdsJson, true) : $request->input('variant_ids');

            if (!is_array($variantIds)) {
                throw new \Exception('Geçersiz varyant ID listesi');
            }

            $priceType = $request->input('price_type');
            if (!in_array($priceType, ['fixed', 'increase', 'decrease'])) {
                throw new \Exception('Geçersiz fiyat tipi');
            }

            $price = $request->input('price');
            if (!is_numeric($price) || $price < 0) {
                throw new \Exception('Geçersiz fiyat değeri');
            }

            DB::beginTransaction();

            foreach ($variantIds as $variantId) {
                $variant = ProductVariant::where('id', $variantId)
                    ->where('product_id', $product->id)
                    ->first();

                if (!$variant) continue;

                switch ($priceType) {
                    case 'fixed':
                        $variant->additional_price = $price;
                        break;
                    case 'increase':
                        $variant->additional_price += $price;
                        break;
                    case 'decrease':
                        $variant->additional_price = max(0, $variant->additional_price - $price);
                        break;
                }

                $variant->save();
            }

            DB::commit();

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Varyant fiyatları başarıyla güncellendi.']);
            }

            return redirect()->back()
                ->with('success', 'Varyant fiyatları başarıyla güncellendi.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error during bulk price update', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Varyant fiyatları güncellenirken bir hata oluştu: ' . $e->getMessage()], 500);
            }

            return redirect()->back()
                ->with('error', 'Varyant fiyatları güncellenirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Set default variant
     */
    public function setDefault(Request $request, Product $product, ProductVariant $variant)
    {
        try {
            DB::beginTransaction();

            // Önce tüm varyantların varsayılan durumunu kaldır
            ProductVariant::where('product_id', $product->id)
                ->update(['is_default' => false]);

            // Seçilen varyantı varsayılan olarak ayarla
            $variant->is_default = true;
            $variant->save();

            DB::commit();

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Varsayılan varyant başarıyla güncellendi.']);
            }

            return redirect()->back()
                ->with('success', 'Varsayılan varyant başarıyla güncellendi.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error setting default variant', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Varsayılan varyant güncellenirken bir hata oluştu: ' . $e->getMessage()], 500);
            }

            return redirect()->back()
                ->with('error', 'Varsayılan varyant güncellenirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete variants
     */
    public function bulkDestroy(Request $request, Product $product)
    {
        Log::info('Bulk delete request received', [
            'product_id' => $product->id,
            'request_data' => $request->all(),
            'request_method' => $request->method(),
            'request_path' => $request->path(),
        ]);

        try {
            // JSON olarak gönderilmiş varyant ID'lerini işle
            $variantIdsJson = $request->input('variant_ids');
            $variantIds = is_string($variantIdsJson) ? json_decode($variantIdsJson, true) : $request->input('variant_ids');

            if (!is_array($variantIds)) {
                throw new \Exception('Geçersiz varyant ID listesi');
            }

            DB::beginTransaction();

            // Sadece bu ürüne ait varyantları sil
            $deletedCount = ProductVariant::whereIn('id', $variantIds)
                ->where('product_id', $product->id)
                ->delete();

            DB::commit();

            Log::info('Variants deleted', ['count' => $deletedCount]);

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Seçili varyantlar başarıyla silindi.']);
            }

            return redirect()->back()
                ->with('success', 'Seçili varyantlar başarıyla silindi.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error during bulk delete', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // AJAX isteği mi kontrol et
            if ($request->ajax() || $request->wantsJson() || $request->expectsJson()) {
                return response()->json(['error' => 'Varyantlar silinirken bir hata oluştu: ' . $e->getMessage()], 500);
            }

            return redirect()->back()
                ->with('error', 'Varyantlar silinirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Remove a variant
     */
    public function destroy(Product $product, ProductVariant $variant)
    {
        $variant->delete();

        return redirect()->route('admin.products.variants.index', $product)
            ->with('success', 'Varyant başarıyla silindi.');
    }
}
