<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\BankAccount;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Services\IyzicoService;

class PaymentController extends Controller
{
    /**
     * İyzico ile ödeme işlemi
     */
    public function processIyzico(Request $request, IyzicoService $iyzicoService)
    {
        // Form verilerini doğrula
        $validated = $request->validate([
            'billing_name' => 'required|string|max:255',
            'billing_email' => 'required|email|max:255',
            'billing_phone' => 'required|string|max:20',
            'billing_address' => 'required|string|max:500',
            'billing_city' => 'required|string|max:100',
            'billing_state' => 'required|string|max:100',
            'billing_zipcode' => 'required|string|max:20',
            'billing_country' => 'required|string|max:100',
            'shipping_name' => 'required|string|max:255',
            'shipping_email' => 'required|email|max:255',
            'shipping_phone' => 'required|string|max:20',
            'shipping_address' => 'required|string|max:500',
            'shipping_city' => 'required|string|max:100',
            'shipping_state' => 'required|string|max:100',
            'shipping_zipcode' => 'required|string|max:20',
            'shipping_country' => 'required|string|max:100',
            'notes' => 'nullable|string|max:1000',
            'card_holder_name' => 'required|string|max:255',
            'card_number' => 'required|string|size:16',
            'expiry_month' => 'required|string|size:2',
            'expiry_year' => 'required|string|size:2',
            'cvc' => 'required|string|size:3',
            'use_3d_secure' => 'required|string|in:true,false',
        ]);

        // Sepeti getir
        $cart = $this->getCart($request);

        // Sepet boşsa hata döndür
        if ($cart->items->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Sepetiniz boş. Ödeme yapılamaz.');
        }

        try {
            // Veritabanı işlemini başlat
            DB::beginTransaction();

            // Sipariş oluştur
            $order = new Order();
            $order->user_id = Auth::id();
            $order->order_number = 'ORD-' . strtoupper(Str::random(8));
            $order->status = OrderStatus::PENDING->value;
            $order->payment_status = PaymentStatus::PENDING->value;
            $order->payment_method = 'credit_card';
            $order->shipping_method = 'standard';
            $order->total_amount = $cart->getTotalAttribute();
            $order->shipping_cost = 0; // Şimdilik ücretsiz kargo
            $order->tax_amount = $cart->getTotalAttribute() * 0.18; // %18 KDV
            $order->discount_amount = $cart->discount_amount;
            $order->coupon_code = $cart->coupon_code;

            // Fatura ve teslimat bilgileri
            $order->billing_name = $validated['billing_name'];
            $order->billing_email = $validated['billing_email'];
            $order->billing_phone = $validated['billing_phone'];
            $order->billing_address = $validated['billing_address'];
            $order->billing_city = $validated['billing_city'];
            $order->billing_state = $validated['billing_state'];
            $order->billing_zipcode = $validated['billing_zipcode'];
            $order->billing_country = $validated['billing_country'];

            $order->shipping_name = $validated['shipping_name'];
            $order->shipping_email = $validated['shipping_email'];
            $order->shipping_phone = $validated['shipping_phone'];
            $order->shipping_address = $validated['shipping_address'];
            $order->shipping_city = $validated['shipping_city'];
            $order->shipping_state = $validated['shipping_state'];
            $order->shipping_zipcode = $validated['shipping_zipcode'];
            $order->shipping_country = $validated['shipping_country'];

            $order->notes = $validated['notes'];

            $order->save();

            // Sipariş öğelerini oluştur
            foreach ($cart->items as $item) {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->product_id = $item->product_id;
                $orderItem->product_name = $item->product->name;
                $orderItem->price = $item->price;
                $orderItem->quantity = $item->quantity;
                $orderItem->subtotal = $item->price * $item->quantity;
                $orderItem->options = $item->options;
                $orderItem->save();
            }

            // Kredi kartı bilgilerini hazırla
            $cardData = [
                'holder_name' => $validated['card_holder_name'],
                'number' => $validated['card_number'],
                'expiry_month' => $validated['expiry_month'],
                'expiry_year' => $validated['expiry_year'],
                'cvc' => $validated['cvc'],
            ];

            // 3D Secure kullanılacak mı?
            // String olarak "true" veya "false" değeri bekliyoruz
            $use3DSecureValue = $request->input('use_3d_secure');
            $use3DSecure = $use3DSecureValue === 'true';

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('3D Secure değeri', [
                'raw_value' => $use3DSecureValue,
                'type' => gettype($use3DSecureValue),
                'parsed_value' => $use3DSecure,
                'all_data' => $request->all()
            ]);

            if ($use3DSecure) {
                // 3D Secure ödeme işlemini başlat
                $paymentResult = $iyzicoService->initiate3DPayment($order, $cardData);

                if (!$paymentResult['success']) {
                    // Ödeme başarısız olduysa işlemi geri al
                    DB::rollBack();
                    return redirect()->route('checkout')->with('error', $paymentResult['message']);
                }

                // Veritabanı işlemini tamamla
                DB::commit();

                // Sipariş numarasını session'a kaydet
                session(['last_order_number' => $order->order_number]);

                // Debug için log ekle
                \Illuminate\Support\Facades\Log::info('3D Secure HTML içeriği döndürülüyor', [
                    'html_length' => strlen($paymentResult['html_content']),
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                ]);

                // HTML içeriğini JSON olarak döndür
                $response = response()->json([
                    'success' => true,
                    'html_content' => $paymentResult['html_content'],
                    'order_number' => $order->order_number,
                    'popup' => true // Popup olarak açılması gerektiğini belirt
                ]);

                // Debug için log ekle
                \Illuminate\Support\Facades\Log::info('3D Secure yanıt oluşturuldu', [
                    'status' => $response->getStatusCode(),
                    'headers' => $response->headers->all(),
                    'html_content_length' => strlen($paymentResult['html_content']),
                ]);

                return $response;
            } else {
                // Normal ödeme işlemini başlat
                $paymentResult = $iyzicoService->processPayment($order, $cardData);

                if (!$paymentResult['success']) {
                    // Ödeme başarısız olduysa işlemi geri al
                    DB::rollBack();
                    return redirect()->route('checkout')->with('error', $paymentResult['message']);
                }

                // Ödeme başarılı olduysa sipariş durumunu güncelle
                $order->status = OrderStatus::PROCESSING->value;
                $order->payment_status = PaymentStatus::PAID->value;
                $order->save();

                // Sipariş notu ekle
                $order->addNote(
                    'Sipariş oluşturuldu. Ödeme yöntemi: Kredi Kartı (iyzico). İşlem ID: ' . ($paymentResult['transaction_id'] ?? 'N/A'),
                    Auth::id(),
                    false,
                    'system'
                );

                // Sepeti temizle
                $cart->items()->delete();
                $cart->removeCoupon();

                // Sepet temizleme işlemini logla
                \Illuminate\Support\Facades\Log::info('Sepet temizlendi (processIyzico)', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'user_id' => $order->user_id,
                    'session_id' => session()->getId()
                ]);

                // Veritabanı işlemini tamamla
                DB::commit();

                // Başarılı sayfasına yönlendir
                return redirect()->route('checkout.success', ['order_number' => $order->order_number]);
            }

        } catch (\Exception $e) {
            // Hata durumunda işlemi geri al
            DB::rollBack();

            return redirect()->route('checkout')->with('error', 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * İyzico 3D ödeme callback
     */
    public function iyzicoCallback(Request $request, IyzicoService $iyzicoService)
    {
        try {
            // İyzico'dan gelen verileri işle
            $callbackData = $request->all();

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('İyzico callback işlemi', [
                'data' => $callbackData,
                'headers' => $request->headers->all(),
                'is_ajax' => $request->ajax(),
                'wants_json' => $request->wantsJson(),
                'user_agent' => $request->header('User-Agent')
            ]);

            // Ödeme sonucunu işle
            $result = $iyzicoService->handleCallback($callbackData);

            if (!$result['success']) {
                // İframe içinde çalışıyorsa özel bir HTML sayfası döndür
                if ($this->isIframeRequest($request)) {
                    return view('payment.callback-error', [
                        'message' => $result['message'],
                        'redirect_url' => route('checkout')
                    ]);
                }

                return redirect()->route('checkout')->with('error', $result['message']);
            }

            // Sipariş numarasını al
            $orderNumber = $result['order_number'];

            // Siparişi bul
            $order = Order::where('order_number', $orderNumber)->first();

            if (!$order) {
                // İframe içinde çalışıyorsa özel bir HTML sayfası döndür
                if ($this->isIframeRequest($request)) {
                    return view('payment.callback-error', [
                        'message' => 'Sipariş bulunamadı.',
                        'redirect_url' => route('checkout')
                    ]);
                }

                return redirect()->route('checkout')->with('error', 'Sipariş bulunamadı.');
            }

            // Sipariş durumunu güncelle
            $order->status = OrderStatus::PROCESSING->value;
            $order->payment_status = PaymentStatus::PAID->value;
            $order->save();

            // Sipariş notu ekle
            $order->addNote(
                '3D Secure ödeme başarıyla tamamlandı.',
                $order->user_id,
                false,
                'system'
            );

            // Sepeti temizle
            $sessionId = session()->getId();

            // Session ID'yi logla
            \Illuminate\Support\Facades\Log::info('Session ID bilgileri (iyzicoCallback)', [
                'session_id' => $sessionId,
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'user_id' => $order->user_id,
                'request_session_id' => $request->session()->getId(),
                'has_session' => $request->hasSession(),
                'cookie' => $request->cookie('laravel_session')
            ]);

            // Misafir kullanıcı için session_id ile sepeti bul
            if (!$order->user_id) {
                $guestCart = Cart::where('session_id', $sessionId)->first();

                // Sepet bulundu mu kontrol et
                if ($guestCart) {
                    \Illuminate\Support\Facades\Log::info('Misafir kullanıcı sepeti bulundu (iyzicoCallback)', [
                        'cart_id' => $guestCart->id,
                        'session_id' => $sessionId,
                        'items_count' => $guestCart->items->count()
                    ]);

                    // Sepeti temizle
                    $guestCart->items()->delete();
                    $guestCart->removeCoupon();
                } else {
                    \Illuminate\Support\Facades\Log::warning('Misafir kullanıcı sepeti bulunamadı (iyzicoCallback)', [
                        'session_id' => $sessionId,
                        'all_carts' => Cart::all()->toArray()
                    ]);
                }
            }

            // Giriş yapmış kullanıcı için user_id ile sepeti bul
            if ($order->user_id) {
                $userCart = Cart::where('user_id', $order->user_id)->first();

                // Sepet bulundu mu kontrol et
                if ($userCart) {
                    \Illuminate\Support\Facades\Log::info('Giriş yapmış kullanıcı sepeti bulundu (iyzicoCallback)', [
                        'cart_id' => $userCart->id,
                        'user_id' => $order->user_id,
                        'items_count' => $userCart->items->count()
                    ]);

                    // Sepeti temizle
                    $userCart->items()->delete();
                    $userCart->removeCoupon();
                } else {
                    \Illuminate\Support\Facades\Log::warning('Giriş yapmış kullanıcı sepeti bulunamadı (iyzicoCallback)', [
                        'user_id' => $order->user_id
                    ]);
                }
            }

            // Sepet temizleme işlemini logla
            \Illuminate\Support\Facades\Log::info('Sepet temizleme işlemi tamamlandı (iyzicoCallback)', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'user_id' => $order->user_id,
                'session_id' => $sessionId
            ]);

            // Başarılı URL'sini oluştur
            $successUrl = route('checkout.success', ['order_number' => $order->order_number]);

            // AJAX isteği ise JSON yanıtı döndür
            if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') == 'XMLHttpRequest') {
                \Illuminate\Support\Facades\Log::info('İyzico callback AJAX isteği algılandı, JSON yanıtı döndürülüyor');

                return response()->json([
                    'success' => true,
                    'order_number' => $order->order_number,
                    'redirect_url' => $successUrl
                ]);
            }

            // İframe içinde çalışıyorsa özel bir HTML sayfası döndür
            if ($this->isIframeRequest($request)) {
                \Illuminate\Support\Facades\Log::info('İyzico callback iframe içinde algılandı, özel HTML döndürülüyor');

                return view('payment.callback-success', [
                    'order_number' => $order->order_number,
                    'redirect_url' => $successUrl
                ]);
            }

            // Başarılı sayfasına yönlendir
            return redirect()->to($successUrl);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('İyzico callback hatası', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // İframe içinde çalışıyorsa özel bir HTML sayfası döndür
            if ($this->isIframeRequest($request)) {
                return view('payment.callback-error', [
                    'message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
                    'redirect_url' => route('checkout')
                ]);
            }

            return redirect()->route('checkout')->with('error', 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * İsteğin iframe içinden gelip gelmediğini kontrol et
     */
    protected function isIframeRequest(Request $request)
    {
        // İframe içinden gelen istekleri tespit etmek için çeşitli yöntemler
        return $request->header('Sec-Fetch-Dest') == 'iframe' ||
               strpos($request->header('User-Agent'), 'iyzico3DSecureFrame') !== false ||
               $request->input('is_iframe') == 'true' ||
               $request->query('is_iframe') == 'true';
    }

    /**
     * İyzico PayWithIyzico ödeme işlemi
     */
    public function processPayWithIyzico(Request $request, IyzicoService $iyzicoService)
    {
        // Debug için log ekle
        \Illuminate\Support\Facades\Log::info('PayWithIyzico ödeme isteği başlatıldı', [
            'request' => $request->all(),
            'headers' => $request->headers->all(),
        ]);

        try {
            // Form verilerini doğrula
            $validated = $request->validate([
                'billing_name' => 'required|string|max:255',
                'billing_email' => 'required|email|max:255',
                'billing_phone' => 'required|string|max:20',
                'billing_address' => 'required|string|max:500',
                'billing_city' => 'required|string|max:100',
                'billing_state' => 'required|string|max:100',
                'billing_zipcode' => 'required|string|max:20',
                'billing_country' => 'required|string|max:100',
                'shipping_name' => 'required|string|max:255',
                'shipping_email' => 'required|email|max:255',
                'shipping_phone' => 'required|string|max:20',
                'shipping_address' => 'required|string|max:500',
                'shipping_city' => 'required|string|max:100',
                'shipping_state' => 'required|string|max:100',
                'shipping_zipcode' => 'required|string|max:20',
                'shipping_country' => 'required|string|max:100',
                'notes' => 'nullable|string|max:1000',
            ]);

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('PayWithIyzico form verileri doğrulandı', [
                'validated' => $validated,
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Doğrulama hatası durumunda log ekle
            \Illuminate\Support\Facades\Log::error('PayWithIyzico form doğrulama hatası', [
                'errors' => $e->errors(),
            ]);

            // JSON yanıtı döndür
            return response()->json([
                'success' => false,
                'message' => 'Form verileri geçersiz.',
                'errors' => $e->errors(),
            ], 422);
        }

        // Sepeti getir
        $cart = $this->getCart($request);

        // Debug için log ekle
        \Illuminate\Support\Facades\Log::info('PayWithIyzico sepet bilgileri', [
            'cart_id' => $cart->id,
            'items_count' => $cart->items->count(),
            'total' => $cart->getTotalAttribute(),
        ]);

        // Sepet boşsa hata döndür
        if ($cart->items->isEmpty()) {
            // Debug için log ekle
            \Illuminate\Support\Facades\Log::error('PayWithIyzico sepet boş hatası');

            // JSON yanıtı döndür
            return response()->json([
                'success' => false,
                'message' => 'Sepetiniz boş. Ödeme yapılamaz.',
            ], 400);
        }

        try {
            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('PayWithIyzico veritabanı işlemi başlatılıyor');

            // Veritabanı işlemini başlat
            DB::beginTransaction();

            // Sipariş oluştur
            $order = new Order();
            $order->user_id = Auth::id();
            $order->order_number = 'ORD-' . strtoupper(Str::random(8));
            $order->status = OrderStatus::PENDING->value;
            $order->payment_status = PaymentStatus::PENDING->value;
            $order->payment_method = 'iyzico';
            $order->shipping_method = 'standard';
            $order->total_amount = $cart->getTotalAttribute();
            $order->shipping_cost = 0; // Şimdilik ücretsiz kargo
            $order->tax_amount = $cart->getTotalAttribute() * 0.18; // %18 KDV
            $order->discount_amount = $cart->discount_amount ?? 0;
            // coupon_code sütunu henüz eklenmemiş olabilir, bu yüzden try-catch içinde ekleyelim
            try {
                if (isset($cart->coupon_code)) {
                    $order->coupon_code = $cart->coupon_code;
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::warning('coupon_code sütunu henüz eklenmemiş olabilir', [
                    'error' => $e->getMessage(),
                ]);
            }

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::info('PayWithIyzico sipariş oluşturuldu', [
                'order_number' => $order->order_number,
                'total_amount' => $order->total_amount,
            ]);

            // Fatura ve teslimat bilgileri
            $order->billing_name = $validated['billing_name'];
            $order->billing_email = $validated['billing_email'];
            $order->billing_phone = $validated['billing_phone'];
            $order->billing_address = $validated['billing_address'];
            $order->billing_city = $validated['billing_city'];
            $order->billing_state = $validated['billing_state'];
            $order->billing_zipcode = $validated['billing_zipcode'];
            $order->billing_country = $validated['billing_country'];

            $order->shipping_name = $validated['shipping_name'];
            $order->shipping_email = $validated['shipping_email'];
            $order->shipping_phone = $validated['shipping_phone'];
            $order->shipping_address = $validated['shipping_address'];
            $order->shipping_city = $validated['shipping_city'];
            $order->shipping_state = $validated['shipping_state'];
            $order->shipping_zipcode = $validated['shipping_zipcode'];
            $order->shipping_country = $validated['shipping_country'];

            $order->notes = $validated['notes'];

            $order->save();

            // Sipariş öğelerini oluştur
            foreach ($cart->items as $item) {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->product_id = $item->product_id;
                $orderItem->product_name = $item->product->name;
                $orderItem->price = $item->price;
                $orderItem->quantity = $item->quantity;
                $orderItem->subtotal = $item->price * $item->quantity;
                $orderItem->options = $item->options;
                $orderItem->save();
            }

            // PayWithIyzico ödeme işlemini başlat
            \Illuminate\Support\Facades\Log::info('PayWithIyzico ödeme işlemi başlatılıyor', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
            ]);

            $paymentResult = $iyzicoService->initiatePayWithIyzico($order);

            \Illuminate\Support\Facades\Log::info('PayWithIyzico ödeme sonucu', [
                'success' => $paymentResult['success'] ?? false,
                'checkout_url' => $paymentResult['checkout_url'] ?? null,
                'message' => $paymentResult['message'] ?? null,
            ]);

            if (!$paymentResult['success']) {
                // Ödeme başarısız olduysa işlemi geri al
                DB::rollBack();

                \Illuminate\Support\Facades\Log::error('PayWithIyzico ödeme başarısız', [
                    'message' => $paymentResult['message'],
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $paymentResult['message'],
                ], 400);
            }

            // Veritabanı işlemini tamamla
            DB::commit();

            \Illuminate\Support\Facades\Log::info('PayWithIyzico işlemi tamamlandı');

            // Sipariş numarasını session'a kaydet
            session(['last_order_number' => $order->order_number]);

            \Illuminate\Support\Facades\Log::info('PayWithIyzico JSON yanıtı oluşturuluyor', [
                'redirect_url' => $paymentResult['checkout_url'],
                'order_number' => $order->order_number,
            ]);

            // JSON yanıtı döndür
            return response()->json([
                'success' => true,
                'redirect_url' => $paymentResult['checkout_url'],
                'order_number' => $order->order_number
            ]);

        } catch (\Exception $e) {
            // Hata durumunda işlemi geri al
            DB::rollBack();

            // Debug için log ekle
            \Illuminate\Support\Facades\Log::error('PayWithIyzico işlemi sırasında hata', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // JSON yanıtı döndür
            return response()->json([
                'success' => false,
                'message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * İyzico PayWithIyzico ödeme callback
     */
    public function payWithIyzicoCallback(Request $request, IyzicoService $iyzicoService)
    {
        try {
            // İyzico'dan gelen token'ı al (GET veya POST)
            $token = $request->query('token') ?? $request->input('token');

            \Illuminate\Support\Facades\Log::info('PayWithIyzico callback alındı', [
                'method' => $request->method(),
                'token' => $token,
                'all_data' => $request->all(),
                'is_ajax' => $request->ajax(),
                'wants_json' => $request->wantsJson(),
                'headers' => $request->headers->all()
            ]);

            if (!$token) {
                return redirect()->route('checkout')->with('error', 'Geçersiz ödeme token\'ı.');
            }

            // Ödeme sonucunu işle
            $result = $iyzicoService->handlePayWithIyzicoCallback($token);

            if (!$result['success']) {
                return redirect()->route('checkout')->with('error', $result['message']);
            }

            // Popup içinde çalışıyorsa özel bir HTML sayfası döndür
            if ($request->header('X-Requested-With') == 'XMLHttpRequest' || $request->ajax() || $request->wantsJson()) {
                \Illuminate\Support\Facades\Log::info('PayWithIyzico callback AJAX isteği algılandı, özel HTML döndürülüyor');

                return response()->json([
                    'success' => true,
                    'order_number' => $result['order_number']
                ]);
            }

            // İframe içinde çalışıyorsa özel bir HTML sayfası döndür
            if ($request->header('Sec-Fetch-Dest') == 'iframe') {
                \Illuminate\Support\Facades\Log::info('PayWithIyzico callback iframe içinde algılandı, özel HTML döndürülüyor');

                return view('payment.callback-success', [
                    'order_number' => $result['order_number'],
                    'redirect_url' => route('checkout.success', ['order_number' => $result['order_number']])
                ]);
            }

            // Normal sayfa ise başarılı sayfasına yönlendir
            return redirect()->route('checkout.success', ['order_number' => $result['order_number']]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('PayWithIyzico callback hatası', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('checkout')->with('error', 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Havale/EFT ile ödeme işlemi
     */
    public function processBankTransfer(Request $request)
    {
        // Form verilerini doğrula
        $validated = $request->validate([
            'bank_account_id' => 'required|exists:bank_accounts,id',
            'billing_name' => 'required|string|max:255',
            'billing_email' => 'required|email|max:255',
            'billing_phone' => 'required|string|max:20',
            'billing_address' => 'required|string|max:500',
            'billing_city' => 'required|string|max:100',
            'billing_state' => 'required|string|max:100',
            'billing_zipcode' => 'required|string|max:20',
            'billing_country' => 'required|string|max:100',
            'shipping_name' => 'required|string|max:255',
            'shipping_email' => 'required|email|max:255',
            'shipping_phone' => 'required|string|max:20',
            'shipping_address' => 'required|string|max:500',
            'shipping_city' => 'required|string|max:100',
            'shipping_state' => 'required|string|max:100',
            'shipping_zipcode' => 'required|string|max:20',
            'shipping_country' => 'required|string|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Sepeti getir
        $cart = $this->getCart($request);

        // Sepet boşsa hata döndür
        if ($cart->items->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Sepetiniz boş. Ödeme yapılamaz.');
        }

        try {
            // Veritabanı işlemini başlat
            DB::beginTransaction();

            // Sipariş oluştur
            $order = new Order();
            $order->user_id = Auth::id();
            $order->order_number = 'ORD-' . strtoupper(Str::random(8));
            $order->status = OrderStatus::PENDING->value;
            $order->payment_status = PaymentStatus::PENDING->value;
            $order->payment_method = 'bank_transfer';
            $order->shipping_method = 'standard';
            $order->total_amount = $cart->getTotalAttribute();
            $order->shipping_cost = 0; // Şimdilik ücretsiz kargo
            $order->tax_amount = $cart->getTotalAttribute() * 0.18; // %18 KDV
            $order->discount_amount = $cart->discount_amount ?? 0;
            // coupon_code sütunu henüz eklenmemiş olabilir, bu yüzden try-catch içinde ekleyelim
            try {
                if (isset($cart->coupon_code)) {
                    $order->coupon_code = $cart->coupon_code;
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::warning('coupon_code sütunu henüz eklenmemiş olabilir', [
                    'error' => $e->getMessage(),
                ]);
            }

            // Fatura ve teslimat bilgileri
            $order->billing_name = $validated['billing_name'];
            $order->billing_email = $validated['billing_email'];
            $order->billing_phone = $validated['billing_phone'];
            $order->billing_address = $validated['billing_address'];
            $order->billing_city = $validated['billing_city'];
            $order->billing_state = $validated['billing_state'];
            $order->billing_zipcode = $validated['billing_zipcode'];
            $order->billing_country = $validated['billing_country'];

            $order->shipping_name = $validated['shipping_name'];
            $order->shipping_email = $validated['shipping_email'];
            $order->shipping_phone = $validated['shipping_phone'];
            $order->shipping_address = $validated['shipping_address'];
            $order->shipping_city = $validated['shipping_city'];
            $order->shipping_state = $validated['shipping_state'];
            $order->shipping_zipcode = $validated['shipping_zipcode'];
            $order->shipping_country = $validated['shipping_country'];

            $order->notes = $validated['notes'];
            $order->bank_account_id = $validated['bank_account_id'];

            $order->save();

            // Sipariş öğelerini oluştur
            foreach ($cart->items as $item) {
                $orderItem = new OrderItem();
                $orderItem->order_id = $order->id;
                $orderItem->product_id = $item->product_id;
                $orderItem->product_name = $item->product->name;
                $orderItem->price = $item->price;
                $orderItem->quantity = $item->quantity;
                $orderItem->subtotal = $item->price * $item->quantity;
                $orderItem->options = $item->options;
                $orderItem->save();
            }

            // Sipariş notu ekle
            $order->addNote(
                'Sipariş oluşturuldu. Ödeme yöntemi: Havale/EFT',
                Auth::id(),
                false,
                'system'
            );

            // Sepeti temizle
            $cart->items()->delete();
            $cart->removeCoupon();

            // Veritabanı işlemini tamamla
            DB::commit();

            // Sipariş numarasını session'a kaydet
            session(['last_order_number' => $order->order_number]);

            // Log ekle
            \Illuminate\Support\Facades\Log::info('Banka transferi ile ödeme başarılı', [
                'order_number' => $order->order_number,
                'is_ajax' => $request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') == 'XMLHttpRequest'
            ]);

            // AJAX isteği ise JSON yanıtı döndür
            if ($request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') == 'XMLHttpRequest') {
                return response()->json([
                    'success' => true,
                    'redirect_url' => route('checkout.success', ['order_number' => $order->order_number]),
                    'order_number' => $order->order_number
                ]);
            }

            // Normal form gönderimi ise yönlendirme yap
            return redirect()->route('checkout.success', ['order_number' => $order->order_number]);

        } catch (\Exception $e) {
            // Hata durumunda işlemi geri al
            DB::rollBack();

            return redirect()->route('checkout')->with('error', 'Sipariş oluşturulurken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Sipariş durumunu kontrol et
     */
    public function checkOrderStatus(Request $request)
    {
        $orderNumber = $request->query('order_number');

        if (!$orderNumber) {
            return response()->json(['success' => false, 'message' => 'Sipariş numarası gerekli']);
        }

        $order = Order::where('order_number', $orderNumber)->first();

        if (!$order) {
            return response()->json(['success' => false, 'message' => 'Sipariş bulunamadı']);
        }

        return response()->json([
            'success' => true,
            'status' => $order->status,
            'payment_status' => $order->payment_status
        ]);
    }

    /**
     * Ödemeye devam et
     */
    public function resumePayment(Request $request, IyzicoService $iyzicoService)
    {
        $orderNumber = $request->query('order_number');

        if (!$orderNumber) {
            return response()->json(['success' => false, 'message' => 'Sipariş numarası gerekli']);
        }

        $order = Order::where('order_number', $orderNumber)->first();

        if (!$order) {
            return response()->json(['success' => false, 'message' => 'Sipariş bulunamadı']);
        }

        // Devam ettirilebilir sipariş durumlarını kontrol et
        $resumableStatuses = [
            OrderStatus::PENDING->value,
            OrderStatus::PROCESSING->value
        ];

        // Devam ettirilebilir ödeme durumlarını kontrol et
        $resumablePaymentStatuses = [
            PaymentStatus::PENDING->value,
            PaymentStatus::FAILED->value
        ];

        // Sipariş durumunu kontrol et
        if (!in_array($order->status, $resumableStatuses)) {
            if ($order->status === OrderStatus::CANCELLED->value) {
                return response()->json(['success' => false, 'message' => 'İptal edilmiş siparişler için ödeme yapılamaz']);
            }

            if ($order->status === OrderStatus::DELIVERED->value) {
                return response()->json(['success' => false, 'message' => 'Teslim edilmiş siparişler için ödeme yapılamaz']);
            }

            return response()->json(['success' => false, 'message' => 'Bu aşamadaki siparişler için ödeme devam ettirilemez']);
        }

        // Ödeme durumunu kontrol et
        if (!in_array($order->payment_status, $resumablePaymentStatuses)) {
            if ($order->payment_status === PaymentStatus::PAID->value) {
                return response()->json(['success' => false, 'message' => 'Bu sipariş için ödeme zaten tamamlanmış']);
            }

            return response()->json(['success' => false, 'message' => 'Bu ödeme durumundaki siparişler için ödeme devam ettirilemez']);
        }

        // Ödeme yöntemine göre işlem yap
        if ($order->payment_method === 'credit_card') {
            // 3D Secure ödeme sayfasını tekrar oluştur

            // Ödeme formuna yönlendir
            return response()->json([
                'success' => true,
                'redirect_url' => route('checkout.payment-form', [
                    'method' => 'credit_card',
                    'data' => base64_encode(json_encode([
                        'order_number' => $order->order_number,
                        'resume' => true
                    ]))
                ])
            ]);
        } else if ($order->payment_method === 'paywithiyzico') {
            // PayWithIyzico ödeme sayfasını tekrar oluştur
            $paymentResult = $iyzicoService->initiatePayWithIyzico($order);

            if (!$paymentResult['success']) {
                return response()->json(['success' => false, 'message' => $paymentResult['message']]);
            }

            return response()->json([
                'success' => true,
                'redirect_url' => $paymentResult['checkout_url']
            ]);
        }

        return response()->json(['success' => false, 'message' => 'Desteklenmeyen ödeme yöntemi']);
    }

    /**
     * Siparişi iptal et
     */
    public function cancelOrder(Request $request)
    {
        $orderNumber = $request->query('order_number');

        if (!$orderNumber) {
            return response()->json(['success' => false, 'message' => 'Sipariş numarası gerekli']);
        }

        $order = Order::where('order_number', $orderNumber)->first();

        if (!$order) {
            return response()->json(['success' => false, 'message' => 'Sipariş bulunamadı']);
        }

        // İptal edilebilir durumları kontrol et
        $cancelableStatuses = [
            OrderStatus::PENDING->value,
            OrderStatus::PROCESSING->value
        ];

        // Sipariş durumunu kontrol et - sadece belirli durumlarda iptal edilebilir
        if (!in_array($order->status, $cancelableStatuses)) {
            // Eğer sipariş zaten iptal edilmişse farklı bir mesaj göster
            if ($order->status === OrderStatus::CANCELLED->value) {
                return response()->json(['success' => false, 'message' => 'Bu sipariş zaten iptal edilmiş']);
            }

            // Eğer sipariş teslim edilmişse farklı bir mesaj göster
            if ($order->status === OrderStatus::DELIVERED->value) {
                return response()->json(['success' => false, 'message' => 'Teslim edilmiş siparişler iptal edilemez']);
            }

            return response()->json(['success' => false, 'message' => 'Bu aşamadaki siparişler iptal edilemez']);
        }

        // Siparişi iptal et
        $order->status = OrderStatus::CANCELLED->value;
        $order->save();

        // Sipariş notu ekle
        $order->addNote(
            'Sipariş kullanıcı tarafından iptal edildi.',
            $order->user_id,
            false,
            'system'
        );

        return response()->json(['success' => true, 'message' => 'Sipariş başarıyla iptal edildi']);
    }

    /**
     * Kullanıcının sepetini getir veya oluştur
     */
    protected function getCart(Request $request)
    {
        if (Auth::check()) {
            // Giriş yapmış kullanıcı için sepeti bul veya oluştur
            $cart = Cart::firstOrCreate(
                ['user_id' => Auth::id()],
                ['session_id' => null]
            );

            // Oturum sepeti varsa, kullanıcı sepetine taşı
            $sessionId = $request->session()->getId();
            $sessionCart = Cart::where('session_id', $sessionId)->first();

            if ($sessionCart && $sessionCart->id !== $cart->id) {
                // Oturum sepetindeki ürünleri kullanıcı sepetine taşı
                foreach ($sessionCart->items as $item) {
                    $existingItem = CartItem::where('cart_id', $cart->id)
                        ->where('product_id', $item->product_id)
                        ->where('options', $item->options)
                        ->first();

                    if ($existingItem) {
                        // Aynı ürün varsa miktarı güncelle
                        $existingItem->quantity += $item->quantity;
                        $existingItem->save();
                    } else {
                        // Yoksa yeni öğe oluştur
                        CartItem::create([
                            'cart_id' => $cart->id,
                            'product_id' => $item->product_id,
                            'quantity' => $item->quantity,
                            'price' => $item->price,
                            'options' => $item->options,
                        ]);
                    }
                }

                // Oturum sepetini sil
                $sessionCart->items()->delete();
                $sessionCart->delete();
            }

            return $cart;
        } else {
            // Giriş yapmamış kullanıcı için oturum sepetini bul veya oluştur
            $sessionId = $request->session()->getId();
            return Cart::firstOrCreate(
                ['session_id' => $sessionId],
                ['user_id' => null]
            );
        }
    }
}
