<?php

namespace App\Application\Categories\DTOs;

use App\Domain\Categories\Entities\CategoryAttribute;

class CategoryAttributeDTO
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $categoryId,
        public readonly int $attributeId,
        public readonly bool $isRequired,
        public readonly bool $isFilterable,
        public readonly bool $isSearchable,
        public readonly int $position,
        public readonly ?string $defaultValue,
        public readonly array $validationRules,
        public readonly string $createdAt,
        public readonly string $updatedAt
    ) {}

    public static function fromEntity(CategoryAttribute $attribute): self
    {
        return new self(
            id: $attribute->getId(),
            categoryId: $attribute->getCategoryId(),
            attributeId: $attribute->getAttributeId(),
            isRequired: $attribute->isRequired(),
            isFilterable: $attribute->isFilterable(),
            isSearchable: $attribute->isSearchable(),
            position: $attribute->getPosition(),
            defaultValue: $attribute->getDefaultValue(),
            validationRules: $attribute->getValidationRules(),
            createdAt: $attribute->getCreatedAt()->toISOString(),
            updatedAt: $attribute->getUpdatedAt()->toISOString()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->categoryId,
            'attribute_id' => $this->attributeId,
            'is_required' => $this->isRequired,
            'is_filterable' => $this->isFilterable,
            'is_searchable' => $this->isSearchable,
            'position' => $this->position,
            'default_value' => $this->defaultValue,
            'validation_rules' => $this->validationRules,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }
}
