<?php

namespace App\Application\Categories\DTOs;

use App\Domain\Categories\Entities\Category;

class CategoryDTO
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly string $slug,
        public readonly ?string $description,
        public readonly ?int $parentId,
        public readonly string $path,
        public readonly array $pathArray,
        public readonly int $level,
        public readonly int $position,
        public readonly bool $status,
        public readonly bool $featured,
        public readonly bool $showInMenu,
        public readonly ?string $image,
        public readonly ?string $icon,
        public readonly ?array $seoData,
        public readonly array $children,
        public readonly array $attributes,
        public readonly int $productCount,
        public readonly bool $hasChildren,
        public readonly bool $hasProducts,
        public readonly bool $isRoot,
        public readonly string $createdAt,
        public readonly string $updatedAt
    ) {}

    public static function fromEntity(Category $category): self
    {
        return new self(
            id: $category->getId() ?? 0,
            name: $category->getName(),
            slug: $category->getSlug()->getValue(),
            description: $category->getDescription(),
            parentId: $category->getParentId(),
            path: $category->getPath()->getPathString(),
            pathArray: $category->getPath()->getPath(),
            level: $category->getLevel(),
            position: $category->getPosition(),
            status: $category->getStatus(),
            featured: $category->isFeatured(),
            showInMenu: $category->isVisibleInMenu(),
            image: $category->getImage(),
            icon: $category->getIcon(),
            seoData: $category->getSEOData()?->toArray(),
            children: array_map(fn($child) => self::fromEntity($child), $category->getChildren()),
            attributes: array_map(fn($attr) => CategoryAttributeDTO::fromEntity($attr), $category->getAttributes()),
            productCount: $category->getProductCount(),
            hasChildren: $category->hasChildren(),
            hasProducts: $category->hasProducts(),
            isRoot: $category->isRoot(),
            createdAt: $category->getCreatedAt()->toISOString(),
            updatedAt: $category->getUpdatedAt()->toISOString()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'parent_id' => $this->parentId,
            'path' => $this->path,
            'path_array' => $this->pathArray,
            'level' => $this->level,
            'position' => $this->position,
            'status' => $this->status,
            'featured' => $this->featured,
            'show_in_menu' => $this->showInMenu,
            'image' => $this->image,
            'icon' => $this->icon,
            'seo_data' => $this->seoData,
            'children' => array_map(fn($child) => $child->toArray(), $this->children),
            'attributes' => array_map(fn($attr) => $attr->toArray(), $this->attributes),
            'product_count' => $this->productCount,
            'has_children' => $this->hasChildren,
            'has_products' => $this->hasProducts,
            'is_root' => $this->isRoot,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }

    public function toSimpleArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'parent_id' => $this->parentId,
            'level' => $this->level,
            'position' => $this->position,
            'status' => $this->status,
            'featured' => $this->featured,
            'show_in_menu' => $this->showInMenu,
            'product_count' => $this->productCount,
            'has_children' => $this->hasChildren,
            'image' => $this->image,
            'icon' => $this->icon,
        ];
    }

    public function toTreeArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'level' => $this->level,
            'product_count' => $this->productCount,
            'children' => array_map(fn($child) => $child->toTreeArray(), $this->children),
        ];
    }

    public function toBreadcrumbArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'url' => '/categories/' . $this->slug,
        ];
    }

    public function getUrl(): string
    {
        return '/categories/' . $this->slug;
    }

    public function getDepth(): int
    {
        return $this->level;
    }

    public function getChildrenCount(): int
    {
        return count($this->children);
    }

    public function getAttributesCount(): int
    {
        return count($this->attributes);
    }
}
