<?php

namespace App\Application\Categories\Commands;

class UpdateCategoryCommand
{
    public function __construct(
        public readonly int $categoryId,
        public readonly ?string $name = null,
        public readonly ?string $slug = null,
        public readonly ?string $description = null,
        public readonly ?int $position = null,
        public readonly ?bool $status = null,
        public readonly ?bool $featured = null,
        public readonly ?bool $showInMenu = null,
        public readonly ?string $image = null,
        public readonly ?string $icon = null,
        public readonly ?string $metaTitle = null,
        public readonly ?string $metaDescription = null,
        public readonly ?string $metaKeywords = null,
        public readonly ?array $attributes = null
    ) {}

    public function getCategoryId(): int
    {
        return $this->categoryId;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function getStatus(): ?bool
    {
        return $this->status;
    }

    public function isFeatured(): ?bool
    {
        return $this->featured;
    }

    public function shouldShowInMenu(): ?bool
    {
        return $this->showInMenu;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->metaKeywords;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function hasBasicInfoUpdates(): bool
    {
        return $this->name !== null || $this->slug !== null || $this->description !== null;
    }

    public function hasDisplayUpdates(): bool
    {
        return $this->status !== null || $this->featured !== null || $this->showInMenu !== null;
    }

    public function hasMediaUpdates(): bool
    {
        return $this->image !== null || $this->icon !== null;
    }

    public function hasSEOUpdates(): bool
    {
        return $this->metaTitle !== null || $this->metaDescription !== null || $this->metaKeywords !== null;
    }

    public function hasAttributeUpdates(): bool
    {
        return $this->attributes !== null;
    }

    public function hasPositionUpdate(): bool
    {
        return $this->position !== null;
    }
}
