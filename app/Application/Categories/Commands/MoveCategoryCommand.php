<?php

namespace App\Application\Categories\Commands;

class MoveCategoryCommand
{
    public function __construct(
        public readonly int $categoryId,
        public readonly ?int $newParentId,
        public readonly ?int $newPosition = null
    ) {}

    public function getCategoryId(): int
    {
        return $this->categoryId;
    }

    public function getNewParentId(): ?int
    {
        return $this->newParentId;
    }

    public function getNewPosition(): ?int
    {
        return $this->newPosition;
    }

    public function isMovingToRoot(): bool
    {
        return $this->newParentId === null;
    }

    public function hasPositionChange(): bool
    {
        return $this->newPosition !== null;
    }
}
