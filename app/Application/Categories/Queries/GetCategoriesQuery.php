<?php

namespace App\Application\Categories\Queries;

class GetCategoriesQuery
{
    public function __construct(
        public readonly ?int $parentId = null,
        public readonly ?bool $status = null,
        public readonly ?bool $featured = null,
        public readonly ?bool $showInMenu = null,
        public readonly ?string $search = null,
        public readonly ?int $level = null,
        public readonly ?string $sortBy = 'position',
        public readonly string $sortDirection = 'asc',
        public readonly int $limit = 100,
        public readonly int $offset = 0,
        public readonly bool $includeChildren = false,
        public readonly bool $includeAttributes = false,
        public readonly bool $includeProductCount = false,
        public readonly int $maxDepth = 1
    ) {}

    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    public function getStatus(): ?bool
    {
        return $this->status;
    }

    public function isFeatured(): ?bool
    {
        return $this->featured;
    }

    public function shouldShowInMenu(): ?bool
    {
        return $this->showInMenu;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function getSortBy(): ?string
    {
        return $this->sortBy;
    }

    public function getSortDirection(): string
    {
        return $this->sortDirection;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function shouldIncludeChildren(): bool
    {
        return $this->includeChildren;
    }

    public function shouldIncludeAttributes(): bool
    {
        return $this->includeAttributes;
    }

    public function shouldIncludeProductCount(): bool
    {
        return $this->includeProductCount;
    }

    public function getMaxDepth(): int
    {
        return $this->maxDepth;
    }

    public function getCriteria(): array
    {
        $criteria = [];

        if ($this->parentId !== null) {
            $criteria['parent_id'] = $this->parentId;
        }

        if ($this->status !== null) {
            $criteria['status'] = $this->status;
        }

        if ($this->featured !== null) {
            $criteria['featured'] = $this->featured;
        }

        if ($this->showInMenu !== null) {
            $criteria['show_in_menu'] = $this->showInMenu;
        }

        if ($this->search !== null) {
            $criteria['search'] = $this->search;
        }

        if ($this->level !== null) {
            $criteria['level'] = $this->level;
        }

        return $criteria;
    }

    public function isRootQuery(): bool
    {
        return $this->parentId === null;
    }

    public function hasSearch(): bool
    {
        return $this->search !== null && trim($this->search) !== '';
    }
}
