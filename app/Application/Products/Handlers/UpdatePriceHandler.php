<?php

namespace App\Application\Products\Handlers;

use App\Application\Products\Commands\UpdatePriceCommand;
use App\Application\Products\DTOs\ProductDTO;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\Exceptions\ProductNotFoundException;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdatePriceHandler
{
    public function __construct(
        private ProductRepositoryInterface $productRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(UpdatePriceCommand $command): ProductDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Ürünü bul
                $product = $this->productRepository->findById($command->getProductId());
                
                if (!$product) {
                    throw new ProductNotFoundException("Product with ID {$command->getProductId()} not found");
                }

                $oldPrice = $product->getPrice();

                // Yeni fiyat oluştur
                $newPrice = Price::fromAmount($command->getPrice(), $command->getCurrency());

                // Fiyatı güncelle
                $product->updatePrice($newPrice);

                // Ürünü kaydet
                $savedProduct = $this->productRepository->save($product);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedProduct);

                Log::info('Product price updated successfully', [
                    'product_id' => $savedProduct->getId(),
                    'product_name' => $savedProduct->getName(),
                    'sku' => $savedProduct->getSKU()->getValue(),
                    'old_price' => $oldPrice->getAmount(),
                    'new_price' => $command->getPrice(),
                    'currency' => $command->getCurrency(),
                    'price_change' => $command->getPrice() - $oldPrice->getAmount(),
                    'reason' => $command->getReason()
                ]);

                return ProductDTO::fromEntity($savedProduct);

            } catch (\Exception $e) {
                Log::error('Failed to update product price', [
                    'product_id' => $command->getProductId(),
                    'price' => $command->getPrice(),
                    'currency' => $command->getCurrency(),
                    'reason' => $command->getReason(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }
}
