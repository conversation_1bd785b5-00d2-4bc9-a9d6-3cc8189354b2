<?php

namespace App\Application\Products\Queries;

class GetProductQuery
{
    public function __construct(
        public readonly ?int $id = null,
        public readonly ?string $slug = null,
        public readonly ?string $sku = null,
        public readonly bool $includeVariants = false,
        public readonly bool $includeAttributes = false,
        public readonly bool $includeImages = false,
        public readonly bool $incrementViewCount = false
    ) {}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function shouldIncludeVariants(): bool
    {
        return $this->includeVariants;
    }

    public function shouldIncludeAttributes(): bool
    {
        return $this->includeAttributes;
    }

    public function shouldIncludeImages(): bool
    {
        return $this->includeImages;
    }

    public function shouldIncrementViewCount(): bool
    {
        return $this->incrementViewCount;
    }

    public function hasIdentifier(): bool
    {
        return $this->id !== null || $this->slug !== null || $this->sku !== null;
    }
}
