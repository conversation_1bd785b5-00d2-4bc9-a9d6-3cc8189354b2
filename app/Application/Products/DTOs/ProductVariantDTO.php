<?php

namespace App\Application\Products\DTOs;

use App\Domain\Products\Entities\ProductVariant;

class ProductVariantDTO
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $productId,
        public readonly string $sku,
        public readonly array $attributeValues,
        public readonly array $formattedAttributes,
        public readonly float $additionalPrice,
        public readonly int $stockQuantity,
        public readonly int $reservedQuantity,
        public readonly int $availableQuantity,
        public readonly string $status,
        public readonly bool $isDefault,
        public readonly bool $isAvailable,
        public readonly bool $isInStock,
        public readonly ?string $image,
        public readonly ?array $weight,
        public readonly ?array $dimensions,
        public readonly ?string $metaTitle,
        public readonly ?string $metaDescription,
        public readonly ?string $metaKeywords,
        public readonly ?string $slug,
        public readonly string $displayName,
        public readonly string $createdAt,
        public readonly string $updatedAt
    ) {}

    public static function fromEntity(ProductVariant $variant): self
    {
        return new self(
            id: $variant->getId(),
            productId: $variant->getProductId(),
            sku: $variant->getSKU()->getValue(),
            attributeValues: $variant->getAttributeValues(),
            formattedAttributes: $variant->getFormattedAttributes(),
            additionalPrice: $variant->getAdditionalPrice()->getAmount(),
            stockQuantity: $variant->getStock()->getQuantity(),
            reservedQuantity: $variant->getStock()->getReservedQuantity(),
            availableQuantity: $variant->getStock()->getAvailableQuantity(),
            status: $variant->getStatus(),
            isDefault: $variant->isDefault(),
            isAvailable: $variant->isAvailable(),
            isInStock: $variant->isInStock(),
            image: $variant->getImage(),
            weight: $variant->getWeight()?->toArray(),
            dimensions: $variant->getDimensions()?->toArray(),
            metaTitle: $variant->getMetaTitle(),
            metaDescription: $variant->getMetaDescription(),
            metaKeywords: $variant->getMetaKeywords(),
            slug: $variant->getSlug(),
            displayName: $variant->generateDisplayName(),
            createdAt: $variant->getCreatedAt()->toISOString(),
            updatedAt: $variant->getUpdatedAt()->toISOString()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'product_id' => $this->productId,
            'sku' => $this->sku,
            'attribute_values' => $this->attributeValues,
            'formatted_attributes' => $this->formattedAttributes,
            'additional_price' => $this->additionalPrice,
            'stock_quantity' => $this->stockQuantity,
            'reserved_quantity' => $this->reservedQuantity,
            'available_quantity' => $this->availableQuantity,
            'status' => $this->status,
            'is_default' => $this->isDefault,
            'is_available' => $this->isAvailable,
            'is_in_stock' => $this->isInStock,
            'image' => $this->image,
            'weight' => $this->weight,
            'dimensions' => $this->dimensions,
            'meta_title' => $this->metaTitle,
            'meta_description' => $this->metaDescription,
            'meta_keywords' => $this->metaKeywords,
            'slug' => $this->slug,
            'display_name' => $this->displayName,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }
}
