<?php

namespace App\Application\Products\Services;

use App\Application\Products\Commands\CreateProductCommand;
use App\Application\Products\Commands\UpdateProductCommand;
use App\Application\Products\Commands\UpdateStockCommand;
use App\Application\Products\Commands\UpdatePriceCommand;
use App\Application\Products\Commands\DeleteProductCommand;
use App\Application\Products\Queries\GetProductQuery;
use App\Application\Products\Queries\GetProductsQuery;
use App\Application\Products\DTOs\ProductDTO;
use App\Application\Products\Handlers\CreateProductHandler;
use App\Application\Products\Handlers\UpdateProductHandler;
use App\Application\Products\Handlers\UpdateStockHandler;
use App\Application\Products\Handlers\UpdatePriceHandler;
use App\Application\Products\Handlers\GetProductHandler;
use App\Application\Products\Handlers\GetProductsHandler;

class ProductApplicationService
{
    public function __construct(
        private CreateProductHandler $createProductHandler,
        private UpdateProductHandler $updateProductHandler,
        private UpdateStockHandler $updateStockHandler,
        private UpdatePriceHandler $updatePriceHandler,
        private GetProductHandler $getProductHandler,
        private GetProductsHandler $getProductsHandler
    ) {}

    /**
     * Yeni ürün oluştur
     */
    public function createProduct(
        string $name,
        string $slug,
        string $sku,
        float $price,
        int $stockQuantity,
        int $categoryId,
        ?string $description = null,
        string $currency = 'TRY',
        bool $status = true,
        bool $isFeatured = false,
        ?float $salePrice = null,
        ?string $saleStartsAt = null,
        ?string $saleEndsAt = null,
        ?float $weight = null,
        string $weightUnit = 'g',
        ?float $length = null,
        ?float $width = null,
        ?float $height = null,
        string $dimensionUnit = 'cm',
        ?string $metaTitle = null,
        ?string $metaDescription = null,
        ?string $metaKeywords = null,
        array $images = [],
        array $attributes = [],
        int $lowStockThreshold = 5,
        bool $trackQuantity = true,
        bool $allowBackorders = false
    ): ProductDTO {
        $command = new CreateProductCommand(
            name: $name,
            slug: $slug,
            sku: $sku,
            price: $price,
            stockQuantity: $stockQuantity,
            categoryId: $categoryId,
            description: $description,
            currency: $currency,
            status: $status,
            isFeatured: $isFeatured,
            salePrice: $salePrice,
            saleStartsAt: $saleStartsAt,
            saleEndsAt: $saleEndsAt,
            weight: $weight,
            weightUnit: $weightUnit,
            length: $length,
            width: $width,
            height: $height,
            dimensionUnit: $dimensionUnit,
            metaTitle: $metaTitle,
            metaDescription: $metaDescription,
            metaKeywords: $metaKeywords,
            images: $images,
            attributes: $attributes,
            lowStockThreshold: $lowStockThreshold,
            trackQuantity: $trackQuantity,
            allowBackorders: $allowBackorders
        );

        return $this->createProductHandler->handle($command);
    }

    /**
     * Ürün güncelle
     */
    public function updateProduct(
        int $productId,
        ?string $name = null,
        ?string $slug = null,
        ?string $description = null,
        ?float $price = null,
        ?string $currency = null,
        ?int $categoryId = null,
        ?bool $status = null,
        ?bool $isFeatured = null,
        ?float $salePrice = null,
        ?string $saleStartsAt = null,
        ?string $saleEndsAt = null,
        ?float $weight = null,
        ?string $weightUnit = null,
        ?float $length = null,
        ?float $width = null,
        ?float $height = null,
        ?string $dimensionUnit = null,
        ?string $metaTitle = null,
        ?string $metaDescription = null,
        ?string $metaKeywords = null,
        ?array $images = null,
        ?array $attributes = null
    ): ProductDTO {
        $command = new UpdateProductCommand(
            productId: $productId,
            name: $name,
            slug: $slug,
            description: $description,
            price: $price,
            currency: $currency,
            categoryId: $categoryId,
            status: $status,
            isFeatured: $isFeatured,
            salePrice: $salePrice,
            saleStartsAt: $saleStartsAt,
            saleEndsAt: $saleEndsAt,
            weight: $weight,
            weightUnit: $weightUnit,
            length: $length,
            width: $width,
            height: $height,
            dimensionUnit: $dimensionUnit,
            metaTitle: $metaTitle,
            metaDescription: $metaDescription,
            metaKeywords: $metaKeywords,
            images: $images,
            attributes: $attributes
        );

        return $this->updateProductHandler->handle($command);
    }

    /**
     * Ürün stoku güncelle
     */
    public function updateStock(
        int $productId,
        int $quantity,
        ?int $reservedQuantity = null,
        ?int $lowStockThreshold = null,
        ?bool $trackQuantity = null,
        ?bool $allowBackorders = null,
        ?string $reason = null
    ): ProductDTO {
        $command = new UpdateStockCommand(
            productId: $productId,
            quantity: $quantity,
            reservedQuantity: $reservedQuantity,
            lowStockThreshold: $lowStockThreshold,
            trackQuantity: $trackQuantity,
            allowBackorders: $allowBackorders,
            reason: $reason
        );

        return $this->updateStockHandler->handle($command);
    }

    /**
     * Ürün fiyatı güncelle
     */
    public function updatePrice(
        int $productId,
        float $price,
        string $currency = 'TRY',
        ?string $reason = null
    ): ProductDTO {
        $command = new UpdatePriceCommand(
            productId: $productId,
            price: $price,
            currency: $currency,
            reason: $reason
        );

        return $this->updatePriceHandler->handle($command);
    }

    /**
     * Ürün detayını al
     */
    public function getProduct(
        ?int $id = null,
        ?string $slug = null,
        ?string $sku = null,
        bool $includeVariants = false,
        bool $includeAttributes = false,
        bool $includeImages = false,
        bool $incrementViewCount = false
    ): ProductDTO {
        $query = new GetProductQuery(
            id: $id,
            slug: $slug,
            sku: $sku,
            includeVariants: $includeVariants,
            includeAttributes: $includeAttributes,
            includeImages: $includeImages,
            incrementViewCount: $incrementViewCount
        );

        return $this->getProductHandler->handle($query);
    }

    /**
     * Ürünleri listele
     */
    public function getProducts(
        ?int $categoryId = null,
        ?bool $status = null,
        ?bool $isFeatured = null,
        ?bool $isOnSale = null,
        ?bool $inStock = null,
        ?float $minPrice = null,
        ?float $maxPrice = null,
        ?string $search = null,
        ?string $sortBy = 'created_at',
        string $sortDirection = 'desc',
        int $limit = 10,
        int $offset = 0,
        bool $includeVariants = false,
        bool $includeAttributes = false,
        bool $includeImages = false,
        array $attributes = []
    ): array {
        $query = new GetProductsQuery(
            categoryId: $categoryId,
            status: $status,
            isFeatured: $isFeatured,
            isOnSale: $isOnSale,
            inStock: $inStock,
            minPrice: $minPrice,
            maxPrice: $maxPrice,
            search: $search,
            sortBy: $sortBy,
            sortDirection: $sortDirection,
            limit: $limit,
            offset: $offset,
            includeVariants: $includeVariants,
            includeAttributes: $includeAttributes,
            includeImages: $includeImages,
            attributes: $attributes
        );

        return $this->getProductsHandler->handle($query);
    }

    /**
     * Aktif ürünleri al
     */
    public function getActiveProducts(int $limit = 10, int $offset = 0): array
    {
        return $this->getProducts(
            status: true,
            limit: $limit,
            offset: $offset
        );
    }

    /**
     * Öne çıkan ürünleri al
     */
    public function getFeaturedProducts(int $limit = 10, int $offset = 0): array
    {
        return $this->getProducts(
            status: true,
            isFeatured: true,
            limit: $limit,
            offset: $offset
        );
    }

    /**
     * İndirimli ürünleri al
     */
    public function getOnSaleProducts(int $limit = 10, int $offset = 0): array
    {
        return $this->getProducts(
            status: true,
            isOnSale: true,
            limit: $limit,
            offset: $offset
        );
    }

    /**
     * Kategori ürünlerini al
     */
    public function getCategoryProducts(int $categoryId, int $limit = 10, int $offset = 0): array
    {
        return $this->getProducts(
            categoryId: $categoryId,
            status: true,
            limit: $limit,
            offset: $offset
        );
    }

    /**
     * Ürün ara
     */
    public function searchProducts(string $search, int $limit = 10, int $offset = 0): array
    {
        return $this->getProducts(
            search: $search,
            status: true,
            limit: $limit,
            offset: $offset
        );
    }

    /**
     * Fiyat aralığında ürünleri al
     */
    public function getProductsByPriceRange(
        float $minPrice,
        float $maxPrice,
        int $limit = 10,
        int $offset = 0
    ): array {
        return $this->getProducts(
            minPrice: $minPrice,
            maxPrice: $maxPrice,
            status: true,
            limit: $limit,
            offset: $offset
        );
    }
}
