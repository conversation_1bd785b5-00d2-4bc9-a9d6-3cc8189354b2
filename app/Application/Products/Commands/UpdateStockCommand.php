<?php

namespace App\Application\Products\Commands;

class UpdateStockCommand
{
    public function __construct(
        public readonly int $productId,
        public readonly int $quantity,
        public readonly ?int $reservedQuantity = null,
        public readonly ?int $lowStockThreshold = null,
        public readonly ?bool $trackQuantity = null,
        public readonly ?bool $allowBackorders = null,
        public readonly ?string $reason = null
    ) {}

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function getReservedQuantity(): ?int
    {
        return $this->reservedQuantity;
    }

    public function getLowStockThreshold(): ?int
    {
        return $this->lowStockThreshold;
    }

    public function isTrackQuantity(): ?bool
    {
        return $this->trackQuantity;
    }

    public function isAllowBackorders(): ?bool
    {
        return $this->allowBackorders;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }
}
