<?php

namespace App\Application\Products\Commands;

class DeleteProductCommand
{
    public function __construct(
        public readonly int $productId,
        public readonly ?string $reason = null,
        public readonly bool $forceDelete = false
    ) {}

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function isForceDelete(): bool
    {
        return $this->forceDelete;
    }
}
