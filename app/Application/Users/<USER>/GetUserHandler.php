<?php

namespace App\Application\Users\Handlers;

use App\Application\Users\Queries\GetUserQuery;
use App\Application\Users\DTOs\UserDTO;
use App\Domain\Users\ValueObjects\Email;
use App\Domain\Users\Repositories\UserRepositoryInterface;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use Illuminate\Support\Facades\Log;

/**
 * GetUserHandler
 * Tekil kullanıcı sorgularını yönetir
 */
class GetUserHandler
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Kullanıcı sorgusunu gerçekleştir
     *
     * @param GetUserQuery $query
     * @return UserDTO
     * @throws EntityNotFoundException
     */
    public function handle(GetUserQuery $query): UserDTO
    {
        try {
            $user = null;

            // ID ile arama
            if ($query->getId()) {
                $user = $this->userRepository->findById($query->getId());
                
                if (!$user) {
                    throw new EntityNotFoundException("User with ID {$query->getId()} not found");
                }
            }
            // Email ile arama
            elseif ($query->getEmail()) {
                $email = Email::fromString($query->getEmail());
                $user = $this->userRepository->findByEmail($email);
                
                if (!$user) {
                    throw new EntityNotFoundException("User with email {$query->getEmail()} not found");
                }
            }
            // Remember token ile arama
            elseif ($query->getRememberToken()) {
                $user = $this->userRepository->findByRememberToken($query->getRememberToken());
                
                if (!$user) {
                    throw new EntityNotFoundException("User with remember token not found");
                }
            }
            else {
                throw new \InvalidArgumentException('At least one search criteria must be provided (id, email, or remember_token)');
            }

            Log::info('User retrieved successfully', [
                'user_id' => $user->getId(),
                'email' => $user->getEmail()->getValue(),
                'search_criteria' => [
                    'id' => $query->getId(),
                    'email' => $query->getEmail(),
                    'has_remember_token' => !empty($query->getRememberToken())
                ]
            ]);

            // DTO'ya dönüştür ve döndür
            return UserDTO::fromEntity(
                user: $user,
                includeRoles: $query->shouldIncludeRoles(),
                includePermissions: $query->shouldIncludePermissions(),
                includeAddresses: $query->shouldIncludeAddresses(),
                includeProfile: $query->shouldIncludeProfile()
            );

        } catch (EntityNotFoundException $e) {
            Log::warning('User not found', [
                'search_criteria' => [
                    'id' => $query->getId(),
                    'email' => $query->getEmail(),
                    'has_remember_token' => !empty($query->getRememberToken())
                ],
                'error' => $e->getMessage()
            ]);
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to retrieve user', [
                'search_criteria' => [
                    'id' => $query->getId(),
                    'email' => $query->getEmail(),
                    'has_remember_token' => !empty($query->getRememberToken())
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \RuntimeException(
                'Failed to retrieve user: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }
}
