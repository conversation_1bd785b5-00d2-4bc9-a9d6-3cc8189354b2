<?php

namespace App\Application\Users\Handlers;

use App\Application\Users\Queries\GetUsersQuery;
use App\Application\Users\DTOs\UserDTO;
use App\Domain\Users\Repositories\UserRepositoryInterface;
use Illuminate\Support\Facades\Log;

/**
 * GetUsersHandler
 * Kullanıcı listesi sorgularını yönetir
 */
class GetUsersHandler
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Kullanıcı listesi sorgusunu gerçekleştir
     *
     * @param GetUsersQuery $query
     * @return array
     */
    public function handle(GetUsersQuery $query): array
    {
        try {
            // Arama kriterlerini hazırla
            $criteria = [];

            if ($query->isActive() !== null) {
                $criteria['is_active'] = $query->isActive();
            }

            if ($query->isEmailVerified() !== null) {
                $criteria['email_verified_at'] = $query->isEmailVerified() ? 'NOT_NULL' : 'NULL';
            }

            if ($query->getRole()) {
                $criteria['role'] = $query->getRole();
            }

            if ($query->getSearch()) {
                $criteria['search'] = $query->getSearch();
            }

            // Repository'den kullanıcıları al
            $users = $this->userRepository->search(
                criteria: $criteria,
                limit: $query->getLimit(),
                offset: $query->getOffset()
            );

            // Toplam sayıyı al
            $total = $this->userRepository->count($criteria);

            // DTO'lara dönüştür
            $userDTOs = array_map(function ($user) use ($query) {
                return UserDTO::fromEntity(
                    user: $user,
                    includeRoles: $query->shouldIncludeRoles(),
                    includePermissions: $query->shouldIncludePermissions(),
                    includeAddresses: $query->shouldIncludeAddresses(),
                    includeProfile: $query->shouldIncludeProfile()
                );
            }, $users);

            Log::info('Users retrieved successfully', [
                'criteria' => $criteria,
                'total_found' => $total,
                'returned_count' => count($userDTOs),
                'limit' => $query->getLimit(),
                'offset' => $query->getOffset()
            ]);

            return [
                'data' => $userDTOs,
                'total' => $total,
                'limit' => $query->getLimit(),
                'offset' => $query->getOffset(),
                'has_more' => ($query->getOffset() + count($userDTOs)) < $total
            ];

        } catch (\Exception $e) {
            Log::error('Failed to retrieve users', [
                'query_params' => [
                    'is_active' => $query->isActive(),
                    'is_email_verified' => $query->isEmailVerified(),
                    'role' => $query->getRole(),
                    'search' => $query->getSearch(),
                    'limit' => $query->getLimit(),
                    'offset' => $query->getOffset()
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \RuntimeException(
                'Failed to retrieve users: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }
}
