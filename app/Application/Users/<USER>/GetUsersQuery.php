<?php

namespace App\Application\Users\Queries;

class GetUsersQuery
{
    public function __construct(
        public readonly ?bool $isActive = null,
        public readonly ?bool $isEmailVerified = null,
        public readonly ?string $role = null,
        public readonly ?string $search = null,
        public readonly ?string $sortBy = 'created_at',
        public readonly string $sortDirection = 'desc',
        public readonly int $limit = 50,
        public readonly int $offset = 0,
        public readonly bool $includeRoles = false,
        public readonly bool $includePermissions = false,
        public readonly bool $includeAddresses = false,
        public readonly bool $includeProfile = false
    ) {}

    public function isActive(): ?bool
    {
        return $this->isActive;
    }

    public function isEmailVerified(): ?bool
    {
        return $this->isEmailVerified;
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }

    public function getSortBy(): ?string
    {
        return $this->sortBy;
    }

    public function getSortDirection(): string
    {
        return $this->sortDirection;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function shouldIncludeRoles(): bool
    {
        return $this->includeRoles;
    }

    public function shouldIncludePermissions(): bool
    {
        return $this->includePermissions;
    }

    public function shouldIncludeAddresses(): bool
    {
        return $this->includeAddresses;
    }

    public function shouldIncludeProfile(): bool
    {
        return $this->includeProfile;
    }

    public function getCriteria(): array
    {
        $criteria = [];

        if ($this->isActive !== null) {
            $criteria['is_active'] = $this->isActive;
        }

        if ($this->isEmailVerified !== null) {
            $criteria['is_email_verified'] = $this->isEmailVerified;
        }

        if ($this->role !== null) {
            $criteria['role'] = $this->role;
        }

        if ($this->search !== null) {
            $criteria['search'] = $this->search;
        }

        return $criteria;
    }

    public function hasSearch(): bool
    {
        return $this->search !== null && trim($this->search) !== '';
    }

    public function hasRoleFilter(): bool
    {
        return $this->role !== null;
    }
}
