<?php

namespace App\Application\Users\Handlers;

use App\Application\Users\Commands\ChangePasswordCommand;
use App\Application\Users\DTOs\UserDTO;
use App\Domain\Users\ValueObjects\Password;
use App\Domain\Users\Repositories\UserRepositoryInterface;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Core\Domain\Exceptions\DomainException;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * ChangePasswordHandler
 * Kullanıcı şifre değiştirme işlemlerini yönetir
 */
class ChangePasswordHandler
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    /**
     * Kullanıcı şifre değiştirme işlemini gerçekleştir
     *
     * @param ChangePasswordCommand $command
     * @return UserDTO
     * @throws DomainException
     * @throws EntityNotFoundException
     */
    public function handle(ChangePasswordCommand $command): UserDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Kullanıcıyı bul
                $user = $this->userRepository->findById($command->getUserId());
                if (!$user) {
                    throw new EntityNotFoundException("User with ID {$command->getUserId()} not found");
                }

                // Mevcut şifreyi doğrula
                $currentPassword = Password::fromPlainText($command->getCurrentPassword());
                if (!$user->verifyPassword($currentPassword)) {
                    throw new DomainException(
                        'Current password is incorrect',
                        'INVALID_CURRENT_PASSWORD'
                    );
                }

                // Yeni şifre value object oluştur
                $newPassword = Password::fromPlainText($command->getNewPassword());

                // Yeni şifrenin mevcut şifreden farklı olduğunu kontrol et
                if ($user->verifyPassword($newPassword)) {
                    throw new DomainException(
                        'New password must be different from current password',
                        'SAME_PASSWORD_NOT_ALLOWED'
                    );
                }

                // Şifreyi değiştir
                $user->changePassword($newPassword);

                // Kullanıcıyı kaydet
                $savedUser = $this->userRepository->save($user);

                // Domain events'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedUser);

                Log::info('Password changed successfully', [
                    'user_id' => $savedUser->getId(),
                    'email' => $savedUser->getEmail()->getValue(),
                    'changed_at' => now()->toISOString()
                ]);

                // DTO'ya dönüştür ve döndür
                return UserDTO::fromEntity($savedUser);

            } catch (DomainException $e) {
                Log::warning('Password change failed - Domain error', [
                    'user_id' => $command->getUserId(),
                    'error' => $e->getMessage(),
                    'error_code' => $e->getErrorCode()
                ]);
                throw $e;
            } catch (EntityNotFoundException $e) {
                Log::warning('Password change failed - User not found', [
                    'user_id' => $command->getUserId(),
                    'error' => $e->getMessage()
                ]);
                throw $e;
            } catch (\Exception $e) {
                Log::error('Password change failed - System error', [
                    'user_id' => $command->getUserId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw new DomainException(
                    'Password change failed due to system error',
                    'PASSWORD_CHANGE_SYSTEM_ERROR',
                    ['original_error' => $e->getMessage()]
                );
            }
        });
    }
}
