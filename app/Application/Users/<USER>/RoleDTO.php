<?php

namespace App\Application\Users\DTOs;

use App\Domain\Users\Entities\Role;

class RoleDTO
{
    public function __construct(
        public readonly int $id,
        public readonly string $name,
        public readonly string $slug,
        public readonly ?string $description,
        public readonly bool $isDefault,
        public readonly array $permissions,
        public readonly string $createdAt,
        public readonly string $updatedAt
    ) {}

    public static function fromEntity(Role $role): self
    {
        return new self(
            id: $role->getId() ?? 0,
            name: $role->getName(),
            slug: $role->getSlug()->getValue(),
            description: $role->getDescription(),
            isDefault: $role->isDefault(),
            permissions: array_map(fn($permission) => PermissionDTO::fromEntity($permission), $role->getPermissions()),
            createdAt: $role->getCreatedAt()->toISOString(),
            updatedAt: $role->getUpdatedAt()->toISOString()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'is_default' => $this->isDefault,
            'permissions' => array_map(fn($permission) => $permission->toArray(), $this->permissions),
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }

    public function hasPermission(string $permissionName): bool
    {
        foreach ($this->permissions as $permission) {
            if ($permission->slug === $permissionName) {
                return true;
            }
        }
        return false;
    }
}
