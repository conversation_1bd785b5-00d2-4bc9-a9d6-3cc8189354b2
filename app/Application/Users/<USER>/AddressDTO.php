<?php

namespace App\Application\Users\DTOs;

use App\Domain\Users\Entities\Address;

class AddressDTO
{
    public function __construct(
        public readonly int $id,
        public readonly int $userId,
        public readonly string $type,
        public readonly string $title,
        public readonly string $firstName,
        public readonly string $lastName,
        public readonly string $company,
        public readonly string $addressLine1,
        public readonly ?string $addressLine2,
        public readonly string $city,
        public readonly string $state,
        public readonly string $postalCode,
        public readonly string $country,
        public readonly ?string $phone,
        public readonly bool $isDefault,
        public readonly string $createdAt,
        public readonly string $updatedAt
    ) {}

    public static function fromEntity(Address $address): self
    {
        return new self(
            id: $address->getId() ?? 0,
            userId: $address->getUserId(),
            type: $address->getType()->getValue(),
            title: $address->getTitle(),
            firstName: $address->getFirstName(),
            lastName: $address->getLastName(),
            company: $address->getCompany(),
            addressLine1: $address->getAddressLine1(),
            addressLine2: $address->getAddressLine2(),
            city: $address->getCity(),
            state: $address->getState(),
            postalCode: $address->getPostalCode(),
            country: $address->getCountry(),
            phone: $address->getPhone(),
            isDefault: $address->isDefault(),
            createdAt: $address->getCreatedAt()->toISOString(),
            updatedAt: $address->getUpdatedAt()->toISOString()
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->userId,
            'type' => $this->type,
            'title' => $this->title,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'full_name' => $this->getFullName(),
            'company' => $this->company,
            'address_line_1' => $this->addressLine1,
            'address_line_2' => $this->addressLine2,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postalCode,
            'country' => $this->country,
            'phone' => $this->phone,
            'is_default' => $this->isDefault,
            'full_address' => $this->getFullAddress(),
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }

    public function getFullName(): string
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }

    public function getFullAddress(): string
    {
        $parts = [
            $this->addressLine1,
            $this->addressLine2,
            $this->city,
            $this->state,
            $this->postalCode,
            $this->country
        ];

        return implode(', ', array_filter($parts));
    }
}
