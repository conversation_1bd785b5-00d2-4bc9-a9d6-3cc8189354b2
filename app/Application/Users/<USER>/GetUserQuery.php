<?php

namespace App\Application\Users\Queries;

class GetUserQuery
{
    public function __construct(
        public readonly ?int $id = null,
        public readonly ?string $email = null,
        public readonly bool $includeRoles = false,
        public readonly bool $includePermissions = false,
        public readonly bool $includeAddresses = false,
        public readonly bool $includeProfile = false
    ) {}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function shouldIncludeRoles(): bool
    {
        return $this->includeRoles;
    }

    public function shouldIncludePermissions(): bool
    {
        return $this->includePermissions;
    }

    public function shouldIncludeAddresses(): bool
    {
        return $this->includeAddresses;
    }

    public function shouldIncludeProfile(): bool
    {
        return $this->includeProfile;
    }

    public function hasIdentifier(): bool
    {
        return $this->id !== null || $this->email !== null;
    }
}
