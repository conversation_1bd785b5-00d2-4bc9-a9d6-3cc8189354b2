<?php

namespace App\Application\Orders\Queries;

use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;

class GetOrderStatisticsQuery
{
    public function __construct(
        public readonly ?string $startDate = null,
        public readonly ?string $endDate = null,
        public readonly ?int $userId = null,
        public readonly ?OrderStatus $status = null,
        public readonly ?PaymentStatus $paymentStatus = null,
        public readonly string $groupBy = 'day', // day, week, month, year
        public readonly bool $includeRevenue = true,
        public readonly bool $includeStatusBreakdown = true,
        public readonly bool $includePaymentBreakdown = true,
        public readonly bool $includeTopProducts = false,
        public readonly int $topProductsLimit = 10
    ) {}

    public function getStartDate(): ?string
    {
        return $this->startDate;
    }

    public function getEndDate(): ?string
    {
        return $this->endDate;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function getStatus(): ?OrderStatus
    {
        return $this->status;
    }

    public function getPaymentStatus(): ?PaymentStatus
    {
        return $this->paymentStatus;
    }

    public function getGroupBy(): string
    {
        return $this->groupBy;
    }

    public function shouldIncludeRevenue(): bool
    {
        return $this->includeRevenue;
    }

    public function shouldIncludeStatusBreakdown(): bool
    {
        return $this->includeStatusBreakdown;
    }

    public function shouldIncludePaymentBreakdown(): bool
    {
        return $this->includePaymentBreakdown;
    }

    public function shouldIncludeTopProducts(): bool
    {
        return $this->includeTopProducts;
    }

    public function getTopProductsLimit(): int
    {
        return $this->topProductsLimit;
    }

    public function getFilters(): array
    {
        $filters = [];

        if ($this->startDate !== null) {
            $filters['start_date'] = $this->startDate;
        }

        if ($this->endDate !== null) {
            $filters['end_date'] = $this->endDate;
        }

        if ($this->userId !== null) {
            $filters['user_id'] = $this->userId;
        }

        if ($this->status !== null) {
            $filters['status'] = $this->status;
        }

        if ($this->paymentStatus !== null) {
            $filters['payment_status'] = $this->paymentStatus;
        }

        return $filters;
    }
}
