<?php

namespace App\Application\Orders\Commands;

use App\Enums\OrderStatus;

class UpdateOrderStatusCommand
{
    public function __construct(
        public readonly int $orderId,
        public readonly OrderStatus $newStatus,
        public readonly ?string $note = null,
        public readonly ?int $userId = null
    ) {}

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getNewStatus(): OrderStatus
    {
        return $this->newStatus;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }
}
