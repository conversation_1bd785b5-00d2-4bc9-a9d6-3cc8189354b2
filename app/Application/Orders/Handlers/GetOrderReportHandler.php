<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Queries\GetOrderReportQuery;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class GetOrderReportHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository
    ) {}

    public function handle(GetOrderReportQuery $query): array
    {
        try {
            // Validasyonlar
            if (!$query->isValidReportType()) {
                throw new \InvalidArgumentException("Invalid report type: {$query->getReportType()}");
            }

            if (!$query->isValidFormat()) {
                throw new \InvalidArgumentException("Invalid format: {$query->getFormat()}");
            }

            // Tarih aralığını al
            $dateRange = $query->getDateRange();
            $filters = array_merge($query->getFilters(), [
                'start_date' => $dateRange['start']->toDateString(),
                'end_date' => $dateRange['end']->toDateString()
            ]);

            // Rapor verilerini oluştur
            $reportData = [
                'report_info' => [
                    'type' => $query->getReportType(),
                    'period' => [
                        'start_date' => $dateRange['start']->toDateString(),
                        'end_date' => $dateRange['end']->toDateString()
                    ],
                    'filters' => $query->getFilters(),
                    'metrics' => $query->getMetrics(),
                    'generated_at' => Carbon::now()->toISOString()
                ],
                'summary' => $this->generateSummary($filters, $query->getMetrics()),
                'data' => $this->generateReportData($query, $filters)
            ];

            // Grafik verilerini ekle
            if ($query->shouldIncludeChartData()) {
                $reportData['chart_data'] = $this->generateChartData($query, $filters);
            }

            // Karşılaştırma verilerini ekle
            if ($query->shouldIncludeComparison()) {
                $reportData['comparison'] = $this->generateComparisonData($query, $filters);
            }

            Log::info('Order report generated successfully', [
                'report_type' => $query->getReportType(),
                'period' => $dateRange,
                'metrics' => $query->getMetrics()
            ]);

            return $reportData;

        } catch (\Exception $e) {
            Log::error('Failed to generate order report', [
                'report_type' => $query->getReportType(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function generateSummary(array $filters, array $metrics): array
    {
        $summary = [];

        if (in_array('orders', $metrics)) {
            $summary['total_orders'] = $this->orderRepository->count($filters);
        }

        if (in_array('revenue', $metrics)) {
            $summary['total_revenue'] = $this->orderRepository->getTotalRevenue($filters);
        }

        if (in_array('average_order_value', $metrics)) {
            $summary['average_order_value'] = $this->orderRepository->getAverageOrderValue($filters);
        }

        if (in_array('customers', $metrics)) {
            $summary['unique_customers'] = $this->orderRepository->getUniqueCustomerCount($filters);
        }

        return $summary;
    }

    private function generateReportData(GetOrderReportQuery $query, array $filters): array
    {
        $groupBy = $query->getReportType();
        $dateFormat = $query->getGroupByFormat();
        
        return $this->orderRepository->getReportData($filters, $groupBy, $query->getMetrics());
    }

    private function generateChartData(GetOrderReportQuery $query, array $filters): array
    {
        $groupBy = $query->getReportType();
        $metrics = $query->getMetrics();
        
        $chartData = [
            'labels' => [],
            'datasets' => []
        ];

        // Her metrik için dataset oluştur
        foreach ($metrics as $metric) {
            $data = $this->orderRepository->getTimeSeriesData($filters, $groupBy, $metric);
            
            $chartData['datasets'][] = [
                'label' => $this->getMetricLabel($metric),
                'data' => array_values($data),
                'metric' => $metric
            ];
            
            // İlk metrikten label'ları al
            if (empty($chartData['labels'])) {
                $chartData['labels'] = array_keys($data);
            }
        }

        return $chartData;
    }

    private function generateComparisonData(GetOrderReportQuery $query, array $filters): array
    {
        if (!$query->getComparisonPeriod()) {
            return [];
        }

        $dateRange = $query->getDateRange();
        $comparisonRange = $this->getComparisonDateRange(
            $dateRange['start'], 
            $dateRange['end'], 
            $query->getComparisonPeriod()
        );

        $comparisonFilters = array_merge($query->getFilters(), [
            'start_date' => $comparisonRange['start']->toDateString(),
            'end_date' => $comparisonRange['end']->toDateString()
        ]);

        $currentSummary = $this->generateSummary($filters, $query->getMetrics());
        $comparisonSummary = $this->generateSummary($comparisonFilters, $query->getMetrics());

        $comparison = [
            'current_period' => $currentSummary,
            'comparison_period' => $comparisonSummary,
            'changes' => []
        ];

        // Değişim oranlarını hesapla
        foreach ($query->getMetrics() as $metric) {
            $currentValue = $currentSummary[$this->getMetricKey($metric)] ?? 0;
            $comparisonValue = $comparisonSummary[$this->getMetricKey($metric)] ?? 0;
            
            $change = $comparisonValue > 0 ? 
                (($currentValue - $comparisonValue) / $comparisonValue) * 100 : 0;
                
            $comparison['changes'][$metric] = [
                'value' => $change,
                'direction' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'same')
            ];
        }

        return $comparison;
    }

    private function getComparisonDateRange(Carbon $start, Carbon $end, string $comparisonPeriod): array
    {
        $diffInDays = $start->diffInDays($end);
        
        switch ($comparisonPeriod) {
            case 'previous_period':
                return [
                    'start' => $start->copy()->subDays($diffInDays + 1),
                    'end' => $start->copy()->subDay()
                ];
            case 'previous_year':
                return [
                    'start' => $start->copy()->subYear(),
                    'end' => $end->copy()->subYear()
                ];
            default:
                throw new \InvalidArgumentException("Invalid comparison period: {$comparisonPeriod}");
        }
    }

    private function getMetricLabel(string $metric): string
    {
        return match($metric) {
            'orders' => 'Siparişler',
            'revenue' => 'Gelir',
            'average_order_value' => 'Ortalama Sipariş Değeri',
            'customers' => 'Müşteriler',
            default => ucfirst($metric)
        };
    }

    private function getMetricKey(string $metric): string
    {
        return match($metric) {
            'orders' => 'total_orders',
            'revenue' => 'total_revenue',
            'average_order_value' => 'average_order_value',
            'customers' => 'unique_customers',
            default => $metric
        };
    }
}
