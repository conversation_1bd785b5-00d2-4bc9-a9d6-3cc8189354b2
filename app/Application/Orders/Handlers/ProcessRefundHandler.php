<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\ProcessRefundCommand;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Entities\OrderNote;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Orders\Exceptions\InvalidRefundException;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessRefundHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(ProcessRefundCommand $command): OrderDTO
    {
        try {
            return DB::transaction(function () use ($command) {
                // Siparişi bul
                $order = $this->orderRepository->findById($command->getOrderId());

                if (!$order) {
                    throw new OrderNotFoundException("Order with ID {$command->getOrderId()} not found");
                }

                // Kullanıcı kontrolü (eğer belirtilmişse)
                if ($command->getUserId() !== null && $order->getUserId() !== $command->getUserId()) {
                    throw new OrderNotFoundException("Order not found for this user");
                }

                // İade edilebilirlik kontrolü
                $this->validateRefundEligibility($order, $command);

                // İade işlemini gerçekleştir
                $this->processRefund($order, $command);

                // İade notunu ekle (Order entity'si string parametreler alıyor)
                $order->addNote(
                    note: "İade işlemi: {$command->getReason()}. Tutar: {$command->getRefundAmount()} TL",
                    type: 'refund',
                    isPrivate: true
                );

                // Kullanıcı notları varsa ekle
                if ($command->getNotes()) {
                    $order->addNote(
                        note: $command->getNotes(),
                        type: 'admin',
                        isPrivate: true
                    );
                }

                // Siparişi kaydet
                $savedOrder = $this->orderRepository->save($order);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEventsFor($savedOrder);

                Log::info('Refund processed successfully', [
                    'order_id' => $command->getOrderId(),
                    'refund_amount' => $command->getRefundAmount(),
                    'reason' => $command->getReason(),
                    'is_partial' => $command->isPartialRefund(),
                    'user_id' => $command->getUserId()
                ]);

                return OrderDTO::fromEntity($savedOrder);
            });

        } catch (\Exception $e) {
            Log::error('Failed to process refund', [
                'order_id' => $command->getOrderId(),
                'refund_amount' => $command->getRefundAmount(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function validateRefundEligibility($order, ProcessRefundCommand $command): void
    {
        // İade tutarı kontrolü
        if ($command->getRefundAmount() <= 0) {
            throw new InvalidRefundException("Refund amount must be greater than 0");
        }

        if ($command->getRefundAmount() > $order->getTotalAmount()->getAmount()) {
            throw new InvalidRefundException("Refund amount cannot exceed order total");
        }

        // Sipariş durumu kontrolü
        $allowedStatuses = [
            OrderStatus::PAYMENT_CONFIRMED,
            OrderStatus::PROCESSING,
            OrderStatus::SHIPPED,
            OrderStatus::DELIVERED
        ];

        if (!in_array($order->getStatus(), $allowedStatuses)) {
            throw new InvalidRefundException("Order status does not allow refunds");
        }
    }

    private function processRefund($order, ProcessRefundCommand $command): void
    {
        // Kısmi iade kontrolü
        if ($command->isPartialRefund()) {
            // Kısmi iade - sipariş durumunu değiştirme
            // Sadece ödeme durumunu güncelle
            if ($command->getRefundAmount() == $order->getTotalAmount()->getAmount()) {
                $order->setPaymentStatus(PaymentStatus::REFUNDED);
                $order->setStatus(OrderStatus::CANCELLED);
            } else {
                $order->setPaymentStatus(PaymentStatus::PARTIALLY_REFUNDED);
            }
        } else {
            // Tam iade
            $order->setPaymentStatus(PaymentStatus::REFUNDED);
            $order->setStatus(OrderStatus::CANCELLED);
        }
    }
}
