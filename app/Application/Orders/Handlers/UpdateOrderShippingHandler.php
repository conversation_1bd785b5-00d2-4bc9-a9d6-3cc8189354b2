<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\UpdateOrderShippingCommand;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateOrderShippingHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(UpdateOrderShippingCommand $command): OrderDTO
    {
        try {
            return DB::transaction(function () use ($command) {
                // Siparişi bul
                $order = $this->orderRepository->findById($command->getOrderId());
                
                if (!$order) {
                    throw new OrderNotFoundException("Order with ID {$command->getOrderId()} not found");
                }

                // Kullanıcı kontrolü (eğer belirtilmişse)
                if ($command->getUserId() !== null && $order->getUserId() !== $command->getUserId()) {
                    throw new OrderNotFoundException("Order not found for this user");
                }

                // Kargo bilgilerini güncelle
                if ($command->getTrackingNumber() !== null) {
                    $order->setTrackingNumber($command->getTrackingNumber());
                }

                if ($command->getShippingCompany() !== null) {
                    $order->setShippingCompany($command->getShippingCompany());
                }

                if ($command->getShippingDate() !== null) {
                    $order->setShippingDate($command->getShippingDate());
                }

                if ($command->getEstimatedDeliveryDate() !== null) {
                    $order->setEstimatedDeliveryDate($command->getEstimatedDeliveryDate());
                }

                if ($command->getActualDeliveryDate() !== null) {
                    $order->setActualDeliveryDate($command->getActualDeliveryDate());
                }

                if ($command->getShippingMethod() !== null) {
                    $order->setShippingMethod($command->getShippingMethod());
                }

                if ($command->getShippingCost() !== null) {
                    $order->setShippingCost(new Money($command->getShippingCost()));
                }

                // Siparişi kaydet
                $savedOrder = $this->orderRepository->save($order);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEventsFor($savedOrder);

                Log::info('Order shipping information updated successfully', [
                    'order_id' => $command->getOrderId(),
                    'tracking_number' => $command->getTrackingNumber(),
                    'shipping_company' => $command->getShippingCompany(),
                    'user_id' => $command->getUserId()
                ]);

                return OrderDTO::fromEntity($savedOrder);
            });

        } catch (\Exception $e) {
            Log::error('Failed to update order shipping information', [
                'order_id' => $command->getOrderId(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
