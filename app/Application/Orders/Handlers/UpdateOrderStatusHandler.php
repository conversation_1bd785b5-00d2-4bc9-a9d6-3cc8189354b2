<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\UpdateOrderStatusCommand;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateOrderStatusHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(UpdateOrderStatusCommand $command): OrderDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Siparişi bul
                $order = $this->orderRepository->findById($command->getOrderId());
                
                if (!$order) {
                    throw new OrderNotFoundException("Order with ID {$command->getOrderId()} not found");
                }

                $oldStatus = $order->getStatus();

                // Durumu güncelle
                $order->changeStatus($command->getNewStatus(), $command->getNote());

                // Siparişi kaydet
                $savedOrder = $this->orderRepository->save($order);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedOrder);

                Log::info('Order status updated successfully', [
                    'order_id' => $savedOrder->getId(),
                    'order_number' => $savedOrder->getOrderNumber()->getValue(),
                    'old_status' => $oldStatus->value,
                    'new_status' => $command->getNewStatus()->value,
                    'user_id' => $command->getUserId(),
                    'note' => $command->getNote()
                ]);

                return OrderDTO::fromEntity($savedOrder);

            } catch (\Exception $e) {
                Log::error('Failed to update order status', [
                    'order_id' => $command->getOrderId(),
                    'new_status' => $command->getNewStatus()->value,
                    'user_id' => $command->getUserId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        });
    }
}
