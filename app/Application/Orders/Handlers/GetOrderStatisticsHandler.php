<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Queries\GetOrderStatisticsQuery;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class GetOrderStatisticsHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository
    ) {}

    public function handle(GetOrderStatisticsQuery $query): array
    {
        try {
            $statistics = [
                'period' => [
                    'start_date' => $query->getStartDate(),
                    'end_date' => $query->getEndDate(),
                    'group_by' => $query->getGroupBy()
                ],
                'summary' => $this->getSummaryStatistics($query),
                'timeline' => $this->getTimelineStatistics($query)
            ];

            // İsteğe bağlı istatistikler
            if ($query->shouldIncludeStatusBreakdown()) {
                $statistics['status_breakdown'] = $this->getStatusBreakdown($query);
            }

            if ($query->shouldIncludePaymentBreakdown()) {
                $statistics['payment_breakdown'] = $this->getPaymentBreakdown($query);
            }

            if ($query->shouldIncludeTopProducts()) {
                $statistics['top_products'] = $this->getTopProducts($query);
            }

            Log::info('Order statistics generated successfully', [
                'filters' => $query->getFilters(),
                'group_by' => $query->getGroupBy()
            ]);

            return $statistics;

        } catch (\Exception $e) {
            Log::error('Failed to generate order statistics', [
                'filters' => $query->getFilters(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function getSummaryStatistics(GetOrderStatisticsQuery $query): array
    {
        $filters = $query->getFilters();
        
        return [
            'total_orders' => $this->orderRepository->count($filters),
            'total_revenue' => $query->shouldIncludeRevenue() ? 
                $this->orderRepository->getTotalRevenue($filters) : 0,
            'average_order_value' => $query->shouldIncludeRevenue() ? 
                $this->orderRepository->getAverageOrderValue($filters) : 0,
            'completed_orders' => $this->orderRepository->count(
                array_merge($filters, ['status' => OrderStatus::DELIVERED])
            ),
            'pending_orders' => $this->orderRepository->count(
                array_merge($filters, ['status' => OrderStatus::PENDING])
            ),
            'cancelled_orders' => $this->orderRepository->count(
                array_merge($filters, ['status' => OrderStatus::CANCELLED])
            )
        ];
    }

    private function getTimelineStatistics(GetOrderStatisticsQuery $query): array
    {
        $filters = $query->getFilters();
        $groupBy = $query->getGroupBy();
        
        // Repository'den zaman serisi verilerini al
        return $this->orderRepository->getTimelineStatistics($filters, $groupBy);
    }

    private function getStatusBreakdown(GetOrderStatisticsQuery $query): array
    {
        $filters = $query->getFilters();
        $breakdown = [];

        foreach (OrderStatus::cases() as $status) {
            $statusFilters = array_merge($filters, ['status' => $status]);
            $breakdown[$status->value] = [
                'count' => $this->orderRepository->count($statusFilters),
                'label' => $status->label(),
                'revenue' => $query->shouldIncludeRevenue() ? 
                    $this->orderRepository->getTotalRevenue($statusFilters) : 0
            ];
        }

        return $breakdown;
    }

    private function getPaymentBreakdown(GetOrderStatisticsQuery $query): array
    {
        $filters = $query->getFilters();
        $breakdown = [];

        foreach (PaymentStatus::cases() as $paymentStatus) {
            $paymentFilters = array_merge($filters, ['payment_status' => $paymentStatus]);
            $breakdown[$paymentStatus->value] = [
                'count' => $this->orderRepository->count($paymentFilters),
                'label' => $paymentStatus->label(),
                'revenue' => $query->shouldIncludeRevenue() ? 
                    $this->orderRepository->getTotalRevenue($paymentFilters) : 0
            ];
        }

        return $breakdown;
    }

    private function getTopProducts(GetOrderStatisticsQuery $query): array
    {
        $filters = $query->getFilters();
        $limit = $query->getTopProductsLimit();
        
        // Repository'den en çok satılan ürünleri al
        return $this->orderRepository->getTopProducts($filters, $limit);
    }
}
