<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\AddOrderNoteCommand;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Entities\OrderNote;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AddOrderNoteHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(AddOrderNoteCommand $command): OrderDTO
    {
        try {
            return DB::transaction(function () use ($command) {
                // Siparişi bul
                $order = $this->orderRepository->findById($command->getOrderId());

                if (!$order) {
                    throw new OrderNotFoundException("Order with ID {$command->getOrderId()} not found");
                }

                // Kullanıcı kontrolü (eğer belirtilmişse)
                if ($command->getUserId() !== null && $order->getUserId() !== $command->getUserId()) {
                    throw new OrderNotFoundException("Order not found for this user");
                }

                // Notu siparişe ekle (Order entity'si string parametreler alıyor)
                $order->addNote(
                    note: $command->getNote(),
                    type: $command->getType() ?? 'general',
                    isPrivate: !$command->isPublic() // isPublic'i isPrivate'a çevir
                );

                // Siparişi kaydet
                $savedOrder = $this->orderRepository->save($order);

                // Domain event'leri dispatch et
                $this->eventDispatcher->dispatchEventsFor($savedOrder);

                Log::info('Order note added successfully', [
                    'order_id' => $command->getOrderId(),
                    'note_type' => $command->getType(),
                    'is_public' => $command->isPublic(),
                    'user_id' => $command->getUserId()
                ]);

                return OrderDTO::fromEntity($savedOrder);
            });

        } catch (\Exception $e) {
            Log::error('Failed to add order note', [
                'order_id' => $command->getOrderId(),
                'note' => $command->getNote(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
