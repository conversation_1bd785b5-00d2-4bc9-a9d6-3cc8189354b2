<?php

namespace App\Application\Orders\Handlers;

use App\Application\Orders\Commands\BulkUpdateOrdersCommand;
use App\Domain\Orders\Entities\OrderNote;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BulkUpdateOrdersHandler
{
    public function __construct(
        private OrderRepositoryInterface $orderRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    public function handle(BulkUpdateOrdersCommand $command): array
    {
        try {
            return DB::transaction(function () use ($command) {
                $results = [
                    'success' => [],
                    'failed' => [],
                    'total' => count($command->getOrderIds()),
                    'updated' => 0,
                    'errors' => []
                ];

                // Güncelleme yapılacak bir şey var mı kontrol et
                if (!$command->hasUpdates()) {
                    throw new \InvalidArgumentException("No updates specified for bulk operation");
                }

                foreach ($command->getOrderIds() as $orderId) {
                    try {
                        $this->updateSingleOrder($orderId, $command);
                        $results['success'][] = $orderId;
                        $results['updated']++;
                    } catch (\Exception $e) {
                        $results['failed'][] = $orderId;
                        $results['errors'][$orderId] = $e->getMessage();
                        
                        Log::warning('Failed to update order in bulk operation', [
                            'order_id' => $orderId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                Log::info('Bulk order update completed', [
                    'total_orders' => $results['total'],
                    'successful_updates' => $results['updated'],
                    'failed_updates' => count($results['failed']),
                    'user_id' => $command->getUserId()
                ]);

                return $results;
            });

        } catch (\Exception $e) {
            Log::error('Bulk order update failed', [
                'order_ids' => $command->getOrderIds(),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function updateSingleOrder(int $orderId, BulkUpdateOrdersCommand $command): void
    {
        // Siparişi bul
        $order = $this->orderRepository->findById($orderId);
        
        if (!$order) {
            throw new OrderNotFoundException("Order with ID {$orderId} not found");
        }

        // Durumu güncelle
        if ($command->getNewStatus() !== null) {
            $order->setStatus($command->getNewStatus());
        }

        // Ödeme durumunu güncelle
        if ($command->getNewPaymentStatus() !== null) {
            $order->setPaymentStatus($command->getNewPaymentStatus());
        }

        // Kargo şirketini güncelle
        if ($command->getShippingCompany() !== null) {
            $order->setShippingCompany($command->getShippingCompany());
        }

        // Ek verileri işle
        if ($command->getAdditionalData()) {
            $this->processAdditionalData($order, $command->getAdditionalData());
        }

        // Not ekle
        if ($command->getNote()) {
            $note = OrderNote::create(
                note: $command->getNote(),
                isPublic: false,
                type: 'bulk_update',
                userId: $command->getUserId()
            );
            $order->addNote($note);
        }

        // Siparişi kaydet
        $savedOrder = $this->orderRepository->save($order);

        // Domain event'leri dispatch et
        $this->eventDispatcher->dispatchEventsFor($savedOrder);
    }

    private function processAdditionalData($order, array $additionalData): void
    {
        foreach ($additionalData as $key => $value) {
            switch ($key) {
                case 'tracking_number':
                    $order->setTrackingNumber($value);
                    break;
                case 'shipping_method':
                    $order->setShippingMethod($value);
                    break;
                case 'admin_notes':
                    $order->setAdminNotes($value);
                    break;
                // Diğer alanlar için genişletilebilir
            }
        }
    }
}
