<?php

namespace App\Application\Orders\Validators;

use App\Application\Orders\Commands\ProcessRefundCommand;
use App\Domain\Orders\Exceptions\InvalidOrderDataException;

class ProcessRefundValidator
{
    public function validate(ProcessRefundCommand $command): void
    {
        $this->validateOrderId($command->getOrderId());
        $this->validateRefundAmount($command->getRefundAmount());
        $this->validateReason($command->getReason());
        $this->validateRefundMethod($command->getRefundMethod());
        $this->validateRefundItems($command->getRefundItems());
        $this->validateNotes($command->getNotes());
        $this->validateUserId($command->getUserId());
    }

    private function validateOrderId(int $orderId): void
    {
        if ($orderId <= 0) {
            throw new InvalidOrderDataException("Order ID must be a positive integer");
        }
    }

    private function validateRefundAmount(float $refundAmount): void
    {
        if ($refundAmount <= 0) {
            throw new InvalidOrderDataException("Refund amount must be greater than 0");
        }

        if ($refundAmount > 999999.99) {
            throw new InvalidOrderDataException("Refund amount cannot exceed 999,999.99");
        }
    }

    private function validateReason(string $reason): void
    {
        if (empty(trim($reason))) {
            throw new InvalidOrderDataException("Refund reason cannot be empty");
        }

        if (strlen($reason) > 500) {
            throw new InvalidOrderDataException("Refund reason cannot exceed 500 characters");
        }

        $allowedReasons = [
            'customer_request',
            'product_defect',
            'wrong_item_sent',
            'damaged_in_shipping',
            'not_as_described',
            'duplicate_order',
            'fraud_prevention',
            'other'
        ];

        // Eğer reason predefined değerlerden biriyse kontrol et
        $reasonWords = explode(' ', strtolower($reason));
        $hasValidReason = false;
        
        foreach ($allowedReasons as $allowedReason) {
            if (in_array(str_replace('_', ' ', $allowedReason), $reasonWords) || 
                $reason === $allowedReason) {
                $hasValidReason = true;
                break;
            }
        }

        // Eğer özel bir sebep yazılmışsa minimum uzunluk kontrolü
        if (!$hasValidReason && strlen(trim($reason)) < 10) {
            throw new InvalidOrderDataException("Custom refund reason must be at least 10 characters long");
        }
    }

    private function validateRefundMethod(?string $refundMethod): void
    {
        if ($refundMethod === null) {
            return;
        }

        $allowedMethods = [
            'original_payment_method',
            'bank_transfer',
            'store_credit',
            'cash',
            'check'
        ];

        if (!in_array($refundMethod, $allowedMethods)) {
            throw new InvalidOrderDataException("Invalid refund method: {$refundMethod}");
        }
    }

    private function validateRefundItems(?array $refundItems): void
    {
        if ($refundItems === null) {
            return;
        }

        if (empty($refundItems)) {
            throw new InvalidOrderDataException("Refund items array cannot be empty when provided");
        }

        foreach ($refundItems as $index => $item) {
            $this->validateRefundItem($item, $index);
        }
    }

    private function validateRefundItem(array $item, int $index): void
    {
        $requiredFields = ['order_item_id', 'quantity', 'amount'];
        
        foreach ($requiredFields as $field) {
            if (!isset($item[$field])) {
                throw new InvalidOrderDataException("Refund item {$index}: Missing required field '{$field}'");
            }
        }

        if (!is_int($item['order_item_id']) || $item['order_item_id'] <= 0) {
            throw new InvalidOrderDataException("Refund item {$index}: Order item ID must be a positive integer");
        }

        if (!is_int($item['quantity']) || $item['quantity'] <= 0) {
            throw new InvalidOrderDataException("Refund item {$index}: Quantity must be a positive integer");
        }

        if (!is_numeric($item['amount']) || $item['amount'] <= 0) {
            throw new InvalidOrderDataException("Refund item {$index}: Amount must be a positive number");
        }
    }

    private function validateNotes(?string $notes): void
    {
        if ($notes !== null && strlen($notes) > 1000) {
            throw new InvalidOrderDataException("Refund notes cannot exceed 1000 characters");
        }
    }

    private function validateUserId(?int $userId): void
    {
        if ($userId !== null && $userId <= 0) {
            throw new InvalidOrderDataException("User ID must be a positive integer");
        }
    }
}
