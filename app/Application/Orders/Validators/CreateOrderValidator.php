<?php

namespace App\Application\Orders\Validators;

use App\Application\Orders\Commands\CreateOrderCommand;
use App\Domain\Orders\Exceptions\InvalidOrderDataException;

class CreateOrderValidator
{
    public function validate(CreateOrderCommand $command): void
    {
        $this->validateUserId($command->getUserId());
        $this->validateItems($command->getItems());
        $this->validatePaymentMethod($command->getPaymentMethod());
        $this->validateShippingMethod($command->getShippingMethod());
        $this->validateAddresses($command->getBillingAddress(), $command->getShippingAddress());
        $this->validateAmounts($command);
    }

    private function validateUserId(int $userId): void
    {
        if ($userId <= 0) {
            throw new InvalidOrderDataException("User ID must be a positive integer");
        }
    }

    private function validateItems(array $items): void
    {
        if (empty($items)) {
            throw new InvalidOrderDataException("Order must contain at least one item");
        }

        foreach ($items as $index => $item) {
            $this->validateItem($item, $index);
        }
    }

    private function validateItem(array $item, int $index): void
    {
        $requiredFields = ['product_id', 'product_name', 'price', 'quantity'];
        
        foreach ($requiredFields as $field) {
            if (!isset($item[$field])) {
                throw new InvalidOrderDataException("Item {$index}: Missing required field '{$field}'");
            }
        }

        if (!is_int($item['product_id']) || $item['product_id'] <= 0) {
            throw new InvalidOrderDataException("Item {$index}: Product ID must be a positive integer");
        }

        if (empty(trim($item['product_name']))) {
            throw new InvalidOrderDataException("Item {$index}: Product name cannot be empty");
        }

        if (!is_numeric($item['price']) || $item['price'] < 0) {
            throw new InvalidOrderDataException("Item {$index}: Price must be a non-negative number");
        }

        if (!is_int($item['quantity']) || $item['quantity'] <= 0) {
            throw new InvalidOrderDataException("Item {$index}: Quantity must be a positive integer");
        }
    }

    private function validatePaymentMethod(string $paymentMethod): void
    {
        if (empty(trim($paymentMethod))) {
            throw new InvalidOrderDataException("Payment method cannot be empty");
        }

        $allowedMethods = ['credit_card', 'debit_card', 'bank_transfer', 'cash_on_delivery', 'paypal'];
        
        if (!in_array($paymentMethod, $allowedMethods)) {
            throw new InvalidOrderDataException("Invalid payment method: {$paymentMethod}");
        }
    }

    private function validateShippingMethod(string $shippingMethod): void
    {
        if (empty(trim($shippingMethod))) {
            throw new InvalidOrderDataException("Shipping method cannot be empty");
        }

        $allowedMethods = ['standard', 'express', 'overnight', 'pickup'];
        
        if (!in_array($shippingMethod, $allowedMethods)) {
            throw new InvalidOrderDataException("Invalid shipping method: {$shippingMethod}");
        }
    }

    private function validateAddresses(?array $billingAddress, ?array $shippingAddress): void
    {
        if ($billingAddress !== null) {
            $this->validateAddress($billingAddress, 'billing');
        }

        if ($shippingAddress !== null) {
            $this->validateAddress($shippingAddress, 'shipping');
        }
    }

    private function validateAddress(array $address, string $type): void
    {
        $requiredFields = ['street', 'city', 'postal_code', 'country'];
        
        foreach ($requiredFields as $field) {
            if (!isset($address[$field]) || empty(trim($address[$field]))) {
                throw new InvalidOrderDataException("{$type} address: Missing or empty required field '{$field}'");
            }
        }

        // Posta kodu formatı kontrolü (Türkiye için)
        if (isset($address['country']) && $address['country'] === 'TR') {
            if (!preg_match('/^\d{5}$/', $address['postal_code'])) {
                throw new InvalidOrderDataException("{$type} address: Invalid postal code format for Turkey");
            }
        }
    }

    private function validateAmounts(CreateOrderCommand $command): void
    {
        if ($command->getShippingCost() !== null && $command->getShippingCost() < 0) {
            throw new InvalidOrderDataException("Shipping cost cannot be negative");
        }

        if ($command->getTaxAmount() !== null && $command->getTaxAmount() < 0) {
            throw new InvalidOrderDataException("Tax amount cannot be negative");
        }

        if ($command->getDiscountAmount() !== null && $command->getDiscountAmount() < 0) {
            throw new InvalidOrderDataException("Discount amount cannot be negative");
        }

        // Toplam tutar kontrolü
        $itemsTotal = array_sum(array_map(
            fn($item) => $item['price'] * $item['quantity'],
            $command->getItems()
        ));

        $shippingCost = $command->getShippingCost() ?? 0;
        $taxAmount = $command->getTaxAmount() ?? 0;
        $discountAmount = $command->getDiscountAmount() ?? 0;

        $calculatedTotal = $itemsTotal + $shippingCost + $taxAmount - $discountAmount;

        if ($calculatedTotal < 0) {
            throw new InvalidOrderDataException("Order total cannot be negative");
        }
    }
}
