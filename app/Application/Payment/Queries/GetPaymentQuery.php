<?php

namespace App\Application\Payment\Queries;

/**
 * GetPaymentQuery
 * Ödeme detayı getirme sorgusu
 */
class GetPaymentQuery
{
    public function __construct(
        public readonly ?int $paymentId = null,
        public readonly ?string $transactionId = null,
        public readonly ?int $orderId = null,
        public readonly ?string $gatewayTransactionId = null,
        public readonly bool $includeRefunds = false,
        public readonly bool $includeGatewayResponse = false,
        public readonly bool $includeMetadata = true
    ) {}

    /**
     * Payment ID'yi getir
     */
    public function getPaymentId(): ?int
    {
        return $this->paymentId;
    }

    /**
     * Transaction ID'yi getir
     */
    public function getTransactionId(): ?string
    {
        return $this->transactionId;
    }

    /**
     * Order ID'yi getir
     */
    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    /**
     * Gateway transaction ID'yi getir
     */
    public function getGatewayTransactionId(): ?string
    {
        return $this->gatewayTransactionId;
    }

    /**
     * İadeleri dahil et mi kontrol et
     */
    public function shouldIncludeRefunds(): bool
    {
        return $this->includeRefunds;
    }

    /**
     * Gateway response'unu dahil et mi kontrol et
     */
    public function shouldIncludeGatewayResponse(): bool
    {
        return $this->includeGatewayResponse;
    }

    /**
     * Metadata'yı dahil et mi kontrol et
     */
    public function shouldIncludeMetadata(): bool
    {
        return $this->includeMetadata;
    }

    /**
     * ID ile arama mı kontrol et
     */
    public function isSearchById(): bool
    {
        return $this->paymentId !== null;
    }

    /**
     * Transaction ID ile arama mı kontrol et
     */
    public function isSearchByTransactionId(): bool
    {
        return $this->transactionId !== null;
    }

    /**
     * Order ID ile arama mı kontrol et
     */
    public function isSearchByOrderId(): bool
    {
        return $this->orderId !== null;
    }

    /**
     * Gateway transaction ID ile arama mı kontrol et
     */
    public function isSearchByGatewayTransactionId(): bool
    {
        return $this->gatewayTransactionId !== null;
    }

    /**
     * Geçerli arama kriterleri var mı kontrol et
     */
    public function hasValidSearchCriteria(): bool
    {
        return $this->paymentId !== null 
            || $this->transactionId !== null 
            || $this->orderId !== null 
            || $this->gatewayTransactionId !== null;
    }
}
