<?php

namespace App\Application\Payment\Queries;

/**
 * GetPaymentHistoryQuery
 * Ödeme geçmişi getirme sorgusu
 */
class GetPaymentHistoryQuery
{
    public function __construct(
        public readonly ?int $userId = null,
        public readonly ?string $status = null,
        public readonly ?string $gateway = null,
        public readonly ?string $paymentMethod = null,
        public readonly ?\DateTime $startDate = null,
        public readonly ?\DateTime $endDate = null,
        public readonly int $limit = 50,
        public readonly int $offset = 0,
        public readonly string $sortBy = 'created_at',
        public readonly string $sortDirection = 'desc',
        public readonly bool $includeRefunds = false,
        public readonly bool $includeMetadata = false
    ) {}

    /**
     * User ID'yi getir
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Durumu getir
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * Gateway'i getir
     */
    public function getGateway(): ?string
    {
        return $this->gateway;
    }

    /**
     * Ödeme yöntemini getir
     */
    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    /**
     * Başlangıç tarihini getir
     */
    public function getStartDate(): ?\DateTime
    {
        return $this->startDate;
    }

    /**
     * Bitiş tarihini getir
     */
    public function getEndDate(): ?\DateTime
    {
        return $this->endDate;
    }

    /**
     * Limit'i getir
     */
    public function getLimit(): int
    {
        return $this->limit;
    }

    /**
     * Offset'i getir
     */
    public function getOffset(): int
    {
        return $this->offset;
    }

    /**
     * Sıralama alanını getir
     */
    public function getSortBy(): string
    {
        return $this->sortBy;
    }

    /**
     * Sıralama yönünü getir
     */
    public function getSortDirection(): string
    {
        return $this->sortDirection;
    }

    /**
     * İadeleri dahil et mi kontrol et
     */
    public function shouldIncludeRefunds(): bool
    {
        return $this->includeRefunds;
    }

    /**
     * Metadata'yı dahil et mi kontrol et
     */
    public function shouldIncludeMetadata(): bool
    {
        return $this->includeMetadata;
    }

    /**
     * Kullanıcı ödemelerini getir mi kontrol et
     */
    public function isUserPayments(): bool
    {
        return $this->userId !== null;
    }

    /**
     * Durum filtresi var mı kontrol et
     */
    public function hasStatusFilter(): bool
    {
        return $this->status !== null;
    }

    /**
     * Gateway filtresi var mı kontrol et
     */
    public function hasGatewayFilter(): bool
    {
        return $this->gateway !== null;
    }

    /**
     * Ödeme yöntemi filtresi var mı kontrol et
     */
    public function hasPaymentMethodFilter(): bool
    {
        return $this->paymentMethod !== null;
    }

    /**
     * Tarih aralığı filtresi var mı kontrol et
     */
    public function hasDateRangeFilter(): bool
    {
        return $this->startDate !== null || $this->endDate !== null;
    }

    /**
     * Herhangi bir filtre var mı kontrol et
     */
    public function hasFilters(): bool
    {
        return $this->hasStatusFilter() 
            || $this->hasGatewayFilter() 
            || $this->hasPaymentMethodFilter() 
            || $this->hasDateRangeFilter();
    }

    /**
     * Sayfalama var mı kontrol et
     */
    public function hasPagination(): bool
    {
        return $this->limit > 0;
    }
}
