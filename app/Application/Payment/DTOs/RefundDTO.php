<?php

namespace App\Application\Payment\DTOs;

use App\Core\Application\DTO\DataTransferObject;
use App\Domain\Payment\Entities\Refund;

/**
 * RefundDTO
 * İade data transfer object
 */
class RefundDTO extends DataTransferObject
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $paymentId,
        public readonly string $transactionId,
        public readonly array $amount,
        public readonly string $type,
        public readonly string $reason,
        public readonly string $status,
        public readonly ?string $description,
        public readonly array $gatewayResponse,
        public readonly ?string $gatewayRefundId,
        public readonly ?string $failureReason,
        public readonly array $metadata,
        public readonly string $createdAt,
        public readonly string $updatedAt,
        public readonly ?string $processedAt,
        public readonly ?string $failedAt,
        public readonly bool $isFullRefund,
        public readonly bool $isPartialRefund,
        public readonly bool $isFinalStatus
    ) {}

    /**
     * Domain entity'den DTO oluştur
     */
    public static function fromEntity(
        Refund $refund,
        bool $includeGatewayResponse = false,
        bool $includeMetadata = true
    ): self {
        return new self(
            id: $refund->getId(),
            paymentId: $refund->getPaymentId(),
            transactionId: $refund->getTransactionId()->getValue(),
            amount: $refund->getAmount()->toArray(),
            type: $refund->getType(),
            reason: $refund->getReason(),
            status: $refund->getStatus(),
            description: $refund->getDescription(),
            gatewayResponse: $includeGatewayResponse ? $refund->getGatewayResponse() : [],
            gatewayRefundId: $refund->getGatewayRefundId(),
            failureReason: $refund->getFailureReason(),
            metadata: $includeMetadata ? $refund->getMetadata() : [],
            createdAt: $refund->getCreatedAt()->toISOString(),
            updatedAt: $refund->getUpdatedAt()->toISOString(),
            processedAt: $refund->getProcessedAt()?->toISOString(),
            failedAt: $refund->getFailedAt()?->toISOString(),
            isFullRefund: $refund->isFullRefund(),
            isPartialRefund: $refund->isPartialRefund(),
            isFinalStatus: $refund->isFinalStatus()
        );
    }

    /**
     * Array'den DTO oluştur
     */
    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? null,
            paymentId: $data['payment_id'],
            transactionId: $data['transaction_id'],
            amount: $data['amount'] ?? [],
            type: $data['type'],
            reason: $data['reason'],
            status: $data['status'],
            description: $data['description'] ?? null,
            gatewayResponse: $data['gateway_response'] ?? [],
            gatewayRefundId: $data['gateway_refund_id'] ?? null,
            failureReason: $data['failure_reason'] ?? null,
            metadata: $data['metadata'] ?? [],
            createdAt: $data['created_at'],
            updatedAt: $data['updated_at'],
            processedAt: $data['processed_at'] ?? null,
            failedAt: $data['failed_at'] ?? null,
            isFullRefund: $data['is_full_refund'] ?? false,
            isPartialRefund: $data['is_partial_refund'] ?? false,
            isFinalStatus: $data['is_final_status'] ?? false
        );
    }

    /**
     * Array'e dönüştür
     */
    public function toArray(): array
    {
        $array = [
            'id' => $this->id,
            'payment_id' => $this->paymentId,
            'transaction_id' => $this->transactionId,
            'amount' => $this->amount,
            'type' => $this->type,
            'reason' => $this->reason,
            'status' => $this->status,
            'description' => $this->description,
            'gateway_refund_id' => $this->gatewayRefundId,
            'failure_reason' => $this->failureReason,
            'metadata' => $this->metadata,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
            'processed_at' => $this->processedAt,
            'failed_at' => $this->failedAt,
            'is_full_refund' => $this->isFullRefund,
            'is_partial_refund' => $this->isPartialRefund,
            'is_final_status' => $this->isFinalStatus,
        ];

        if (!empty($this->gatewayResponse)) {
            $array['gateway_response'] = $this->gatewayResponse;
        }

        return $array;
    }

    /**
     * Beklemede mi kontrol et
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * İşlemde mi kontrol et
     */
    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * Tamamlanmış mı kontrol et
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Başarısız mı kontrol et
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * İptal edilmiş mi kontrol et
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Gateway refund ID var mı kontrol et
     */
    public function hasGatewayRefundId(): bool
    {
        return $this->gatewayRefundId !== null;
    }

    /**
     * Başarısızlık nedeni var mı kontrol et
     */
    public function hasFailureReason(): bool
    {
        return $this->failureReason !== null;
    }

    /**
     * Tutarı getir
     */
    public function getAmount(): float
    {
        return $this->amount['amount'] ?? 0.0;
    }

    /**
     * Net tutarı getir
     */
    public function getNetAmount(): float
    {
        return $this->amount['net_amount'] ?? 0.0;
    }

    /**
     * Para birimini getir
     */
    public function getCurrency(): string
    {
        return $this->amount['currency'] ?? 'TRY';
    }

    /**
     * Kısa transaction ID getir
     */
    public function getShortTransactionId(): string
    {
        return substr($this->transactionId, -8);
    }

    /**
     * Müşteri talebi mi kontrol et
     */
    public function isCustomerRequest(): bool
    {
        return $this->reason === 'customer_request';
    }

    /**
     * Kusurlu ürün nedeniyle mi kontrol et
     */
    public function isDefectiveProduct(): bool
    {
        return $this->reason === 'defective_product';
    }

    /**
     * Chargeback nedeniyle mi kontrol et
     */
    public function isChargeback(): bool
    {
        return $this->reason === 'chargeback';
    }

    /**
     * Fraud nedeniyle mi kontrol et
     */
    public function isFraud(): bool
    {
        return $this->reason === 'fraud';
    }

    /**
     * Reason'ı human readable formatta getir
     */
    public function getReasonLabel(): string
    {
        return match($this->reason) {
            'customer_request' => 'Müşteri Talebi',
            'defective_product' => 'Kusurlu Ürün',
            'wrong_product' => 'Yanlış Ürün',
            'late_delivery' => 'Geç Teslimat',
            'duplicate_payment' => 'Çift Ödeme',
            'fraud' => 'Dolandırıcılık',
            'chargeback' => 'Chargeback',
            'other' => 'Diğer',
            default => ucfirst(str_replace('_', ' ', $this->reason))
        };
    }

    /**
     * Status'u human readable formatta getir
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'pending' => 'Beklemede',
            'processing' => 'İşleniyor',
            'completed' => 'Tamamlandı',
            'failed' => 'Başarısız',
            'cancelled' => 'İptal Edildi',
            default => ucfirst($this->status)
        };
    }

    /**
     * Type'ı human readable formatta getir
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'full' => 'Tam İade',
            'partial' => 'Kısmi İade',
            default => ucfirst($this->type)
        };
    }
}
