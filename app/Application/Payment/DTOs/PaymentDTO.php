<?php

namespace App\Application\Payment\DTOs;

use App\Core\Application\DTO\DataTransferObject;
use App\Domain\Payment\Entities\Payment;

/**
 * PaymentDTO
 * Ödeme data transfer object
 */
class PaymentDTO extends DataTransferObject
{
    public function __construct(
        public readonly ?int $id,
        public readonly string $transactionId,
        public readonly int $orderId,
        public readonly ?int $userId,
        public readonly array $amount,
        public readonly array $gateway,
        public readonly string $paymentMethod,
        public readonly string $status,
        public readonly ?array $token,
        public readonly array $gatewayResponse,
        public readonly ?string $gatewayTransactionId,
        public readonly ?string $failureReason,
        public readonly ?string $description,
        public readonly array $metadata,
        public readonly string $createdAt,
        public readonly string $updatedAt,
        public readonly ?string $completedAt,
        public readonly ?string $failedAt,
        public readonly bool $isSuccessful,
        public readonly bool $isFinalStatus,
        public readonly bool $canBeRefunded,
        public readonly ?array $refunds = null
    ) {}

    /**
     * Domain entity'den DTO oluştur
     */
    public static function fromEntity(
        Payment $payment, 
        bool $includeRefunds = false,
        bool $includeGatewayResponse = false,
        bool $includeMetadata = true,
        ?array $refunds = null
    ): self {
        return new self(
            id: $payment->getId(),
            transactionId: $payment->getTransactionId()->getValue(),
            orderId: $payment->getOrderId(),
            userId: $payment->getUserId(),
            amount: $payment->getAmount()->toArray(),
            gateway: $payment->getGateway()->toArray(),
            paymentMethod: $payment->getPaymentMethod(),
            status: $payment->getStatus(),
            token: $payment->getToken()?->toArray(),
            gatewayResponse: $includeGatewayResponse ? $payment->getGatewayResponse() : [],
            gatewayTransactionId: $payment->getGatewayTransactionId(),
            failureReason: $payment->getFailureReason(),
            description: $payment->getDescription(),
            metadata: $includeMetadata ? $payment->getMetadata() : [],
            createdAt: $payment->getCreatedAt()->toISOString(),
            updatedAt: $payment->getUpdatedAt()->toISOString(),
            completedAt: $payment->getCompletedAt()?->toISOString(),
            failedAt: $payment->getFailedAt()?->toISOString(),
            isSuccessful: $payment->isSuccessful(),
            isFinalStatus: $payment->isFinalStatus(),
            canBeRefunded: $payment->canBeRefunded(),
            refunds: $includeRefunds ? $refunds : null
        );
    }

    /**
     * Array'den DTO oluştur
     */
    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'] ?? null,
            transactionId: $data['transaction_id'],
            orderId: $data['order_id'],
            userId: $data['user_id'] ?? null,
            amount: $data['amount'] ?? [],
            gateway: $data['gateway'] ?? [],
            paymentMethod: $data['payment_method'],
            status: $data['status'],
            token: $data['token'] ?? null,
            gatewayResponse: $data['gateway_response'] ?? [],
            gatewayTransactionId: $data['gateway_transaction_id'] ?? null,
            failureReason: $data['failure_reason'] ?? null,
            description: $data['description'] ?? null,
            metadata: $data['metadata'] ?? [],
            createdAt: $data['created_at'],
            updatedAt: $data['updated_at'],
            completedAt: $data['completed_at'] ?? null,
            failedAt: $data['failed_at'] ?? null,
            isSuccessful: $data['is_successful'] ?? false,
            isFinalStatus: $data['is_final_status'] ?? false,
            canBeRefunded: $data['can_be_refunded'] ?? false,
            refunds: $data['refunds'] ?? null
        );
    }

    /**
     * Array'e dönüştür
     */
    public function toArray(): array
    {
        $array = [
            'id' => $this->id,
            'transaction_id' => $this->transactionId,
            'order_id' => $this->orderId,
            'user_id' => $this->userId,
            'amount' => $this->amount,
            'gateway' => $this->gateway,
            'payment_method' => $this->paymentMethod,
            'status' => $this->status,
            'token' => $this->token,
            'gateway_transaction_id' => $this->gatewayTransactionId,
            'failure_reason' => $this->failureReason,
            'description' => $this->description,
            'metadata' => $this->metadata,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
            'completed_at' => $this->completedAt,
            'failed_at' => $this->failedAt,
            'is_successful' => $this->isSuccessful,
            'is_final_status' => $this->isFinalStatus,
            'can_be_refunded' => $this->canBeRefunded,
        ];

        if (!empty($this->gatewayResponse)) {
            $array['gateway_response'] = $this->gatewayResponse;
        }

        if ($this->refunds !== null) {
            $array['refunds'] = $this->refunds;
        }

        return $array;
    }

    /**
     * Misafir ödeme mi kontrol et
     */
    public function isGuestPayment(): bool
    {
        return $this->userId === null;
    }

    /**
     * Beklemede mi kontrol et
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * İşlemde mi kontrol et
     */
    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * Tamamlanmış mı kontrol et
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Başarısız mı kontrol et
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * İptal edilmiş mi kontrol et
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * İade edilmiş mi kontrol et
     */
    public function isRefunded(): bool
    {
        return in_array($this->status, ['refunded', 'partially_refunded']);
    }

    /**
     * Token var mı kontrol et
     */
    public function hasToken(): bool
    {
        return $this->token !== null;
    }

    /**
     * Gateway transaction ID var mı kontrol et
     */
    public function hasGatewayTransactionId(): bool
    {
        return $this->gatewayTransactionId !== null;
    }

    /**
     * Başarısızlık nedeni var mı kontrol et
     */
    public function hasFailureReason(): bool
    {
        return $this->failureReason !== null;
    }

    /**
     * İadeler var mı kontrol et
     */
    public function hasRefunds(): bool
    {
        return $this->refunds !== null && !empty($this->refunds);
    }

    /**
     * Tutarı getir
     */
    public function getAmount(): float
    {
        return $this->amount['amount'] ?? 0.0;
    }

    /**
     * Net tutarı getir
     */
    public function getNetAmount(): float
    {
        return $this->amount['net_amount'] ?? 0.0;
    }

    /**
     * Komisyon tutarını getir
     */
    public function getFeeAmount(): float
    {
        return $this->amount['fee_amount'] ?? 0.0;
    }

    /**
     * Para birimini getir
     */
    public function getCurrency(): string
    {
        return $this->amount['currency'] ?? 'TRY';
    }

    /**
     * Gateway adını getir
     */
    public function getGatewayName(): string
    {
        return $this->gateway['name'] ?? '';
    }

    /**
     * Gateway provider'ını getir
     */
    public function getGatewayProvider(): string
    {
        return $this->gateway['provider'] ?? '';
    }

    /**
     * Kısa transaction ID getir
     */
    public function getShortTransactionId(): string
    {
        return substr($this->transactionId, -8);
    }

    /**
     * Formatted transaction ID getir
     */
    public function getFormattedTransactionId(): string
    {
        $parts = explode('_', $this->transactionId);
        if (count($parts) >= 3) {
            return strtoupper($parts[1]) . '-' . strtoupper(substr($parts[2], -6));
        }
        
        return strtoupper(substr($this->transactionId, -8));
    }
}
