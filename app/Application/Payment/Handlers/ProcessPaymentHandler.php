<?php

namespace App\Application\Payment\Handlers;

use App\Application\Payment\Commands\ProcessPaymentCommand;
use App\Application\Payment\DTOs\PaymentDTO;
use App\Domain\Payment\Entities\Payment;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Domain\Payment\ValueObjects\PaymentGateway;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Core\Domain\ValueObjects\Money;
use App\Core\Domain\Exceptions\DomainException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * ProcessPaymentHandler
 * Ödeme işleme komutlarını yönetir
 */
class ProcessPaymentHandler
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    /**
     * Ödeme işleme komutunu gerçekleştir
     *
     * @param ProcessPaymentCommand $command
     * @return PaymentDTO
     * @throws DomainException
     */
    public function handle(ProcessPaymentCommand $command): PaymentDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Order için zaten ödeme var mı kontrol et
                if ($this->paymentRepository->existsForOrder($command->getOrderId())) {
                    throw new DomainException(
                        'Payment already exists for this order',
                        'PAYMENT_ALREADY_EXISTS'
                    );
                }

                // Payment amount oluştur
                $amount = $this->createPaymentAmount($command);

                // Payment gateway oluştur
                $gateway = $this->createPaymentGateway($command);

                // Payment entity oluştur
                $payment = $this->createPayment($command, $amount, $gateway);

                // Ödemeyi kaydet
                $savedPayment = $this->paymentRepository->save($payment);

                // Domain events'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedPayment);

                Log::info('Payment initiated successfully', [
                    'payment_id' => $savedPayment->getId(),
                    'transaction_id' => $savedPayment->getTransactionId()->getValue(),
                    'order_id' => $savedPayment->getOrderId(),
                    'user_id' => $savedPayment->getUserId(),
                    'amount' => $savedPayment->getAmount()->getAmount()->getAmount(),
                    'gateway' => $savedPayment->getGateway()->getProvider(),
                    'payment_method' => $savedPayment->getPaymentMethod()
                ]);

                // DTO'ya dönüştür ve döndür
                return PaymentDTO::fromEntity($savedPayment, false, false, true);

            } catch (DomainException $e) {
                Log::warning('Payment processing failed - Domain error', [
                    'order_id' => $command->getOrderId(),
                    'user_id' => $command->getUserId(),
                    'amount' => $command->getAmount(),
                    'gateway' => $command->getGateway(),
                    'error' => $e->getMessage()
                ]);
                throw $e;
            } catch (\Exception $e) {
                Log::error('Payment processing failed - System error', [
                    'order_id' => $command->getOrderId(),
                    'user_id' => $command->getUserId(),
                    'amount' => $command->getAmount(),
                    'gateway' => $command->getGateway(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw new DomainException(
                    'Failed to process payment due to system error',
                    'PAYMENT_PROCESSING_ERROR',
                    ['original_error' => $e->getMessage()]
                );
            }
        });
    }

    /**
     * PaymentAmount oluştur
     */
    private function createPaymentAmount(ProcessPaymentCommand $command): PaymentAmount
    {
        $amount = Money::fromAmount($command->getAmount(), $command->getCurrency());
        $feeAmount = $command->hasFee() 
            ? Money::fromAmount($command->getFeeAmount(), $command->getCurrency())
            : null;

        return PaymentAmount::create($amount, $feeAmount);
    }

    /**
     * PaymentGateway oluştur
     */
    private function createPaymentGateway(ProcessPaymentCommand $command): PaymentGateway
    {
        $configuration = $this->buildGatewayConfiguration($command);

        return match (strtolower($command->getGateway())) {
            'iyzico' => PaymentGateway::iyzico($configuration),
            'stripe' => PaymentGateway::stripe($configuration),
            'paypal' => PaymentGateway::paypal($configuration),
            'bank_transfer' => PaymentGateway::bankTransfer($configuration),
            default => throw new DomainException(
                "Unsupported payment gateway: {$command->getGateway()}",
                'UNSUPPORTED_GATEWAY'
            )
        };
    }

    /**
     * Gateway konfigürasyonu oluştur
     */
    private function buildGatewayConfiguration(ProcessPaymentCommand $command): array
    {
        $configuration = [];

        if ($command->shouldUse3DSecure()) {
            $configuration['3d_secure_enabled'] = true;
        }

        if ($command->isInstallmentPayment()) {
            $configuration['installments'] = $command->getInstallments();
        }

        if ($command->hasCallbackUrl()) {
            $configuration['callback_url'] = $command->getCallbackUrl();
        }

        if ($command->hasErrorUrl()) {
            $configuration['error_url'] = $command->getErrorUrl();
        }

        // Gateway-specific configuration
        if ($command->isIyzicoPayment()) {
            $configuration = array_merge($configuration, $this->getIyzicoConfiguration($command));
        } elseif ($command->isStripePayment()) {
            $configuration = array_merge($configuration, $this->getStripeConfiguration($command));
        }

        return $configuration;
    }

    /**
     * İyzico konfigürasyonu
     */
    private function getIyzicoConfiguration(ProcessPaymentCommand $command): array
    {
        $config = [
            'api_key' => config('payment.iyzico.api_key'),
            'secret_key' => config('payment.iyzico.secret_key'),
            'base_url' => config('payment.iyzico.base_url'),
        ];

        // Billing ve shipping bilgilerini ekle
        if (!empty($command->getBillingDetails())) {
            $config['billing_address'] = $command->getBillingDetails();
        }

        if (!empty($command->getShippingDetails())) {
            $config['shipping_address'] = $command->getShippingDetails();
        }

        return $config;
    }

    /**
     * Stripe konfigürasyonu
     */
    private function getStripeConfiguration(ProcessPaymentCommand $command): array
    {
        return [
            'api_key' => config('payment.stripe.api_key'),
            'webhook_secret' => config('payment.stripe.webhook_secret'),
        ];
    }

    /**
     * Payment entity oluştur
     */
    private function createPayment(
        ProcessPaymentCommand $command,
        PaymentAmount $amount,
        PaymentGateway $gateway
    ): Payment {
        // Gateway-specific payment creation
        if ($command->isIyzicoPayment()) {
            return Payment::createForIyzico(
                $command->getOrderId(),
                $command->getUserId(),
                $amount,
                $command->getPaymentMethod(),
                $command->getDescription(),
                $command->getMetadata()
            );
        } elseif ($command->isStripePayment()) {
            return Payment::createForStripe(
                $command->getOrderId(),
                $command->getUserId(),
                $amount,
                $command->getPaymentMethod(),
                $command->getDescription(),
                $command->getMetadata()
            );
        } elseif ($command->isBankTransferPayment()) {
            return Payment::createForBankTransfer(
                $command->getOrderId(),
                $command->getUserId(),
                $amount,
                $command->getDescription(),
                $command->getMetadata()
            );
        } else {
            return Payment::create(
                $command->getOrderId(),
                $command->getUserId(),
                $amount,
                $gateway,
                $command->getPaymentMethod(),
                $command->getDescription(),
                $command->getMetadata()
            );
        }
    }
}
