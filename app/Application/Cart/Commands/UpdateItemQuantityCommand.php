<?php

namespace App\Application\Cart\Commands;

/**
 * UpdateItemQuantityCommand
 * Sepet ürün miktarı güncelleme komutu
 */
class UpdateItemQuantityCommand
{
    public function __construct(
        public readonly ?int $userId,
        public readonly ?string $sessionId,
        public readonly int $productId,
        public readonly int $newQuantity,
        public readonly ?int $productVariantId = null
    ) {}

    /**
     * User ID'yi getir
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Session ID'yi getir
     */
    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    /**
     * Product ID'yi getir
     */
    public function getProductId(): int
    {
        return $this->productId;
    }

    /**
     * Yeni miktarı getir
     */
    public function getNewQuantity(): int
    {
        return $this->newQuantity;
    }

    /**
     * Product Variant ID'yi getir
     */
    public function getProductVariantId(): ?int
    {
        return $this->productVariantId;
    }

    /**
     * <PERSON>llanı<PERSON>ı sepeti mi kontrol et
     */
    public function isUserCart(): bool
    {
        return $this->userId !== null;
    }

    /**
     * Misafir sepeti mi kontrol et
     */
    public function isGuestCart(): bool
    {
        return $this->userId === null && $this->sessionId !== null;
    }

    /**
     * Varyant var mı kontrol et
     */
    public function hasVariant(): bool
    {
        return $this->productVariantId !== null;
    }

    /**
     * Miktar sıfır mı kontrol et
     */
    public function isQuantityZero(): bool
    {
        return $this->newQuantity === 0;
    }

    /**
     * Miktar pozitif mi kontrol et
     */
    public function isQuantityPositive(): bool
    {
        return $this->newQuantity > 0;
    }
}
