<?php

namespace App\Application\Cart\Commands;

/**
 * SetPaymentMethodCommand
 * Ödeme yöntemi ayarlama komutu
 */
class SetPaymentMethodCommand
{
    public function __construct(
        public readonly int $checkoutId,
        public readonly string $paymentMethodType,
        public readonly string $provider = '',
        public readonly array $metadata = []
    ) {}

    /**
     * Checkout ID'yi getir
     */
    public function getCheckoutId(): int
    {
        return $this->checkoutId;
    }

    /**
     * Ödeme yöntemi tipini getir
     */
    public function getPaymentMethodType(): string
    {
        return $this->paymentMethodType;
    }

    /**
     * Ödeme sağlayıcısını getir
     */
    public function getProvider(): string
    {
        return $this->provider;
    }

    /**
     * Metadata'yı getir
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Belirli metadata değerini getir
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Online ödeme mi kontrol et
     */
    public function isOnlinePayment(): bool
    {
        return in_array($this->paymentMethodType, [
            'credit_card',
            'digital_wallet',
            'installment'
        ]);
    }

    /**
     * Offline ödeme mi kontrol et
     */
    public function isOfflinePayment(): bool
    {
        return in_array($this->paymentMethodType, [
            'bank_transfer',
            'cash_on_delivery'
        ]);
    }

    /**
     * Taksitli ödeme mi kontrol et
     */
    public function isInstallmentPayment(): bool
    {
        return $this->paymentMethodType === 'installment';
    }

    /**
     * 3D Secure gerekli mi kontrol et
     */
    public function requires3DSecure(): bool
    {
        return $this->isOnlinePayment() && 
               $this->getMetadataValue('requires_3d_secure', true);
    }

    /**
     * Geçerli ödeme yöntemi tipi mu kontrol et
     */
    public function hasValidPaymentMethodType(): bool
    {
        $validTypes = [
            'credit_card',
            'bank_transfer',
            'cash_on_delivery',
            'digital_wallet',
            'installment'
        ];

        return in_array($this->paymentMethodType, $validTypes);
    }

    /**
     * Geçerli sağlayıcı mu kontrol et
     */
    public function hasValidProvider(): bool
    {
        if (empty($this->provider)) {
            return !$this->isOnlinePayment(); // Online ödemeler için sağlayıcı gerekli
        }

        $validProviders = [
            'iyzico',
            'stripe',
            'paypal',
            'paywithiyzico'
        ];

        return in_array($this->provider, $validProviders);
    }

    /**
     * Taksit sayısını getir
     */
    public function getInstallmentCount(): int
    {
        return (int) $this->getMetadataValue('installment_count', 1);
    }

    /**
     * Komisyon oranını getir
     */
    public function getCommissionRate(): float
    {
        return (float) $this->getMetadataValue('commission_rate', 0.0);
    }

    /**
     * Minimum tutarı getir
     */
    public function getMinimumAmount(): float
    {
        return (float) $this->getMetadataValue('minimum_amount', 0.0);
    }

    /**
     * Maksimum tutarı getir
     */
    public function getMaximumAmount(): ?float
    {
        $max = $this->getMetadataValue('maximum_amount');
        return $max ? (float) $max : null;
    }

    /**
     * Desteklenen para birimlerini getir
     */
    public function getSupportedCurrencies(): array
    {
        return $this->getMetadataValue('supported_currencies', ['TRY']);
    }

    /**
     * Para birimi destekleniyor mu kontrol et
     */
    public function supportsCurrency(string $currency): bool
    {
        return in_array(strtoupper($currency), 
                       array_map('strtoupper', $this->getSupportedCurrencies()));
    }
}
