<?php

namespace App\Application\Cart\Commands;

/**
 * SetBillingAddressCommand
 * Fatura adresi ayarlama komutu
 */
class SetBillingAddressCommand
{
    public function __construct(
        public readonly int $checkoutId,
        public readonly array $billingAddress,
        public readonly bool $sameAsShipping = false
    ) {}

    /**
     * Checkout ID'yi getir
     */
    public function getCheckoutId(): int
    {
        return $this->checkoutId;
    }

    /**
     * Fatura adresini getir
     */
    public function getBillingAddress(): array
    {
        return $this->billingAddress;
    }

    /**
     * Kargo adresi ile aynı mı kontrol et
     */
    public function isSameAsShipping(): bool
    {
        return $this->sameAsShipping;
    }

    /**
     * Adres alanını getir
     */
    public function getAddressField(string $field, $default = null)
    {
        return $this->billingAddress[$field] ?? $default;
    }

    /**
     * Kurumsal fatura mı kontrol et
     */
    public function isCompanyBilling(): bool
    {
        return (bool) $this->getAddressField('is_company', false);
    }

    /**
     * Gerekli alanlar var mı kontrol et
     */
    public function hasRequiredFields(): bool
    {
        if ($this->sameAsShipping) {
            return true; // Kargo adresi ile aynıysa validasyon gerekmiyor
        }

        $requiredFields = [
            'address_line_1',
            'city',
            'postal_code',
            'country',
            'phone'
        ];

        // Kurumsal fatura için ek alanlar
        if ($this->isCompanyBilling()) {
            $requiredFields[] = 'company';
            $requiredFields[] = 'tax_number';
        } else {
            $requiredFields[] = 'first_name';
            $requiredFields[] = 'last_name';
        }

        foreach ($requiredFields as $field) {
            if (empty($this->getAddressField($field))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Eksik alanları getir
     */
    public function getMissingFields(): array
    {
        if ($this->sameAsShipping) {
            return [];
        }

        $requiredFields = [
            'address_line_1' => 'Adres',
            'city' => 'Şehir',
            'postal_code' => 'Posta Kodu',
            'country' => 'Ülke',
            'phone' => 'Telefon'
        ];

        // Kurumsal fatura için ek alanlar
        if ($this->isCompanyBilling()) {
            $requiredFields['company'] = 'Şirket Adı';
            $requiredFields['tax_number'] = 'Vergi Numarası';
        } else {
            $requiredFields['first_name'] = 'Ad';
            $requiredFields['last_name'] = 'Soyad';
        }

        $missing = [];
        
        foreach ($requiredFields as $field => $label) {
            if (empty($this->getAddressField($field))) {
                $missing[$field] = $label;
            }
        }

        return $missing;
    }

    /**
     * Türkiye adresi mi kontrol et
     */
    public function isTurkishAddress(): bool
    {
        $country = strtoupper($this->getAddressField('country', ''));
        return $country === 'TR' || $country === 'TURKEY';
    }

    /**
     * Vergi numarası geçerli mi kontrol et
     */
    public function hasValidTaxNumber(): bool
    {
        if (!$this->isCompanyBilling()) {
            return true; // Bireysel fatura için vergi numarası zorunlu değil
        }

        $taxNumber = $this->getAddressField('tax_number', '');
        return !empty($taxNumber) && strlen(trim($taxNumber)) >= 10;
    }
}
