<?php

namespace App\Application\Cart\Commands;

/**
 * SetShippingAddressCommand
 * Kargo adresi ayarlama komutu
 */
class SetShippingAddressCommand
{
    public function __construct(
        public readonly int $checkoutId,
        public readonly array $shippingAddress
    ) {}

    /**
     * Checkout ID'yi getir
     */
    public function getCheckoutId(): int
    {
        return $this->checkoutId;
    }

    /**
     * Kargo adresini getir
     */
    public function getShippingAddress(): array
    {
        return $this->shippingAddress;
    }

    /**
     * <PERSON>res alanını getir
     */
    public function getAddressField(string $field, $default = null)
    {
        return $this->shippingAddress[$field] ?? $default;
    }

    /**
     * Gerekli alanlar var mı kontrol et
     */
    public function hasRequiredFields(): bool
    {
        $requiredFields = [
            'first_name',
            'last_name', 
            'address_line_1',
            'city',
            'postal_code',
            'country',
            'phone'
        ];

        foreach ($requiredFields as $field) {
            if (empty($this->getAddressField($field))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Eksik alanları getir
     */
    public function getMissingFields(): array
    {
        $requiredFields = [
            'first_name' => 'Ad',
            'last_name' => 'Soyad',
            'address_line_1' => 'Adres',
            'city' => 'Şehir',
            'postal_code' => 'Posta Kodu',
            'country' => 'Ülke',
            'phone' => 'Telefon'
        ];

        $missing = [];
        
        foreach ($requiredFields as $field => $label) {
            if (empty($this->getAddressField($field))) {
                $missing[$field] = $label;
            }
        }

        return $missing;
    }

    /**
     * Şirket adresi mi kontrol et
     */
    public function isCompanyAddress(): bool
    {
        return !empty($this->getAddressField('company'));
    }

    /**
     * Türkiye adresi mi kontrol et
     */
    public function isTurkishAddress(): bool
    {
        $country = strtoupper($this->getAddressField('country', ''));
        return $country === 'TR' || $country === 'TURKEY';
    }
}
