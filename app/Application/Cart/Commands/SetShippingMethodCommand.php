<?php

namespace App\Application\Cart\Commands;

/**
 * SetShippingMethodCommand
 * Kargo yöntemi ayarlama komutu
 */
class SetShippingMethodCommand
{
    public function __construct(
        public readonly int $checkoutId,
        public readonly string $shippingMethodCode,
        public readonly float $shippingCost,
        public readonly string $currency = 'TRY',
        public readonly array $metadata = []
    ) {}

    /**
     * Checkout ID'yi getir
     */
    public function getCheckoutId(): int
    {
        return $this->checkoutId;
    }

    /**
     * Kargo yöntemi kodunu getir
     */
    public function getShippingMethodCode(): string
    {
        return $this->shippingMethodCode;
    }

    /**
     * Kargo ücretini getir
     */
    public function getShippingCost(): float
    {
        return $this->shippingCost;
    }

    /**
     * Para birimini getir
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * Metadata'yı getir
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Belirli metadata değerini getir
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Ücretsiz kargo mu kontrol et
     */
    public function isFreeShipping(): bool
    {
        return $this->shippingCost <= 0 || $this->shippingMethodCode === 'free';
    }

    /**
     * Hızlı kargo mu kontrol et
     */
    public function isExpressShipping(): bool
    {
        return in_array($this->shippingMethodCode, ['express', 'overnight']);
    }

    /**
     * Mağazadan teslim alma mı kontrol et
     */
    public function isPickupMethod(): bool
    {
        return $this->shippingMethodCode === 'pickup';
    }

    /**
     * Geçerli kargo yöntemi kodu mu kontrol et
     */
    public function hasValidShippingMethodCode(): bool
    {
        $validCodes = [
            'standard',
            'express', 
            'overnight',
            'pickup',
            'free'
        ];

        return in_array($this->shippingMethodCode, $validCodes);
    }

    /**
     * Geçerli para birimi mu kontrol et
     */
    public function hasValidCurrency(): bool
    {
        $validCurrencies = ['TRY', 'USD', 'EUR'];
        return in_array(strtoupper($this->currency), $validCurrencies);
    }

    /**
     * Geçerli kargo ücreti mi kontrol et
     */
    public function hasValidShippingCost(): bool
    {
        return $this->shippingCost >= 0 && $this->shippingCost <= 1000; // Maksimum 1000 TL
    }

    /**
     * Tahmini teslimat gününü getir
     */
    public function getEstimatedDays(): int
    {
        return match ($this->shippingMethodCode) {
            'pickup' => 0,
            'overnight' => 1,
            'express' => 1,
            'standard' => 3,
            'free' => 5,
            default => 3
        };
    }

    /**
     * Kargo şirketini getir
     */
    public function getCarrier(): string
    {
        return $this->getMetadataValue('carrier', match ($this->shippingMethodCode) {
            'express' => 'MNG Kargo',
            'overnight' => 'Yurtiçi Kargo',
            'standard' => 'Aras Kargo',
            'free' => 'PTT Kargo',
            'pickup' => '',
            default => 'Aras Kargo'
        });
    }
}
