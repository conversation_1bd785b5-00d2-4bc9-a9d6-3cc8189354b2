<?php

namespace App\Application\Cart\Commands;

/**
 * ClearCartCommand
 * Sepeti temizleme komutu
 */
class ClearCartCommand
{
    public function __construct(
        public readonly ?int $userId,
        public readonly ?string $sessionId
    ) {}

    /**
     * User ID'yi getir
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Session ID'yi getir
     */
    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    /**
     * Kullanıcı sepeti mi kontrol et
     */
    public function isUserCart(): bool
    {
        return $this->userId !== null;
    }

    /**
     * Misafir sepeti mi kontrol et
     */
    public function isGuestCart(): bool
    {
        return $this->userId === null && $this->sessionId !== null;
    }
}
