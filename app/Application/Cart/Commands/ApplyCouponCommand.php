<?php

namespace App\Application\Cart\Commands;

/**
 * ApplyCouponCommand
 * Sepete kupon uygulama komutu
 */
class ApplyCouponCommand
{
    public function __construct(
        public readonly ?int $userId,
        public readonly ?string $sessionId,
        public readonly string $couponCode
    ) {}

    /**
     * User ID'yi getir
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Session ID'yi getir
     */
    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    /**
     * Kupon kodunu getir
     */
    public function getCouponCode(): string
    {
        return $this->couponCode;
    }

    /**
     * Kullanıcı sepeti mi kontrol et
     */
    public function isUserCart(): bool
    {
        return $this->userId !== null;
    }

    /**
     * Misafir sepeti mi kontrol et
     */
    public function isGuestCart(): bool
    {
        return $this->userId === null && $this->sessionId !== null;
    }
}
