<?php

namespace App\Application\Cart\Handlers;

use App\Application\Cart\Commands\RemoveItemFromCartCommand;
use App\Application\Cart\DTOs\CartDTO;
use App\Domain\Cart\ValueObjects\SessionId;
use App\Domain\Cart\Repositories\CartRepositoryInterface;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Core\Domain\Exceptions\DomainException;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * RemoveItemFromCartHandler
 * Sepetten ürün çıkarma işlemlerini yönetir
 */
class RemoveItemFromCartHandler
{
    public function __construct(
        private CartRepositoryInterface $cartRepository,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    /**
     * Sepetten ürün çıkarma işlemini gerçekleştir
     *
     * @param RemoveItemFromCartCommand $command
     * @return CartDTO
     * @throws DomainException
     * @throws EntityNotFoundException
     */
    public function handle(RemoveItemFromCartCommand $command): CartDTO
    {
        return DB::transaction(function () use ($command) {
            try {
                // Sepeti bul
                $cart = $this->findCart($command);

                if (!$cart) {
                    throw new EntityNotFoundException('Cart not found');
                }

                // Sepetten ürünü çıkar
                $cart->removeItem(
                    productId: $command->getProductId(),
                    productVariantId: $command->getProductVariantId()
                );

                // Sepeti kaydet
                $savedCart = $this->cartRepository->save($cart);

                // Domain events'leri dispatch et
                $this->eventDispatcher->dispatchEvents($savedCart);

                Log::info('Item removed from cart successfully', [
                    'cart_id' => $savedCart->getId(),
                    'user_id' => $savedCart->getUserId(),
                    'session_id' => $savedCart->getSessionId()?->getValue(),
                    'product_id' => $command->getProductId(),
                    'product_variant_id' => $command->getProductVariantId()
                ]);

                // DTO'ya dönüştür ve döndür
                return CartDTO::fromEntity($savedCart, true, false);

            } catch (EntityNotFoundException $e) {
                Log::warning('Remove item from cart failed - Cart not found', [
                    'user_id' => $command->getUserId(),
                    'session_id' => $command->getSessionId(),
                    'product_id' => $command->getProductId(),
                    'error' => $e->getMessage()
                ]);
                throw $e;
            } catch (DomainException $e) {
                Log::warning('Remove item from cart failed - Domain error', [
                    'user_id' => $command->getUserId(),
                    'session_id' => $command->getSessionId(),
                    'product_id' => $command->getProductId(),
                    'error' => $e->getMessage()
                ]);
                throw $e;
            } catch (\Exception $e) {
                Log::error('Remove item from cart failed - System error', [
                    'user_id' => $command->getUserId(),
                    'session_id' => $command->getSessionId(),
                    'product_id' => $command->getProductId(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw new DomainException(
                    'Failed to remove item from cart due to system error',
                    'REMOVE_ITEM_SYSTEM_ERROR',
                    ['original_error' => $e->getMessage()]
                );
            }
        });
    }

    /**
     * Sepeti bul
     */
    private function findCart(RemoveItemFromCartCommand $command)
    {
        if ($command->isUserCart()) {
            return $this->cartRepository->findByUserId($command->getUserId());
        } elseif ($command->isGuestCart()) {
            $sessionId = SessionId::fromString($command->getSessionId());
            return $this->cartRepository->findBySessionId($sessionId);
        } else {
            throw new DomainException(
                'Either user ID or session ID must be provided',
                'INVALID_CART_IDENTIFIER'
            );
        }
    }
}
