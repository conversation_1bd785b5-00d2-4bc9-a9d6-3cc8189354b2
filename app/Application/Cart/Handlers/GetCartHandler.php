<?php

namespace App\Application\Cart\Handlers;

use App\Application\Cart\Queries\GetCartQuery;
use App\Application\Cart\DTOs\CartDTO;
use App\Domain\Cart\ValueObjects\SessionId;
use App\Domain\Cart\Repositories\CartRepositoryInterface;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use Illuminate\Support\Facades\Log;

/**
 * GetCartHandler
 * Sepet detayı getirme sorgularını yönetir
 */
class GetCartHandler
{
    public function __construct(
        private CartRepositoryInterface $cartRepository
    ) {}

    /**
     * Sepet detayı getirme sorgusunu gerçekleştir
     *
     * @param GetCartQuery $query
     * @return CartDTO
     * @throws EntityNotFoundException
     */
    public function handle(GetCartQuery $query): CartDTO
    {
        try {
            if (!$query->hasValidSearchCriteria()) {
                throw new \InvalidArgumentException('At least one search criteria must be provided (cart_id, user_id, or session_id)');
            }

            $cart = null;

            // ID ile arama
            if ($query->isSearchById()) {
                $cart = $this->cartRepository->findById($query->getCartId());
                
                if (!$cart) {
                    throw new EntityNotFoundException("Cart with ID {$query->getCartId()} not found");
                }
            }
            // Kullanıcı ID ile arama
            elseif ($query->isUserCart()) {
                $cart = $this->cartRepository->findByUserId($query->getUserId());
                
                if (!$cart) {
                    throw new EntityNotFoundException("Cart for user {$query->getUserId()} not found");
                }
            }
            // Session ID ile arama
            elseif ($query->isGuestCart()) {
                $sessionId = SessionId::fromString($query->getSessionId());
                $cart = $this->cartRepository->findBySessionId($sessionId);
                
                if (!$cart) {
                    throw new EntityNotFoundException("Cart for session {$query->getSessionId()} not found");
                }
            }

            Log::info('Cart retrieved successfully', [
                'cart_id' => $cart->getId(),
                'user_id' => $cart->getUserId(),
                'session_id' => $cart->getSessionId()?->getValue(),
                'search_criteria' => [
                    'cart_id' => $query->getCartId(),
                    'user_id' => $query->getUserId(),
                    'session_id' => $query->getSessionId()
                ]
            ]);

            // DTO'ya dönüştür ve döndür
            return CartDTO::fromEntity(
                cart: $cart,
                includeItems: $query->shouldIncludeItems(),
                includeProductDetails: $query->shouldIncludeProductDetails()
            );

        } catch (EntityNotFoundException $e) {
            Log::warning('Cart not found', [
                'search_criteria' => [
                    'cart_id' => $query->getCartId(),
                    'user_id' => $query->getUserId(),
                    'session_id' => $query->getSessionId()
                ],
                'error' => $e->getMessage()
            ]);
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to retrieve cart', [
                'search_criteria' => [
                    'cart_id' => $query->getCartId(),
                    'user_id' => $query->getUserId(),
                    'session_id' => $query->getSessionId()
                ],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \RuntimeException(
                'Failed to retrieve cart: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }
}
