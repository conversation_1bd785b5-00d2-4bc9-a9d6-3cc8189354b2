<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class GenericEmail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * The email data.
     *
     * @var array
     */
    public $mailData;

    /**
     * Create a new message instance.
     *
     * @param array $mailData
     */
    public function __construct(array $mailData)
    {
        $this->mailData = $mailData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->mailData['subject'],
            to: [
                $this->mailData['name']
                    ? new \Illuminate\Mail\Mailables\Address($this->mailData['to'], $this->mailData['name'])
                    : $this->mailData['to'],
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        // If we have a text body, use both HTML and text views
        if (!empty($this->mailData['text_body'])) {
            return new Content(
                htmlString: $this->mailData['html_body'],
                text: 'emails.text',
                with: [
                    'content' => $this->mailData['text_body'],
                ],
            );
        }

        // Otherwise, just use the HTML view
        return new Content(
            htmlString: $this->mailData['html_body'],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return $this->mailData['attachments'] ?? [];
    }
}
