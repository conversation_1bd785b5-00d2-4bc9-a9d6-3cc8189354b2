<?php

namespace App\Console\Commands;

use App\Models\ShippingZone;
use App\Models\ShippingZoneLocation;
use App\Models\ShippingZoneMethod;
use App\Models\ShippingMethod;
use App\Models\ShippingCompany;
use App\Models\Country;
use Illuminate\Console\Command;

class CreateTestShippingZone extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shipping:create-test-zone';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test shipping zone for Turkey';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating test shipping zone...');
        
        // Türkiye'yi bul
        $turkey = Country::where('iso2', 'TR')->first();
        
        if (!$turkey) {
            $this->error('Turkey not found in countries table!');
            return;
        }
        
        // Kargo şirketi oluştur
        $company = new ShippingCompany();
        $company->name = 'Test Kargo';
        $company->code = 'test';
        $company->description = 'Test kargo şirketi';
        $company->logo = '/images/shipping/test.png';
        $company->is_active = true;
        $company->save();
        
        $this->info('Created shipping company: ' . $company->name . ' (ID: ' . $company->id . ')');
        
        // Kargo metodu oluştur
        $method = new ShippingMethod();
        $method->name = 'Test Kargo';
        $method->code = 'test_' . time(); // Benzersiz kod
        $method->description = 'Test kargo metodu';
        $method->shipping_company_id = $company->id;
        $method->delivery_time = '1-3 gün';
        $method->is_active = true;
        $method->save();
        
        $this->info('Created shipping method: ' . $method->name . ' (ID: ' . $method->id . ')');
        
        // Kargo bölgesi oluştur
        $zone = new ShippingZone();
        $zone->name = 'Test Bölge';
        $zone->order = 1;
        $zone->is_active = true;
        $zone->save();
        
        $this->info('Created shipping zone: ' . $zone->name . ' (ID: ' . $zone->id . ')');
        
        // Türkiye lokasyonu ekle
        $location = new ShippingZoneLocation();
        $location->zone_id = $zone->id;
        $location->location_type = 'country';
        $location->location_id = $turkey->id;
        $location->save();
        
        $this->info('Added Turkey location to zone');
        
        // Kargo bölgesi metodu oluştur
        $zoneMethod = new ShippingZoneMethod();
        $zoneMethod->zone_id = $zone->id;
        $zoneMethod->method_id = $method->id;
        $zoneMethod->cost = 15.00;
        $zoneMethod->cost_per_order_percent = 0;
        $zoneMethod->cost_per_weight = 0;
        $zoneMethod->min_order_amount = 0;
        $zoneMethod->max_order_amount = 0;
        $zoneMethod->free_shipping_min_amount = 250.00;
        $zoneMethod->is_active = true;
        $zoneMethod->save();
        
        $this->info('Created shipping zone method (ID: ' . $zoneMethod->id . ')');
        
        $this->info('Test shipping zone created successfully!');
    }
}
