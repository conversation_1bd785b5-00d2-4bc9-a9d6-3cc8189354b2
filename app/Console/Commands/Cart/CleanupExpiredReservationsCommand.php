<?php

namespace App\Console\Commands\Cart;

use Illuminate\Console\Command;
use App\Infrastructure\Cart\Services\InventoryService;

/**
 * CleanupExpiredReservationsCommand
 * Süresi dolmuş stok rezervasyonlarını temizleme komutu
 */
class CleanupExpiredReservationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cart:cleanup-reservations';

    /**
     * The console command description.
     */
    protected $description = 'Cleanup expired stock reservations';

    public function __construct(
        private InventoryService $inventoryService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting expired reservations cleanup...');

        try {
            $cleanedCount = $this->inventoryService->cleanupExpiredReservations();

            if ($cleanedCount > 0) {
                $this->info("Successfully cleaned up {$cleanedCount} expired reservations");
            } else {
                $this->info('No expired reservations found to cleanup');
            }

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to cleanup expired reservations: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
