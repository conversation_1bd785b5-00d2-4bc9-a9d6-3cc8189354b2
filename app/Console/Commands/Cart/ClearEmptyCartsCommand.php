<?php

namespace App\Console\Commands\Cart;

use Illuminate\Console\Command;
use App\Domain\Cart\Repositories\CartRepositoryInterface;

/**
 * ClearEmptyCartsCommand
 * Boş sepetleri temizleme komutu
 */
class ClearEmptyCartsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cart:clear-empty 
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Clear empty carts from the database';

    public function __construct(
        private CartRepositoryInterface $cartRepository
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting empty cart cleanup...');

        try {
            if ($this->option('dry-run')) {
                $this->info('DRY RUN MODE - No carts will be deleted');
                
                // Dry run için count al
                $count = $this->cartRepository->countActiveCarts();
                $totalCount = $this->cartRepository->getCartStatistics()['total_carts'];
                $emptyCount = $totalCount - $count;
                
                $this->info("Found {$emptyCount} empty carts that would be deleted");
                
                return self::SUCCESS;
            }

            $deletedCount = $this->cartRepository->clearEmptyCarts();

            if ($deletedCount > 0) {
                $this->info("Successfully deleted {$deletedCount} empty carts");
            } else {
                $this->info('No empty carts found to delete');
            }

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to clear empty carts: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
