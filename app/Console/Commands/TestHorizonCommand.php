<?php

namespace App\Console\Commands;

use App\Jobs\ProcessOrderJob;
use App\Jobs\SendEmailJob;
use Illuminate\Console\Command;

class TestHorizonCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'horizon:test {count=10 : Kaç adet test job gönderilecek}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Horizon test için örnek job\'lar gönderir';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = $this->argument('count');
        $this->info("Horizon test için {$count} adet job gönderiliyor...");

        $bar = $this->output->createProgressBar($count * 2);
        $bar->start();

        // Sipariş işleme job'ları
        for ($i = 1; $i <= $count; $i++) {
            ProcessOrderJob::dispatch($i);
            $bar->advance();
        }

        // E-posta gönderme job'ları
        for ($i = 1; $i <= $count; $i++) {
            SendEmailJob::dispatch(
                "user{$i}@example.com",
                "Test E-posta #{$i}",
                "Bu bir test e-postasıdır. Numara: {$i}"
            );
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Toplam " . ($count * 2) . " adet job başarıyla kuyruğa eklendi.");
        $this->info("Horizon dashboard'unu kontrol edebilirsiniz: " . url('/horizon'));
    }
}
