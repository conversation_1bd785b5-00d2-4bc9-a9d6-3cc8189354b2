<?php

namespace App\Console\Commands;

use App\Models\City;
use App\Models\Country;
use App\Models\State;
use Illuminate\Console\Command;

class ImportStatesAndCities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-states-and-cities {--country=* : Ülke ID\'leri (örn: --country=225 veya --country=225 --country=226)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'İlleri ve ilçeleri içe aktar';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('İlleri ve ilçeleri içe aktarma işlemi başlatılıyor...');

        // Ülke ID'lerini al
        $countryIds = $this->option('country');

        // Eğer ülke ID'si belirtilmemişse, tüm ülkeleri kullan
        if (empty($countryIds)) {
            if ($this->confirm('Hiçbir ülke ID\'si belirtilmedi. Tüm ülkeler için illeri ve ilçeleri içe aktarmak istiyor musunuz? Bu işlem uzun sürebilir.', false)) {
                $countries = Country::all();
                $countryIds = $countries->pluck('id')->toArray();
            } else {
                // Türkiye'yi varsayılan olarak ekle
                $turkey = Country::where('iso2', 'TR')->first();
                if ($turkey) {
                    $countryIds = [$turkey->id];
                    $this->info("Varsayılan olarak Türkiye (ID: {$turkey->id}) seçildi.");
                } else {
                    $this->error('Türkiye bulunamadı! Lütfen önce ülkeleri içe aktarın.');
                    return 1;
                }
            }
        }

        // Eyaletler/İller JSON dosyasını oku
        $statesJsonPath = storage_path('app/data/states.json');

        if (!file_exists($statesJsonPath)) {
            $this->error('İller JSON dosyası bulunamadı: ' . $statesJsonPath);
            return 1;
        }

        $statesJson = json_decode(file_get_contents($statesJsonPath), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('JSON dosyası geçersiz: ' . json_last_error_msg());
            return 1;
        }

        // Şehirler/İlçeler JSON dosyasını oku
        $citiesJsonPath = storage_path('app/data/cities.json');

        if (!file_exists($citiesJsonPath)) {
            $this->error('İlçeler JSON dosyası bulunamadı: ' . $citiesJsonPath);
            return 1;
        }

        $citiesJson = json_decode(file_get_contents($citiesJsonPath), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('JSON dosyası geçersiz: ' . json_last_error_msg());
            return 1;
        }

        $this->info('Toplam ' . count($statesJson) . ' il ve ' . count($citiesJson) . ' ilçe bulundu.');

        // İlleri içe aktar
        $stateCount = 0;
        $stateIds = [];

        $this->info('İller içe aktarılıyor...');
        $bar = $this->output->createProgressBar(count($statesJson));
        $bar->start();

        foreach ($statesJson as $state) {
            if (in_array($state['country_id'], $countryIds)) {
                // İlin zaten var olup olmadığını kontrol et
                $existingState = State::where('id', $state['id'])->first();

                if ($existingState) {
                    // İl zaten var, güncelle
                    $existingState->update([
                        'country_id' => $state['country_id'],
                        'name' => $state['name'],
                        'country_code' => $state['country_code'],
                        'state_code' => $state['state_code'],
                        'type' => $state['type'] ?? null,
                        'latitude' => $state['latitude'],
                        'longitude' => $state['longitude'],
                        'is_active' => true,
                    ]);
                } else {
                    // Yeni il ekle
                    State::create([
                        'id' => $state['id'],
                        'country_id' => $state['country_id'],
                        'name' => $state['name'],
                        'country_code' => $state['country_code'],
                        'state_code' => $state['state_code'],
                        'type' => $state['type'] ?? null,
                        'latitude' => $state['latitude'],
                        'longitude' => $state['longitude'],
                        'is_active' => true,
                    ]);
                }

                $stateIds[] = $state['id'];
                $stateCount++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Toplam $stateCount il başarıyla içe aktarıldı.");

        // İlçeleri içe aktar
        $cityCount = 0;
        $cityBatch = [];
        $batchSize = 1000;

        $this->info('İlçeler içe aktarılıyor...');
        $bar = $this->output->createProgressBar(count($citiesJson));
        $bar->start();

        foreach ($citiesJson as $city) {
            if (in_array($city['state_id'], $stateIds)) {
                // İlçenin zaten var olup olmadığını kontrol et
                $existingCity = City::where('id', $city['id'])->first();

                if ($existingCity) {
                    // İlçe zaten var, güncelle
                    $existingCity->update([
                        'state_id' => $city['state_id'],
                        'name' => $city['name'],
                        'state_code' => $city['state_code'],
                        'country_code' => $city['country_code'],
                        'latitude' => $city['latitude'],
                        'longitude' => $city['longitude'],
                        'wikiDataId' => $city['wikiDataId'] ?? null,
                        'is_active' => true,
                    ]);
                } else {
                    // Yeni ilçe ekle
                    $cityBatch[] = [
                        'id' => $city['id'],
                        'state_id' => $city['state_id'],
                        'name' => $city['name'],
                        'state_code' => $city['state_code'],
                        'country_code' => $city['country_code'],
                        'latitude' => $city['latitude'],
                        'longitude' => $city['longitude'],
                        'wikiDataId' => $city['wikiDataId'] ?? null,
                        'is_active' => true,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    // Toplu ekleme için batch işlemi
                    if (count($cityBatch) >= $batchSize) {
                        City::insert($cityBatch);
                        $cityCount += count($cityBatch);
                        $cityBatch = [];
                    }
                }

                $cityCount++;
            }

            $bar->advance();
        }

        // Kalan ilçeleri ekle
        if (count($cityBatch) > 0) {
            City::insert($cityBatch);
            $cityCount += count($cityBatch);
        }

        $bar->finish();
        $this->newLine();
        $this->info("Toplam $cityCount ilçe başarıyla içe aktarıldı.");

        // Önbelleği yenile
        $this->info('Konum önbelleği yenileniyor...');
        \App\Services\LocationService::refreshCache();
        $this->info('Konum önbelleği başarıyla yenilendi.');

        return 0;
    }
}
