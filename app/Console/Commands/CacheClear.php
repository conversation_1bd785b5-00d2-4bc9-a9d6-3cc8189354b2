<?php

namespace App\Console\Commands;

use App\Services\Cache\CategoryCacheService;
use App\Services\Cache\ProductCacheService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CacheClear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-type {type? : Belirli bir tür için cache temizleme}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Belirli türler için cache\'i temizler';

    /**
     * @var ProductCacheService
     */
    protected $productCacheService;

    /**
     * @var CategoryCacheService
     */
    protected $categoryCacheService;

    /**
     * Create a new command instance.
     */
    public function __construct(
        ProductCacheService $productCacheService,
        CategoryCacheService $categoryCacheService
    ) {
        parent::__construct();
        $this->productCacheService = $productCacheService;
        $this->categoryCacheService = $categoryCacheService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');

        if ($type) {
            switch ($type) {
                case 'products':
                    $this->productCacheService->invalidateAllProducts();
                    $this->info('Ürün cache\'leri temizlendi.');
                    break;
                case 'categories':
                    $this->categoryCacheService->invalidateAllCategories();
                    $this->info('Kategori cache\'leri temizlendi.');
                    break;
                case 'category-tree':
                    $this->categoryCacheService->invalidateCategoryTree();
                    $this->info('Kategori ağacı cache\'i temizlendi.');
                    break;
                case 'all':
                    Cache::flush();
                    $this->info('Tüm cache\'ler temizlendi.');
                    break;
                default:
                    $this->error('Geçersiz tür: ' . $type);
                    return 1;
            }
        } else {
            $this->info('Hangi tür için cache temizlemek istediğinizi belirtin:');
            $this->line('- products: Ürün cache\'lerini temizler');
            $this->line('- categories: Kategori cache\'lerini temizler');
            $this->line('- category-tree: Kategori ağacı cache\'ini temizler');
            $this->line('- all: Tüm cache\'leri temizler');
        }

        return 0;
    }
}
