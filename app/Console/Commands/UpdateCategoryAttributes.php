<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\Attribute;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateCategoryAttributes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-category-attributes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update category attributes with is_filterable and position values';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating category attributes...');

        // Tüm kategorileri al
        $categories = Category::all();
        $this->info("Found {$categories->count()} categories");

        // Tüm özellikleri al
        $attributes = Attribute::all();
        $this->info("Found {$attributes->count()} attributes");

        // Her kategori için
        foreach ($categories as $category) {
            $this->info("Processing category: {$category->name} (ID: {$category->id})");
            
            // Kategorinin mevcut özelliklerini al
            $categoryAttributes = DB::table('category_attributes')
                ->where('category_id', $category->id)
                ->get();
            
            $this->info("  Found {$categoryAttributes->count()} attributes for this category");
            
            // Her özellik için
            foreach ($categoryAttributes as $index => $categoryAttribute) {
                // Özelliği bul
                $attribute = $attributes->firstWhere('id', $categoryAttribute->attribute_id);
                
                if (!$attribute) {
                    $this->warn("  Attribute with ID {$categoryAttribute->attribute_id} not found, skipping");
                    continue;
                }
                
                $this->info("  Updating attribute: {$attribute->name} (ID: {$attribute->id})");
                
                // Özelliği güncelle
                DB::table('category_attributes')
                    ->where('category_id', $category->id)
                    ->where('attribute_id', $attribute->id)
                    ->update([
                        'is_filterable' => $attribute->is_filterable,
                        'position' => $index + 1,
                    ]);
                
                $this->info("  Updated with is_filterable: {$attribute->is_filterable}, position: " . ($index + 1));
            }
        }

        $this->info('Category attributes updated successfully!');
    }
}
