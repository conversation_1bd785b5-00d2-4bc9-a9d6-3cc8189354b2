<?php

namespace App\Console\Commands;

use App\Models\ShippingCompany;
use App\Models\ShippingMethod;
use Illuminate\Console\Command;

class CheckShippingCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shipping:check-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check shipping companies and methods';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking shipping companies...');
        
        // Kargo şirketlerini kontrol et
        $companies = ShippingCompany::all();
        
        if ($companies->isEmpty()) {
            $this->error('No shipping companies found!');
            
            // Varsayılan kargo şirketlerini oluştur
            if ($this->confirm('Do you want to create default shipping companies?')) {
                $this->createDefaultShippingCompanies();
            }
            
            return;
        }
        
        $this->info('Found ' . $companies->count() . ' shipping companies:');
        
        foreach ($companies as $company) {
            $this->line('');
            $this->line('Company ID: ' . $company->id);
            $this->line('Company Name: ' . $company->name);
            $this->line('Company Code: ' . $company->code);
            $this->line('Company Active: ' . ($company->is_active ? 'Yes' : 'No'));
            
            // Şirket metodlarını kontrol et
            $methods = ShippingMethod::where('shipping_company_id', $company->id)->get();
            
            if ($methods->isEmpty()) {
                $this->warn('  No methods for this company');
                
                // Varsayılan kargo metodları oluştur
                if ($this->confirm('  Do you want to create default shipping methods for this company?')) {
                    $this->createDefaultShippingMethods($company);
                }
            } else {
                $this->info('  Methods (' . $methods->count() . '):');
                
                foreach ($methods as $method) {
                    $this->line('    - ' . $method->name . ' (ID: ' . $method->id . ')');
                    $this->line('      Code: ' . $method->code);
                    $this->line('      Description: ' . $method->description);
                    $this->line('      Delivery Time: ' . $method->delivery_time);
                    $this->line('      Active: ' . ($method->is_active ? 'Yes' : 'No'));
                }
            }
        }
        
        // Kargo metodlarını kontrol et
        $methods = ShippingMethod::all();
        
        if ($methods->isEmpty()) {
            $this->error('No shipping methods found!');
            return;
        }
        
        $this->info('');
        $this->info('Found ' . $methods->count() . ' shipping methods:');
        
        foreach ($methods as $method) {
            $this->line('');
            $this->line('Method ID: ' . $method->id);
            $this->line('Method Name: ' . $method->name);
            $this->line('Method Code: ' . $method->code);
            $this->line('Method Active: ' . ($method->is_active ? 'Yes' : 'No'));
            
            // Şirket bilgisini kontrol et
            $company = ShippingCompany::find($method->shipping_company_id);
            
            if (!$company) {
                $this->error('  Company not found for this method!');
                
                // Kargo şirketini güncelle
                if ($this->confirm('  Do you want to assign this method to a company?')) {
                    $companies = ShippingCompany::all()->pluck('name', 'id')->toArray();
                    
                    if (empty($companies)) {
                        $this->error('  No companies available!');
                        continue;
                    }
                    
                    $companyId = $this->choice('  Select a company:', $companies);
                    $companyId = array_search($companyId, $companies);
                    
                    $method->shipping_company_id = $companyId;
                    $method->save();
                    
                    $this->info('  Method assigned to company ID: ' . $companyId);
                }
            } else {
                $this->line('  Company: ' . $company->name . ' (ID: ' . $company->id . ')');
            }
        }
    }
    
    /**
     * Varsayılan kargo şirketlerini oluştur
     */
    private function createDefaultShippingCompanies()
    {
        $companies = [
            [
                'name' => 'Yurtiçi Kargo',
                'code' => 'yurtici',
                'description' => 'Yurtiçi Kargo',
                'logo' => '/images/shipping/yurtici.png',
                'is_active' => true,
            ],
            [
                'name' => 'Aras Kargo',
                'code' => 'aras',
                'description' => 'Aras Kargo',
                'logo' => '/images/shipping/aras.png',
                'is_active' => true,
            ],
            [
                'name' => 'MNG Kargo',
                'code' => 'mng',
                'description' => 'MNG Kargo',
                'logo' => '/images/shipping/mng.png',
                'is_active' => true,
            ],
            [
                'name' => 'PTT Kargo',
                'code' => 'ptt',
                'description' => 'PTT Kargo',
                'logo' => '/images/shipping/ptt.png',
                'is_active' => true,
            ],
            [
                'name' => 'UPS',
                'code' => 'ups',
                'description' => 'UPS',
                'logo' => '/images/shipping/ups.png',
                'is_active' => true,
            ],
            [
                'name' => 'DHL',
                'code' => 'dhl',
                'description' => 'DHL',
                'logo' => '/images/shipping/dhl.png',
                'is_active' => true,
            ],
            [
                'name' => 'FedEx',
                'code' => 'fedex',
                'description' => 'FedEx',
                'logo' => '/images/shipping/fedex.png',
                'is_active' => true,
            ],
        ];
        
        foreach ($companies as $companyData) {
            $company = new ShippingCompany();
            $company->name = $companyData['name'];
            $company->code = $companyData['code'];
            $company->description = $companyData['description'];
            $company->logo = $companyData['logo'];
            $company->is_active = $companyData['is_active'];
            $company->save();
            
            $this->info('Created shipping company: ' . $company->name . ' (ID: ' . $company->id . ')');
            
            // Varsayılan kargo metodları oluştur
            $this->createDefaultShippingMethods($company);
        }
    }
    
    /**
     * Varsayılan kargo metodları oluştur
     */
    private function createDefaultShippingMethods($company)
    {
        $methods = [
            [
                'name' => 'Standart Kargo',
                'code' => 'standard',
                'description' => 'Standart kargo hizmeti (2-4 iş günü)',
                'delivery_time' => '2-4 iş günü',
                'is_active' => true,
            ],
            [
                'name' => 'Hızlı Kargo',
                'code' => 'express',
                'description' => 'Hızlı kargo hizmeti (1-2 iş günü)',
                'delivery_time' => '1-2 iş günü',
                'is_active' => true,
            ],
        ];
        
        // Uluslararası kargo şirketleri için ek metodlar
        if (in_array($company->code, ['ups', 'dhl', 'fedex'])) {
            $methods[] = [
                'name' => 'Uluslararası Kargo',
                'code' => 'international',
                'description' => 'Uluslararası kargo hizmeti (3-7 iş günü)',
                'delivery_time' => '3-7 iş günü',
                'is_active' => true,
            ];
        }
        
        // Yurtiçi kargo şirketleri için ek metodlar
        if (in_array($company->code, ['yurtici', 'aras', 'mng', 'ptt'])) {
            $methods[] = [
                'name' => 'Ekonomik Kargo',
                'code' => 'economy',
                'description' => 'Ekonomik kargo hizmeti (3-5 iş günü)',
                'delivery_time' => '3-5 iş günü',
                'is_active' => true,
            ];
            
            $methods[] = [
                'name' => 'Aynı Gün Teslimat',
                'code' => 'same_day',
                'description' => 'Aynı gün teslimat hizmeti (sadece belirli bölgelerde)',
                'delivery_time' => 'Aynı gün',
                'is_active' => true,
            ];
        }
        
        foreach ($methods as $methodData) {
            // Aynı isimde metod var mı kontrol et
            $existingMethod = ShippingMethod::where('shipping_company_id', $company->id)
                ->where('code', $methodData['code'])
                ->first();
            
            if ($existingMethod) {
                $this->line('Shipping method already exists: ' . $existingMethod->name . ' (ID: ' . $existingMethod->id . ')');
                continue;
            }
            
            $method = new ShippingMethod();
            $method->name = $methodData['name'];
            $method->code = $methodData['code'];
            $method->description = $methodData['description'];
            $method->shipping_company_id = $company->id;
            $method->delivery_time = $methodData['delivery_time'];
            $method->is_active = $methodData['is_active'];
            $method->save();
            
            $this->info('Created shipping method: ' . $method->name . ' (ID: ' . $method->id . ')');
        }
    }
}
