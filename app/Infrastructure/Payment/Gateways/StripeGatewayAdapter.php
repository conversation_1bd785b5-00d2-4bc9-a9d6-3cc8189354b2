<?php

namespace App\Infrastructure\Payment\Gateways;

use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayInterface;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayResponse;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentWebhookData;
use App\Domain\Payment\Entities\Payment;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Core\Domain\Exceptions\DomainException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

/**
 * StripeGatewayAdapter
 * Stripe ödeme gateway'i için adapter
 */
class StripeGatewayAdapter implements PaymentGatewayInterface
{
    private array $config;
    private \Stripe\StripeClient $stripe;

    public function __construct()
    {
        $this->config = Config::get('payment.gateways.stripe', []);
        $this->initializeStripe();
    }

    /**
     * Stripe client'ı başlat
     */
    private function initializeStripe(): void
    {
        \Stripe\Stripe::setApiKey($this->config['secret_key'] ?? '');
        $this->stripe = new \Stripe\StripeClient($this->config['secret_key'] ?? '');
    }

    /**
     * Ödeme işlemini başlat
     */
    public function processPayment(Payment $payment, array $paymentDetails): PaymentGatewayResponse
    {
        try {
            $paymentIntent = $this->createPaymentIntent($payment, $paymentDetails);

            if ($paymentIntent->status === 'succeeded') {
                return new PaymentGatewayResponse(
                    success: true,
                    message: 'Payment successful',
                    gatewayTransactionId: $paymentIntent->id,
                    status: 'completed',
                    feeAmount: $this->calculateFeeFromCharges($paymentIntent->charges->data),
                    data: [
                        'payment_intent_id' => $paymentIntent->id,
                        'client_secret' => $paymentIntent->client_secret,
                        'charges' => $paymentIntent->charges->data
                    ]
                );
            }

            if ($paymentIntent->status === 'requires_action') {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'Payment requires additional action',
                    gatewayTransactionId: $paymentIntent->id,
                    status: 'requires_action',
                    data: [
                        'payment_intent_id' => $paymentIntent->id,
                        'client_secret' => $paymentIntent->client_secret,
                        'next_action' => $paymentIntent->next_action
                    ]
                );
            }

            return new PaymentGatewayResponse(
                success: false,
                message: 'Payment failed',
                gatewayTransactionId: $paymentIntent->id,
                status: 'failed',
                errorCode: $paymentIntent->last_payment_error->code ?? 'unknown_error'
            );

        } catch (\Stripe\Exception\CardException $e) {
            Log::error('Stripe card error', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage(),
                'code' => $e->getStripeCode()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: $e->getMessage(),
                errorCode: $e->getStripeCode()
            );

        } catch (\Exception $e) {
            Log::error('Stripe payment processing failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Payment processing failed: ' . $e->getMessage(),
                errorCode: 'STRIPE_PAYMENT_FAILED'
            );
        }
    }

    /**
     * 3D Secure ödeme işlemini başlat
     */
    public function process3DSecurePayment(
        Payment $payment,
        array $paymentDetails,
        string $callbackUrl,
        string $errorUrl
    ): PaymentGatewayResponse {
        try {
            $paymentIntent = $this->createPaymentIntent($payment, $paymentDetails, [
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => $callbackUrl
            ]);

            if ($paymentIntent->status === 'requires_action') {
                return new PaymentGatewayResponse(
                    success: true,
                    message: '3D Secure authentication required',
                    gatewayTransactionId: $paymentIntent->id,
                    status: 'requires_action',
                    redirectUrl: $paymentIntent->next_action->redirect_to_url->url ?? null,
                    data: [
                        'payment_intent_id' => $paymentIntent->id,
                        'client_secret' => $paymentIntent->client_secret,
                        'next_action' => $paymentIntent->next_action
                    ]
                );
            }

            return $this->processPayment($payment, $paymentDetails);

        } catch (\Exception $e) {
            Log::error('Stripe 3D Secure processing failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: '3D Secure processing failed: ' . $e->getMessage(),
                errorCode: 'STRIPE_3D_SECURE_FAILED'
            );
        }
    }

    /**
     * Taksitli ödeme işlemini başlat
     */
    public function processInstallmentPayment(
        Payment $payment,
        array $paymentDetails,
        int $installments
    ): PaymentGatewayResponse {
        // Stripe'da taksit genellikle farklı şekilde handle edilir
        // Bu implementasyon ülkeye özel olabilir
        return new PaymentGatewayResponse(
            success: false,
            message: 'Installment payments not supported for this gateway',
            errorCode: 'INSTALLMENT_NOT_SUPPORTED'
        );
    }

    /**
     * Ödeme durumunu sorgula
     */
    public function queryPayment(string $gatewayTransactionId): PaymentGatewayResponse
    {
        try {
            $paymentIntent = $this->stripe->paymentIntents->retrieve($gatewayTransactionId);

            return new PaymentGatewayResponse(
                success: true,
                message: 'Payment query successful',
                gatewayTransactionId: $paymentIntent->id,
                status: $this->mapStripeStatus($paymentIntent->status),
                data: [
                    'payment_intent' => $paymentIntent->toArray(),
                    'charges' => $paymentIntent->charges->data
                ]
            );

        } catch (\Exception $e) {
            Log::error('Stripe payment query failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Payment query failed: ' . $e->getMessage(),
                errorCode: 'STRIPE_QUERY_FAILED'
            );
        }
    }

    /**
     * İade işlemini başlat
     */
    public function processRefund(
        string $gatewayTransactionId,
        PaymentAmount $refundAmount,
        string $reason
    ): PaymentGatewayResponse {
        try {
            // Önce payment intent'i al
            $paymentIntent = $this->stripe->paymentIntents->retrieve($gatewayTransactionId);

            if (empty($paymentIntent->charges->data)) {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'No charges found for this payment',
                    errorCode: 'NO_CHARGES_FOUND'
                );
            }

            $charge = $paymentIntent->charges->data[0];

            $refund = $this->stripe->refunds->create([
                'charge' => $charge->id,
                'amount' => $this->convertToStripeAmount($refundAmount->getAmount(), $refundAmount->getCurrency()),
                'reason' => $this->mapRefundReason($reason),
                'metadata' => [
                    'original_payment_id' => $gatewayTransactionId,
                    'refund_reason' => $reason
                ]
            ]);

            if ($refund->status === 'succeeded') {
                return new PaymentGatewayResponse(
                    success: true,
                    message: 'Refund processed successfully',
                    gatewayTransactionId: $refund->id,
                    status: 'refunded',
                    data: [
                        'refund_id' => $refund->id,
                        'charge_id' => $charge->id,
                        'amount' => $refund->amount,
                        'currency' => $refund->currency
                    ]
                );
            }

            return new PaymentGatewayResponse(
                success: false,
                message: 'Refund failed',
                errorCode: 'REFUND_FAILED'
            );

        } catch (\Exception $e) {
            Log::error('Stripe refund processing failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'amount' => $refundAmount->getAmount(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Refund processing failed: ' . $e->getMessage(),
                errorCode: 'STRIPE_REFUND_FAILED'
            );
        }
    }

    /**
     * Webhook'u doğrula
     */
    public function verifyWebhook(array $webhookData, string $signature): bool
    {
        try {
            $webhookSecret = $this->config['webhook_secret'] ?? '';

            \Stripe\Webhook::constructEvent(
                json_encode($webhookData),
                $signature,
                $webhookSecret
            );

            return true;

        } catch (\Exception $e) {
            Log::error('Stripe webhook verification failed', [
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Webhook verisini parse et
     */
    public function parseWebhook(array $webhookData): PaymentWebhookData
    {
        $eventType = $webhookData['type'] ?? '';
        $data = $webhookData['data']['object'] ?? [];

        return new PaymentWebhookData(
            gatewayTransactionId: $data['id'] ?? '',
            status: $this->mapWebhookStatus($eventType),
            amount: isset($data['amount']) ? $this->convertFromStripeAmount($data['amount'], $data['currency']) : null,
            currency: strtoupper($data['currency'] ?? ''),
            paymentMethod: $data['payment_method_types'][0] ?? null,
            rawData: $webhookData
        );
    }

    /**
     * Gateway'in desteklediği para birimlerini getir
     */
    public function getSupportedCurrencies(): array
    {
        return ['USD', 'EUR', 'TRY', 'GBP', 'CAD', 'AUD'];
    }

    /**
     * Gateway'in desteklediği ödeme yöntemlerini getir
     */
    public function getSupportedPaymentMethods(): array
    {
        return ['credit_card', 'apple_pay', 'google_pay', 'sepa_debit', 'ideal', 'sofort'];
    }

    /**
     * Gateway'in desteklediği taksit seçeneklerini getir
     */
    public function getSupportedInstallments(PaymentAmount $amount): array
    {
        // Stripe'da taksit genellikle desteklenmez
        return [1];
    }

    /**
     * Gateway'in minimum/maksimum tutarlarını getir
     */
    public function getAmountLimits(): array
    {
        return [
            'min' => 0.5,
            'max' => 999999.99
        ];
    }

    /**
     * Gateway'in komisyon oranlarını getir
     */
    public function getFeeRates(
        PaymentAmount $amount,
        string $paymentMethod,
        ?int $installments = null
    ): array {
        // Stripe komisyon oranları (örnek)
        $baseRate = 2.9; // %2.9
        $fixedFee = 0.30; // $0.30

        return [
            'fixed' => $fixedFee,
            'percentage' => $baseRate
        ];
    }

    /**
     * Gateway test modunda mı kontrol et
     */
    public function isTestMode(): bool
    {
        return $this->config['test_mode'] ?? true;
    }

    /**
     * Gateway aktif mi kontrol et
     */
    public function isActive(): bool
    {
        return $this->config['active'] ?? false;
    }

    /**
     * Gateway konfigürasyonunu doğrula
     */
    public function validateConfiguration(): bool
    {
        return !empty($this->config['public_key']) &&
               !empty($this->config['secret_key']);
    }

    /**
     * Payment Intent oluştur
     */
    private function createPaymentIntent(Payment $payment, array $paymentDetails, array $options = []): \Stripe\PaymentIntent
    {
        $params = array_merge([
            'amount' => $this->convertToStripeAmount($payment->getAmount()->getAmount(), $payment->getAmount()->getCurrency()),
            'currency' => strtolower($payment->getAmount()->getCurrency()),
            'payment_method_types' => ['card'],
            'metadata' => [
                'order_id' => $payment->getOrderId(),
                'transaction_id' => $payment->getTransactionId()->getValue(),
                'user_id' => $payment->getUserId()
            ]
        ], $options);

        // Payment method varsa ekle
        if (isset($paymentDetails['payment_method_id'])) {
            $params['payment_method'] = $paymentDetails['payment_method_id'];
        }

        // Customer varsa ekle
        if (isset($paymentDetails['customer_id'])) {
            $params['customer'] = $paymentDetails['customer_id'];
        }

        // Billing details varsa ekle
        if (isset($paymentDetails['billing_details'])) {
            $params['payment_method_data'] = [
                'type' => 'card',
                'billing_details' => $paymentDetails['billing_details']
            ];
        }

        return $this->stripe->paymentIntents->create($params);
    }

    /**
     * Stripe amount formatına çevir
     */
    private function convertToStripeAmount(float $amount, string $currency): int
    {
        // Stripe bazı para birimleri için cent kullanır
        $zeroDecimalCurrencies = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'];

        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (int) $amount;
        }

        return (int) ($amount * 100);
    }

    /**
     * Stripe amount'ı normal formata çevir
     */
    private function convertFromStripeAmount(int $amount, string $currency): float
    {
        $zeroDecimalCurrencies = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'];

        if (in_array(strtoupper($currency), $zeroDecimalCurrencies)) {
            return (float) $amount;
        }

        return $amount / 100;
    }

    /**
     * Stripe status'unu domain status'a map et
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return match ($stripeStatus) {
            'requires_payment_method', 'requires_confirmation' => 'pending',
            'requires_action' => 'requires_action',
            'processing' => 'processing',
            'succeeded' => 'completed',
            'canceled' => 'cancelled',
            default => 'failed'
        };
    }

    /**
     * Webhook event type'ını status'a map et
     */
    private function mapWebhookStatus(string $eventType): string
    {
        return match ($eventType) {
            'payment_intent.succeeded' => 'completed',
            'payment_intent.payment_failed' => 'failed',
            'payment_intent.canceled' => 'cancelled',
            'payment_intent.requires_action' => 'requires_action',
            'payment_intent.processing' => 'processing',
            'charge.dispute.created' => 'disputed',
            default => 'unknown'
        };
    }

    /**
     * Refund reason'ı Stripe formatına map et
     */
    private function mapRefundReason(string $reason): string
    {
        return match (strtolower($reason)) {
            'duplicate' => 'duplicate',
            'fraudulent' => 'fraudulent',
            'requested_by_customer' => 'requested_by_customer',
            default => 'requested_by_customer'
        };
    }

    /**
     * Charge'lardan fee hesapla
     */
    private function calculateFeeFromCharges(array $charges): float
    {
        $totalFee = 0;

        foreach ($charges as $charge) {
            if (isset($charge->balance_transaction)) {
                $balanceTransaction = $this->stripe->balanceTransactions->retrieve($charge->balance_transaction);
                $totalFee += $this->convertFromStripeAmount($balanceTransaction->fee, $balanceTransaction->currency);
            }
        }

        return $totalFee;
    }
}
