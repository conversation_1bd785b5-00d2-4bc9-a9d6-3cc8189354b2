<?php

namespace App\Infrastructure\Payment\Gateways;

use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayInterface;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayResponse;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentWebhookData;
use App\Domain\Payment\Entities\Payment;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Core\Domain\Exceptions\DomainException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;

/**
 * BankTransferGatewayAdapter
 * Banka transferi ödeme gateway'i için adapter
 */
class BankTransferGatewayAdapter implements PaymentGatewayInterface
{
    private array $config;

    public function __construct()
    {
        $this->config = Config::get('payment.gateways.bank_transfer', []);
    }

    /**
     * Ödeme işlemini başlat
     */
    public function processPayment(Payment $payment, array $paymentDetails): PaymentGatewayResponse
    {
        try {
            // Banka transferi için referans numarası oluştur
            $referenceNumber = $this->generateReferenceNumber($payment);
            
            // Banka bilgilerini hazırla
            $bankInfo = $this->prepareBankInformation($payment, $referenceNumber);

            Log::info('Bank transfer payment initiated', [
                'payment_id' => $payment->getId(),
                'reference_number' => $referenceNumber,
                'amount' => $payment->getAmount()->getAmount()
            ]);

            return new PaymentGatewayResponse(
                success: true,
                message: 'Banka transferi bilgileri hazırlandı',
                gatewayTransactionId: $referenceNumber,
                status: 'pending_transfer',
                data: [
                    'reference_number' => $referenceNumber,
                    'bank_accounts' => $bankInfo['accounts'],
                    'transfer_instructions' => $bankInfo['instructions'],
                    'amount' => $payment->getAmount()->getAmount(),
                    'currency' => $payment->getAmount()->getCurrency(),
                    'expires_at' => now()->addDays(3)->toISOString()
                ]
            );

        } catch (\Exception $e) {
            Log::error('Bank transfer payment processing failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Banka transferi işlemi başarısız: ' . $e->getMessage(),
                errorCode: 'BANK_TRANSFER_FAILED'
            );
        }
    }

    /**
     * 3D Secure ödeme işlemini başlat
     */
    public function process3DSecurePayment(
        Payment $payment,
        array $paymentDetails,
        string $callbackUrl,
        string $errorUrl
    ): PaymentGatewayResponse {
        // Banka transferinde 3D Secure yoktur
        return new PaymentGatewayResponse(
            success: false,
            message: '3D Secure not supported for bank transfers',
            errorCode: '3D_SECURE_NOT_SUPPORTED'
        );
    }

    /**
     * Taksitli ödeme işlemini başlat
     */
    public function processInstallmentPayment(
        Payment $payment,
        array $paymentDetails,
        int $installments
    ): PaymentGatewayResponse {
        // Banka transferinde taksit yoktur
        return new PaymentGatewayResponse(
            success: false,
            message: 'Installment payments not supported for bank transfers',
            errorCode: 'INSTALLMENT_NOT_SUPPORTED'
        );
    }

    /**
     * Ödeme durumunu sorgula
     */
    public function queryPayment(string $gatewayTransactionId): PaymentGatewayResponse
    {
        try {
            // Banka transferi manuel onay gerektirdiği için
            // gerçek durumu admin panelinden kontrol etmek gerekir
            Log::info('Bank transfer payment query', [
                'reference_number' => $gatewayTransactionId
            ]);

            return new PaymentGatewayResponse(
                success: true,
                message: 'Banka transferi sorgulandı',
                gatewayTransactionId: $gatewayTransactionId,
                status: 'pending_verification',
                data: [
                    'reference_number' => $gatewayTransactionId,
                    'status' => 'pending_verification',
                    'message' => 'Transfer manuel olarak doğrulanması bekleniyor'
                ]
            );

        } catch (\Exception $e) {
            Log::error('Bank transfer payment query failed', [
                'reference_number' => $gatewayTransactionId,
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Banka transferi sorgulama başarısız: ' . $e->getMessage(),
                errorCode: 'BANK_TRANSFER_QUERY_FAILED'
            );
        }
    }

    /**
     * İade işlemini başlat
     */
    public function processRefund(
        string $gatewayTransactionId,
        PaymentAmount $refundAmount,
        string $reason
    ): PaymentGatewayResponse {
        try {
            // Banka transferi iadesi manuel işlemdir
            $refundReference = 'REF-' . $gatewayTransactionId . '-' . time();

            Log::info('Bank transfer refund initiated', [
                'original_reference' => $gatewayTransactionId,
                'refund_reference' => $refundReference,
                'amount' => $refundAmount->getAmount(),
                'reason' => $reason
            ]);

            return new PaymentGatewayResponse(
                success: true,
                message: 'Banka transferi iadesi başlatıldı',
                gatewayTransactionId: $refundReference,
                status: 'refund_pending',
                data: [
                    'refund_reference' => $refundReference,
                    'original_reference' => $gatewayTransactionId,
                    'amount' => $refundAmount->getAmount(),
                    'currency' => $refundAmount->getCurrency(),
                    'reason' => $reason,
                    'status' => 'Manuel işlem gerekli',
                    'estimated_days' => '3-5 iş günü'
                ]
            );

        } catch (\Exception $e) {
            Log::error('Bank transfer refund processing failed', [
                'reference_number' => $gatewayTransactionId,
                'amount' => $refundAmount->getAmount(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Banka transferi iadesi başarısız: ' . $e->getMessage(),
                errorCode: 'BANK_TRANSFER_REFUND_FAILED'
            );
        }
    }

    /**
     * Webhook'u doğrula
     */
    public function verifyWebhook(array $webhookData, string $signature): bool
    {
        // Banka transferinde webhook yoktur
        return false;
    }

    /**
     * Webhook verisini parse et
     */
    public function parseWebhook(array $webhookData): PaymentWebhookData
    {
        // Banka transferinde webhook yoktur
        return new PaymentWebhookData(
            gatewayTransactionId: '',
            status: 'unknown',
            rawData: $webhookData
        );
    }

    /**
     * Gateway'in desteklediği para birimlerini getir
     */
    public function getSupportedCurrencies(): array
    {
        return ['TRY'];
    }

    /**
     * Gateway'in desteklediği ödeme yöntemlerini getir
     */
    public function getSupportedPaymentMethods(): array
    {
        return ['bank_transfer'];
    }

    /**
     * Gateway'in desteklediği taksit seçeneklerini getir
     */
    public function getSupportedInstallments(PaymentAmount $amount): array
    {
        return [1]; // Banka transferinde taksit yoktur
    }

    /**
     * Gateway'in minimum/maksimum tutarlarını getir
     */
    public function getAmountLimits(): array
    {
        return [
            'min' => $this->config['min_amount'] ?? 10.0,
            'max' => $this->config['max_amount'] ?? 100000.0
        ];
    }

    /**
     * Gateway'in komisyon oranlarını getir
     */
    public function getFeeRates(
        PaymentAmount $amount,
        string $paymentMethod,
        ?int $installments = null
    ): array {
        return [
            'fixed' => $this->config['fee_rates']['fixed_fee'] ?? 0.0,
            'percentage' => $this->config['fee_rates']['base_percentage'] ?? 0.0
        ];
    }

    /**
     * Gateway test modunda mı kontrol et
     */
    public function isTestMode(): bool
    {
        return $this->config['test_mode'] ?? false;
    }

    /**
     * Gateway aktif mi kontrol et
     */
    public function isActive(): bool
    {
        return $this->config['active'] ?? true;
    }

    /**
     * Gateway konfigürasyonunu doğrula
     */
    public function validateConfiguration(): bool
    {
        return !empty($this->config['banks']) && 
               is_array($this->config['banks']) &&
               count($this->config['banks']) > 0;
    }

    /**
     * Referans numarası oluştur
     */
    private function generateReferenceNumber(Payment $payment): string
    {
        $prefix = 'BT';
        $orderId = str_pad($payment->getOrderId(), 6, '0', STR_PAD_LEFT);
        $timestamp = substr(time(), -6);
        $random = strtoupper(Str::random(4));
        
        return $prefix . $orderId . $timestamp . $random;
    }

    /**
     * Banka bilgilerini hazırla
     */
    private function prepareBankInformation(Payment $payment, string $referenceNumber): array
    {
        $banks = $this->config['banks'] ?? [];
        $accounts = [];

        foreach ($banks as $bank) {
            $accounts[] = [
                'bank_name' => $bank['name'],
                'iban' => $bank['iban'],
                'account_name' => $bank['account_name'],
                'swift_code' => $bank['swift_code'] ?? null,
                'branch_code' => $bank['branch_code'] ?? null
            ];
        }

        $instructions = [
            'Havale/EFT yaparken açıklama kısmına mutlaka referans numarasını yazınız: ' . $referenceNumber,
            'Ödeme tutarı: ' . $payment->getAmount()->getAmount() . ' ' . $payment->getAmount()->getCurrency(),
            'Sipariş numarası: ' . $payment->getOrderId(),
            'Ödeme 3 gün içinde yapılmalıdır, aksi takdirde sipariş iptal edilecektir.',
            'Havale/EFT dekontu müşteri hizmetlerine gönderilmelidir.',
            'İşlem onaylandıktan sonra siparişiniz hazırlanmaya başlanacaktır.'
        ];

        return [
            'accounts' => $accounts,
            'instructions' => $instructions
        ];
    }

    /**
     * Manuel ödeme onayı
     */
    public function confirmManualPayment(
        string $referenceNumber,
        float $amount,
        string $bankName,
        string $transactionId,
        array $metadata = []
    ): PaymentGatewayResponse {
        try {
            Log::info('Manual bank transfer confirmation', [
                'reference_number' => $referenceNumber,
                'amount' => $amount,
                'bank_name' => $bankName,
                'transaction_id' => $transactionId
            ]);

            return new PaymentGatewayResponse(
                success: true,
                message: 'Banka transferi manuel olarak onaylandı',
                gatewayTransactionId: $referenceNumber,
                status: 'completed',
                data: array_merge([
                    'reference_number' => $referenceNumber,
                    'confirmed_amount' => $amount,
                    'bank_name' => $bankName,
                    'bank_transaction_id' => $transactionId,
                    'confirmed_at' => now()->toISOString(),
                    'confirmation_method' => 'manual'
                ], $metadata)
            );

        } catch (\Exception $e) {
            Log::error('Manual bank transfer confirmation failed', [
                'reference_number' => $referenceNumber,
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Manuel onay başarısız: ' . $e->getMessage(),
                errorCode: 'MANUAL_CONFIRMATION_FAILED'
            );
        }
    }

    /**
     * Manuel ödeme reddi
     */
    public function rejectManualPayment(
        string $referenceNumber,
        string $reason,
        array $metadata = []
    ): PaymentGatewayResponse {
        try {
            Log::info('Manual bank transfer rejection', [
                'reference_number' => $referenceNumber,
                'reason' => $reason
            ]);

            return new PaymentGatewayResponse(
                success: true,
                message: 'Banka transferi manuel olarak reddedildi',
                gatewayTransactionId: $referenceNumber,
                status: 'failed',
                data: array_merge([
                    'reference_number' => $referenceNumber,
                    'rejection_reason' => $reason,
                    'rejected_at' => now()->toISOString(),
                    'rejection_method' => 'manual'
                ], $metadata)
            );

        } catch (\Exception $e) {
            Log::error('Manual bank transfer rejection failed', [
                'reference_number' => $referenceNumber,
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'Manuel red başarısız: ' . $e->getMessage(),
                errorCode: 'MANUAL_REJECTION_FAILED'
            );
        }
    }
}
