<?php

namespace App\Infrastructure\Payment\Gateways;

use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayInterface;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentGatewayResponse;
use App\Infrastructure\Payment\Gateways\Contracts\PaymentWebhookData;
use App\Domain\Payment\Entities\Payment;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Core\Domain\Exceptions\DomainException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

/**
 * PayPalGatewayAdapter
 * PayPal ödeme gateway'i için adapter
 */
class PayPalGatewayAdapter implements PaymentGatewayInterface
{
    private array $config;
    private string $baseUrl;
    private ?string $accessToken = null;

    public function __construct()
    {
        $this->config = Config::get('payment.gateways.paypal', []);
        $this->baseUrl = $this->config['test_mode'] 
            ? 'https://api.sandbox.paypal.com'
            : 'https://api.paypal.com';
    }

    /**
     * Ödeme işlemini başlat
     */
    public function processPayment(Payment $payment, array $paymentDetails): PaymentGatewayResponse
    {
        try {
            // PayPal için order oluştur
            $order = $this->createPayPalOrder($payment, $paymentDetails);

            if (!$order || !isset($order['id'])) {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'Failed to create PayPal order',
                    errorCode: 'PAYPAL_ORDER_CREATION_FAILED'
                );
            }

            // Approval URL'ini bul
            $approvalUrl = null;
            foreach ($order['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approvalUrl = $link['href'];
                    break;
                }
            }

            return new PaymentGatewayResponse(
                success: true,
                message: 'PayPal order created successfully',
                gatewayTransactionId: $order['id'],
                status: 'pending_approval',
                redirectUrl: $approvalUrl,
                data: [
                    'order_id' => $order['id'],
                    'approval_url' => $approvalUrl,
                    'order_data' => $order
                ]
            );

        } catch (\Exception $e) {
            Log::error('PayPal payment processing failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'PayPal payment processing failed: ' . $e->getMessage(),
                errorCode: 'PAYPAL_PAYMENT_FAILED'
            );
        }
    }

    /**
     * 3D Secure ödeme işlemini başlat
     */
    public function process3DSecurePayment(
        Payment $payment,
        array $paymentDetails,
        string $callbackUrl,
        string $errorUrl
    ): PaymentGatewayResponse {
        // PayPal'da 3D Secure ayrı bir işlem değil, normal flow içinde
        return $this->processPayment($payment, $paymentDetails);
    }

    /**
     * Taksitli ödeme işlemini başlat
     */
    public function processInstallmentPayment(
        Payment $payment,
        array $paymentDetails,
        int $installments
    ): PaymentGatewayResponse {
        // PayPal'da taksit genellikle desteklenmez
        return new PaymentGatewayResponse(
            success: false,
            message: 'Installment payments not supported for PayPal',
            errorCode: 'INSTALLMENT_NOT_SUPPORTED'
        );
    }

    /**
     * Ödeme durumunu sorgula
     */
    public function queryPayment(string $gatewayTransactionId): PaymentGatewayResponse
    {
        try {
            $accessToken = $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->get("{$this->baseUrl}/v2/checkout/orders/{$gatewayTransactionId}");

            if (!$response->successful()) {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'Failed to query PayPal order',
                    errorCode: 'PAYPAL_QUERY_FAILED'
                );
            }

            $order = $response->json();
            $status = $this->mapPayPalStatus($order['status']);

            return new PaymentGatewayResponse(
                success: true,
                message: 'PayPal order query successful',
                gatewayTransactionId: $order['id'],
                status: $status,
                data: [
                    'order' => $order,
                    'paypal_status' => $order['status']
                ]
            );

        } catch (\Exception $e) {
            Log::error('PayPal payment query failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'PayPal payment query failed: ' . $e->getMessage(),
                errorCode: 'PAYPAL_QUERY_FAILED'
            );
        }
    }

    /**
     * İade işlemini başlat
     */
    public function processRefund(
        string $gatewayTransactionId,
        PaymentAmount $refundAmount,
        string $reason
    ): PaymentGatewayResponse {
        try {
            $accessToken = $this->getAccessToken();
            
            // Önce capture ID'yi bul
            $captureId = $this->getCaptureId($gatewayTransactionId);
            
            if (!$captureId) {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'Capture ID not found for refund',
                    errorCode: 'CAPTURE_ID_NOT_FOUND'
                );
            }

            $refundData = [
                'amount' => [
                    'value' => number_format($refundAmount->getAmount(), 2, '.', ''),
                    'currency_code' => $refundAmount->getCurrency()
                ],
                'note_to_payer' => $reason
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/v2/payments/captures/{$captureId}/refund", $refundData);

            if (!$response->successful()) {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'PayPal refund failed',
                    errorCode: 'PAYPAL_REFUND_FAILED'
                );
            }

            $refund = $response->json();

            return new PaymentGatewayResponse(
                success: true,
                message: 'PayPal refund processed successfully',
                gatewayTransactionId: $refund['id'],
                status: 'refunded',
                data: [
                    'refund_id' => $refund['id'],
                    'capture_id' => $captureId,
                    'refund_data' => $refund
                ]
            );

        } catch (\Exception $e) {
            Log::error('PayPal refund processing failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'amount' => $refundAmount->getAmount(),
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'PayPal refund processing failed: ' . $e->getMessage(),
                errorCode: 'PAYPAL_REFUND_FAILED'
            );
        }
    }

    /**
     * Webhook'u doğrula
     */
    public function verifyWebhook(array $webhookData, string $signature): bool
    {
        try {
            // PayPal webhook doğrulama
            $webhookId = $this->config['webhook_id'] ?? '';
            
            if (empty($webhookId)) {
                return false;
            }

            $accessToken = $this->getAccessToken();
            
            $verificationData = [
                'auth_algo' => 'SHA256withRSA',
                'cert_id' => $webhookData['cert_id'] ?? '',
                'transmission_id' => $webhookData['transmission_id'] ?? '',
                'transmission_sig' => $signature,
                'transmission_time' => $webhookData['transmission_time'] ?? '',
                'webhook_id' => $webhookId,
                'webhook_event' => $webhookData
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/v1/notifications/verify-webhook-signature", $verificationData);

            $result = $response->json();
            return $result['verification_status'] === 'SUCCESS';

        } catch (\Exception $e) {
            Log::error('PayPal webhook verification failed', [
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Webhook verisini parse et
     */
    public function parseWebhook(array $webhookData): PaymentWebhookData
    {
        $eventType = $webhookData['event_type'] ?? '';
        $resource = $webhookData['resource'] ?? [];

        return new PaymentWebhookData(
            gatewayTransactionId: $resource['id'] ?? '',
            status: $this->mapWebhookStatus($eventType),
            amount: isset($resource['amount']['value']) ? (float) $resource['amount']['value'] : null,
            currency: $resource['amount']['currency_code'] ?? '',
            paymentMethod: 'paypal',
            rawData: $webhookData
        );
    }

    /**
     * Gateway'in desteklediği para birimlerini getir
     */
    public function getSupportedCurrencies(): array
    {
        return ['USD', 'EUR', 'TRY', 'GBP', 'CAD', 'AUD', 'JPY'];
    }

    /**
     * Gateway'in desteklediği ödeme yöntemlerini getir
     */
    public function getSupportedPaymentMethods(): array
    {
        return ['paypal', 'credit_card'];
    }

    /**
     * Gateway'in desteklediği taksit seçeneklerini getir
     */
    public function getSupportedInstallments(PaymentAmount $amount): array
    {
        return [1]; // PayPal'da taksit yoktur
    }

    /**
     * Gateway'in minimum/maksimum tutarlarını getir
     */
    public function getAmountLimits(): array
    {
        return [
            'min' => $this->config['min_amount'] ?? 1.0,
            'max' => $this->config['max_amount'] ?? 10000.0
        ];
    }

    /**
     * Gateway'in komisyon oranlarını getir
     */
    public function getFeeRates(
        PaymentAmount $amount,
        string $paymentMethod,
        ?int $installments = null
    ): array {
        return [
            'fixed' => $this->config['fee_rates']['fixed_fee'] ?? 0.35,
            'percentage' => $this->config['fee_rates']['base_percentage'] ?? 3.4
        ];
    }

    /**
     * Gateway test modunda mı kontrol et
     */
    public function isTestMode(): bool
    {
        return $this->config['test_mode'] ?? true;
    }

    /**
     * Gateway aktif mi kontrol et
     */
    public function isActive(): bool
    {
        return $this->config['active'] ?? false;
    }

    /**
     * Gateway konfigürasyonunu doğrula
     */
    public function validateConfiguration(): bool
    {
        return !empty($this->config['client_id']) && 
               !empty($this->config['client_secret']);
    }

    /**
     * PayPal access token al
     */
    private function getAccessToken(): string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $clientId = $this->config['client_id'];
        $clientSecret = $this->config['client_secret'];
        
        $response = Http::withBasicAuth($clientId, $clientSecret)
            ->asForm()
            ->post("{$this->baseUrl}/v1/oauth2/token", [
                'grant_type' => 'client_credentials'
            ]);

        if (!$response->successful()) {
            throw new \RuntimeException('Failed to get PayPal access token');
        }

        $data = $response->json();
        $this->accessToken = $data['access_token'];

        return $this->accessToken;
    }

    /**
     * PayPal order oluştur
     */
    private function createPayPalOrder(Payment $payment, array $paymentDetails): ?array
    {
        $accessToken = $this->getAccessToken();
        
        $orderData = [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => $payment->getTransactionId()->getValue(),
                    'amount' => [
                        'currency_code' => $payment->getAmount()->getCurrency(),
                        'value' => number_format($payment->getAmount()->getAmount(), 2, '.', '')
                    ],
                    'description' => $payment->getDescription() ?: "Order #{$payment->getOrderId()}"
                ]
            ],
            'application_context' => [
                'return_url' => $paymentDetails['return_url'] ?? config('app.url') . '/payment/success',
                'cancel_url' => $paymentDetails['cancel_url'] ?? config('app.url') . '/payment/cancel',
                'brand_name' => config('app.name'),
                'landing_page' => 'BILLING',
                'user_action' => 'PAY_NOW'
            ]
        ];

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ])->post("{$this->baseUrl}/v2/checkout/orders", $orderData);

        if (!$response->successful()) {
            Log::error('PayPal order creation failed', [
                'response' => $response->json(),
                'status' => $response->status()
            ]);
            return null;
        }

        return $response->json();
    }

    /**
     * Capture ID'yi al
     */
    private function getCaptureId(string $orderId): ?string
    {
        $accessToken = $this->getAccessToken();
        
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ])->get("{$this->baseUrl}/v2/checkout/orders/{$orderId}");

        if (!$response->successful()) {
            return null;
        }

        $order = $response->json();
        
        foreach ($order['purchase_units'] as $unit) {
            if (isset($unit['payments']['captures'][0]['id'])) {
                return $unit['payments']['captures'][0]['id'];
            }
        }

        return null;
    }

    /**
     * PayPal status'unu domain status'a map et
     */
    private function mapPayPalStatus(string $paypalStatus): string
    {
        return match ($paypalStatus) {
            'CREATED' => 'pending',
            'SAVED' => 'pending',
            'APPROVED' => 'approved',
            'VOIDED' => 'cancelled',
            'COMPLETED' => 'completed',
            'PAYER_ACTION_REQUIRED' => 'requires_action',
            default => 'pending'
        };
    }

    /**
     * Webhook event type'ını status'a map et
     */
    private function mapWebhookStatus(string $eventType): string
    {
        return match ($eventType) {
            'CHECKOUT.ORDER.APPROVED' => 'approved',
            'CHECKOUT.ORDER.COMPLETED' => 'completed',
            'PAYMENT.CAPTURE.COMPLETED' => 'completed',
            'PAYMENT.CAPTURE.DENIED' => 'failed',
            'PAYMENT.CAPTURE.REFUNDED' => 'refunded',
            'CHECKOUT.ORDER.VOIDED' => 'cancelled',
            default => 'unknown'
        };
    }

    /**
     * PayPal order'ı capture et
     */
    public function captureOrder(string $orderId): PaymentGatewayResponse
    {
        try {
            $accessToken = $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post("{$this->baseUrl}/v2/checkout/orders/{$orderId}/capture");

            if (!$response->successful()) {
                return new PaymentGatewayResponse(
                    success: false,
                    message: 'PayPal order capture failed',
                    errorCode: 'PAYPAL_CAPTURE_FAILED'
                );
            }

            $capture = $response->json();

            return new PaymentGatewayResponse(
                success: true,
                message: 'PayPal order captured successfully',
                gatewayTransactionId: $orderId,
                status: 'completed',
                data: [
                    'capture_data' => $capture,
                    'capture_id' => $capture['purchase_units'][0]['payments']['captures'][0]['id'] ?? null
                ]
            );

        } catch (\Exception $e) {
            Log::error('PayPal order capture failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return new PaymentGatewayResponse(
                success: false,
                message: 'PayPal order capture failed: ' . $e->getMessage(),
                errorCode: 'PAYPAL_CAPTURE_FAILED'
            );
        }
    }
}
