<?php

namespace App\Infrastructure\Payment\Repositories;

use App\Domain\Payment\Entities\Payment;
use App\Domain\Payment\ValueObjects\TransactionId;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * PaymentTransactionRepository
 * Payment transaction tracking repository
 */
class PaymentTransactionRepository
{
    private string $table = 'payment_transactions';

    /**
     * Transaction kaydı oluştur
     */
    public function createTransaction(Payment $payment, string $action, array $data = []): int
    {
        try {
            $transactionData = [
                'payment_id' => $payment->getId(),
                'transaction_id' => $payment->getTransactionId()->getValue(),
                'gateway_provider' => $payment->getGateway()->getProvider(),
                'action' => $action, // process, refund, cancel, verify, etc.
                'status' => 'pending',
                'amount' => $payment->getAmount()->getAmount(),
                'currency' => $payment->getAmount()->getCurrency(),
                'gateway_request' => json_encode($data['request'] ?? []),
                'gateway_response' => json_encode($data['response'] ?? []),
                'ip_address' => $data['ip_address'] ?? request()->ip(),
                'user_agent' => $data['user_agent'] ?? request()->userAgent(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            $id = DB::table($this->table)->insertGetId($transactionData);

            Log::info('Payment transaction created', [
                'transaction_log_id' => $id,
                'payment_id' => $payment->getId(),
                'action' => $action
            ]);

            return $id;

        } catch (\Exception $e) {
            Log::error('Payment transaction creation failed', [
                'payment_id' => $payment->getId(),
                'action' => $action,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Transaction'ı güncelle
     */
    public function updateTransaction(int $transactionLogId, string $status, array $data = []): bool
    {
        try {
            $updateData = [
                'status' => $status,
                'updated_at' => Carbon::now(),
            ];

            if (isset($data['response'])) {
                $updateData['gateway_response'] = json_encode($data['response']);
            }

            if (isset($data['gateway_transaction_id'])) {
                $updateData['gateway_transaction_id'] = $data['gateway_transaction_id'];
            }

            if (isset($data['error_message'])) {
                $updateData['error_message'] = $data['error_message'];
            }

            if (isset($data['processed_at'])) {
                $updateData['processed_at'] = $data['processed_at'];
            }

            $result = DB::table($this->table)
                ->where('id', $transactionLogId)
                ->update($updateData);

            Log::info('Payment transaction updated', [
                'transaction_log_id' => $transactionLogId,
                'status' => $status
            ]);

            return $result > 0;

        } catch (\Exception $e) {
            Log::error('Payment transaction update failed', [
                'transaction_log_id' => $transactionLogId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Payment'a ait transaction'ları al
     */
    public function getPaymentTransactions(int $paymentId): array
    {
        try {
            return DB::table($this->table)
                ->where('payment_id', $paymentId)
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get payment transactions', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Transaction ID ile transaction'ları al
     */
    public function getTransactionsByTransactionId(string $transactionId): array
    {
        try {
            return DB::table($this->table)
                ->where('transaction_id', $transactionId)
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get transactions by transaction ID', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Gateway transaction ID ile transaction bul
     */
    public function findByGatewayTransactionId(string $gatewayTransactionId): ?object
    {
        try {
            return DB::table($this->table)
                ->where('gateway_transaction_id', $gatewayTransactionId)
                ->first();

        } catch (\Exception $e) {
            Log::error('Failed to find transaction by gateway transaction ID', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Başarısız transaction'ları al
     */
    public function getFailedTransactions(int $limit = 100, int $offset = 0): array
    {
        try {
            return DB::table($this->table)
                ->where('status', 'failed')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->offset($offset)
                ->get()
                ->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get failed transactions', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Pending transaction'ları al
     */
    public function getPendingTransactions(int $timeoutMinutes = 30): array
    {
        try {
            $cutoffTime = Carbon::now()->subMinutes($timeoutMinutes);

            return DB::table($this->table)
                ->where('status', 'pending')
                ->where('created_at', '<', $cutoffTime)
                ->orderBy('created_at', 'asc')
                ->get()
                ->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get pending transactions', [
                'timeout_minutes' => $timeoutMinutes,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Transaction istatistiklerini al
     */
    public function getTransactionStatistics(Carbon $startDate = null, Carbon $endDate = null): array
    {
        try {
            $startDate = $startDate ?? Carbon::now()->subDays(30);
            $endDate = $endDate ?? Carbon::now();

            $stats = DB::table($this->table)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('
                    COUNT(*) as total_transactions,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as successful_transactions,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_transactions,
                    COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_transactions,
                    COUNT(DISTINCT payment_id) as unique_payments,
                    COUNT(DISTINCT gateway_provider) as gateway_count,
                    AVG(CASE WHEN processed_at IS NOT NULL THEN 
                        TIMESTAMPDIFF(SECOND, created_at, processed_at) 
                    END) as avg_processing_time_seconds
                ')
                ->first();

            return [
                'period' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                ],
                'total_transactions' => $stats->total_transactions ?? 0,
                'successful_transactions' => $stats->successful_transactions ?? 0,
                'failed_transactions' => $stats->failed_transactions ?? 0,
                'pending_transactions' => $stats->pending_transactions ?? 0,
                'unique_payments' => $stats->unique_payments ?? 0,
                'gateway_count' => $stats->gateway_count ?? 0,
                'success_rate' => $stats->total_transactions > 0 
                    ? round(($stats->successful_transactions / $stats->total_transactions) * 100, 2) 
                    : 0,
                'failure_rate' => $stats->total_transactions > 0 
                    ? round(($stats->failed_transactions / $stats->total_transactions) * 100, 2) 
                    : 0,
                'average_processing_time_seconds' => round($stats->avg_processing_time_seconds ?? 0, 2),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get transaction statistics', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Gateway bazında istatistikleri al
     */
    public function getGatewayStatistics(Carbon $startDate = null, Carbon $endDate = null): array
    {
        try {
            $startDate = $startDate ?? Carbon::now()->subDays(30);
            $endDate = $endDate ?? Carbon::now();

            $stats = DB::table($this->table)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('
                    gateway_provider,
                    COUNT(*) as total_transactions,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as successful_transactions,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_transactions,
                    SUM(amount) as total_amount,
                    AVG(amount) as average_amount,
                    AVG(CASE WHEN processed_at IS NOT NULL THEN 
                        TIMESTAMPDIFF(SECOND, created_at, processed_at) 
                    END) as avg_processing_time_seconds
                ')
                ->groupBy('gateway_provider')
                ->orderBy('total_transactions', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'gateway_provider' => $item->gateway_provider,
                        'total_transactions' => $item->total_transactions,
                        'successful_transactions' => $item->successful_transactions,
                        'failed_transactions' => $item->failed_transactions,
                        'total_amount' => $item->total_amount,
                        'average_amount' => round($item->average_amount ?? 0, 2),
                        'success_rate' => $item->total_transactions > 0 
                            ? round(($item->successful_transactions / $item->total_transactions) * 100, 2) 
                            : 0,
                        'average_processing_time_seconds' => round($item->avg_processing_time_seconds ?? 0, 2),
                    ];
                })
                ->toArray();

            return [
                'period' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                ],
                'gateways' => $stats,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get gateway statistics', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Transaction log'larını temizle
     */
    public function cleanupOldTransactions(int $daysOld = 90): int
    {
        try {
            $cutoffDate = Carbon::now()->subDays($daysOld);

            $count = DB::table($this->table)
                ->where('created_at', '<', $cutoffDate)
                ->where('status', '!=', 'pending') // Pending olanları silme
                ->delete();

            Log::info('Old payment transactions cleaned up', [
                'days_old' => $daysOld,
                'deleted_count' => $count
            ]);

            return $count;

        } catch (\Exception $e) {
            Log::error('Failed to cleanup old transactions', [
                'days_old' => $daysOld,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }
}
