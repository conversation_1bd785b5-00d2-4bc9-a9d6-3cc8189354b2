<?php

namespace App\Infrastructure\Payment\Services;

use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Payment\ValueObjects\TransactionId;
use App\Infrastructure\Payment\Repositories\PaymentTransactionRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * PaymentWebhookHandler
 * Payment gateway webhook'larını işleyen service
 */
class PaymentWebhookHandler
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private PaymentTransactionRepository $transactionRepository
    ) {}

    /**
     * Webhook'u işle
     */
    public function handleWebhook(string $provider, array $payload, array $headers = []): array
    {
        try {
            // Webhook kaydını oluştur
            $webhookId = $this->createWebhookRecord($provider, $payload, $headers);

            // Provider'a göre webhook'u işle
            $result = match ($provider) {
                'iyzico' => $this->handleIyzicoWebhook($payload, $headers),
                'stripe' => $this->handleStripeWebhook($payload, $headers),
                'paypal' => $this->handlePayPalWebhook($payload, $headers),
                default => $this->handleGenericWebhook($provider, $payload, $headers)
            };

            // Webhook kaydını güncelle
            $this->updateWebhookRecord($webhookId, $result['status'], $result);

            Log::info('Webhook processed successfully', [
                'provider' => $provider,
                'webhook_id' => $webhookId,
                'status' => $result['status']
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'processed' => false
            ];
        }
    }

    /**
     * Iyzico webhook'unu işle
     */
    private function handleIyzicoWebhook(array $payload, array $headers): array
    {
        try {
            // Iyzico webhook signature doğrulama
            if (!$this->verifyIyzicoSignature($payload, $headers)) {
                throw new \Exception('Invalid Iyzico webhook signature');
            }

            $eventType = $payload['eventType'] ?? '';
            $paymentId = $payload['paymentId'] ?? '';

            switch ($eventType) {
                case 'payment.success':
                    return $this->handlePaymentSuccess($paymentId, $payload, 'iyzico');

                case 'payment.failure':
                    return $this->handlePaymentFailure($paymentId, $payload, 'iyzico');

                case 'refund.success':
                    return $this->handleRefundSuccess($paymentId, $payload, 'iyzico');

                case 'refund.failure':
                    return $this->handleRefundFailure($paymentId, $payload, 'iyzico');

                default:
                    Log::warning('Unknown Iyzico webhook event type', [
                        'event_type' => $eventType,
                        'payload' => $payload
                    ]);

                    return [
                        'status' => 'ignored',
                        'message' => 'Unknown event type',
                        'processed' => false
                    ];
            }

        } catch (\Exception $e) {
            Log::error('Iyzico webhook handling failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw $e;
        }
    }

    /**
     * Stripe webhook'unu işle
     */
    private function handleStripeWebhook(array $payload, array $headers): array
    {
        try {
            // Stripe webhook signature doğrulama
            if (!$this->verifyStripeSignature($payload, $headers)) {
                throw new \Exception('Invalid Stripe webhook signature');
            }

            $eventType = $payload['type'] ?? '';
            $paymentIntentId = $payload['data']['object']['id'] ?? '';

            switch ($eventType) {
                case 'payment_intent.succeeded':
                    return $this->handlePaymentSuccess($paymentIntentId, $payload, 'stripe');

                case 'payment_intent.payment_failed':
                    return $this->handlePaymentFailure($paymentIntentId, $payload, 'stripe');

                case 'charge.dispute.created':
                    return $this->handleChargeDispute($paymentIntentId, $payload, 'stripe');

                default:
                    Log::warning('Unknown Stripe webhook event type', [
                        'event_type' => $eventType,
                        'payload' => $payload
                    ]);

                    return [
                        'status' => 'ignored',
                        'message' => 'Unknown event type',
                        'processed' => false
                    ];
            }

        } catch (\Exception $e) {
            Log::error('Stripe webhook handling failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw $e;
        }
    }

    /**
     * PayPal webhook'unu işle
     */
    private function handlePayPalWebhook(array $payload, array $headers): array
    {
        try {
            // PayPal webhook signature doğrulama
            if (!$this->verifyPayPalSignature($payload, $headers)) {
                throw new \Exception('Invalid PayPal webhook signature');
            }

            $eventType = $payload['event_type'] ?? '';
            $resourceId = $payload['resource']['id'] ?? '';

            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePaymentSuccess($resourceId, $payload, 'paypal');

                case 'PAYMENT.CAPTURE.DENIED':
                    return $this->handlePaymentFailure($resourceId, $payload, 'paypal');

                case 'PAYMENT.CAPTURE.REFUNDED':
                    return $this->handleRefundSuccess($resourceId, $payload, 'paypal');

                default:
                    Log::warning('Unknown PayPal webhook event type', [
                        'event_type' => $eventType,
                        'payload' => $payload
                    ]);

                    return [
                        'status' => 'ignored',
                        'message' => 'Unknown event type',
                        'processed' => false
                    ];
            }

        } catch (\Exception $e) {
            Log::error('PayPal webhook handling failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw $e;
        }
    }

    /**
     * Generic webhook handler
     */
    private function handleGenericWebhook(string $provider, array $payload, array $headers): array
    {
        Log::info('Generic webhook received', [
            'provider' => $provider,
            'payload' => $payload
        ]);

        return [
            'status' => 'received',
            'message' => 'Generic webhook processed',
            'processed' => true
        ];
    }

    /**
     * Payment başarı durumunu işle
     */
    private function handlePaymentSuccess(string $gatewayTransactionId, array $payload, string $provider): array
    {
        try {
            // Gateway transaction ID ile payment'ı bul
            $payment = $this->paymentRepository->findByGatewayTransactionId($gatewayTransactionId);

            if (!$payment) {
                Log::warning('Payment not found for gateway transaction ID', [
                    'gateway_transaction_id' => $gatewayTransactionId,
                    'provider' => $provider
                ]);

                return [
                    'status' => 'not_found',
                    'message' => 'Payment not found',
                    'processed' => false
                ];
            }

            // Payment durumunu güncelle
            if ($payment->getStatus() !== 'completed') {
                $payment->markAsCompleted();
                $this->paymentRepository->save($payment);

                // Transaction log'u oluştur
                $this->transactionRepository->createTransaction($payment, 'webhook_success', [
                    'response' => $payload,
                    'provider' => $provider
                ]);

                Log::info('Payment marked as completed via webhook', [
                    'payment_id' => $payment->getId(),
                    'transaction_id' => $payment->getTransactionId()->getValue(),
                    'provider' => $provider
                ]);
            }

            return [
                'status' => 'success',
                'message' => 'Payment completed',
                'processed' => true,
                'payment_id' => $payment->getId()
            ];

        } catch (\Exception $e) {
            Log::error('Payment success handling failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Payment başarısızlık durumunu işle
     */
    private function handlePaymentFailure(string $gatewayTransactionId, array $payload, string $provider): array
    {
        try {
            // Gateway transaction ID ile payment'ı bul
            $payment = $this->paymentRepository->findByGatewayTransactionId($gatewayTransactionId);

            if (!$payment) {
                Log::warning('Payment not found for gateway transaction ID', [
                    'gateway_transaction_id' => $gatewayTransactionId,
                    'provider' => $provider
                ]);

                return [
                    'status' => 'not_found',
                    'message' => 'Payment not found',
                    'processed' => false
                ];
            }

            // Payment durumunu güncelle
            if ($payment->getStatus() !== 'failed') {
                $errorMessage = $this->extractErrorMessage($payload, $provider);
                $payment->markAsFailed($errorMessage);
                $this->paymentRepository->save($payment);

                // Transaction log'u oluştur
                $this->transactionRepository->createTransaction($payment, 'webhook_failure', [
                    'response' => $payload,
                    'provider' => $provider,
                    'error_message' => $errorMessage
                ]);

                Log::info('Payment marked as failed via webhook', [
                    'payment_id' => $payment->getId(),
                    'transaction_id' => $payment->getTransactionId()->getValue(),
                    'provider' => $provider,
                    'error_message' => $errorMessage
                ]);
            }

            return [
                'status' => 'failed',
                'message' => 'Payment failed',
                'processed' => true,
                'payment_id' => $payment->getId()
            ];

        } catch (\Exception $e) {
            Log::error('Payment failure handling failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Refund başarı durumunu işle
     */
    private function handleRefundSuccess(string $gatewayTransactionId, array $payload, string $provider): array
    {
        try {
            $payment = $this->paymentRepository->findByGatewayTransactionId($gatewayTransactionId);

            if (!$payment) {
                return [
                    'status' => 'not_found',
                    'message' => 'Payment not found',
                    'processed' => false
                ];
            }

            // Refund amount'u extract et
            $refundAmount = $this->extractRefundAmount($payload, $provider);

            // Transaction log'u oluştur
            $this->transactionRepository->createTransaction($payment, 'webhook_refund_success', [
                'response' => $payload,
                'provider' => $provider,
                'refund_amount' => $refundAmount
            ]);

            Log::info('Refund completed via webhook', [
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount,
                'provider' => $provider
            ]);

            return [
                'status' => 'refund_success',
                'message' => 'Refund completed',
                'processed' => true,
                'payment_id' => $payment->getId(),
                'refund_amount' => $refundAmount
            ];

        } catch (\Exception $e) {
            Log::error('Refund success handling failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Refund başarısızlık durumunu işle
     */
    private function handleRefundFailure(string $gatewayTransactionId, array $payload, string $provider): array
    {
        try {
            $payment = $this->paymentRepository->findByGatewayTransactionId($gatewayTransactionId);

            if (!$payment) {
                return [
                    'status' => 'not_found',
                    'message' => 'Payment not found',
                    'processed' => false
                ];
            }

            $errorMessage = $this->extractErrorMessage($payload, $provider);

            // Transaction log'u oluştur
            $this->transactionRepository->createTransaction($payment, 'webhook_refund_failure', [
                'response' => $payload,
                'provider' => $provider,
                'error_message' => $errorMessage
            ]);

            Log::warning('Refund failed via webhook', [
                'payment_id' => $payment->getId(),
                'provider' => $provider,
                'error_message' => $errorMessage
            ]);

            return [
                'status' => 'refund_failed',
                'message' => 'Refund failed',
                'processed' => true,
                'payment_id' => $payment->getId(),
                'error_message' => $errorMessage
            ];

        } catch (\Exception $e) {
            Log::error('Refund failure handling failed', [
                'gateway_transaction_id' => $gatewayTransactionId,
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Webhook kaydı oluştur
     */
    private function createWebhookRecord(string $provider, array $payload, array $headers): int
    {
        try {
            $webhookData = [
                'provider' => $provider,
                'event_type' => $this->extractEventType($payload, $provider),
                'payload' => json_encode($payload),
                'headers' => json_encode($headers),
                'status' => 'processing',
                'ip_address' => request()->ip(),
                'received_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            return DB::table('payment_webhooks')->insertGetId($webhookData);

        } catch (\Exception $e) {
            Log::error('Webhook record creation failed', [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Webhook kaydını güncelle
     */
    private function updateWebhookRecord(int $webhookId, string $status, array $result): void
    {
        try {
            DB::table('payment_webhooks')
                ->where('id', $webhookId)
                ->update([
                    'status' => $status,
                    'response' => json_encode($result),
                    'processed_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);

        } catch (\Exception $e) {
            Log::error('Webhook record update failed', [
                'webhook_id' => $webhookId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Event type'ını extract et
     */
    private function extractEventType(array $payload, string $provider): string
    {
        return match ($provider) {
            'iyzico' => $payload['eventType'] ?? 'unknown',
            'stripe' => $payload['type'] ?? 'unknown',
            'paypal' => $payload['event_type'] ?? 'unknown',
            default => 'unknown'
        };
    }

    /**
     * Error message'ı extract et
     */
    private function extractErrorMessage(array $payload, string $provider): string
    {
        return match ($provider) {
            'iyzico' => $payload['errorMessage'] ?? 'Unknown error',
            'stripe' => $payload['data']['object']['last_payment_error']['message'] ?? 'Unknown error',
            'paypal' => $payload['resource']['reason_code'] ?? 'Unknown error',
            default => 'Unknown error'
        };
    }

    /**
     * Refund amount'ı extract et
     */
    private function extractRefundAmount(array $payload, string $provider): float
    {
        return match ($provider) {
            'iyzico' => (float) ($payload['refundPrice'] ?? 0),
            'stripe' => (float) ($payload['data']['object']['amount'] ?? 0) / 100, // Stripe cent cinsinden
            'paypal' => (float) ($payload['resource']['amount']['value'] ?? 0),
            default => 0
        };
    }

    /**
     * Signature doğrulama metodları
     */
    private function verifyIyzicoSignature(array $payload, array $headers): bool
    {
        // Iyzico webhook signature doğrulama implementasyonu
        // Bu gerçek implementasyonda Iyzico'nun signature algoritması kullanılmalı
        return true; // Geçici olarak true döndürüyoruz
    }

    private function verifyStripeSignature(array $payload, array $headers): bool
    {
        // Stripe webhook signature doğrulama implementasyonu
        // Bu gerçek implementasyonda Stripe'ın signature algoritması kullanılmalı
        return true; // Geçici olarak true döndürüyoruz
    }

    private function verifyPayPalSignature(array $payload, array $headers): bool
    {
        // PayPal webhook signature doğrulama implementasyonu
        // Bu gerçek implementasyonda PayPal'ın signature algoritması kullanılmalı
        return true; // Geçici olarak true döndürüyoruz
    }

    /**
     * Webhook istatistiklerini al
     */
    public function getWebhookStatistics(Carbon $startDate = null, Carbon $endDate = null): array
    {
        try {
            $startDate = $startDate ?? Carbon::now()->subDays(7);
            $endDate = $endDate ?? Carbon::now();

            $stats = DB::table('payment_webhooks')
                ->whereBetween('received_at', [$startDate, $endDate])
                ->selectRaw('
                    COUNT(*) as total_webhooks,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as successful_webhooks,
                    COUNT(CASE WHEN status = "error" THEN 1 END) as failed_webhooks,
                    COUNT(CASE WHEN status = "processing" THEN 1 END) as processing_webhooks,
                    COUNT(DISTINCT provider) as provider_count
                ')
                ->first();

            $providerStats = DB::table('payment_webhooks')
                ->whereBetween('received_at', [$startDate, $endDate])
                ->selectRaw('
                    provider,
                    COUNT(*) as webhook_count,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as success_count
                ')
                ->groupBy('provider')
                ->get()
                ->toArray();

            return [
                'period' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                ],
                'total_webhooks' => $stats->total_webhooks ?? 0,
                'successful_webhooks' => $stats->successful_webhooks ?? 0,
                'failed_webhooks' => $stats->failed_webhooks ?? 0,
                'processing_webhooks' => $stats->processing_webhooks ?? 0,
                'provider_count' => $stats->provider_count ?? 0,
                'success_rate' => $stats->total_webhooks > 0
                    ? round(($stats->successful_webhooks / $stats->total_webhooks) * 100, 2)
                    : 0,
                'provider_stats' => $providerStats,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get webhook statistics', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage(),
            ];
        }
    }
}