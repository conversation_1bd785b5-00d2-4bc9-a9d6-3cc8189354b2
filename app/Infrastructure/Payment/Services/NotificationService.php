<?php

namespace App\Infrastructure\Payment\Services;

use App\Domain\Payment\Entities\Payment;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification as BaseNotification;

/**
 * NotificationService
 * Payment bildirimleri için service
 */
class NotificationService
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * Ödeme oluşturuldu bildirimi gönder
     */
    public function sendPaymentCreatedNotification(Payment $payment): void
    {
        try {
            if (!$this->isNotificationEnabled('email')) {
                return;
            }

            $user = $this->getUser($payment->getUserId());
            if (!$user) {
                return;
            }

            $notification = new PaymentCreatedNotification($payment);
            $user->notify($notification);

            Log::info('Payment created notification sent', [
                'payment_id' => $payment->getId(),
                'user_id' => $payment->getUserId()
            ]);

        } catch (\Exception $e) {
            Log::error('Payment created notification failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Ödeme başarılı bildirimi gönder
     */
    public function sendPaymentSuccessNotification(Payment $payment): void
    {
        try {
            if (!$this->isNotificationEnabled('email')) {
                return;
            }

            $user = $this->getUser($payment->getUserId());
            if (!$user) {
                return;
            }

            $notification = new PaymentSuccessNotification($payment);
            $user->notify($notification);

            Log::info('Payment success notification sent', [
                'payment_id' => $payment->getId(),
                'user_id' => $payment->getUserId()
            ]);

        } catch (\Exception $e) {
            Log::error('Payment success notification failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Ödeme başarısız bildirimi gönder
     */
    public function sendPaymentFailedNotification(Payment $payment, string $reason = ''): void
    {
        try {
            if (!$this->isNotificationEnabled('email')) {
                return;
            }

            $user = $this->getUser($payment->getUserId());
            if (!$user) {
                return;
            }

            $notification = new PaymentFailedNotification($payment, $reason);
            $user->notify($notification);

            Log::info('Payment failed notification sent', [
                'payment_id' => $payment->getId(),
                'user_id' => $payment->getUserId(),
                'reason' => $reason
            ]);

        } catch (\Exception $e) {
            Log::error('Payment failed notification failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * İade işlemi bildirimi gönder
     */
    public function sendRefundNotification(Payment $payment, float $refundAmount): void
    {
        try {
            if (!$this->isNotificationEnabled('email')) {
                return;
            }

            $user = $this->getUser($payment->getUserId());
            if (!$user) {
                return;
            }

            $notification = new RefundProcessedNotification($payment, $refundAmount);
            $user->notify($notification);

            Log::info('Refund notification sent', [
                'payment_id' => $payment->getId(),
                'user_id' => $payment->getUserId(),
                'refund_amount' => $refundAmount
            ]);

        } catch (\Exception $e) {
            Log::error('Refund notification failed', [
                'payment_id' => $payment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Admin uyarısı gönder
     */
    public function sendAdminAlert(string $subject, string $message, array $data = []): void
    {
        try {
            if (!$this->isAdminNotificationEnabled()) {
                return;
            }

            $adminEmail = $this->config['admin_notifications']['email'] ?? config('mail.from.address');
            
            Mail::raw($message, function ($mail) use ($adminEmail, $subject, $data) {
                $mail->to($adminEmail)
                     ->subject('[Payment Alert] ' . $subject);
                
                if (!empty($data)) {
                    $mail->attach('data.json', json_encode($data, JSON_PRETTY_PRINT), [
                        'mime' => 'application/json'
                    ]);
                }
            });

            Log::info('Admin alert sent', [
                'subject' => $subject,
                'admin_email' => $adminEmail
            ]);

        } catch (\Exception $e) {
            Log::error('Admin alert failed', [
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * SMS bildirimi gönder
     */
    public function sendSmsNotification(int $userId, string $message): void
    {
        try {
            if (!$this->isNotificationEnabled('sms')) {
                return;
            }

            $user = $this->getUser($userId);
            if (!$user || !$user->phone) {
                return;
            }

            // SMS provider'a göre gönderim
            $provider = $this->config['channels']['sms']['provider'] ?? 'twilio';
            
            switch ($provider) {
                case 'twilio':
                    $this->sendTwilioSms($user->phone, $message);
                    break;
                case 'nexmo':
                    $this->sendNexmoSms($user->phone, $message);
                    break;
                default:
                    Log::warning('Unsupported SMS provider', ['provider' => $provider]);
            }

        } catch (\Exception $e) {
            Log::error('SMS notification failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Push bildirimi gönder
     */
    public function sendPushNotification(int $userId, string $title, string $message, array $data = []): void
    {
        try {
            if (!$this->isNotificationEnabled('push')) {
                return;
            }

            $user = $this->getUser($userId);
            if (!$user) {
                return;
            }

            // Push notification provider'a göre gönderim
            $provider = $this->config['channels']['push']['provider'] ?? 'fcm';
            
            switch ($provider) {
                case 'fcm':
                    $this->sendFcmPush($user, $title, $message, $data);
                    break;
                case 'apns':
                    $this->sendApnsPush($user, $title, $message, $data);
                    break;
                default:
                    Log::warning('Unsupported push provider', ['provider' => $provider]);
            }

        } catch (\Exception $e) {
            Log::error('Push notification failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Bildirim türü aktif mi kontrol et
     */
    private function isNotificationEnabled(string $channel): bool
    {
        return $this->config['enabled'] ?? true &&
               $this->config['channels'][$channel]['enabled'] ?? false;
    }

    /**
     * Admin bildirimleri aktif mi kontrol et
     */
    private function isAdminNotificationEnabled(): bool
    {
        return $this->config['admin_notifications']['enabled'] ?? true;
    }

    /**
     * Kullanıcıyı getir
     */
    private function getUser(?int $userId)
    {
        if (!$userId) {
            return null;
        }

        try {
            return \App\Models\User::find($userId);
        } catch (\Exception $e) {
            Log::warning('User not found for notification', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Twilio SMS gönder
     */
    private function sendTwilioSms(string $phone, string $message): void
    {
        // Twilio SMS implementation
        Log::info('Twilio SMS sent', ['phone' => $phone]);
    }

    /**
     * Nexmo SMS gönder
     */
    private function sendNexmoSms(string $phone, string $message): void
    {
        // Nexmo SMS implementation
        Log::info('Nexmo SMS sent', ['phone' => $phone]);
    }

    /**
     * FCM Push gönder
     */
    private function sendFcmPush($user, string $title, string $message, array $data): void
    {
        // FCM Push implementation
        Log::info('FCM push sent', ['user_id' => $user->id]);
    }

    /**
     * APNS Push gönder
     */
    private function sendApnsPush($user, string $title, string $message, array $data): void
    {
        // APNS Push implementation
        Log::info('APNS push sent', ['user_id' => $user->id]);
    }
}

/**
 * PaymentCreatedNotification
 */
class PaymentCreatedNotification extends BaseNotification
{
    public function __construct(private Payment $payment) {}

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Ödeme Oluşturuldu')
            ->line("Sipariş numaranız #{$this->payment->getOrderId()} için ödeme oluşturuldu.")
            ->line("Tutar: {$this->payment->getAmount()->getAmount()} {$this->payment->getAmount()->getCurrency()}")
            ->line("Ödeme yöntemi: {$this->payment->getPaymentMethod()}")
            ->line('Ödeme işleminiz işleme alınmıştır.');
    }
}

/**
 * PaymentSuccessNotification
 */
class PaymentSuccessNotification extends BaseNotification
{
    public function __construct(private Payment $payment) {}

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Ödeme Başarılı')
            ->line("Sipariş numaranız #{$this->payment->getOrderId()} için ödeme başarıyla tamamlandı.")
            ->line("Tutar: {$this->payment->getAmount()->getAmount()} {$this->payment->getAmount()->getCurrency()}")
            ->line("İşlem ID: {$this->payment->getTransactionId()->getValue()}")
            ->line('Siparişiniz hazırlanmaya başlanacaktır.');
    }
}

/**
 * PaymentFailedNotification
 */
class PaymentFailedNotification extends BaseNotification
{
    public function __construct(
        private Payment $payment,
        private string $reason
    ) {}

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Ödeme Başarısız')
            ->line("Sipariş numaranız #{$this->payment->getOrderId()} için ödeme başarısız oldu.")
            ->line("Tutar: {$this->payment->getAmount()->getAmount()} {$this->payment->getAmount()->getCurrency()}")
            ->line("Hata nedeni: {$this->reason}")
            ->line('Lütfen ödeme bilgilerinizi kontrol ederek tekrar deneyiniz.');
    }
}

/**
 * RefundProcessedNotification
 */
class RefundProcessedNotification extends BaseNotification
{
    public function __construct(
        private Payment $payment,
        private float $refundAmount
    ) {}

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('İade İşlemi Tamamlandı')
            ->line("Sipariş numaranız #{$this->payment->getOrderId()} için iade işlemi tamamlandı.")
            ->line("İade tutarı: {$this->refundAmount} {$this->payment->getAmount()->getCurrency()}")
            ->line("İşlem ID: {$this->payment->getTransactionId()->getValue()}")
            ->line('İade tutarı 3-5 iş günü içinde hesabınıza yansıyacaktır.');
    }
}
