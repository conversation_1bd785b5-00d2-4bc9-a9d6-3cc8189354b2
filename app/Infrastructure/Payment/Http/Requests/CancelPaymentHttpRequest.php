<?php

namespace App\Infrastructure\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * CancelPaymentHttpRequest
 * Ödeme iptali için HTTP request validation
 */
class CancelPaymentHttpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'reason' => [
                'required',
                'string',
                'min:3',
                'max:500',
                Rule::in([
                    'customer_request',
                    'duplicate_payment',
                    'fraud_suspected',
                    'technical_error',
                    'order_cancelled',
                    'payment_timeout',
                    'insufficient_funds',
                    'other'
                ])
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'notify_customer' => [
                'nullable',
                'boolean'
            ],
            'refund_amount' => [
                'nullable',
                'numeric',
                'min:0'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'reason.required' => 'İptal nedeni gereklidir.',
            'reason.in' => 'Geçersiz iptal nedeni.',
            'reason.min' => 'İptal nedeni en az 3 karakter olmalıdır.',
            'reason.max' => 'İptal nedeni en fazla 500 karakter olmalıdır.',
            'description.max' => 'Açıklama en fazla 1000 karakter olmalıdır.',
            'refund_amount.numeric' => 'İade tutarı sayısal olmalıdır.',
            'refund_amount.min' => 'İade tutarı 0 veya daha büyük olmalıdır.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'reason' => 'iptal nedeni',
            'description' => 'açıklama',
            'notify_customer' => 'müşteri bildirimi',
            'refund_amount' => 'iade tutarı'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'notify_customer' => $this->boolean('notify_customer', true)
        ]);
    }
}

/**
 * ConfirmPaymentHttpRequest
 * 3D Secure ödeme onayı için HTTP request validation
 */
class ConfirmPaymentHttpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'gateway_response' => [
                'required',
                'array'
            ],
            'gateway_response.status' => [
                'required',
                'string'
            ],
            'gateway_response.transaction_id' => [
                'nullable',
                'string'
            ],
            'gateway_response.conversation_id' => [
                'nullable',
                'string'
            ],
            'gateway_response.payment_id' => [
                'nullable',
                'string'
            ],
            'gateway_response.md_status' => [
                'nullable',
                'string'
            ],
            'gateway_response.md_error_message' => [
                'nullable',
                'string'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'gateway_response.required' => 'Gateway yanıt verisi gereklidir.',
            'gateway_response.array' => 'Gateway yanıt verisi array formatında olmalıdır.',
            'gateway_response.status.required' => 'Gateway durum bilgisi gereklidir.',
            'gateway_response.status.string' => 'Gateway durum bilgisi string formatında olmalıdır.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'gateway_response' => 'gateway yanıt verisi',
            'gateway_response.status' => 'gateway durumu',
            'gateway_response.transaction_id' => 'işlem ID\'si',
            'gateway_response.conversation_id' => 'konuşma ID\'si',
            'gateway_response.payment_id' => 'ödeme ID\'si'
        ];
    }
}
