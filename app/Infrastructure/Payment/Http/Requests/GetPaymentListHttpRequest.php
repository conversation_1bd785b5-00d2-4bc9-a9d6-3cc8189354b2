<?php

namespace App\Infrastructure\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * GetPaymentListHttpRequest
 * Ödeme listesi için HTTP request validation
 */
class GetPaymentListHttpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'nullable',
                'integer',
                'min:1',
                'exists:users,id'
            ],
            'order_id' => [
                'nullable',
                'integer',
                'min:1',
                'exists:orders,id'
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in([
                    'pending',
                    'processing',
                    'completed',
                    'failed',
                    'cancelled',
                    'refunded',
                    'pending_3d',
                    'requires_action'
                ])
            ],
            'gateway' => [
                'nullable',
                'string',
                Rule::in(['iyzico', 'stripe', 'paypal', 'bank_transfer'])
            ],
            'payment_method' => [
                'nullable',
                'string',
                Rule::in([
                    'credit_card',
                    '3d_secure',
                    'installment',
                    'paypal',
                    'bank_transfer',
                    'apple_pay',
                    'google_pay'
                ])
            ],
            'currency' => [
                'nullable',
                'string',
                'size:3',
                Rule::in(['TRY', 'USD', 'EUR', 'GBP'])
            ],
            'min_amount' => [
                'nullable',
                'numeric',
                'min:0'
            ],
            'max_amount' => [
                'nullable',
                'numeric',
                'min:0',
                'gte:min_amount'
            ],
            'start_date' => [
                'nullable',
                'date',
                'before_or_equal:end_date'
            ],
            'end_date' => [
                'nullable',
                'date',
                'after_or_equal:start_date',
                'before_or_equal:today'
            ],
            'transaction_id' => [
                'nullable',
                'string',
                'max:100'
            ],
            'gateway_transaction_id' => [
                'nullable',
                'string',
                'max:100'
            ],
            'include_refunds' => [
                'nullable',
                'boolean'
            ],
            'include_metadata' => [
                'nullable',
                'boolean'
            ],
            'sort_by' => [
                'nullable',
                'string',
                Rule::in(['created_at', 'updated_at', 'amount', 'status'])
            ],
            'sort_direction' => [
                'nullable',
                'string',
                Rule::in(['asc', 'desc'])
            ],
            'limit' => [
                'nullable',
                'integer',
                'min:1',
                'max:100'
            ],
            'offset' => [
                'nullable',
                'integer',
                'min:0'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.exists' => 'Geçersiz kullanıcı ID\'si.',
            'order_id.exists' => 'Geçersiz sipariş ID\'si.',
            'status.in' => 'Geçersiz ödeme durumu.',
            'gateway.in' => 'Geçersiz ödeme gateway\'i.',
            'payment_method.in' => 'Geçersiz ödeme yöntemi.',
            'currency.in' => 'Geçersiz para birimi.',
            'currency.size' => 'Para birimi 3 karakter olmalıdır.',
            'min_amount.min' => 'Minimum tutar 0 veya daha büyük olmalıdır.',
            'max_amount.min' => 'Maksimum tutar 0 veya daha büyük olmalıdır.',
            'max_amount.gte' => 'Maksimum tutar minimum tutardan büyük veya eşit olmalıdır.',
            'start_date.date' => 'Geçersiz başlangıç tarihi formatı.',
            'start_date.before_or_equal' => 'Başlangıç tarihi bitiş tarihinden önce veya eşit olmalıdır.',
            'end_date.date' => 'Geçersiz bitiş tarihi formatı.',
            'end_date.after_or_equal' => 'Bitiş tarihi başlangıç tarihinden sonra veya eşit olmalıdır.',
            'end_date.before_or_equal' => 'Bitiş tarihi bugünden önce veya eşit olmalıdır.',
            'sort_by.in' => 'Geçersiz sıralama alanı.',
            'sort_direction.in' => 'Geçersiz sıralama yönü.',
            'limit.min' => 'Limit en az 1 olmalıdır.',
            'limit.max' => 'Limit en fazla 100 olmalıdır.',
            'offset.min' => 'Offset 0 veya daha büyük olmalıdır.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'kullanıcı ID\'si',
            'order_id' => 'sipariş ID\'si',
            'status' => 'durum',
            'gateway' => 'gateway',
            'payment_method' => 'ödeme yöntemi',
            'currency' => 'para birimi',
            'min_amount' => 'minimum tutar',
            'max_amount' => 'maksimum tutar',
            'start_date' => 'başlangıç tarihi',
            'end_date' => 'bitiş tarihi',
            'transaction_id' => 'işlem ID\'si',
            'gateway_transaction_id' => 'gateway işlem ID\'si',
            'sort_by' => 'sıralama alanı',
            'sort_direction' => 'sıralama yönü',
            'limit' => 'limit',
            'offset' => 'offset'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Default değerleri set et
        $this->merge([
            'limit' => $this->input('limit', 20),
            'offset' => $this->input('offset', 0),
            'sort_by' => $this->input('sort_by', 'created_at'),
            'sort_direction' => $this->input('sort_direction', 'desc'),
            'include_refunds' => $this->boolean('include_refunds', false),
            'include_metadata' => $this->boolean('include_metadata', false)
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Tarih aralığı kontrolü
            $startDate = $this->input('start_date');
            $endDate = $this->input('end_date');
            
            if ($startDate && $endDate) {
                $start = \Carbon\Carbon::parse($startDate);
                $end = \Carbon\Carbon::parse($endDate);
                
                // Maksimum 1 yıllık aralık
                if ($start->diffInDays($end) > 365) {
                    $validator->errors()->add(
                        'end_date',
                        'Tarih aralığı maksimum 1 yıl olabilir.'
                    );
                }
            }

            // Kullanıcı yetki kontrolü (sadece kendi ödemelerini görebilir)
            $requestedUserId = $this->input('user_id');
            $currentUserId = auth()->id();
            
            if ($requestedUserId && $currentUserId && $requestedUserId != $currentUserId) {
                // Admin değilse sadece kendi ödemelerini görebilir
                if (!auth()->user()->hasRole('admin')) {
                    $validator->errors()->add(
                        'user_id',
                        'Sadece kendi ödemelerinizi görüntüleyebilirsiniz.'
                    );
                }
            }
        });
    }
}
