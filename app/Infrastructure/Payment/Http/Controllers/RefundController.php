<?php

namespace App\Infrastructure\Payment\Http\Controllers;

use App\Application\Payment\UseCases\ProcessRefundUseCase;
use App\Application\Payment\UseCases\GetRefundUseCase;
use App\Application\Payment\UseCases\GetRefundListUseCase;
use App\Application\Payment\DTOs\ProcessRefundRequest;
use App\Application\Payment\DTOs\GetRefundListRequest;
use App\Infrastructure\Payment\Http\Requests\ProcessRefundHttpRequest;
use App\Infrastructure\Payment\Http\Requests\GetRefundListHttpRequest;
use App\Infrastructure\Payment\Http\Resources\RefundResource;
use App\Infrastructure\Payment\Http\Resources\RefundListResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;

/**
 * RefundController
 * Refund API endpoints
 */
class RefundController extends Controller
{
    public function __construct(
        private ProcessRefundUseCase $processRefundUseCase,
        private GetRefundUseCase $getRefundUseCase,
        private GetRefundListUseCase $getRefundListUseCase
    ) {}

    /**
     * İade işlemi başlat
     * 
     * @group Refund Management
     * @urlParam payment_id integer required Payment ID Example: 1
     * @bodyParam amount number required İade tutarı Example: 50.00
     * @bodyParam reason string required İade nedeni Example: customer_request
     * @bodyParam description string İade açıklaması
     */
    public function store(int $paymentId, ProcessRefundHttpRequest $request): JsonResponse
    {
        try {
            $dto = ProcessRefundRequest::fromArray(
                array_merge($request->validated(), ['payment_id' => $paymentId])
            );
            
            $response = $this->processRefundUseCase->execute($dto);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode(),
                    'errors' => $response->getErrors()
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'Refund processed successfully',
                'data' => [
                    'refund' => new RefundResource($response->getRefund()),
                    'payment' => $response->getPayment() ? new \App\Infrastructure\Payment\Http\Resources\PaymentResource($response->getPayment()) : null
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Refund processing failed', [
                'payment_id' => $paymentId,
                'request_data' => $request->validated(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed',
                'error_code' => 'REFUND_PROCESSING_FAILED'
            ], 500);
        }
    }

    /**
     * İade detayını getir
     * 
     * @group Refund Management
     * @urlParam id integer required Refund ID Example: 1
     */
    public function show(int $id): JsonResponse
    {
        try {
            $response = $this->getRefundUseCase->execute($id);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Refund retrieved successfully',
                'data' => new RefundResource($response->getRefund())
            ]);

        } catch (\Exception $e) {
            Log::error('Refund retrieval failed', [
                'refund_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Refund retrieval failed',
                'error_code' => 'REFUND_RETRIEVAL_FAILED'
            ], 500);
        }
    }

    /**
     * İade listesini getir
     * 
     * @group Refund Management
     * @queryParam payment_id integer Payment ID Example: 1
     * @queryParam user_id integer Kullanıcı ID Example: 1
     * @queryParam status string İade durumu Example: completed
     * @queryParam refund_type string İade tipi Example: full
     * @queryParam start_date string Başlangıç tarihi Example: 2024-01-01
     * @queryParam end_date string Bitiş tarihi Example: 2024-01-31
     * @queryParam limit integer Sayfa başına kayıt sayısı Example: 20
     * @queryParam offset integer Offset Example: 0
     */
    public function index(GetRefundListHttpRequest $request): JsonResponse
    {
        try {
            $dto = GetRefundListRequest::fromArray($request->validated());
            $response = $this->getRefundListUseCase->execute($dto);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'Refund list retrieved successfully',
                'data' => [
                    'refunds' => RefundListResource::collection($response->getRefunds()),
                    'pagination' => [
                        'total' => $response->getTotal(),
                        'limit' => $dto->getLimit(),
                        'offset' => $dto->getOffset(),
                        'has_more' => $response->hasMore()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Refund list retrieval failed', [
                'request_data' => $request->validated(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Refund list retrieval failed',
                'error_code' => 'REFUND_LIST_RETRIEVAL_FAILED'
            ], 500);
        }
    }

    /**
     * Payment'ın iade geçmişini getir
     * 
     * @group Refund Management
     * @urlParam payment_id integer required Payment ID Example: 1
     */
    public function paymentRefunds(int $paymentId): JsonResponse
    {
        try {
            $dto = GetRefundListRequest::fromArray([
                'payment_id' => $paymentId,
                'limit' => 50,
                'offset' => 0
            ]);
            
            $response = $this->getRefundListUseCase->execute($dto);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 422);
            }

            // İade istatistikleri hesapla
            $refunds = $response->getRefunds();
            $totalRefunded = 0;
            $refundCount = count($refunds);
            
            foreach ($refunds as $refund) {
                $totalRefunded += $refund->getRefundAmount();
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment refunds retrieved successfully',
                'data' => [
                    'refunds' => RefundListResource::collection($refunds),
                    'statistics' => [
                        'total_refunded' => $totalRefunded,
                        'refund_count' => $refundCount,
                        'has_refunds' => $refundCount > 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment refunds retrieval failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment refunds retrieval failed',
                'error_code' => 'PAYMENT_REFUNDS_RETRIEVAL_FAILED'
            ], 500);
        }
    }
}
