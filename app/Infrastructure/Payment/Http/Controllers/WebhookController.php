<?php

namespace App\Infrastructure\Payment\Http\Controllers;

use App\Infrastructure\Payment\Services\WebhookService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;

/**
 * WebhookController
 * Payment webhook endpoints
 */
class WebhookController extends Controller
{
    public function __construct(
        private WebhookService $webhookService
    ) {}

    /**
     * İyzico webhook handler
     * 
     * @group Webhooks
     */
    public function iyzico(Request $request): JsonResponse
    {
        try {
            Log::info('İyzico webhook received', [
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip()
            ]);

            $result = $this->webhookService->handleWebhook('iyzico', $request);

            return response()->json($result, $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            Log::error('İyzico webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed'
            ], 500);
        }
    }

    /**
     * Stripe webhook handler
     * 
     * @group Webhooks
     */
    public function stripe(Request $request): JsonResponse
    {
        try {
            Log::info('Stripe webhook received', [
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip()
            ]);

            $result = $this->webhookService->handleWebhook('stripe', $request);

            return response()->json($result, $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            Log::error('Stripe webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed'
            ], 500);
        }
    }

    /**
     * PayPal webhook handler
     * 
     * @group Webhooks
     */
    public function paypal(Request $request): JsonResponse
    {
        try {
            Log::info('PayPal webhook received', [
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip()
            ]);

            $result = $this->webhookService->handleWebhook('paypal', $request);

            return response()->json($result, $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            Log::error('PayPal webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed'
            ], 500);
        }
    }

    /**
     * Generic webhook handler
     * 
     * @group Webhooks
     * @urlParam gateway string required Gateway name Example: iyzico
     */
    public function handle(string $gateway, Request $request): JsonResponse
    {
        try {
            Log::info('Generic webhook received', [
                'gateway' => $gateway,
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip()
            ]);

            $result = $this->webhookService->handleWebhook($gateway, $request);

            return response()->json($result, $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            Log::error('Generic webhook processing failed', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed'
            ], 500);
        }
    }

    /**
     * Webhook test endpoint
     * 
     * @group Webhooks
     * @urlParam gateway string required Gateway name Example: iyzico
     */
    public function test(string $gateway, Request $request): JsonResponse
    {
        try {
            // Test webhook'u için özel işlem
            Log::info('Test webhook received', [
                'gateway' => $gateway,
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip()
            ]);

            // Test modunda signature doğrulama yapma
            $originalConfig = config('payment.webhooks.verify_signature');
            config(['payment.webhooks.verify_signature' => false]);

            $result = $this->webhookService->handleWebhook($gateway, $request);

            // Orijinal config'i geri yükle
            config(['payment.webhooks.verify_signature' => $originalConfig]);

            return response()->json(array_merge($result, [
                'test_mode' => true,
                'note' => 'This is a test webhook - signature verification was disabled'
            ]), $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            Log::error('Test webhook processing failed', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test webhook processing failed',
                'test_mode' => true
            ], 500);
        }
    }

    /**
     * Webhook status endpoint
     * 
     * @group Webhooks
     */
    public function status(): JsonResponse
    {
        try {
            $config = config('payment.webhooks');
            
            return response()->json([
                'success' => true,
                'message' => 'Webhook service status',
                'data' => [
                    'enabled' => $config['enabled'] ?? false,
                    'verify_signature' => $config['verify_signature'] ?? true,
                    'timeout' => $config['timeout'] ?? 30,
                    'retry_attempts' => $config['retry_attempts'] ?? 3,
                    'retry_delay' => $config['retry_delay'] ?? 60,
                    'allowed_ips' => $config['allowed_ips'] ?? '',
                    'supported_gateways' => [
                        'iyzico' => [
                            'enabled' => config('payment.gateways.iyzico.webhook_enabled', false),
                            'url' => config('payment.gateways.iyzico.webhook_url', '/webhooks/iyzico')
                        ],
                        'stripe' => [
                            'enabled' => config('payment.gateways.stripe.webhook_enabled', false),
                            'url' => config('payment.gateways.stripe.webhook_url', '/webhooks/stripe')
                        ],
                        'paypal' => [
                            'enabled' => config('payment.gateways.paypal.webhook_enabled', false),
                            'url' => config('payment.gateways.paypal.webhook_url', '/webhooks/paypal')
                        ]
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Webhook status check failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook status check failed'
            ], 500);
        }
    }
}
