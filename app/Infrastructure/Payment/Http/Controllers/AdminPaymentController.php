<?php

namespace App\Infrastructure\Payment\Http\Controllers;

use App\Application\Payment\UseCases\GetPaymentListUseCase;
use App\Application\Payment\UseCases\GetPaymentUseCase;
use App\Application\Payment\UseCases\ApprovePaymentUseCase;
use App\Application\Payment\UseCases\RejectPaymentUseCase;
use App\Application\Payment\DTOs\GetPaymentListRequest;
use App\Application\Payment\DTOs\ApprovePaymentRequest;
use App\Application\Payment\DTOs\RejectPaymentRequest;
use App\Infrastructure\Payment\Http\Resources\PaymentResource;
use App\Infrastructure\Payment\Http\Resources\PaymentListResource;
use App\Infrastructure\Payment\Services\FraudDetectionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * AdminPaymentController
 * Admin payment management endpoints
 */
class AdminPaymentController extends Controller
{
    public function __construct(
        private GetPaymentListUseCase $getPaymentListUseCase,
        private GetPaymentUseCase $getPaymentUseCase,
        private ApprovePaymentUseCase $approvePaymentUseCase,
        private RejectPaymentUseCase $rejectPaymentUseCase,
        private FraudDetectionService $fraudDetectionService
    ) {}

    /**
     * Admin ödeme listesi
     * 
     * @group Admin Payment Management
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $dto = GetPaymentListRequest::fromArray($request->all());
            $response = $this->getPaymentListUseCase->execute($dto);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'Admin payment list retrieved successfully',
                'data' => [
                    'payments' => PaymentListResource::collection($response->getPayments()),
                    'pagination' => [
                        'total' => $response->getTotal(),
                        'limit' => $dto->getLimit(),
                        'offset' => $dto->getOffset(),
                        'has_more' => $response->hasMore()
                    ],
                    'filters' => $this->getAvailableFilters()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Admin payment list retrieval failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Admin payment list retrieval failed'
            ], 500);
        }
    }

    /**
     * Admin ödeme detayı
     * 
     * @group Admin Payment Management
     */
    public function show(int $id): JsonResponse
    {
        try {
            $response = $this->getPaymentUseCase->execute($id);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 404);
            }

            $payment = $response->getPayment();

            return response()->json([
                'success' => true,
                'message' => 'Admin payment detail retrieved successfully',
                'data' => [
                    'payment' => new PaymentResource($payment),
                    'admin_actions' => $this->getAdminActions($payment),
                    'fraud_score' => $this->fraudDetectionService->calculateRiskScore($payment),
                    'related_payments' => $this->getRelatedPayments($payment),
                    'audit_log' => $this->getAuditLog($payment)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Admin payment detail retrieval failed', [
                'payment_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Admin payment detail retrieval failed'
            ], 500);
        }
    }

    /**
     * Ödemeyi onayla
     * 
     * @group Admin Payment Management
     */
    public function approve(int $id, Request $request): JsonResponse
    {
        try {
            $dto = ApprovePaymentRequest::fromArray([
                'payment_id' => $id,
                'admin_notes' => $request->input('admin_notes'),
                'approved_by' => auth()->id()
            ]);

            $response = $this->approvePaymentUseCase->execute($dto);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment approved successfully',
                'data' => new PaymentResource($response->getPayment())
            ]);

        } catch (\Exception $e) {
            Log::error('Payment approval failed', [
                'payment_id' => $id,
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment approval failed'
            ], 500);
        }
    }

    /**
     * Ödemeyi reddet
     * 
     * @group Admin Payment Management
     */
    public function reject(int $id, Request $request): JsonResponse
    {
        try {
            $dto = RejectPaymentRequest::fromArray([
                'payment_id' => $id,
                'rejection_reason' => $request->input('rejection_reason'),
                'admin_notes' => $request->input('admin_notes'),
                'rejected_by' => auth()->id()
            ]);

            $response = $this->rejectPaymentUseCase->execute($dto);

            if (!$response->isSuccess()) {
                return response()->json([
                    'success' => false,
                    'message' => $response->getMessage(),
                    'error_code' => $response->getErrorCode()
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment rejected successfully',
                'data' => new PaymentResource($response->getPayment())
            ]);

        } catch (\Exception $e) {
            Log::error('Payment rejection failed', [
                'payment_id' => $id,
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment rejection failed'
            ], 500);
        }
    }

    /**
     * Dashboard istatistikleri
     * 
     * @group Admin Payment Management
     */
    public function dashboard(): JsonResponse
    {
        try {
            $stats = $this->getDashboardStats();

            return response()->json([
                'success' => true,
                'message' => 'Dashboard statistics retrieved successfully',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Dashboard statistics retrieval failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Dashboard statistics retrieval failed'
            ], 500);
        }
    }

    /**
     * Gelir istatistikleri
     * 
     * @group Admin Payment Management
     */
    public function revenueStats(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', 'last_30_days');
            $stats = $this->getRevenueStats($period);

            return response()->json([
                'success' => true,
                'message' => 'Revenue statistics retrieved successfully',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Revenue statistics retrieval failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Revenue statistics retrieval failed'
            ], 500);
        }
    }

    /**
     * Gateway istatistikleri
     * 
     * @group Admin Payment Management
     */
    public function gatewayStats(): JsonResponse
    {
        try {
            $stats = $this->getGatewayStats();

            return response()->json([
                'success' => true,
                'message' => 'Gateway statistics retrieved successfully',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Gateway statistics retrieval failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gateway statistics retrieval failed'
            ], 500);
        }
    }

    /**
     * Mevcut filtreleri getir
     */
    private function getAvailableFilters(): array
    {
        return [
            'statuses' => [
                'pending' => 'Beklemede',
                'processing' => 'İşleniyor',
                'completed' => 'Tamamlandı',
                'failed' => 'Başarısız',
                'cancelled' => 'İptal Edildi',
                'refunded' => 'İade Edildi'
            ],
            'gateways' => [
                'iyzico' => 'İyzico',
                'stripe' => 'Stripe',
                'paypal' => 'PayPal',
                'bank_transfer' => 'Banka Transferi'
            ],
            'payment_methods' => [
                'credit_card' => 'Kredi Kartı',
                '3d_secure' => '3D Secure',
                'installment' => 'Taksitli Ödeme',
                'paypal' => 'PayPal',
                'bank_transfer' => 'Banka Transferi'
            ]
        ];
    }

    /**
     * Admin aksiyonlarını getir
     */
    private function getAdminActions($payment): array
    {
        $actions = [];

        if ($payment->canBeCancelled()) {
            $actions[] = [
                'action' => 'cancel',
                'label' => 'İptal Et',
                'type' => 'danger',
                'requires_confirmation' => true
            ];
        }

        if ($payment->canBeRefunded()) {
            $actions[] = [
                'action' => 'refund',
                'label' => 'İade Et',
                'type' => 'warning',
                'requires_confirmation' => true
            ];
        }

        if ($payment->isPending() && $payment->getGateway()->getProvider() === 'bank_transfer') {
            $actions[] = [
                'action' => 'approve',
                'label' => 'Onayla',
                'type' => 'success',
                'requires_confirmation' => false
            ];
            
            $actions[] = [
                'action' => 'reject',
                'label' => 'Reddet',
                'type' => 'danger',
                'requires_confirmation' => true
            ];
        }

        return $actions;
    }

    /**
     * İlgili ödemeleri getir
     */
    private function getRelatedPayments($payment): array
    {
        try {
            $relatedPayments = DB::table('payments')
                ->where('user_id', $payment->getUserId())
                ->where('id', '!=', $payment->getId())
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            return $relatedPayments->map(function ($p) {
                return [
                    'id' => $p->id,
                    'transaction_id' => $p->transaction_id,
                    'amount' => $p->amount,
                    'currency' => $p->currency,
                    'status' => $p->status,
                    'created_at' => $p->created_at
                ];
            })->toArray();

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Audit log getir
     */
    private function getAuditLog($payment): array
    {
        // Audit log implementasyonu
        return [
            [
                'action' => 'created',
                'timestamp' => $payment->getCreatedAt()->toISOString(),
                'user' => 'System',
                'details' => 'Payment created'
            ]
        ];
    }

    /**
     * Dashboard istatistiklerini getir
     */
    private function getDashboardStats(): array
    {
        $cacheKey = 'admin_dashboard_stats';
        
        return Cache::remember($cacheKey, 300, function () {
            $today = now()->startOfDay();
            $yesterday = now()->subDay()->startOfDay();
            $thisMonth = now()->startOfMonth();
            $lastMonth = now()->subMonth()->startOfMonth();

            return [
                'today' => [
                    'total_payments' => DB::table('payments')->whereDate('created_at', $today)->count(),
                    'total_amount' => DB::table('payments')->whereDate('created_at', $today)->where('status', 'completed')->sum('amount') ?? 0,
                    'success_rate' => $this->calculateSuccessRate($today, now()->endOfDay())
                ],
                'yesterday' => [
                    'total_payments' => DB::table('payments')->whereDate('created_at', $yesterday)->count(),
                    'total_amount' => DB::table('payments')->whereDate('created_at', $yesterday)->where('status', 'completed')->sum('amount') ?? 0,
                    'success_rate' => $this->calculateSuccessRate($yesterday, $yesterday->copy()->endOfDay())
                ],
                'this_month' => [
                    'total_payments' => DB::table('payments')->where('created_at', '>=', $thisMonth)->count(),
                    'total_amount' => DB::table('payments')->where('created_at', '>=', $thisMonth)->where('status', 'completed')->sum('amount') ?? 0,
                    'success_rate' => $this->calculateSuccessRate($thisMonth, now())
                ],
                'pending_approvals' => DB::table('payments')->where('status', 'pending')->where('gateway_provider', 'bank_transfer')->count()
            ];
        });
    }

    /**
     * Gelir istatistiklerini getir
     */
    private function getRevenueStats(string $period): array
    {
        $cacheKey = "revenue_stats_{$period}";
        
        return Cache::remember($cacheKey, 600, function () use ($period) {
            [$startDate, $endDate] = $this->getPeriodDates($period);

            $dailyRevenue = DB::table('payments')
                ->selectRaw('DATE(created_at) as date, SUM(amount) as revenue, COUNT(*) as count')
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return [
                'period' => $period,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'total_revenue' => $dailyRevenue->sum('revenue'),
                'total_transactions' => $dailyRevenue->sum('count'),
                'daily_data' => $dailyRevenue->toArray()
            ];
        });
    }

    /**
     * Gateway istatistiklerini getir
     */
    private function getGatewayStats(): array
    {
        $cacheKey = 'gateway_stats';
        
        return Cache::remember($cacheKey, 300, function () {
            $last30Days = now()->subDays(30);

            return DB::table('payments')
                ->selectRaw('
                    gateway_provider,
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as successful_payments,
                    SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_revenue,
                    AVG(CASE WHEN status = "completed" THEN amount ELSE NULL END) as avg_amount
                ')
                ->where('created_at', '>=', $last30Days)
                ->groupBy('gateway_provider')
                ->get()
                ->map(function ($stat) {
                    $successRate = $stat->total_payments > 0 
                        ? ($stat->successful_payments / $stat->total_payments) * 100 
                        : 0;

                    return [
                        'gateway' => $stat->gateway_provider,
                        'total_payments' => $stat->total_payments,
                        'successful_payments' => $stat->successful_payments,
                        'success_rate' => round($successRate, 2),
                        'total_revenue' => $stat->total_revenue ?? 0,
                        'avg_amount' => $stat->avg_amount ?? 0
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Başarı oranını hesapla
     */
    private function calculateSuccessRate($startDate, $endDate): float
    {
        $total = DB::table('payments')->whereBetween('created_at', [$startDate, $endDate])->count();
        $successful = DB::table('payments')->whereBetween('created_at', [$startDate, $endDate])->where('status', 'completed')->count();
        
        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * Periyot tarihlerini getir
     */
    private function getPeriodDates(string $period): array
    {
        return match ($period) {
            'last_7_days' => [now()->subDays(7), now()],
            'last_30_days' => [now()->subDays(30), now()],
            'last_90_days' => [now()->subDays(90), now()],
            'this_month' => [now()->startOfMonth(), now()],
            'last_month' => [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()],
            default => [now()->subDays(30), now()]
        };
    }
}
