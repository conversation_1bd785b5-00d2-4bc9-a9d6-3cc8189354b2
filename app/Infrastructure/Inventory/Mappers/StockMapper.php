<?php

namespace App\Infrastructure\Inventory\Mappers;

use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use App\Domain\Inventory\ValueObjects\StockLocation;
use App\Infrastructure\Inventory\Models\EloquentStock;
use App\Infrastructure\Inventory\Models\EloquentStockLocation;
use Carbon\Carbon;
use ReflectionClass;

/**
 * StockMapper
 * Domain Stock entity ile Eloquent model arasında mapping
 */
class StockMapper
{
    /**
     * Eloquent model'i domain entity'ye çevir
     */
    public function toDomain(EloquentStock $eloquentStock): Stock
    {
        // StockLocation value object oluştur
        $location = null;
        if ($eloquentStock->location) {
            $location = new StockLocation(
                $eloquentStock->location->code,
                $eloquentStock->location->name,
                $eloquentStock->location->type,
                $eloquentStock->location->address ?? [],
                $eloquentStock->location->settings ?? []
            );
        }

        // Stock entity oluştur
        $stock = Stock::create(
            $eloquentStock->product_id,
            $eloquentStock->product_variant_id,
            new StockLevel($eloquentStock->available_quantity),
            new StockLevel($eloquentStock->low_stock_threshold),
            new StockLevel($eloquentStock->reorder_level),
            $location,
            $eloquentStock->track_inventory,
            $eloquentStock->allow_backorder
        );

        // Private property'leri reflection ile set et
        $this->setPrivateProperty($stock, 'id', $eloquentStock->id);
        $this->setPrivateProperty($stock, 'reservedQuantity', new StockLevel($eloquentStock->reserved_quantity));
        $this->setPrivateProperty($stock, 'totalQuantity', new StockLevel($eloquentStock->total_quantity));
        $this->setPrivateProperty($stock, 'reservations', $eloquentStock->reservations ?? []);
        $this->setPrivateProperty($stock, 'isActive', $eloquentStock->is_active);
        $this->setPrivateProperty($stock, 'lastUpdated', $eloquentStock->last_movement_at ?? $eloquentStock->updated_at);
        $this->setPrivateProperty($stock, 'createdAt', $eloquentStock->created_at);
        $this->setPrivateProperty($stock, 'updatedAt', $eloquentStock->updated_at);

        // Max stock level varsa set et
        if ($eloquentStock->max_stock_level !== null) {
            $this->setPrivateProperty($stock, 'maxStockLevel', new StockLevel($eloquentStock->max_stock_level));
        }

        return $stock;
    }

    /**
     * Domain entity'yi Eloquent model'e çevir
     */
    public function toEloquent(Stock $stock): EloquentStock
    {
        $eloquentStock = new EloquentStock();

        // ID varsa mevcut kaydı bul
        if ($stock->getId()) {
            $eloquentStock = EloquentStock::find($stock->getId()) ?? new EloquentStock();
        }

        // Basic properties
        $eloquentStock->product_id = $stock->getProductId();
        $eloquentStock->product_variant_id = $stock->getProductVariantId();
        $eloquentStock->available_quantity = $stock->getAvailableQuantity()->getValue();
        $eloquentStock->reserved_quantity = $stock->getReservedQuantity()->getValue();
        $eloquentStock->total_quantity = $stock->getTotalQuantity()->getValue();
        $eloquentStock->low_stock_threshold = $stock->getLowStockThreshold()->getValue();
        $eloquentStock->reorder_level = $stock->getReorderLevel()->getValue();
        $eloquentStock->track_inventory = $stock->isTrackingInventory();
        $eloquentStock->allow_backorder = $stock->canBackorder();
        $eloquentStock->is_active = $stock->isActive();

        // Max stock level
        $maxStockLevel = $this->getPrivateProperty($stock, 'maxStockLevel');
        if ($maxStockLevel instanceof StockLevel) {
            $eloquentStock->max_stock_level = $maxStockLevel->getValue();
        }

        // Location
        $location = $stock->getLocation();
        if ($location) {
            $eloquentLocation = EloquentStockLocation::where('code', $location->getCode())->first();
            if ($eloquentLocation) {
                $eloquentStock->location_id = $eloquentLocation->id;
            }
        }

        // Reservations
        $reservations = $this->getPrivateProperty($stock, 'reservations');
        if (is_array($reservations)) {
            $eloquentStock->reservations = $reservations;
        }

        // Timestamps
        $lastUpdated = $this->getPrivateProperty($stock, 'lastUpdated');
        if ($lastUpdated instanceof Carbon) {
            $eloquentStock->last_movement_at = $lastUpdated;
        }

        return $eloquentStock;
    }

    /**
     * Domain entity array'ini Eloquent collection'a çevir
     */
    public function toEloquentCollection(array $stocks): array
    {
        return array_map(fn(Stock $stock) => $this->toEloquent($stock), $stocks);
    }

    /**
     * Eloquent collection'ı domain entity array'ine çevir
     */
    public function toDomainCollection($eloquentStocks): array
    {
        if (is_array($eloquentStocks)) {
            return array_map(fn(EloquentStock $stock) => $this->toDomain($stock), $eloquentStocks);
        }

        return $eloquentStocks->map(fn(EloquentStock $stock) => $this->toDomain($stock))->toArray();
    }

    /**
     * Stock entity'den array'e çevir (API response için)
     */
    public function toArray(Stock $stock): array
    {
        return [
            'id' => $stock->getId(),
            'product_id' => $stock->getProductId(),
            'product_variant_id' => $stock->getProductVariantId(),
            'location' => $stock->getLocation()?->toArray(),
            'available_quantity' => $stock->getAvailableQuantity()->getValue(),
            'reserved_quantity' => $stock->getReservedQuantity()->getValue(),
            'total_quantity' => $stock->getTotalQuantity()->getValue(),
            'low_stock_threshold' => $stock->getLowStockThreshold()->getValue(),
            'reorder_level' => $stock->getReorderLevel()->getValue(),
            'track_inventory' => $stock->isTrackingInventory(),
            'allow_backorder' => $stock->canBackorder(),
            'is_active' => $stock->isActive(),
            'is_in_stock' => $stock->isInStock(),
            'is_low_stock' => $stock->isLowStock(),
            'needs_reorder' => $stock->needsReorder(),
            'reservations' => $this->getPrivateProperty($stock, 'reservations') ?? [],
            'last_updated' => $this->getPrivateProperty($stock, 'lastUpdated')?->toISOString(),
            'created_at' => $this->getPrivateProperty($stock, 'createdAt')?->toISOString(),
            'updated_at' => $this->getPrivateProperty($stock, 'updatedAt')?->toISOString()
        ];
    }

    /**
     * Array'den Stock entity oluştur
     */
    public function fromArray(array $data): Stock
    {
        // Location oluştur
        $location = null;
        if (!empty($data['location'])) {
            $locationData = $data['location'];
            $location = new StockLocation(
                $locationData['code'],
                $locationData['name'],
                $locationData['type'] ?? 'warehouse',
                $locationData['address'] ?? [],
                $locationData['settings'] ?? []
            );
        }

        // Stock entity oluştur
        $stock = Stock::create(
            $data['product_id'],
            $data['product_variant_id'] ?? null,
            new StockLevel($data['available_quantity'] ?? 0),
            new StockLevel($data['low_stock_threshold'] ?? 0),
            new StockLevel($data['reorder_level'] ?? 0),
            $location,
            $data['track_inventory'] ?? true,
            $data['allow_backorder'] ?? false
        );

        // ID varsa set et
        if (!empty($data['id'])) {
            $this->setPrivateProperty($stock, 'id', $data['id']);
        }

        // Reserved quantity
        if (isset($data['reserved_quantity'])) {
            $this->setPrivateProperty($stock, 'reservedQuantity', new StockLevel($data['reserved_quantity']));
        }

        // Total quantity
        if (isset($data['total_quantity'])) {
            $this->setPrivateProperty($stock, 'totalQuantity', new StockLevel($data['total_quantity']));
        }

        // Reservations
        if (!empty($data['reservations'])) {
            $this->setPrivateProperty($stock, 'reservations', $data['reservations']);
        }

        // Active status
        if (isset($data['is_active'])) {
            $this->setPrivateProperty($stock, 'isActive', $data['is_active']);
        }

        // Timestamps
        if (!empty($data['last_updated'])) {
            $this->setPrivateProperty($stock, 'lastUpdated', Carbon::parse($data['last_updated']));
        }

        if (!empty($data['created_at'])) {
            $this->setPrivateProperty($stock, 'createdAt', Carbon::parse($data['created_at']));
        }

        if (!empty($data['updated_at'])) {
            $this->setPrivateProperty($stock, 'updatedAt', Carbon::parse($data['updated_at']));
        }

        return $stock;
    }

    /**
     * Private property'yi reflection ile set et
     */
    private function setPrivateProperty(object $object, string $property, $value): void
    {
        try {
            $reflection = new ReflectionClass($object);
            $prop = $reflection->getProperty($property);
            $prop->setAccessible(true);
            $prop->setValue($object, $value);
        } catch (\ReflectionException $e) {
            // Property bulunamazsa sessizce geç
        }
    }

    /**
     * Private property'yi reflection ile al
     */
    private function getPrivateProperty(object $object, string $property)
    {
        try {
            $reflection = new ReflectionClass($object);
            $prop = $reflection->getProperty($property);
            $prop->setAccessible(true);
            return $prop->getValue($object);
        } catch (\ReflectionException $e) {
            return null;
        }
    }
}
