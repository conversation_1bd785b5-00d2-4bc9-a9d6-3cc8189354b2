<?php

namespace App\Infrastructure\Inventory\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Product;
use App\Models\ProductVariant;

/**
 * EloquentStock
 * Stok tablosu için Eloquent model
 */
class EloquentStock extends Model
{
    use SoftDeletes;

    protected $table = 'stocks';

    protected $fillable = [
        'product_id',
        'product_variant_id',
        'location_id',
        'available_quantity',
        'reserved_quantity',
        'total_quantity',
        'committed_quantity',
        'incoming_quantity',
        'low_stock_threshold',
        'reorder_level',
        'max_stock_level',
        'track_inventory',
        'allow_backorder',
        'is_active',
        'reservations',
        'metadata',
        'last_movement_at',
        'last_count_at'
    ];

    protected $casts = [
        'product_id' => 'integer',
        'product_variant_id' => 'integer',
        'location_id' => 'integer',
        'available_quantity' => 'integer',
        'reserved_quantity' => 'integer',
        'total_quantity' => 'integer',
        'committed_quantity' => 'integer',
        'incoming_quantity' => 'integer',
        'low_stock_threshold' => 'integer',
        'reorder_level' => 'integer',
        'max_stock_level' => 'integer',
        'track_inventory' => 'boolean',
        'allow_backorder' => 'boolean',
        'is_active' => 'boolean',
        'reservations' => 'array',
        'metadata' => 'array',
        'last_movement_at' => 'datetime',
        'last_count_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Product relationship
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Product variant relationship
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Stock location relationship
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(EloquentStockLocation::class, 'location_id');
    }

    /**
     * Scope: Active stocks
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Tracked inventory
     */
    public function scopeTracked($query)
    {
        return $query->where('track_inventory', true);
    }

    /**
     * Scope: Low stock
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('available_quantity <= low_stock_threshold');
    }

    /**
     * Scope: Out of stock
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('available_quantity', '<=', 0);
    }

    /**
     * Scope: Needs reorder
     */
    public function scopeNeedsReorder($query)
    {
        return $query->whereRaw('available_quantity <= reorder_level');
    }

    /**
     * Scope: By product
     */
    public function scopeByProduct($query, int $productId, ?int $variantId = null)
    {
        $query->where('product_id', $productId);
        
        if ($variantId) {
            $query->where('product_variant_id', $variantId);
        } else {
            $query->whereNull('product_variant_id');
        }
        
        return $query;
    }

    /**
     * Scope: By location
     */
    public function scopeByLocation($query, int $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Check if stock is in stock
     */
    public function isInStock(): bool
    {
        return $this->available_quantity > 0;
    }

    /**
     * Check if stock is low
     */
    public function isLowStock(): bool
    {
        return $this->track_inventory && $this->available_quantity <= $this->low_stock_threshold;
    }

    /**
     * Check if stock needs reorder
     */
    public function needsReorder(): bool
    {
        return $this->track_inventory && $this->available_quantity <= $this->reorder_level;
    }

    /**
     * Get stock health percentage
     */
    public function getStockHealthPercentage(): float
    {
        if (!$this->track_inventory || $this->low_stock_threshold <= 0) {
            return 100.0;
        }

        return min(100.0, ($this->available_quantity / $this->low_stock_threshold) * 100);
    }

    /**
     * Get active reservations
     */
    public function getActiveReservations(): array
    {
        if (!$this->reservations) {
            return [];
        }

        return array_filter($this->reservations, function ($reservation) {
            return isset($reservation['status']) && $reservation['status'] === 'active';
        });
    }

    /**
     * Get total reserved quantity from reservations
     */
    public function getTotalReservedFromReservations(): int
    {
        $activeReservations = $this->getActiveReservations();
        
        return array_sum(array_column($activeReservations, 'quantity'));
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-update total_quantity when saving
        static::saving(function ($stock) {
            $stock->total_quantity = $stock->available_quantity + $stock->reserved_quantity;
        });
    }
}
