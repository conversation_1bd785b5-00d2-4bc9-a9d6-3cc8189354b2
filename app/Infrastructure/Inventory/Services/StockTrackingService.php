<?php

namespace App\Infrastructure\Inventory\Services;

use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use App\Domain\Inventory\ValueObjects\ReservationId;
use App\Infrastructure\Inventory\Models\EloquentStock;
use App\Infrastructure\Inventory\Models\EloquentStockLocation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * StockTrackingService
 * Stok takip ve hareket yönetimi servisi
 */
class StockTrackingService
{
    /**
     * Stok hareketi kaydet
     */
    public function recordMovement(
        int $stockId,
        string $type,
        int $quantity,
        string $reason,
        ?string $referenceType = null,
        ?int $referenceId = null,
        ?float $unitCost = null,
        ?array $metadata = null,
        ?int $createdBy = null
    ): array {
        try {
            return DB::transaction(function () use (
                $stockId, $type, $quantity, $reason, $referenceType, 
                $referenceId, $unitCost, $metadata, $createdBy
            ) {
                // Mevcut stok durumunu al
                $stock = EloquentStock::find($stockId);
                if (!$stock) {
                    throw new \InvalidArgumentException("Stock not found: {$stockId}");
                }

                $previousQuantity = $stock->available_quantity;
                $newQuantity = $previousQuantity;

                // Hareket tipine göre yeni miktarı hesapla
                switch ($type) {
                    case 'in':
                        $newQuantity = $previousQuantity + abs($quantity);
                        break;
                    case 'out':
                        $newQuantity = $previousQuantity - abs($quantity);
                        if ($newQuantity < 0 && !$stock->allow_backorder) {
                            throw new \DomainException("Insufficient stock for movement");
                        }
                        break;
                    case 'adjustment':
                        $newQuantity = $previousQuantity + $quantity; // quantity can be negative
                        break;
                    case 'transfer':
                        // Transfer için özel logic
                        $newQuantity = $previousQuantity + $quantity;
                        break;
                    case 'reservation':
                        // Rezervasyon için available_quantity azalt, reserved_quantity artır
                        $stock->available_quantity = max(0, $previousQuantity - abs($quantity));
                        $stock->reserved_quantity += abs($quantity);
                        $newQuantity = $stock->available_quantity;
                        break;
                    case 'release':
                        // Rezervasyon serbest bırak
                        $stock->available_quantity = $previousQuantity + abs($quantity);
                        $stock->reserved_quantity = max(0, $stock->reserved_quantity - abs($quantity));
                        $newQuantity = $stock->available_quantity;
                        break;
                    default:
                        throw new \InvalidArgumentException("Invalid movement type: {$type}");
                }

                // Stok miktarını güncelle (reservation ve release hariç)
                if (!in_array($type, ['reservation', 'release'])) {
                    $stock->available_quantity = max(0, $newQuantity);
                }

                // Total quantity'yi güncelle
                $stock->total_quantity = $stock->available_quantity + $stock->reserved_quantity;
                $stock->last_movement_at = Carbon::now();
                $stock->save();

                // Hareket kaydını oluştur
                $movementId = DB::table('stock_movements')->insertGetId([
                    'stock_id' => $stockId,
                    'type' => $type,
                    'reason' => $reason,
                    'reference_type' => $referenceType,
                    'reference_id' => $referenceId,
                    'quantity' => $quantity,
                    'previous_quantity' => $previousQuantity,
                    'new_quantity' => $stock->available_quantity,
                    'unit_cost' => $unitCost,
                    'total_cost' => $unitCost ? abs($quantity) * $unitCost : null,
                    'metadata' => $metadata ? json_encode($metadata) : null,
                    'created_by' => $createdBy,
                    'movement_date' => Carbon::now(),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

                // Cache'i temizle
                $this->clearStockCache($stockId);

                Log::info('Stock movement recorded', [
                    'movement_id' => $movementId,
                    'stock_id' => $stockId,
                    'type' => $type,
                    'quantity' => $quantity,
                    'previous_quantity' => $previousQuantity,
                    'new_quantity' => $stock->available_quantity,
                    'reason' => $reason
                ]);

                return [
                    'movement_id' => $movementId,
                    'stock_id' => $stockId,
                    'type' => $type,
                    'quantity' => $quantity,
                    'previous_quantity' => $previousQuantity,
                    'new_quantity' => $stock->available_quantity,
                    'total_quantity' => $stock->total_quantity,
                    'reserved_quantity' => $stock->reserved_quantity,
                    'movement_date' => Carbon::now()->toISOString()
                ];
            });
        } catch (\Exception $e) {
            Log::error('Failed to record stock movement', [
                'stock_id' => $stockId,
                'type' => $type,
                'quantity' => $quantity,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Stok rezervasyonu oluştur
     */
    public function createReservation(
        int $stockId,
        int $quantity,
        string $referenceType,
        int $referenceId,
        string $reason = 'order',
        ?int $expirationMinutes = null,
        ?array $metadata = null,
        ?int $createdBy = null
    ): array {
        try {
            return DB::transaction(function () use (
                $stockId, $quantity, $referenceType, $referenceId, 
                $reason, $expirationMinutes, $metadata, $createdBy
            ) {
                // Stok kontrolü
                $stock = EloquentStock::find($stockId);
                if (!$stock) {
                    throw new \InvalidArgumentException("Stock not found: {$stockId}");
                }

                if ($stock->available_quantity < $quantity && !$stock->allow_backorder) {
                    throw new \DomainException("Insufficient stock for reservation");
                }

                // Rezervasyon ID oluştur
                $reservationId = 'RES_' . time() . '_' . uniqid();

                // Expiration tarihi hesapla
                $expiresAt = $expirationMinutes 
                    ? Carbon::now()->addMinutes($expirationMinutes)
                    : null;

                // Rezervasyon kaydını oluştur
                $reservationDbId = DB::table('stock_reservations')->insertGetId([
                    'reservation_id' => $reservationId,
                    'stock_id' => $stockId,
                    'reference_type' => $referenceType,
                    'reference_id' => $referenceId,
                    'quantity' => $quantity,
                    'status' => 'active',
                    'reason' => $reason,
                    'metadata' => $metadata ? json_encode($metadata) : null,
                    'reserved_at' => Carbon::now(),
                    'expires_at' => $expiresAt,
                    'created_by' => $createdBy,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);

                // Stok hareketini kaydet
                $this->recordMovement(
                    $stockId,
                    'reservation',
                    $quantity,
                    $reason,
                    $referenceType,
                    $referenceId,
                    null,
                    array_merge($metadata ?? [], ['reservation_id' => $reservationId]),
                    $createdBy
                );

                Log::info('Stock reservation created', [
                    'reservation_db_id' => $reservationDbId,
                    'reservation_id' => $reservationId,
                    'stock_id' => $stockId,
                    'quantity' => $quantity,
                    'reference_type' => $referenceType,
                    'reference_id' => $referenceId
                ]);

                return [
                    'reservation_db_id' => $reservationDbId,
                    'reservation_id' => $reservationId,
                    'stock_id' => $stockId,
                    'quantity' => $quantity,
                    'status' => 'active',
                    'expires_at' => $expiresAt?->toISOString(),
                    'reserved_at' => Carbon::now()->toISOString()
                ];
            });
        } catch (\Exception $e) {
            Log::error('Failed to create stock reservation', [
                'stock_id' => $stockId,
                'quantity' => $quantity,
                'reference_type' => $referenceType,
                'reference_id' => $referenceId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Rezervasyonu serbest bırak
     */
    public function releaseReservation(string $reservationId, string $reason = 'cancelled'): array
    {
        try {
            return DB::transaction(function () use ($reservationId, $reason) {
                // Rezervasyonu bul
                $reservation = DB::table('stock_reservations')
                    ->where('reservation_id', $reservationId)
                    ->where('status', 'active')
                    ->first();

                if (!$reservation) {
                    throw new \InvalidArgumentException("Active reservation not found: {$reservationId}");
                }

                // Rezervasyon durumunu güncelle
                DB::table('stock_reservations')
                    ->where('id', $reservation->id)
                    ->update([
                        'status' => 'released',
                        'released_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);

                // Stok hareketini kaydet
                $this->recordMovement(
                    $reservation->stock_id,
                    'release',
                    $reservation->quantity,
                    $reason,
                    $reservation->reference_type,
                    $reservation->reference_id,
                    null,
                    ['reservation_id' => $reservationId]
                );

                Log::info('Stock reservation released', [
                    'reservation_id' => $reservationId,
                    'stock_id' => $reservation->stock_id,
                    'quantity' => $reservation->quantity,
                    'reason' => $reason
                ]);

                return [
                    'reservation_id' => $reservationId,
                    'stock_id' => $reservation->stock_id,
                    'quantity' => $reservation->quantity,
                    'status' => 'released',
                    'released_at' => Carbon::now()->toISOString()
                ];
            });
        } catch (\Exception $e) {
            Log::error('Failed to release stock reservation', [
                'reservation_id' => $reservationId,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Rezervasyonu tamamla (fulfill)
     */
    public function fulfillReservation(string $reservationId, string $reason = 'fulfilled'): array
    {
        try {
            return DB::transaction(function () use ($reservationId, $reason) {
                // Rezervasyonu bul
                $reservation = DB::table('stock_reservations')
                    ->where('reservation_id', $reservationId)
                    ->where('status', 'active')
                    ->first();

                if (!$reservation) {
                    throw new \InvalidArgumentException("Active reservation not found: {$reservationId}");
                }

                // Rezervasyon durumunu güncelle
                DB::table('stock_reservations')
                    ->where('id', $reservation->id)
                    ->update([
                        'status' => 'fulfilled',
                        'fulfilled_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);

                // Reserved quantity'yi azalt (stok çıkışı olarak kaydet)
                $stock = EloquentStock::find($reservation->stock_id);
                if ($stock) {
                    $stock->reserved_quantity = max(0, $stock->reserved_quantity - $reservation->quantity);
                    $stock->total_quantity = $stock->available_quantity + $stock->reserved_quantity;
                    $stock->save();
                }

                // Stok hareketini kaydet (out movement)
                $this->recordMovement(
                    $reservation->stock_id,
                    'out',
                    $reservation->quantity,
                    $reason,
                    $reservation->reference_type,
                    $reservation->reference_id,
                    null,
                    ['reservation_id' => $reservationId, 'fulfilled' => true]
                );

                Log::info('Stock reservation fulfilled', [
                    'reservation_id' => $reservationId,
                    'stock_id' => $reservation->stock_id,
                    'quantity' => $reservation->quantity,
                    'reason' => $reason
                ]);

                return [
                    'reservation_id' => $reservationId,
                    'stock_id' => $reservation->stock_id,
                    'quantity' => $reservation->quantity,
                    'status' => 'fulfilled',
                    'fulfilled_at' => Carbon::now()->toISOString()
                ];
            });
        } catch (\Exception $e) {
            Log::error('Failed to fulfill stock reservation', [
                'reservation_id' => $reservationId,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Süresi dolmuş rezervasyonları temizle
     */
    public function cleanupExpiredReservations(): array
    {
        try {
            $expiredReservations = DB::table('stock_reservations')
                ->where('status', 'active')
                ->where('expires_at', '<', Carbon::now())
                ->get();

            $cleanedCount = 0;
            $errors = [];

            foreach ($expiredReservations as $reservation) {
                try {
                    $this->releaseReservation($reservation->reservation_id, 'expired');
                    $cleanedCount++;
                } catch (\Exception $e) {
                    $errors[] = [
                        'reservation_id' => $reservation->reservation_id,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('Expired reservations cleanup completed', [
                'total_expired' => count($expiredReservations),
                'cleaned_count' => $cleanedCount,
                'errors_count' => count($errors)
            ]);

            return [
                'total_expired' => count($expiredReservations),
                'cleaned_count' => $cleanedCount,
                'errors_count' => count($errors),
                'errors' => $errors
            ];
        } catch (\Exception $e) {
            Log::error('Failed to cleanup expired reservations', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Stok cache'ini temizle
     */
    private function clearStockCache(int $stockId): void
    {
        $cacheKeys = [
            "stock.{$stockId}",
            "stock.summary.{$stockId}",
            "stock.movements.{$stockId}",
            "stock.reservations.{$stockId}"
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Tag-based cache clearing (if supported)
        if (method_exists(Cache::getStore(), 'tags')) {
            Cache::tags(['stocks', "stock.{$stockId}"])->flush();
        }
    }
}
