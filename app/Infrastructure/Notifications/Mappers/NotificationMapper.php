<?php

namespace App\Infrastructure\Notifications\Mappers;

use App\Domain\Notifications\Entities\Notification;
use App\Domain\Notifications\ValueObjects\NotificationType;
use App\Domain\Notifications\ValueObjects\NotificationChannel;
use App\Domain\Notifications\ValueObjects\NotificationStatus;
use App\Domain\Notifications\ValueObjects\NotificationPriority;
use App\Domain\Notifications\ValueObjects\RecipientInfo;
use App\Infrastructure\Notifications\Models\EloquentNotification;
use Carbon\Carbon;
use ReflectionClass;
use ReflectionProperty;

/**
 * NotificationMapper
 * Domain Notification entity ile Eloquent model arasında mapping
 */
class NotificationMapper
{
    /**
     * Domain entity'yi Eloquent model'e çevir
     */
    public function toEloquent(Notification $notification): EloquentNotification
    {
        $eloquentModel = new EloquentNotification();

        // ID varsa set et
        if ($notification->getId()) {
            $eloquentModel->id = $notification->getId();
            $eloquentModel->exists = true;
        }

        // Temel alanlar
        $eloquentModel->type = $notification->getType()->getValue();
        $eloquentModel->channel = $notification->getChannel()->getValue();
        $eloquentModel->status = $notification->getStatus()->getValue();
        $eloquentModel->priority = $notification->getPriority()->getValue();

        // Alıcı bilgileri
        $recipient = $notification->getRecipient();
        $eloquentModel->recipient_type = $recipient->getType();
        $eloquentModel->recipient_user_id = $recipient->getId();
        $eloquentModel->recipient_email = $recipient->getEmail();
        $eloquentModel->recipient_phone = $recipient->getPhone();
        $eloquentModel->recipient_data = $recipient->getData();

        // İçerik
        $eloquentModel->title = $notification->getTitle();
        $eloquentModel->message = $notification->getMessage();
        $eloquentModel->data = $notification->getData();

        // Template bilgileri
        $eloquentModel->template_id = $notification->getTemplateId();
        $eloquentModel->template_variables = $notification->getTemplateVariables();

        // Zamanlama
        $eloquentModel->scheduled_at = $notification->getScheduledAt();
        $eloquentModel->sent_at = $notification->getSentAt();
        $eloquentModel->read_at = $notification->getReadAt();
        $eloquentModel->expires_at = $notification->getExpiresAt();

        // Yeniden deneme
        $eloquentModel->retry_count = $notification->getRetryCount();
        $eloquentModel->max_retries = $notification->getMaxRetries();
        $eloquentModel->failure_reason = $notification->getFailureReason();

        // Teslimat denemeleri
        $eloquentModel->delivery_attempts = $notification->getDeliveryAttempts();

        // Metadata
        $eloquentModel->metadata = $notification->getMetadata();

        // Timestamps
        $eloquentModel->created_at = $notification->getCreatedAt();
        $eloquentModel->updated_at = $notification->getUpdatedAt();

        return $eloquentModel;
    }

    /**
     * Eloquent model'i domain entity'ye çevir
     */
    public function toDomain(EloquentNotification $eloquentModel): Notification
    {
        // Value object'leri oluştur
        $type = NotificationType::fromString($eloquentModel->type);
        $channel = NotificationChannel::fromString($eloquentModel->channel);
        $status = NotificationStatus::fromString($eloquentModel->status);
        $priority = NotificationPriority::fromString($eloquentModel->priority);

        // RecipientInfo oluştur
        $recipient = RecipientInfo::create(
            $eloquentModel->recipient_type,
            $eloquentModel->recipient_user_id,
            $eloquentModel->recipient_email,
            $eloquentModel->recipient_phone,
            $eloquentModel->recipient_data ?? []
        );

        // Domain entity oluştur
        $notification = Notification::create(
            $type,
            $channel,
            $recipient,
            $eloquentModel->title,
            $eloquentModel->message,
            $eloquentModel->data ?? [],
            $priority,
            $eloquentModel->template_id,
            $eloquentModel->template_variables ?? [],
            $eloquentModel->scheduled_at,
            $eloquentModel->expires_at,
            $eloquentModel->max_retries,
            $eloquentModel->metadata ?? []
        );

        // Private property'leri reflection ile set et
        $this->setPrivateProperty($notification, 'id', $eloquentModel->id);
        $this->setPrivateProperty($notification, 'status', $status);
        $this->setPrivateProperty($notification, 'sentAt', $eloquentModel->sent_at);
        $this->setPrivateProperty($notification, 'readAt', $eloquentModel->read_at);
        $this->setPrivateProperty($notification, 'retryCount', $eloquentModel->retry_count);
        $this->setPrivateProperty($notification, 'failureReason', $eloquentModel->failure_reason);
        $this->setPrivateProperty($notification, 'deliveryAttempts', $eloquentModel->delivery_attempts ?? []);
        $this->setPrivateProperty($notification, 'createdAt', $eloquentModel->created_at);
        $this->setPrivateProperty($notification, 'updatedAt', $eloquentModel->updated_at);

        return $notification;
    }

    /**
     * Domain entity'ye ID set et (repository'den çağrılır)
     */
    public function setId(Notification $notification, int $id): void
    {
        $this->setPrivateProperty($notification, 'id', $id);
    }

    /**
     * Private property'yi reflection ile set et
     */
    private function setPrivateProperty(object $object, string $propertyName, $value): void
    {
        try {
            $reflection = new ReflectionClass($object);
            $property = $reflection->getProperty($propertyName);
            $property->setAccessible(true);
            $property->setValue($object, $value);
        } catch (\ReflectionException $e) {
            // Property bulunamadı, sessizce devam et
        }
    }

    /**
     * Private property'yi reflection ile al
     */
    private function getPrivateProperty(object $object, string $propertyName)
    {
        try {
            $reflection = new ReflectionClass($object);
            $property = $reflection->getProperty($propertyName);
            $property->setAccessible(true);
            return $property->getValue($object);
        } catch (\ReflectionException $e) {
            return null;
        }
    }

    /**
     * Domain entity'yi array'e çevir
     */
    public function toArray(Notification $notification): array
    {
        return [
            'id' => $notification->getId(),
            'type' => $notification->getType()->getValue(),
            'channel' => $notification->getChannel()->getValue(),
            'status' => $notification->getStatus()->getValue(),
            'priority' => $notification->getPriority()->getValue(),
            'recipient' => [
                'type' => $notification->getRecipient()->getType(),
                'user_id' => $notification->getRecipient()->getId(),
                'email' => $notification->getRecipient()->getEmail(),
                'phone' => $notification->getRecipient()->getPhone(),
                'data' => $notification->getRecipient()->getData(),
            ],
            'title' => $notification->getTitle(),
            'message' => $notification->getMessage(),
            'data' => $notification->getData(),
            'template_id' => $notification->getTemplateId(),
            'template_variables' => $notification->getTemplateVariables(),
            'scheduled_at' => $notification->getScheduledAt()?->toISOString(),
            'sent_at' => $notification->getSentAt()?->toISOString(),
            'read_at' => $notification->getReadAt()?->toISOString(),
            'expires_at' => $notification->getExpiresAt()?->toISOString(),
            'retry_count' => $notification->getRetryCount(),
            'max_retries' => $notification->getMaxRetries(),
            'failure_reason' => $notification->getFailureReason(),
            'delivery_attempts' => $notification->getDeliveryAttempts(),
            'metadata' => $notification->getMetadata(),
            'created_at' => $notification->getCreatedAt()->toISOString(),
            'updated_at' => $notification->getUpdatedAt()->toISOString(),
        ];
    }

    /**
     * Array'den domain entity oluştur
     */
    public function fromArray(array $data): Notification
    {
        // Value object'leri oluştur
        $type = NotificationType::fromString($data['type']);
        $channel = NotificationChannel::fromString($data['channel']);
        $priority = isset($data['priority']) ? NotificationPriority::fromString($data['priority']) : NotificationPriority::normal();

        // RecipientInfo oluştur
        $recipientData = $data['recipient'] ?? [];
        $recipient = RecipientInfo::create(
            $recipientData['type'] ?? 'user',
            $recipientData['user_id'] ?? null,
            $recipientData['email'] ?? null,
            $recipientData['phone'] ?? null,
            $recipientData['data'] ?? []
        );

        // Tarih alanlarını Carbon'a çevir
        $scheduledAt = isset($data['scheduled_at']) ? Carbon::parse($data['scheduled_at']) : null;
        $expiresAt = isset($data['expires_at']) ? Carbon::parse($data['expires_at']) : null;

        // Domain entity oluştur
        $notification = Notification::create(
            $type,
            $channel,
            $recipient,
            $data['title'],
            $data['message'],
            $data['data'] ?? [],
            $priority,
            $data['template_id'] ?? null,
            $data['template_variables'] ?? [],
            $scheduledAt,
            $expiresAt,
            $data['max_retries'] ?? 3,
            $data['metadata'] ?? []
        );

        // Ek alanları set et
        if (isset($data['id'])) {
            $this->setPrivateProperty($notification, 'id', $data['id']);
        }

        if (isset($data['status'])) {
            $status = NotificationStatus::fromString($data['status']);
            $this->setPrivateProperty($notification, 'status', $status);
        }

        if (isset($data['sent_at'])) {
            $sentAt = Carbon::parse($data['sent_at']);
            $this->setPrivateProperty($notification, 'sentAt', $sentAt);
        }

        if (isset($data['read_at'])) {
            $readAt = Carbon::parse($data['read_at']);
            $this->setPrivateProperty($notification, 'readAt', $readAt);
        }

        if (isset($data['retry_count'])) {
            $this->setPrivateProperty($notification, 'retryCount', $data['retry_count']);
        }

        if (isset($data['failure_reason'])) {
            $this->setPrivateProperty($notification, 'failureReason', $data['failure_reason']);
        }

        if (isset($data['delivery_attempts'])) {
            $this->setPrivateProperty($notification, 'deliveryAttempts', $data['delivery_attempts']);
        }

        if (isset($data['created_at'])) {
            $createdAt = Carbon::parse($data['created_at']);
            $this->setPrivateProperty($notification, 'createdAt', $createdAt);
        }

        if (isset($data['updated_at'])) {
            $updatedAt = Carbon::parse($data['updated_at']);
            $this->setPrivateProperty($notification, 'updatedAt', $updatedAt);
        }

        return $notification;
    }

    /**
     * Toplu mapping - Eloquent collection'ı domain entity array'ine çevir
     */
    public function toDomainArray($eloquentModels): array
    {
        return $eloquentModels->map(function ($model) {
            return $this->toDomain($model);
        })->toArray();
    }

    /**
     * Toplu mapping - Domain entity array'ini Eloquent collection'a çevir
     */
    public function toEloquentArray(array $notifications): array
    {
        return array_map(function ($notification) {
            return $this->toEloquent($notification);
        }, $notifications);
    }
}
