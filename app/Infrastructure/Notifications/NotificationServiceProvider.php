<?php

namespace App\Infrastructure\Notifications;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Domain\Notifications\Repositories\NotificationRepositoryInterface;
use App\Infrastructure\Notifications\Repositories\EloquentNotificationRepository;
use App\Infrastructure\Notifications\Mappers\NotificationMapper;
use App\Infrastructure\Notifications\Services\NotificationDeliveryService;
use App\Infrastructure\Notifications\Services\NotificationAnalyticsService;
use App\Infrastructure\Notifications\Services\NotificationTemplateService;
use App\Infrastructure\Notifications\Channels\ChannelAdapterFactory;
use App\Infrastructure\Notifications\Listeners\NotificationEventListener;

/**
 * NotificationServiceProvider
 * Notifications Infrastructure Layer için service provider
 */
class NotificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Repository binding
        $this->app->bind(
            NotificationRepositoryInterface::class,
            EloquentNotificationRepository::class
        );

        // Mapper binding
        $this->app->singleton(NotificationMapper::class);

        // Channel Factory binding
        $this->app->singleton(ChannelAdapterFactory::class, function ($app) {
            return new ChannelAdapterFactory(config('notifications.channels', []));
        });

        // Services binding
        $this->app->singleton(NotificationDeliveryService::class);
        $this->app->singleton(NotificationAnalyticsService::class);
        $this->app->singleton(NotificationTemplateService::class);

        // Event Listener binding
        $this->app->singleton(NotificationEventListener::class);

        // Configuration binding
        $this->mergeConfigFrom(
            __DIR__ . '/../../../config/notifications.php',
            'notifications'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Event listeners kaydet
        $this->registerEventListeners();

        // Migrations publish et
        $this->publishMigrations();

        // Config publish et
        $this->publishConfig();

        // Views publish et
        $this->publishViews();

        // Commands kaydet
        $this->registerCommands();

        // Scheduled tasks kaydet
        $this->registerScheduledTasks();
    }

    /**
     * Event listeners kaydet
     */
    private function registerEventListeners(): void
    {
        $eventListener = $this->app->make(NotificationEventListener::class);
        $eventListener->subscribe(Event::getFacadeRoot());
    }

    /**
     * Migrations publish et
     */
    private function publishMigrations(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../../../database/migrations/2024_01_15_000001_create_notifications_infrastructure_tables.php' => 
                    database_path('migrations/2024_01_15_000001_create_notifications_infrastructure_tables.php'),
                __DIR__ . '/../../../database/migrations/2024_01_15_000002_create_notification_analytics_tables.php' => 
                    database_path('migrations/2024_01_15_000002_create_notification_analytics_tables.php'),
            ], 'notifications-migrations');
        }
    }

    /**
     * Config publish et
     */
    private function publishConfig(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../../../config/notifications.php' => config_path('notifications.php'),
            ], 'notifications-config');
        }
    }

    /**
     * Views publish et
     */
    private function publishViews(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/resources/views' => resource_path('views/notifications'),
            ], 'notifications-views');
        }

        // Views load et
        $this->loadViewsFrom(__DIR__ . '/resources/views', 'notifications');
    }

    /**
     * Commands kaydet
     */
    private function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Infrastructure\Notifications\Console\ProcessNotificationQueueCommand::class,
                \App\Infrastructure\Notifications\Console\CleanupOldNotificationsCommand::class,
                \App\Infrastructure\Notifications\Console\GenerateNotificationReportCommand::class,
                \App\Infrastructure\Notifications\Console\TestNotificationChannelCommand::class,
                \App\Infrastructure\Notifications\Console\RetryFailedNotificationsCommand::class,
            ]);
        }
    }

    /**
     * Scheduled tasks kaydet
     */
    private function registerScheduledTasks(): void
    {
        // Laravel scheduler ile otomatik task'lar
        $this->app->booted(function () {
            $schedule = $this->app->make(\Illuminate\Console\Scheduling\Schedule::class);

            // Her dakika bekleyen bildirimleri işle
            $schedule->command('notifications:process-queue')
                ->everyMinute()
                ->withoutOverlapping()
                ->runInBackground();

            // Her saat başarısız bildirimleri yeniden dene
            $schedule->command('notifications:retry-failed')
                ->hourly()
                ->withoutOverlapping();

            // Günlük eski bildirimleri temizle
            $schedule->command('notifications:cleanup-old')
                ->daily()
                ->at('02:00');

            // Haftalık rapor oluştur
            $schedule->command('notifications:generate-report --weekly')
                ->weekly()
                ->sundays()
                ->at('06:00');

            // Aylık rapor oluştur
            $schedule->command('notifications:generate-report --monthly')
                ->monthly()
                ->at('06:00');
        });
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            NotificationRepositoryInterface::class,
            NotificationMapper::class,
            ChannelAdapterFactory::class,
            NotificationDeliveryService::class,
            NotificationAnalyticsService::class,
            NotificationTemplateService::class,
            NotificationEventListener::class,
        ];
    }
}
