<?php

namespace App\Infrastructure\Notifications\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * EloquentNotification Model
 * Notification entity'si için Eloquent model
 */
class EloquentNotification extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'notifications';

    protected $fillable = [
        'type',
        'channel',
        'status',
        'priority',
        'recipient_type',
        'recipient_user_id',
        'recipient_email',
        'recipient_phone',
        'recipient_data',
        'title',
        'message',
        'data',
        'template_id',
        'template_variables',
        'scheduled_at',
        'sent_at',
        'read_at',
        'expires_at',
        'retry_count',
        'max_retries',
        'failure_reason',
        'delivery_attempts',
        'metadata',
    ];

    protected $casts = [
        'recipient_data' => 'array',
        'data' => 'array',
        'template_variables' => 'array',
        'delivery_attempts' => 'array',
        'metadata' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'expires_at' => 'datetime',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $dates = [
        'scheduled_at',
        'sent_at',
        'read_at',
        'expires_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Scope: Bekleyen bildirimleri al
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope: Gönderilmiş bildirimleri al
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope: Başarısız bildirimleri al
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope: Yeniden deneme gereken bildirimleri al
     */
    public function scopeForRetry($query)
    {
        return $query->where('status', 'retrying')
            ->whereRaw('retry_count < max_retries');
    }

    /**
     * Scope: Süresi dolmuş bildirimleri al
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', Carbon::now())
            ->whereIn('status', ['pending', 'retrying']);
    }

    /**
     * Scope: Yüksek öncelikli bildirimleri al
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['critical', 'urgent', 'high']);
    }

    /**
     * Scope: Belirli kanaldaki bildirimleri al
     */
    public function scopeByChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope: Belirli tipteki bildirimleri al
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope: Belirli alıcıya ait bildirimleri al
     */
    public function scopeByRecipient($query, string $recipientType, ?int $userId = null, ?string $email = null)
    {
        $query->where('recipient_type', $recipientType);

        if ($userId) {
            $query->where('recipient_user_id', $userId);
        }

        if ($email) {
            $query->where('recipient_email', $email);
        }

        return $query;
    }

    /**
     * Scope: Zamanlanmış bildirimleri al
     */
    public function scopeScheduledForDelivery($query, Carbon $before = null)
    {
        $before = $before ?: Carbon::now();

        return $query->where('status', 'pending')
            ->where(function ($q) use ($before) {
                $q->whereNull('scheduled_at')
                  ->orWhere('scheduled_at', '<=', $before);
            })
            ->where(function ($q) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>', Carbon::now());
            });
    }

    /**
     * Scope: Okunmamış bildirimleri al
     */
    public function scopeUnread($query)
    {
        return $query->where('status', 'sent')
            ->whereNull('read_at');
    }

    /**
     * Scope: Tarih aralığındaki bildirimleri al
     */
    public function scopeDateRange($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }

    /**
     * Scope: Template'e göre bildirimleri al
     */
    public function scopeByTemplate($query, string $templateId)
    {
        return $query->where('template_id', $templateId);
    }

    /**
     * Bildirim süresi dolmuş mu?
     */
    public function isExpired(): bool
    {
        return $this->expires_at && Carbon::now()->isAfter($this->expires_at);
    }

    /**
     * Bildirim gönderilmeye hazır mı?
     */
    public function isReadyForDelivery(): bool
    {
        if (!in_array($this->status, ['pending', 'retrying'])) {
            return false;
        }

        if ($this->isExpired()) {
            return false;
        }

        if ($this->scheduled_at && Carbon::now()->isBefore($this->scheduled_at)) {
            return false;
        }

        return true;
    }

    /**
     * Yeniden deneme gerekli mi?
     */
    public function needsRetry(): bool
    {
        return $this->status === 'retrying' && $this->retry_count < $this->max_retries;
    }

    /**
     * Teslimat süresi (saniye)
     */
    public function getDeliveryDuration(): ?int
    {
        if (!$this->sent_at) {
            return null;
        }

        return $this->created_at->diffInSeconds($this->sent_at);
    }

    /**
     * Okunma süresi (saniye)
     */
    public function getReadDuration(): ?int
    {
        if (!$this->read_at || !$this->sent_at) {
            return null;
        }

        return $this->sent_at->diffInSeconds($this->read_at);
    }

    /**
     * Bildirim yaşı (saniye)
     */
    public function getAge(): int
    {
        return $this->created_at->diffInSeconds(Carbon::now());
    }

    /**
     * Kalan süre (saniye)
     */
    public function getTimeToExpiry(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        $now = Carbon::now();
        if ($now->isAfter($this->expires_at)) {
            return 0;
        }

        return $now->diffInSeconds($this->expires_at);
    }

    /**
     * Başarısız teslimat denemelerini getir
     */
    public function getFailedAttempts(): array
    {
        if (!$this->delivery_attempts) {
            return [];
        }

        return array_filter($this->delivery_attempts, function($attempt) {
            return $attempt['status'] === 'failed';
        });
    }

    /**
     * Son teslimat denemesini getir
     */
    public function getLastDeliveryAttempt(): ?array
    {
        if (!$this->delivery_attempts || empty($this->delivery_attempts)) {
            return null;
        }

        return end($this->delivery_attempts);
    }

    /**
     * Teslimat denemesi sayısını getir
     */
    public function getDeliveryAttemptCount(): int
    {
        return $this->delivery_attempts ? count($this->delivery_attempts) : 0;
    }

    /**
     * Bildirim template kullanıyor mu?
     */
    public function usesTemplate(): bool
    {
        return !empty($this->template_id);
    }

    /**
     * Bildirim zamanlanmış mı?
     */
    public function isScheduled(): bool
    {
        return $this->scheduled_at && Carbon::now()->isBefore($this->scheduled_at);
    }

    /**
     * Bildirim okunmuş mu?
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Bildirim gönderilmiş mi?
     */
    public function isSent(): bool
    {
        return in_array($this->status, ['sent', 'delivered', 'read']);
    }

    /**
     * Bildirim başarısız mı?
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Bildirim iptal edilmiş mi?
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Bildirim aktif mi?
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['pending', 'sending', 'retrying']);
    }
}
