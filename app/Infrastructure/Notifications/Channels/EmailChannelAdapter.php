<?php

namespace App\Infrastructure\Notifications\Channels;

use App\Domain\Notifications\Entities\Notification;
use App\Domain\Notifications\ValueObjects\RecipientInfo;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Mail\Mailable;
use Carbon\Carbon;

/**
 * EmailChannelAdapter
 * Email kanalı için adapter implementasyonu
 */
class EmailChannelAdapter implements ChannelAdapterInterface
{
    private array $config = [];
    private array $statistics = [
        'sent' => 0,
        'failed' => 0,
        'total_duration' => 0,
    ];

    /**
     * Kanal adını getir
     */
    public function getChannelName(): string
    {
        return 'email';
    }

    /**
     * Kanal desteklenen mi kontrol et
     */
    public function isSupported(): bool
    {
        return true; // Email her zaman desteklenir
    }

    /**
     * Kanal konfigüre edilmiş mi kontrol et
     */
    public function isConfigured(): bool
    {
        // Laravel mail konfigürasyonu kontrol et
        $mailConfig = config('mail');
        
        return !empty($mailConfig['default']) && 
               !empty($mailConfig['mailers'][$mailConfig['default']]);
    }

    /**
     * Alıcı bu kanal için geçerli mi kontrol et
     */
    public function validateRecipient(RecipientInfo $recipient): bool
    {
        $email = $recipient->getEmail();
        
        if (empty($email)) {
            return false;
        }

        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Tek bildirim gönder
     */
    public function send(Notification $notification): ChannelDeliveryResult
    {
        $startTime = microtime(true);

        try {
            $recipient = $notification->getRecipient();
            $email = $recipient->getEmail();

            if (!$this->validateRecipient($recipient)) {
                return ChannelDeliveryResult::failure('Invalid email address: ' . $email);
            }

            // Email içeriğini hazırla
            $emailData = $this->prepareEmailData($notification);

            // Email gönder
            Mail::send('emails.notification', $emailData, function ($message) use ($notification, $email) {
                $message->to($email)
                        ->subject($notification->getTitle());

                // CC/BCC varsa ekle
                $metadata = $notification->getMetadata();
                if (!empty($metadata['cc'])) {
                    $message->cc($metadata['cc']);
                }
                if (!empty($metadata['bcc'])) {
                    $message->bcc($metadata['bcc']);
                }

                // Attachments varsa ekle
                if (!empty($metadata['attachments'])) {
                    foreach ($metadata['attachments'] as $attachment) {
                        $message->attach($attachment['path'], [
                            'as' => $attachment['name'] ?? null,
                            'mime' => $attachment['mime'] ?? null,
                        ]);
                    }
                }
            });

            $duration = microtime(true) - $startTime;
            $this->updateStatistics(true, $duration);

            Log::info('Email notification sent successfully', [
                'notification_id' => $notification->getId(),
                'email' => $email,
                'duration_ms' => round($duration * 1000, 2),
            ]);

            return ChannelDeliveryResult::success(
                uniqid('email_'), 
                ['duration_ms' => round($duration * 1000, 2)]
            );

        } catch (\Exception $e) {
            $duration = microtime(true) - $startTime;
            $this->updateStatistics(false, $duration);

            Log::error('Email notification failed', [
                'notification_id' => $notification->getId(),
                'email' => $recipient->getEmail(),
                'error' => $e->getMessage(),
                'duration_ms' => round($duration * 1000, 2),
            ]);

            return ChannelDeliveryResult::failure(
                'Email sending failed: ' . $e->getMessage(),
                'EMAIL_SEND_ERROR',
                ['duration_ms' => round($duration * 1000, 2)]
            );
        }
    }

    /**
     * Toplu bildirim gönder
     */
    public function sendBatch(array $notifications): array
    {
        $results = [];

        foreach ($notifications as $notification) {
            $results[$notification->getId()] = $this->send($notification)->isSuccess();
        }

        Log::info('Email batch sending completed', [
            'total' => count($notifications),
            'successful' => count(array_filter($results)),
        ]);

        return $results;
    }

    /**
     * Toplu gönderim destekliyor mu
     */
    public function supportsBatchDelivery(): bool
    {
        return true;
    }

    /**
     * Teslimat durumu kontrolü destekliyor mu
     */
    public function supportsStatusCheck(): bool
    {
        return false; // Standart SMTP ile teslimat durumu takibi zor
    }

    /**
     * Teslimat durumunu kontrol et
     */
    public function getDeliveryStatus(Notification $notification): ?array
    {
        return null; // Desteklenmiyor
    }

    /**
     * Okundu bilgisi destekliyor mu
     */
    public function supportsReadReceipts(): bool
    {
        return true; // Email read receipts desteklenebilir
    }

    /**
     * Okundu bilgisini kontrol et
     */
    public function getReadStatus(Notification $notification): ?array
    {
        return null; // Implementasyon gerekli
    }

    /**
     * Zamanlanmış gönderim destekliyor mu
     */
    public function supportsScheduledDelivery(): bool
    {
        return true;
    }

    /**
     * Yeniden deneme destekliyor mu
     */
    public function supportsRetry(): bool
    {
        return true;
    }

    /**
     * Maksimum mesaj uzunluğunu getir
     */
    public function getMaxMessageLength(): ?int
    {
        return null; // Email için pratik limit yok
    }

    /**
     * Maksimum başlık uzunluğunu getir
     */
    public function getMaxTitleLength(): ?int
    {
        return 998; // RFC 2822 limit
    }

    /**
     * Zengin içerik destekliyor mu
     */
    public function supportsRichContent(): bool
    {
        return true; // HTML email desteklenir
    }

    /**
     * Dosya eki destekliyor mu
     */
    public function supportsAttachments(): bool
    {
        return true;
    }

    /**
     * İnteraktif özellikler destekliyor mu
     */
    public function supportsInteractivity(): bool
    {
        return true; // HTML butonlar, linkler vs.
    }

    /**
     * Kanal konfigürasyonunu getir
     */
    public function getConfiguration(): array
    {
        return $this->config;
    }

    /**
     * Kanal konfigürasyonunu set et
     */
    public function setConfiguration(array $config): void
    {
        $this->config = $config;
    }

    /**
     * Kanal sağlık durumunu kontrol et
     */
    public function healthCheck(): ChannelHealthResult
    {
        try {
            // Mail konfigürasyonunu kontrol et
            if (!$this->isConfigured()) {
                return ChannelHealthResult::unhealthy('Email configuration is missing');
            }

            // Test email göndermeyi dene (sadece konfigürasyon kontrolü)
            $mailConfig = config('mail');
            $defaultMailer = $mailConfig['default'];
            $mailerConfig = $mailConfig['mailers'][$defaultMailer] ?? null;

            if (!$mailerConfig) {
                return ChannelHealthResult::unhealthy('Default mailer configuration not found');
            }

            // SMTP bağlantısını test et (opsiyonel)
            if ($mailerConfig['transport'] === 'smtp') {
                $host = $mailerConfig['host'] ?? null;
                $port = $mailerConfig['port'] ?? 587;

                if ($host && !$this->testSmtpConnection($host, $port)) {
                    return ChannelHealthResult::unhealthy("Cannot connect to SMTP server: {$host}:{$port}");
                }
            }

            return ChannelHealthResult::healthy('Email channel is healthy', [
                'mailer' => $defaultMailer,
                'transport' => $mailerConfig['transport'] ?? 'unknown',
                'host' => $mailerConfig['host'] ?? null,
            ]);

        } catch (\Exception $e) {
            return ChannelHealthResult::unhealthy('Email health check failed: ' . $e->getMessage());
        }
    }

    /**
     * Kanal istatistiklerini getir
     */
    public function getStatistics(): array
    {
        $total = $this->statistics['sent'] + $this->statistics['failed'];
        
        return [
            'sent' => $this->statistics['sent'],
            'failed' => $this->statistics['failed'],
            'total' => $total,
            'success_rate' => $total > 0 ? round(($this->statistics['sent'] / $total) * 100, 2) : 0,
            'avg_duration_ms' => $total > 0 ? round(($this->statistics['total_duration'] / $total) * 1000, 2) : 0,
        ];
    }

    /**
     * Rate limiting bilgilerini getir
     */
    public function getRateLimits(): array
    {
        return [
            'per_minute' => $this->config['rate_limit_per_minute'] ?? 60,
            'per_hour' => $this->config['rate_limit_per_hour'] ?? 1000,
            'per_day' => $this->config['rate_limit_per_day'] ?? 10000,
        ];
    }

    /**
     * Maliyet bilgilerini getir
     */
    public function getCostInfo(): array
    {
        return [
            'cost_per_email' => $this->config['cost_per_email'] ?? 0.0001, // $0.0001 per email
            'currency' => 'USD',
            'billing_model' => 'per_message',
        ];
    }

    /**
     * Email verilerini hazırla
     */
    private function prepareEmailData(Notification $notification): array
    {
        return [
            'title' => $notification->getTitle(),
            'message' => $notification->getMessage(),
            'data' => $notification->getData(),
            'notification_id' => $notification->getId(),
            'recipient' => $notification->getRecipient()->toArray(),
            'metadata' => $notification->getMetadata(),
        ];
    }

    /**
     * SMTP bağlantısını test et
     */
    private function testSmtpConnection(string $host, int $port): bool
    {
        try {
            $connection = @fsockopen($host, $port, $errno, $errstr, 5);
            if ($connection) {
                fclose($connection);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * İstatistikleri güncelle
     */
    private function updateStatistics(bool $success, float $duration): void
    {
        if ($success) {
            $this->statistics['sent']++;
        } else {
            $this->statistics['failed']++;
        }
        
        $this->statistics['total_duration'] += $duration;
    }
}
