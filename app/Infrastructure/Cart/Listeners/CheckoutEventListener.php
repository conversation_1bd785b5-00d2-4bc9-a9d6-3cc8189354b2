<?php

namespace App\Infrastructure\Cart\Listeners;

use App\Domain\Cart\Events\CheckoutStepCompleted;
use App\Domain\Cart\Events\CheckoutCompleted;
use App\Domain\Cart\Events\CheckoutCancelled;
use App\Domain\Cart\Events\CheckoutRestarted;
use App\Domain\Cart\Events\CheckoutStarted;
use App\Domain\Cart\Entities\CheckoutProcess;
use App\Infrastructure\Cart\Services\CheckoutNotificationService;
use App\Infrastructure\Cart\Services\CheckoutAnalyticsService;
use App\Infrastructure\Cart\Services\CheckoutValidationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Cache;

/**
 * CheckoutEventListener
 * Checkout domain event'lerini dinleyen infrastructure listener
 */
class CheckoutEventListener
{
    public function __construct(
        private CheckoutNotificationService $notificationService,
        private CheckoutAnalyticsService $analyticsService,
        private CheckoutValidationService $validationService
    ) {}

    /**
     * Checkout başladığında
     */
    public function handleCheckoutStarted(CheckoutStarted $event): void
    {
        $cart = $event->getCart();

        try {
            Log::info('Checkout started event handled', [
                'cart_id' => $cart->getId(),
                'user_id' => $cart->getUserId(),
                'item_count' => $cart->getTotalItems(),
                'total_amount' => $cart->getTotal()->getTotal()->getAmount(),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // 1. Cache'i temizle
            $this->clearCheckoutCaches($cart);

            // 2. Analytics'e kaydet
            $this->recordCheckoutStartedAnalytics($cart);

            // 3. Bildirim gönder (opsiyonel)
            if (config('checkout.notifications.started_enabled', false)) {
                $this->notificationService->sendCheckoutStartedNotification($cart);
            }

            // 4. Validation başlat
            $this->validationService->validateCartForCheckout($cart);

            // 5. Abandoned cart tracking'i durdur
            $this->stopAbandonedCartTracking($cart);

        } catch (\Exception $e) {
            Log::error('Checkout started event handling failed', [
                'cart_id' => $cart->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Checkout adımı tamamlandığında
     */
    public function handleCheckoutStepCompleted(CheckoutStepCompleted $event): void
    {
        $checkoutProcess = $event->getCheckoutProcess();
        $step = $event->getStep();

        try {
            Log::info('Checkout step completed event handled', [
                'checkout_id' => $checkoutProcess->getId(),
                'cart_id' => $checkoutProcess->getCart()->getId(),
                'step' => $step->getValue(),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // 1. Cache'i temizle
            $this->clearCheckoutProcessCaches($checkoutProcess);

            // 2. Analytics'e kaydet
            $this->recordStepCompletedAnalytics($checkoutProcess, $step);

            // 3. Adım bazlı işlemler
            $this->handleStepSpecificActions($checkoutProcess, $step);

            // 4. Progress tracking
            $this->updateCheckoutProgress($checkoutProcess, $step);

            // 5. Bildirim gönder (opsiyonel)
            if (config('checkout.notifications.step_completed_enabled', false)) {
                $this->notificationService->sendStepCompletedNotification($checkoutProcess, $step);
            }

        } catch (\Exception $e) {
            Log::error('Checkout step completed event handling failed', [
                'checkout_id' => $checkoutProcess->getId(),
                'step' => $step->getValue(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Checkout tamamlandığında
     */
    public function handleCheckoutCompleted(CheckoutCompleted $event): void
    {
        $checkoutProcess = $event->getCheckoutProcess();

        try {
            Log::info('Checkout completed event handled', [
                'checkout_id' => $checkoutProcess->getId(),
                'cart_id' => $checkoutProcess->getCart()->getId(),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // 1. Cache'i temizle
            $this->clearCheckoutProcessCaches($checkoutProcess);

            // 2. Analytics'e kaydet
            $this->recordCheckoutCompletedAnalytics($checkoutProcess);

            // 3. Bildirim gönder
            $this->notificationService->sendCheckoutCompletedNotification($checkoutProcess);

            // 4. Order oluşturma işlemini başlat
            $this->initiateOrderCreation($checkoutProcess);

            // 5. Conversion tracking
            $this->trackConversion($checkoutProcess);

            // 6. Customer segmentation güncelle
            $this->updateCustomerSegmentation($checkoutProcess);

            // 7. Loyalty points hesapla
            $this->calculateLoyaltyPoints($checkoutProcess);

        } catch (\Exception $e) {
            Log::error('Checkout completed event handling failed', [
                'checkout_id' => $checkoutProcess->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Checkout iptal edildiğinde
     */
    public function handleCheckoutCancelled(CheckoutCancelled $event): void
    {
        $checkoutProcess = $event->getCheckoutProcess();
        $reason = $event->getReason();

        try {
            Log::info('Checkout cancelled event handled', [
                'checkout_id' => $checkoutProcess->getId(),
                'cart_id' => $checkoutProcess->getCart()->getId(),
                'reason' => $reason,
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // 1. Cache'i temizle
            $this->clearCheckoutProcessCaches($checkoutProcess);

            // 2. Analytics'e kaydet
            $this->recordCheckoutCancelledAnalytics($checkoutProcess, $reason);

            // 3. Bildirim gönder (opsiyonel)
            if (config('checkout.notifications.cancelled_enabled', false)) {
                $this->notificationService->sendCheckoutCancelledNotification($checkoutProcess, $reason);
            }

            // 4. Stok rezervasyonunu iptal et
            $this->cancelStockReservation($checkoutProcess);

            // 5. Abandoned cart tracking'i yeniden başlat
            $this->restartAbandonedCartTracking($checkoutProcess->getCart());

            // 6. Cancellation reason analizi
            $this->analyzeCancellationReason($checkoutProcess, $reason);

        } catch (\Exception $e) {
            Log::error('Checkout cancelled event handling failed', [
                'checkout_id' => $checkoutProcess->getId(),
                'reason' => $reason,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Checkout yeniden başlatıldığında
     */
    public function handleCheckoutRestarted(CheckoutRestarted $event): void
    {
        $checkoutProcess = $event->getCheckoutProcess();

        try {
            Log::info('Checkout restarted event handled', [
                'checkout_id' => $checkoutProcess->getId(),
                'cart_id' => $checkoutProcess->getCart()->getId(),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // 1. Cache'i temizle
            $this->clearCheckoutProcessCaches($checkoutProcess);

            // 2. Analytics'e kaydet
            $this->recordCheckoutRestartedAnalytics($checkoutProcess);

            // 3. Validation yeniden başlat
            $this->validationService->validateCartForCheckout($checkoutProcess->getCart());

            // 4. Progress tracking'i sıfırla
            $this->resetCheckoutProgress($checkoutProcess);

        } catch (\Exception $e) {
            Log::error('Checkout restarted event handling failed', [
                'checkout_id' => $checkoutProcess->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Checkout cache'lerini temizle
     */
    private function clearCheckoutCaches($cart): void
    {
        try {
            $patterns = [
                'checkout:cart_id:' . $cart->getId(),
                'checkout:active_user:' . $cart->getUserId(),
                'checkout:statistics',
                'checkout:conversion_rate',
                'checkout:funnel_analysis',
            ];

            foreach ($patterns as $pattern) {
                Cache::forget($pattern);
            }

            Log::debug('Checkout caches cleared', ['cart_id' => $cart->getId()]);

        } catch (\Exception $e) {
            Log::warning('Failed to clear checkout caches', [
                'cart_id' => $cart->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Checkout process cache'lerini temizle
     */
    private function clearCheckoutProcessCaches(CheckoutProcess $checkoutProcess): void
    {
        try {
            $patterns = [
                'checkout:id:' . $checkoutProcess->getId(),
                'checkout:cart_id:' . $checkoutProcess->getCart()->getId(),
                'checkout:statistics',
                'checkout:conversion_rate',
                'checkout:step_conversion_rates',
                'checkout:funnel_analysis',
            ];

            foreach ($patterns as $pattern) {
                Cache::forget($pattern);
            }

            Log::debug('Checkout process caches cleared', ['checkout_id' => $checkoutProcess->getId()]);

        } catch (\Exception $e) {
            Log::warning('Failed to clear checkout process caches', [
                'checkout_id' => $checkoutProcess->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Checkout başladı analytics'ini kaydet
     */
    private function recordCheckoutStartedAnalytics($cart): void
    {
        Queue::push('analytics.checkout.started', [
            'cart_id' => $cart->getId(),
            'user_id' => $cart->getUserId(),
            'session_id' => $cart->getSessionId()?->getValue(),
            'item_count' => $cart->getTotalItems(),
            'total_amount' => $cart->getTotal()->getTotal()->getAmount(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Adım tamamlandı analytics'ini kaydet
     */
    private function recordStepCompletedAnalytics(CheckoutProcess $checkoutProcess, $step): void
    {
        Queue::push('analytics.checkout.step_completed', [
            'checkout_id' => $checkoutProcess->getId(),
            'cart_id' => $checkoutProcess->getCart()->getId(),
            'step' => $step->getValue(),
            'completed_steps' => $checkoutProcess->getCompletedSteps(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Checkout tamamlandı analytics'ini kaydet
     */
    private function recordCheckoutCompletedAnalytics(CheckoutProcess $checkoutProcess): void
    {
        $cart = $checkoutProcess->getCart();

        Queue::push('analytics.checkout.completed', [
            'checkout_id' => $checkoutProcess->getId(),
            'cart_id' => $cart->getId(),
            'user_id' => $cart->getUserId(),
            'total_amount' => $cart->getTotal()->getTotal()->getAmount(),
            'completion_time_minutes' => $checkoutProcess->getCreatedAt()->diffInMinutes(now()),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Checkout iptal edildi analytics'ini kaydet
     */
    private function recordCheckoutCancelledAnalytics(CheckoutProcess $checkoutProcess, string $reason): void
    {
        Queue::push('analytics.checkout.cancelled', [
            'checkout_id' => $checkoutProcess->getId(),
            'cart_id' => $checkoutProcess->getCart()->getId(),
            'reason' => $reason,
            'current_step' => $checkoutProcess->getCurrentStep()?->getValue(),
            'completed_steps' => $checkoutProcess->getCompletedSteps(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Checkout yeniden başladı analytics'ini kaydet
     */
    private function recordCheckoutRestartedAnalytics(CheckoutProcess $checkoutProcess): void
    {
        Queue::push('analytics.checkout.restarted', [
            'checkout_id' => $checkoutProcess->getId(),
            'cart_id' => $checkoutProcess->getCart()->getId(),
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Adım bazlı özel işlemler
     */
    private function handleStepSpecificActions(CheckoutProcess $checkoutProcess, $step): void
    {
        switch ($step->getValue()) {
            case 'shipping_address':
                $this->handleShippingAddressCompleted($checkoutProcess);
                break;
            case 'billing_address':
                $this->handleBillingAddressCompleted($checkoutProcess);
                break;
            case 'shipping_method':
                $this->handleShippingMethodCompleted($checkoutProcess);
                break;
            case 'payment_method':
                $this->handlePaymentMethodCompleted($checkoutProcess);
                break;
            case 'review':
                $this->handleReviewCompleted($checkoutProcess);
                break;
        }
    }

    /**
     * Kargo adresi tamamlandığında
     */
    private function handleShippingAddressCompleted(CheckoutProcess $checkoutProcess): void
    {
        // Kargo seçeneklerini hesapla
        Queue::push('checkout.calculate_shipping_options', [
            'checkout_id' => $checkoutProcess->getId(),
        ]);
    }

    /**
     * Fatura adresi tamamlandığında
     */
    private function handleBillingAddressCompleted(CheckoutProcess $checkoutProcess): void
    {
        // Vergi hesaplaması
        Queue::push('checkout.calculate_tax', [
            'checkout_id' => $checkoutProcess->getId(),
        ]);
    }

    /**
     * Kargo yöntemi tamamlandığında
     */
    private function handleShippingMethodCompleted(CheckoutProcess $checkoutProcess): void
    {
        // Kargo maliyetini hesapla
        Queue::push('checkout.calculate_shipping_cost', [
            'checkout_id' => $checkoutProcess->getId(),
        ]);
    }

    /**
     * Ödeme yöntemi tamamlandığında
     */
    private function handlePaymentMethodCompleted(CheckoutProcess $checkoutProcess): void
    {
        // Ödeme gateway'ini hazırla
        Queue::push('checkout.prepare_payment_gateway', [
            'checkout_id' => $checkoutProcess->getId(),
        ]);
    }

    /**
     * İnceleme tamamlandığında
     */
    private function handleReviewCompleted(CheckoutProcess $checkoutProcess): void
    {
        // Final validasyon
        Queue::push('checkout.final_validation', [
            'checkout_id' => $checkoutProcess->getId(),
        ]);
    }

    /**
     * Checkout progress'ini güncelle
     */
    private function updateCheckoutProgress(CheckoutProcess $checkoutProcess, $step): void
    {
        Cache::put(
            'checkout_progress:' . $checkoutProcess->getId(),
            [
                'current_step' => $step->getValue(),
                'completed_steps' => $checkoutProcess->getCompletedSteps(),
                'progress_percentage' => $this->calculateProgressPercentage($checkoutProcess),
                'updated_at' => now()->toISOString(),
            ],
            3600 // 1 saat
        );
    }

    /**
     * Progress yüzdesini hesapla
     */
    private function calculateProgressPercentage(CheckoutProcess $checkoutProcess): float
    {
        $totalSteps = 5; // shipping_address, billing_address, shipping_method, payment_method, review
        $completedSteps = count($checkoutProcess->getCompletedSteps());

        return round(($completedSteps / $totalSteps) * 100, 2);
    }

    /**
     * Checkout progress'ini sıfırla
     */
    private function resetCheckoutProgress(CheckoutProcess $checkoutProcess): void
    {
        Cache::forget('checkout_progress:' . $checkoutProcess->getId());
    }

    /**
     * Abandoned cart tracking'i durdur
     */
    private function stopAbandonedCartTracking($cart): void
    {
        Cache::forget('abandoned_cart:' . $cart->getId());
    }

    /**
     * Abandoned cart tracking'i yeniden başlat
     */
    private function restartAbandonedCartTracking($cart): void
    {
        Cache::put(
            'abandoned_cart:' . $cart->getId(),
            [
                'cart_id' => $cart->getId(),
                'user_id' => $cart->getUserId(),
                'session_id' => $cart->getSessionId()?->getValue(),
                'started_at' => now()->toISOString(),
            ],
            86400 // 24 saat
        );
    }

    /**
     * Order oluşturma işlemini başlat
     */
    private function initiateOrderCreation(CheckoutProcess $checkoutProcess): void
    {
        Queue::push('orders.create_from_checkout', [
            'checkout_id' => $checkoutProcess->getId(),
        ]);
    }

    /**
     * Conversion tracking
     */
    private function trackConversion(CheckoutProcess $checkoutProcess): void
    {
        Queue::push('analytics.track_conversion', [
            'checkout_id' => $checkoutProcess->getId(),
            'cart_id' => $checkoutProcess->getCart()->getId(),
            'conversion_value' => $checkoutProcess->getCart()->getTotal()->getTotal()->getAmount(),
        ]);
    }

    /**
     * Customer segmentation güncelle
     */
    private function updateCustomerSegmentation(CheckoutProcess $checkoutProcess): void
    {
        if ($checkoutProcess->getCart()->getUserId()) {
            Queue::push('customers.update_segmentation', [
                'user_id' => $checkoutProcess->getCart()->getUserId(),
                'checkout_completed' => true,
                'order_value' => $checkoutProcess->getCart()->getTotal()->getTotal()->getAmount(),
            ]);
        }
    }

    /**
     * Loyalty points hesapla
     */
    private function calculateLoyaltyPoints(CheckoutProcess $checkoutProcess): void
    {
        if ($checkoutProcess->getCart()->getUserId()) {
            Queue::push('loyalty.calculate_points', [
                'user_id' => $checkoutProcess->getCart()->getUserId(),
                'order_value' => $checkoutProcess->getCart()->getTotal()->getTotal()->getAmount(),
                'checkout_id' => $checkoutProcess->getId(),
            ]);
        }
    }

    /**
     * Stok rezervasyonunu iptal et
     */
    private function cancelStockReservation(CheckoutProcess $checkoutProcess): void
    {
        Queue::push('inventory.cancel_reservation', [
            'checkout_id' => $checkoutProcess->getId(),
            'cart_id' => $checkoutProcess->getCart()->getId(),
        ]);
    }

    /**
     * İptal sebebi analizini yap
     */
    private function analyzeCancellationReason(CheckoutProcess $checkoutProcess, string $reason): void
    {
        Queue::push('analytics.analyze_cancellation', [
            'checkout_id' => $checkoutProcess->getId(),
            'reason' => $reason,
            'current_step' => $checkoutProcess->getCurrentStep()?->getValue(),
            'completed_steps' => $checkoutProcess->getCompletedSteps(),
        ]);
    }
}