<?php

namespace App\Infrastructure\Cart\Repositories;

use App\Domain\Cart\Entities\CheckoutProcess;
use App\Domain\Cart\Entities\Cart;
use App\Domain\Cart\ValueObjects\CheckoutStep;
use App\Domain\Cart\ValueObjects\ShippingAddress;
use App\Domain\Cart\ValueObjects\BillingAddress;
use App\Domain\Cart\ValueObjects\PaymentMethod;
use App\Domain\Cart\ValueObjects\ShippingMethod;
use App\Domain\Cart\Repositories\CheckoutRepositoryInterface;
use App\Core\Domain\ValueObjects\Money;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * EloquentCheckoutRepository
 * Eloquent ORM kullanarak CheckoutProcess domain entity'lerini yönetir
 */
class EloquentCheckoutRepository implements CheckoutRepositoryInterface
{
    private string $table = 'checkout_processes';

    /**
     * Checkout process'i kaydet
     */
    public function save(CheckoutProcess $checkoutProcess): CheckoutProcess
    {
        try {
            $data = $this->mapToDatabase($checkoutProcess);

            if ($checkoutProcess->getId()) {
                // Güncelleme
                DB::table($this->table)
                    ->where('id', $checkoutProcess->getId())
                    ->update(array_merge($data, [
                        'updated_at' => Carbon::now()
                    ]));

                Log::info('Checkout process updated', ['checkout_id' => $checkoutProcess->getId()]);
            } else {
                // Yeni kayıt
                $id = DB::table($this->table)->insertGetId(array_merge($data, [
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]));

                // CheckoutProcess entity'sine ID'yi set et
                $checkoutProcess = $this->setCheckoutProcessId($checkoutProcess, $id);

                Log::info('Checkout process created', ['checkout_id' => $id]);
            }

            return $checkoutProcess;

        } catch (\Exception $e) {
            Log::error('Checkout process save failed', [
                'checkout_id' => $checkoutProcess->getId(),
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * ID ile checkout process bul
     */
    public function findById(int $id): ?CheckoutProcess
    {
        try {
            $data = DB::table($this->table)
                ->where('id', $id)
                ->where('deleted_at', null)
                ->first();

            return $data ? $this->mapToDomainEntity($data) : null;

        } catch (\Exception $e) {
            Log::error('Checkout process find by ID failed', [
                'checkout_id' => $id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Cart ID ile checkout process bul
     */
    public function findByCartId(int $cartId): ?CheckoutProcess
    {
        try {
            $data = DB::table($this->table)
                ->where('cart_id', $cartId)
                ->where('deleted_at', null)
                ->orderBy('created_at', 'desc')
                ->first();

            return $data ? $this->mapToDomainEntity($data) : null;

        } catch (\Exception $e) {
            Log::error('Checkout process find by cart ID failed', [
                'cart_id' => $cartId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Kullanıcı ID ile aktif checkout process bul
     */
    public function findActiveByUserId(int $userId): ?CheckoutProcess
    {
        try {
            $data = DB::table($this->table . ' as cp')
                ->join('carts as c', 'cp.cart_id', '=', 'c.id')
                ->where('c.user_id', $userId)
                ->whereIn('cp.status', ['initiated', 'in_progress'])
                ->where('cp.deleted_at', null)
                ->where('c.deleted_at', null)
                ->select('cp.*')
                ->orderBy('cp.created_at', 'desc')
                ->first();

            return $data ? $this->mapToDomainEntity($data) : null;

        } catch (\Exception $e) {
            Log::error('Active checkout process find by user ID failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Session ID ile aktif checkout process bul
     */
    public function findActiveBySessionId(string $sessionId): ?CheckoutProcess
    {
        try {
            $data = DB::table($this->table . ' as cp')
                ->join('carts as c', 'cp.cart_id', '=', 'c.id')
                ->where('c.session_id', $sessionId)
                ->whereIn('cp.status', ['initiated', 'in_progress'])
                ->where('cp.deleted_at', null)
                ->where('c.deleted_at', null)
                ->select('cp.*')
                ->orderBy('cp.created_at', 'desc')
                ->first();

            return $data ? $this->mapToDomainEntity($data) : null;

        } catch (\Exception $e) {
            Log::error('Active checkout process find by session ID failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Duruma göre checkout process'leri bul
     */
    public function findByStatus(string $status, int $limit = 10, int $offset = 0): array
    {
        try {
            $data = DB::table($this->table)
                ->where('status', $status)
                ->where('deleted_at', null)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->offset($offset)
                ->get();

            return $data->map(fn($item) => $this->mapToDomainEntity($item))->toArray();

        } catch (\Exception $e) {
            Log::error('Checkout processes find by status failed', [
                'status' => $status,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Adıma göre checkout process'leri bul
     */
    public function findByCurrentStep(CheckoutStep $step, int $limit = 10, int $offset = 0): array
    {
        try {
            $data = DB::table($this->table)
                ->where('current_step', $step->getValue())
                ->where('deleted_at', null)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->offset($offset)
                ->get();

            return $data->map(fn($item) => $this->mapToDomainEntity($item))->toArray();

        } catch (\Exception $e) {
            Log::error('Checkout processes find by current step failed', [
                'step' => $step->getValue(),
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Checkout process'i sil
     */
    public function delete(CheckoutProcess $checkoutProcess): bool
    {
        return $this->deleteById($checkoutProcess->getId());
    }

    /**
     * ID ile checkout process'i sil
     */
    public function deleteById(int $id): bool
    {
        try {
            $result = DB::table($this->table)
                ->where('id', $id)
                ->update([
                    'deleted_at' => Carbon::now()
                ]);

            Log::info('Checkout process soft deleted', ['checkout_id' => $id]);

            return $result > 0;

        } catch (\Exception $e) {
            Log::error('Checkout process delete failed', [
                'checkout_id' => $id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Checkout process var mı kontrol et
     */
    public function exists(int $id): bool
    {
        try {
            return DB::table($this->table)
                ->where('id', $id)
                ->where('deleted_at', null)
                ->exists();

        } catch (\Exception $e) {
            Log::error('Checkout process exists check failed', [
                'checkout_id' => $id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Cart için aktif checkout var mı kontrol et
     */
    public function existsActiveForCart(int $cartId): bool
    {
        try {
            return DB::table($this->table)
                ->where('cart_id', $cartId)
                ->whereIn('status', ['initiated', 'in_progress'])
                ->where('deleted_at', null)
                ->exists();

        } catch (\Exception $e) {
            Log::error('Active checkout exists check for cart failed', [
                'cart_id' => $cartId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Kullanıcı için aktif checkout var mı kontrol et
     */
    public function existsActiveForUser(int $userId): bool
    {
        try {
            return DB::table($this->table . ' as cp')
                ->join('carts as c', 'cp.cart_id', '=', 'c.id')
                ->where('c.user_id', $userId)
                ->whereIn('cp.status', ['initiated', 'in_progress'])
                ->where('cp.deleted_at', null)
                ->where('c.deleted_at', null)
                ->exists();

        } catch (\Exception $e) {
            Log::error('Active checkout exists check for user failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Terk edilmiş checkout process'leri bul
     */
    public function findAbandoned(int $hoursOld = 24, int $limit = 100): array
    {
        try {
            $cutoffTime = Carbon::now()->subHours($hoursOld);

            $data = DB::table($this->table)
                ->whereIn('status', ['initiated', 'in_progress'])
                ->where('updated_at', '<', $cutoffTime)
                ->where('deleted_at', null)
                ->orderBy('updated_at', 'asc')
                ->limit($limit)
                ->get();

            return $data->map(fn($item) => $this->mapToDomainEntity($item))->toArray();

        } catch (\Exception $e) {
            Log::error('Find abandoned checkout processes failed', [
                'hours_old' => $hoursOld,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Tamamlanmamış checkout process'leri temizle
     */
    public function clearIncomplete(int $daysOld = 7): int
    {
        try {
            $cutoffTime = Carbon::now()->subDays($daysOld);

            $count = DB::table($this->table)
                ->whereIn('status', ['initiated', 'in_progress'])
                ->where('created_at', '<', $cutoffTime)
                ->where('deleted_at', null)
                ->update([
                    'deleted_at' => Carbon::now()
                ]);

            Log::info('Incomplete checkout processes cleared', [
                'days_old' => $daysOld,
                'count' => $count
            ]);

            return $count;

        } catch (\Exception $e) {
            Log::error('Clear incomplete checkout processes failed', [
                'days_old' => $daysOld,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Checkout istatistiklerini al
     */
    public function getCheckoutStatistics(): array
    {
        try {
            $stats = DB::table($this->table)
                ->selectRaw('
                    COUNT(*) as total_checkouts,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_checkouts,
                    COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_checkouts,
                    COUNT(CASE WHEN status IN ("initiated", "in_progress") THEN 1 END) as active_checkouts,
                    AVG(CASE WHEN status = "completed" THEN TIMESTAMPDIFF(MINUTE, created_at, updated_at) END) as avg_completion_time_minutes
                ')
                ->where('deleted_at', null)
                ->first();

            return [
                'total_checkouts' => $stats->total_checkouts ?? 0,
                'completed_checkouts' => $stats->completed_checkouts ?? 0,
                'cancelled_checkouts' => $stats->cancelled_checkouts ?? 0,
                'active_checkouts' => $stats->active_checkouts ?? 0,
                'completion_rate' => $stats->total_checkouts > 0
                    ? round(($stats->completed_checkouts / $stats->total_checkouts) * 100, 2)
                    : 0,
                'cancellation_rate' => $stats->total_checkouts > 0
                    ? round(($stats->cancelled_checkouts / $stats->total_checkouts) * 100, 2)
                    : 0,
                'average_completion_time_minutes' => round($stats->avg_completion_time_minutes ?? 0, 2),
            ];

        } catch (\Exception $e) {
            Log::error('Get checkout statistics failed', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Checkout dönüşüm oranını al
     */
    public function getConversionRate(): float
    {
        try {
            $stats = DB::table($this->table)
                ->selectRaw('
                    COUNT(*) as total_checkouts,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_checkouts
                ')
                ->where('deleted_at', null)
                ->first();

            if ($stats->total_checkouts > 0) {
                return round(($stats->completed_checkouts / $stats->total_checkouts) * 100, 2);
            }

            return 0.0;

        } catch (\Exception $e) {
            Log::error('Get checkout conversion rate failed', [
                'error' => $e->getMessage()
            ]);

            return 0.0;
        }
    }

    /**
     * Adım bazında dönüşüm oranlarını al
     */
    public function getStepConversionRates(): array
    {
        try {
            $steps = ['shipping_address', 'billing_address', 'shipping_method', 'payment_method', 'review'];
            $rates = [];

            foreach ($steps as $step) {
                $stats = DB::table($this->table)
                    ->selectRaw('
                        COUNT(*) as total_reached,
                        COUNT(CASE WHEN JSON_CONTAINS(completed_steps, ?) THEN 1 END) as completed_step
                    ')
                    ->where('current_step', $step)
                    ->orWhere(function($query) use ($step) {
                        $query->whereRaw('JSON_CONTAINS(completed_steps, ?)', ['"' . $step . '"']);
                    })
                    ->where('deleted_at', null)
                    ->setBindings(['"' . $step . '"', '"' . $step . '"'])
                    ->first();

                $rates[$step] = [
                    'total_reached' => $stats->total_reached ?? 0,
                    'completed' => $stats->completed_step ?? 0,
                    'conversion_rate' => $stats->total_reached > 0
                        ? round(($stats->completed_step / $stats->total_reached) * 100, 2)
                        : 0
                ];
            }

            return $rates;

        } catch (\Exception $e) {
            Log::error('Get step conversion rates failed', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Ortalama checkout süresini al
     */
    public function getAverageCheckoutTime(): float
    {
        try {
            $result = DB::table($this->table)
                ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)) as avg_time')
                ->where('status', 'completed')
                ->where('deleted_at', null)
                ->first();

            return round($result->avg_time ?? 0, 2);

        } catch (\Exception $e) {
            Log::error('Get average checkout time failed', [
                'error' => $e->getMessage()
            ]);

            return 0.0;
        }
    }

    /**
     * En çok terk edilen adımları al
     */
    public function getMostAbandonedSteps(int $limit = 5): array
    {
        try {
            $data = DB::table($this->table)
                ->selectRaw('current_step, COUNT(*) as abandonment_count')
                ->whereIn('status', ['initiated', 'in_progress'])
                ->where('updated_at', '<', Carbon::now()->subHours(24))
                ->where('deleted_at', null)
                ->whereNotNull('current_step')
                ->groupBy('current_step')
                ->orderBy('abandonment_count', 'desc')
                ->limit($limit)
                ->get();

            return $data->map(function($item) {
                return [
                    'step' => $item->current_step,
                    'abandonment_count' => $item->abandonment_count
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Get most abandoned steps failed', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Checkout funnel analizini al
     */
    public function getFunnelAnalysis(): array
    {
        try {
            $steps = ['shipping_address', 'billing_address', 'shipping_method', 'payment_method', 'review'];
            $funnel = [];

            foreach ($steps as $index => $step) {
                $reached = DB::table($this->table)
                    ->where(function($query) use ($step) {
                        $query->where('current_step', $step)
                              ->orWhereRaw('JSON_CONTAINS(completed_steps, ?)', ['"' . $step . '"']);
                    })
                    ->where('deleted_at', null)
                    ->count();

                $completed = DB::table($this->table)
                    ->whereRaw('JSON_CONTAINS(completed_steps, ?)', ['"' . $step . '"'])
                    ->where('deleted_at', null)
                    ->count();

                $funnel[] = [
                    'step' => $step,
                    'step_number' => $index + 1,
                    'reached' => $reached,
                    'completed' => $completed,
                    'completion_rate' => $reached > 0 ? round(($completed / $reached) * 100, 2) : 0,
                    'drop_off_rate' => $reached > 0 ? round((($reached - $completed) / $reached) * 100, 2) : 0
                ];
            }

            return $funnel;

        } catch (\Exception $e) {
            Log::error('Get funnel analysis failed', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Tarih aralığında checkout sayısını al
     */
    public function countByDateRange(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): int
    {
        try {
            return DB::table($this->table)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('deleted_at', null)
                ->count();

        } catch (\Exception $e) {
            Log::error('Count checkouts by date range failed', [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Başarılı checkout'ları al
     */
    public function findCompleted(int $limit = 10, int $offset = 0): array
    {
        return $this->findByStatus('completed', $limit, $offset);
    }

    /**
     * İptal edilen checkout'ları al
     */
    public function findCancelled(int $limit = 10, int $offset = 0): array
    {
        return $this->findByStatus('cancelled', $limit, $offset);
    }

    /**
     * Devam eden checkout'ları al
     */
    public function findInProgress(int $limit = 10, int $offset = 0): array
    {
        try {
            $data = DB::table($this->table)
                ->whereIn('status', ['initiated', 'in_progress'])
                ->where('deleted_at', null)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->offset($offset)
                ->get();

            return $data->map(fn($item) => $this->mapToDomainEntity($item))->toArray();

        } catch (\Exception $e) {
            Log::error('Find in progress checkouts failed', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Domain entity'yi database formatına çevir
     */
    private function mapToDatabase(CheckoutProcess $checkoutProcess): array
    {
        $data = [
            'cart_id' => $checkoutProcess->getCart()->getId(),
            'status' => $checkoutProcess->getStatus(),
            'current_step' => $checkoutProcess->getCurrentStep()?->getValue(),
            'completed_steps' => json_encode($checkoutProcess->getCompletedSteps()),
            'cancellation_reason' => $checkoutProcess->getCancellationReason(),
        ];

        // Shipping address
        if ($checkoutProcess->getShippingAddress()) {
            $data['shipping_address'] = json_encode([
                'address' => $checkoutProcess->getShippingAddress()->getAddress(),
                'city' => $checkoutProcess->getShippingAddress()->getCity(),
                'state' => $checkoutProcess->getShippingAddress()->getState(),
                'postal_code' => $checkoutProcess->getShippingAddress()->getPostalCode(),
                'country' => $checkoutProcess->getShippingAddress()->getCountry(),
                'phone' => $checkoutProcess->getShippingAddress()->getPhone(),
                'first_name' => $checkoutProcess->getShippingAddress()->getFirstName(),
                'last_name' => $checkoutProcess->getShippingAddress()->getLastName(),
            ]);
        }

        // Billing address
        if ($checkoutProcess->getBillingAddress()) {
            $data['billing_address'] = json_encode([
                'address' => $checkoutProcess->getBillingAddress()->getAddress(),
                'city' => $checkoutProcess->getBillingAddress()->getCity(),
                'state' => $checkoutProcess->getBillingAddress()->getState(),
                'postal_code' => $checkoutProcess->getBillingAddress()->getPostalCode(),
                'country' => $checkoutProcess->getBillingAddress()->getCountry(),
                'phone' => $checkoutProcess->getBillingAddress()->getPhone(),
                'first_name' => $checkoutProcess->getBillingAddress()->getFirstName(),
                'last_name' => $checkoutProcess->getBillingAddress()->getLastName(),
            ]);
        }

        // Shipping method
        if ($checkoutProcess->getShippingMethod()) {
            $data['shipping_method'] = $checkoutProcess->getShippingMethod()->getValue();
        }

        // Shipping cost
        if ($checkoutProcess->getShippingCost()) {
            $data['shipping_cost'] = $checkoutProcess->getShippingCost()->getAmount();
        }

        // Payment method
        if ($checkoutProcess->getPaymentMethod()) {
            $data['payment_method'] = $checkoutProcess->getPaymentMethod()->getValue();
        }

        return $data;
    }

    /**
     * Database verisini domain entity'ye çevir
     */
    private function mapToDomainEntity($data): CheckoutProcess
    {
        // Önce Cart entity'yi oluşturmamız gerekiyor
        // Bu basit bir implementasyon - gerçek uygulamada Cart repository'den alınmalı
        $cart = $this->createDummyCart($data->cart_id);

        $checkoutProcess = CheckoutProcess::initiate($cart);

        // ID'yi set et
        $checkoutProcess = $this->setCheckoutProcessId($checkoutProcess, $data->id);

        // Status'u set et
        if ($data->status !== 'initiated') {
            $reflection = new \ReflectionClass($checkoutProcess);
            $statusProperty = $reflection->getProperty('status');
            $statusProperty->setAccessible(true);
            $statusProperty->setValue($checkoutProcess, $data->status);
        }

        // Current step'i set et
        if ($data->current_step) {
            $reflection = new \ReflectionClass($checkoutProcess);
            $currentStepProperty = $reflection->getProperty('currentStep');
            $currentStepProperty->setAccessible(true);
            $currentStepProperty->setValue($checkoutProcess, CheckoutStep::fromString($data->current_step));
        }

        // Completed steps'i set et
        if ($data->completed_steps) {
            $completedSteps = json_decode($data->completed_steps, true) ?? [];
            $reflection = new \ReflectionClass($checkoutProcess);
            $completedStepsProperty = $reflection->getProperty('completedSteps');
            $completedStepsProperty->setAccessible(true);
            $completedStepsProperty->setValue($checkoutProcess, $completedSteps);
        }

        // Shipping address'i set et
        if ($data->shipping_address) {
            $addressData = json_decode($data->shipping_address, true);
            if ($addressData) {
                $shippingAddress = ShippingAddress::create(
                    $addressData['address'] ?? '',
                    $addressData['city'] ?? '',
                    $addressData['state'] ?? '',
                    $addressData['postal_code'] ?? '',
                    $addressData['country'] ?? '',
                    $addressData['phone'] ?? '',
                    $addressData['first_name'] ?? '',
                    $addressData['last_name'] ?? ''
                );
                $checkoutProcess->setShippingAddress($shippingAddress);
            }
        }

        // Billing address'i set et
        if ($data->billing_address) {
            $addressData = json_decode($data->billing_address, true);
            if ($addressData) {
                $billingAddress = BillingAddress::create(
                    $addressData['address'] ?? '',
                    $addressData['city'] ?? '',
                    $addressData['state'] ?? '',
                    $addressData['postal_code'] ?? '',
                    $addressData['country'] ?? '',
                    $addressData['phone'] ?? '',
                    $addressData['first_name'] ?? '',
                    $addressData['last_name'] ?? ''
                );
                $checkoutProcess->setBillingAddress($billingAddress);
            }
        }

        // Shipping method'u set et
        if ($data->shipping_method) {
            $shippingMethod = ShippingMethod::fromString($data->shipping_method);
            $checkoutProcess->setShippingMethod($shippingMethod);
        }

        // Shipping cost'u set et
        if ($data->shipping_cost) {
            $shippingCost = Money::fromAmount($data->shipping_cost);
            $checkoutProcess->setShippingCost($shippingCost);
        }

        // Payment method'u set et
        if ($data->payment_method) {
            $paymentMethod = PaymentMethod::fromString($data->payment_method);
            $checkoutProcess->setPaymentMethod($paymentMethod);
        }

        // Cancellation reason'u set et
        if ($data->cancellation_reason) {
            $reflection = new \ReflectionClass($checkoutProcess);
            $cancellationReasonProperty = $reflection->getProperty('cancellationReason');
            $cancellationReasonProperty->setAccessible(true);
            $cancellationReasonProperty->setValue($checkoutProcess, $data->cancellation_reason);
        }

        return $checkoutProcess;
    }

    /**
     * CheckoutProcess entity'sine ID set et
     */
    private function setCheckoutProcessId(CheckoutProcess $checkoutProcess, int $id): CheckoutProcess
    {
        $reflection = new \ReflectionClass($checkoutProcess);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($checkoutProcess, $id);

        return $checkoutProcess;
    }

    /**
     * Dummy Cart entity oluştur
     * Gerçek implementasyonda Cart repository'den alınmalı
     */
    private function createDummyCart(int $cartId): Cart
    {
        // Bu basit bir implementasyon
        // Gerçek uygulamada Cart repository'den alınmalı
        $cart = Cart::createForUser(1); // Dummy user ID

        $reflection = new \ReflectionClass($cart);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($cart, $cartId);

        return $cart;
    }
}