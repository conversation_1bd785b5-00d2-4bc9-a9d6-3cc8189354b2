<?php

namespace App\Infrastructure\Cart\Repositories;

use App\Domain\Cart\Entities\CheckoutProcess;
use App\Domain\Cart\ValueObjects\CheckoutStep;
use App\Domain\Cart\Repositories\CheckoutRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * CacheCheckoutRepository
 * Cache wrapper for CheckoutRepository - Decorator pattern
 */
class CacheCheckoutRepository implements CheckoutRepositoryInterface
{
    private const CACHE_TTL = 1800; // 30 dakika (checkout daha dinamik)
    private const CACHE_PREFIX = 'checkout:';

    public function __construct(
        private CheckoutRepositoryInterface $repository
    ) {}

    /**
     * Checkout process'i kaydet
     */
    public function save(CheckoutProcess $checkoutProcess): CheckoutProcess
    {
        $savedCheckoutProcess = $this->repository->save($checkoutProcess);

        // Cache'i güncelle
        $this->cacheCheckoutProcess($savedCheckoutProcess);

        return $savedCheckoutProcess;
    }

    /**
     * ID ile checkout process bul
     */
    public function findById(int $id): ?CheckoutProcess
    {
        $cacheKey = $this->getCacheKey('id', $id);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($id) {
            return $this->repository->findById($id);
        });
    }

    /**
     * Cart ID ile checkout process bul
     */
    public function findByCartId(int $cartId): ?CheckoutProcess
    {
        $cacheKey = $this->getCacheKey('cart_id', $cartId);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($cartId) {
            return $this->repository->findByCartId($cartId);
        });
    }

    /**
     * Kullanıcı ID ile aktif checkout process bul
     */
    public function findActiveByUserId(int $userId): ?CheckoutProcess
    {
        $cacheKey = $this->getCacheKey('active_user', $userId);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId) {
            return $this->repository->findActiveByUserId($userId);
        });
    }

    /**
     * Session ID ile aktif checkout process bul
     */
    public function findActiveBySessionId(string $sessionId): ?CheckoutProcess
    {
        $cacheKey = $this->getCacheKey('active_session', $sessionId);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($sessionId) {
            return $this->repository->findActiveBySessionId($sessionId);
        });
    }

    /**
     * Duruma göre checkout process'leri bul
     */
    public function findByStatus(string $status, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey('status', $status, $limit, $offset);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($status, $limit, $offset) {
            return $this->repository->findByStatus($status, $limit, $offset);
        });
    }

    /**
     * Adıma göre checkout process'leri bul
     */
    public function findByCurrentStep(CheckoutStep $step, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey('step', $step->getValue(), $limit, $offset);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($step, $limit, $offset) {
            return $this->repository->findByCurrentStep($step, $limit, $offset);
        });
    }

    /**
     * Checkout process'i sil
     */
    public function delete(CheckoutProcess $checkoutProcess): bool
    {
        $result = $this->repository->delete($checkoutProcess);

        if ($result) {
            $this->clearCheckoutProcessCache($checkoutProcess->getId());
        }

        return $result;
    }

    /**
     * ID ile checkout process'i sil
     */
    public function deleteById(int $id): bool
    {
        $result = $this->repository->deleteById($id);

        if ($result) {
            $this->clearCheckoutProcessCache($id);
        }

        return $result;
    }

    /**
     * Checkout process var mı kontrol et
     */
    public function exists(int $id): bool
    {
        $cacheKey = $this->getCacheKey('exists', $id);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($id) {
            return $this->repository->exists($id);
        });
    }

    /**
     * Cart için aktif checkout var mı kontrol et
     */
    public function existsActiveForCart(int $cartId): bool
    {
        $cacheKey = $this->getCacheKey('exists_active_cart', $cartId);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($cartId) {
            return $this->repository->existsActiveForCart($cartId);
        });
    }

    /**
     * Kullanıcı için aktif checkout var mı kontrol et
     */
    public function existsActiveForUser(int $userId): bool
    {
        $cacheKey = $this->getCacheKey('exists_active_user', $userId);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId) {
            return $this->repository->existsActiveForUser($userId);
        });
    }

    /**
     * Terk edilmiş checkout process'leri bul
     */
    public function findAbandoned(int $hoursOld = 24, int $limit = 100): array
    {
        // Abandoned checkout'lar cache'lenmez (dinamik veri)
        return $this->repository->findAbandoned($hoursOld, $limit);
    }

    /**
     * Tamamlanmamış checkout process'leri temizle
     */
    public function clearIncomplete(int $daysOld = 7): int
    {
        $result = $this->repository->clearIncomplete($daysOld);

        // Cache'i temizle
        $this->clearAllCaches();

        return $result;
    }

    /**
     * Checkout istatistiklerini al
     */
    public function getCheckoutStatistics(): array
    {
        $cacheKey = $this->getCacheKey('statistics');

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return $this->repository->getCheckoutStatistics();
        });
    }

    /**
     * Checkout dönüşüm oranını al
     */
    public function getConversionRate(): float
    {
        $cacheKey = $this->getCacheKey('conversion_rate');

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return $this->repository->getConversionRate();
        });
    }

    /**
     * Adım bazında dönüşüm oranlarını al
     */
    public function getStepConversionRates(): array
    {
        $cacheKey = $this->getCacheKey('step_conversion_rates');

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return $this->repository->getStepConversionRates();
        });
    }

    /**
     * Ortalama checkout süresini al
     */
    public function getAverageCheckoutTime(): float
    {
        $cacheKey = $this->getCacheKey('average_checkout_time');

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return $this->repository->getAverageCheckoutTime();
        });
    }

    /**
     * En çok terk edilen adımları al
     */
    public function getMostAbandonedSteps(int $limit = 5): array
    {
        $cacheKey = $this->getCacheKey('most_abandoned_steps', $limit);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($limit) {
            return $this->repository->getMostAbandonedSteps($limit);
        });
    }

    /**
     * Checkout funnel analizini al
     */
    public function getFunnelAnalysis(): array
    {
        $cacheKey = $this->getCacheKey('funnel_analysis');

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return $this->repository->getFunnelAnalysis();
        });
    }

    /**
     * Tarih aralığında checkout sayısını al
     */
    public function countByDateRange(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): int
    {
        $cacheKey = $this->getCacheKey('count_date_range', $startDate->toDateString(), $endDate->toDateString());

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($startDate, $endDate) {
            return $this->repository->countByDateRange($startDate, $endDate);
        });
    }

    /**
     * Başarılı checkout'ları al
     */
    public function findCompleted(int $limit = 10, int $offset = 0): array
    {
        return $this->findByStatus('completed', $limit, $offset);
    }

    /**
     * İptal edilen checkout'ları al
     */
    public function findCancelled(int $limit = 10, int $offset = 0): array
    {
        return $this->findByStatus('cancelled', $limit, $offset);
    }

    /**
     * Devam eden checkout'ları al
     */
    public function findInProgress(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey('in_progress', $limit, $offset);

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($limit, $offset) {
            return $this->repository->findInProgress($limit, $offset);
        });
    }

    /**
     * Cache key oluştur
     */
    private function getCacheKey(string $type, ...$params): string
    {
        $key = self::CACHE_PREFIX . $type;

        if (!empty($params)) {
            $key .= ':' . implode(':', $params);
        }

        return $key;
    }

    /**
     * Checkout process'i cache'le
     */
    private function cacheCheckoutProcess(CheckoutProcess $checkoutProcess): void
    {
        try {
            $id = $checkoutProcess->getId();
            $cartId = $checkoutProcess->getCart()->getId();

            // ID ile cache'le
            $cacheKey = $this->getCacheKey('id', $id);
            Cache::put($cacheKey, $checkoutProcess, self::CACHE_TTL);

            // Cart ID ile cache'le
            $cacheKey = $this->getCacheKey('cart_id', $cartId);
            Cache::put($cacheKey, $checkoutProcess, self::CACHE_TTL);

            Log::debug('Checkout process cached', ['checkout_id' => $id]);

        } catch (\Exception $e) {
            Log::warning('Failed to cache checkout process', [
                'checkout_id' => $checkoutProcess->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Checkout process cache'ini temizle
     */
    private function clearCheckoutProcessCache(int $checkoutProcessId): void
    {
        try {
            $patterns = [
                $this->getCacheKey('id', $checkoutProcessId),
                $this->getCacheKey('cart_id', '*'),
                $this->getCacheKey('active_user', '*'),
                $this->getCacheKey('active_session', '*'),
                $this->getCacheKey('status', '*'),
                $this->getCacheKey('step', '*'),
                $this->getCacheKey('exists', $checkoutProcessId),
                $this->getCacheKey('exists_active_cart', '*'),
                $this->getCacheKey('exists_active_user', '*'),
            ];

            foreach ($patterns as $pattern) {
                if (strpos($pattern, '*') !== false) {
                    // Wildcard pattern - tüm ilgili cache'leri temizle
                    $this->clearCacheByPattern($pattern);
                } else {
                    // Exact key - direkt sil
                    Cache::forget($pattern);
                }
            }

            // İstatistik cache'lerini de temizle
            $this->clearStatisticsCaches();

            Log::debug('Checkout process cache cleared', ['checkout_id' => $checkoutProcessId]);

        } catch (\Exception $e) {
            Log::warning('Failed to clear checkout process cache', [
                'checkout_id' => $checkoutProcessId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * İstatistik cache'lerini temizle
     */
    private function clearStatisticsCaches(): void
    {
        $statisticsKeys = [
            'statistics',
            'conversion_rate',
            'step_conversion_rates',
            'average_checkout_time',
            'most_abandoned_steps',
            'funnel_analysis',
        ];

        foreach ($statisticsKeys as $key) {
            $this->clearCacheByPattern($this->getCacheKey($key, '*'));
        }
    }

    /**
     * Pattern ile cache temizle
     */
    private function clearCacheByPattern(string $pattern): void
    {
        try {
            // Laravel cache store'a göre farklı implementasyonlar gerekebilir
            // Bu basit bir implementasyon
            $prefix = str_replace('*', '', $pattern);

            // Redis kullanılıyorsa
            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                $redis = Cache::getStore()->getRedis();
                $keys = $redis->keys($prefix . '*');
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // Diğer cache store'lar için genel temizlik
                // Bu durumda tüm cache'i temizlemek gerekebilir
                Log::warning('Cache pattern clearing not fully supported for current cache store', [
                    'pattern' => $pattern
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to clear cache by pattern', [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Tüm cache'leri temizle
     */
    private function clearAllCaches(): void
    {
        try {
            $this->clearCacheByPattern(self::CACHE_PREFIX . '*');
            Log::debug('All checkout caches cleared');

        } catch (\Exception $e) {
            Log::warning('Failed to clear all checkout caches', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
