<?php

namespace App\Infrastructure\Cart\Services;

use App\Domain\Cart\Entities\Cart;
use App\Domain\Cart\Entities\CheckoutProcess;
use App\Domain\Cart\ValueObjects\ShippingAddress;
use App\Domain\Cart\ValueObjects\BillingAddress;
use App\Domain\Cart\ValueObjects\PaymentMethod;
use App\Domain\Cart\ValueObjects\ShippingMethod;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

/**
 * CheckoutValidationService
 * Checkout validasyon infrastructure service
 */
class CheckoutValidationService
{
    private array $validationRules;
    private array $requiredFields;

    public function __construct()
    {
        $this->validationRules = config('checkout.validation.rules', []);
        $this->requiredFields = config('checkout.validation.required_fields', []);
    }

    /**
     * Cart'ı checkout için validate et
     */
    public function validateCartForCheckout(Cart $cart): array
    {
        $errors = [];

        try {
            // 1. Sepet boş mu kontrolü
            if ($cart->isEmpty()) {
                $errors[] = 'Cart is empty';
            }

            // 2. Minimum sipariş tutarı kontrolü
            $minOrderAmount = config('checkout.validation.min_order_amount', 0);
            if ($cart->getTotal()->getTotal()->getAmount() < $minOrderAmount) {
                $errors[] = "Minimum order amount is {$minOrderAmount}";
            }

            // 3. Maksimum sipariş tutarı kontrolü
            $maxOrderAmount = config('checkout.validation.max_order_amount', 999999);
            if ($cart->getTotal()->getTotal()->getAmount() > $maxOrderAmount) {
                $errors[] = "Maximum order amount is {$maxOrderAmount}";
            }

            // 4. Ürün stok kontrolü
            $stockErrors = $this->validateCartItemsStock($cart);
            $errors = array_merge($errors, $stockErrors);

            // 5. Ürün aktiflik kontrolü
            $activeErrors = $this->validateCartItemsActive($cart);
            $errors = array_merge($errors, $activeErrors);

            // 6. Kupon geçerliliği kontrolü
            if ($cart->getCouponCode()) {
                $couponErrors = $this->validateCoupon($cart);
                $errors = array_merge($errors, $couponErrors);
            }

            // 7. Kullanıcı kısıtlamaları kontrolü
            if ($cart->getUserId()) {
                $userErrors = $this->validateUserRestrictions($cart);
                $errors = array_merge($errors, $userErrors);
            }

            Log::info('Cart validation completed', [
                'cart_id' => $cart->getId(),
                'error_count' => count($errors),
                'is_valid' => empty($errors)
            ]);

            return $errors;

        } catch (\Exception $e) {
            Log::error('Cart validation failed', [
                'cart_id' => $cart->getId(),
                'error' => $e->getMessage()
            ]);

            return ['Validation service error: ' . $e->getMessage()];
        }
    }

    /**
     * Checkout process'i validate et
     */
    public function validateCheckoutProcess(CheckoutProcess $checkoutProcess): array
    {
        $errors = [];

        try {
            // 1. Cart validasyonu
            $cartErrors = $this->validateCartForCheckout($checkoutProcess->getCart());
            $errors = array_merge($errors, $cartErrors);

            // 2. Shipping address validasyonu
            if ($checkoutProcess->getShippingAddress()) {
                $shippingErrors = $this->validateShippingAddress($checkoutProcess->getShippingAddress());
                $errors = array_merge($errors, $shippingErrors);
            } else {
                $errors[] = 'Shipping address is required';
            }

            // 3. Billing address validasyonu
            if ($checkoutProcess->getBillingAddress()) {
                $billingErrors = $this->validateBillingAddress($checkoutProcess->getBillingAddress());
                $errors = array_merge($errors, $billingErrors);
            } else {
                $errors[] = 'Billing address is required';
            }

            // 4. Shipping method validasyonu
            if ($checkoutProcess->getShippingMethod()) {
                $shippingMethodErrors = $this->validateShippingMethod($checkoutProcess);
                $errors = array_merge($errors, $shippingMethodErrors);
            } else {
                $errors[] = 'Shipping method is required';
            }

            // 5. Payment method validasyonu
            if ($checkoutProcess->getPaymentMethod()) {
                $paymentErrors = $this->validatePaymentMethod($checkoutProcess->getPaymentMethod());
                $errors = array_merge($errors, $paymentErrors);
            } else {
                $errors[] = 'Payment method is required';
            }

            // 6. Checkout process durumu kontrolü
            $statusErrors = $this->validateCheckoutStatus($checkoutProcess);
            $errors = array_merge($errors, $statusErrors);

            Log::info('Checkout process validation completed', [
                'checkout_id' => $checkoutProcess->getId(),
                'error_count' => count($errors),
                'is_valid' => empty($errors)
            ]);

            return $errors;

        } catch (\Exception $e) {
            Log::error('Checkout process validation failed', [
                'checkout_id' => $checkoutProcess->getId(),
                'error' => $e->getMessage()
            ]);

            return ['Checkout validation service error: ' . $e->getMessage()];
        }
    }

    /**
     * Shipping address'i validate et
     */
    public function validateShippingAddress(ShippingAddress $address): array
    {
        $errors = [];

        try {
            // Required field kontrolü
            if (empty($address->getFirstName())) {
                $errors[] = 'First name is required';
            }

            if (empty($address->getLastName())) {
                $errors[] = 'Last name is required';
            }

            if (empty($address->getAddress())) {
                $errors[] = 'Address is required';
            }

            if (empty($address->getCity())) {
                $errors[] = 'City is required';
            }

            if (empty($address->getCountry())) {
                $errors[] = 'Country is required';
            }

            // Postal code format kontrolü
            $postalCodeErrors = $this->validatePostalCode($address->getPostalCode(), $address->getCountry());
            $errors = array_merge($errors, $postalCodeErrors);

            // Phone number format kontrolü
            if ($address->getPhone()) {
                $phoneErrors = $this->validatePhoneNumber($address->getPhone(), $address->getCountry());
                $errors = array_merge($errors, $phoneErrors);
            }

            // Ülke ve eyalet kombinasyonu kontrolü
            $locationErrors = $this->validateLocationCombination($address->getCountry(), $address->getState());
            $errors = array_merge($errors, $locationErrors);

            // Kargo bölgesi kontrolü
            $shippingZoneErrors = $this->validateShippingZone($address);
            $errors = array_merge($errors, $shippingZoneErrors);

            return $errors;

        } catch (\Exception $e) {
            Log::error('Shipping address validation failed', [
                'error' => $e->getMessage()
            ]);

            return ['Shipping address validation error: ' . $e->getMessage()];
        }
    }

    /**
     * Billing address'i validate et
     */
    public function validateBillingAddress(BillingAddress $address): array
    {
        $errors = [];

        try {
            // Required field kontrolü
            if (empty($address->getFirstName())) {
                $errors[] = 'Billing first name is required';
            }

            if (empty($address->getLastName())) {
                $errors[] = 'Billing last name is required';
            }

            if (empty($address->getAddress())) {
                $errors[] = 'Billing address is required';
            }

            if (empty($address->getCity())) {
                $errors[] = 'Billing city is required';
            }

            if (empty($address->getCountry())) {
                $errors[] = 'Billing country is required';
            }

            // Postal code format kontrolü
            $postalCodeErrors = $this->validatePostalCode($address->getPostalCode(), $address->getCountry());
            $errors = array_merge($errors, array_map(function($error) {
                return 'Billing ' . lcfirst($error);
            }, $postalCodeErrors));

            // Phone number format kontrolü
            if ($address->getPhone()) {
                $phoneErrors = $this->validatePhoneNumber($address->getPhone(), $address->getCountry());
                $errors = array_merge($errors, array_map(function($error) {
                    return 'Billing ' . lcfirst($error);
                }, $phoneErrors));
            }

            // Ülke ve eyalet kombinasyonu kontrolü
            $locationErrors = $this->validateLocationCombination($address->getCountry(), $address->getState());
            $errors = array_merge($errors, array_map(function($error) {
                return 'Billing ' . lcfirst($error);
            }, $locationErrors));

            return $errors;

        } catch (\Exception $e) {
            Log::error('Billing address validation failed', [
                'error' => $e->getMessage()
            ]);

            return ['Billing address validation error: ' . $e->getMessage()];
        }
    }

    /**
     * Payment method'u validate et
     */
    public function validatePaymentMethod(PaymentMethod $paymentMethod): array
    {
        $errors = [];

        try {
            $allowedMethods = config('checkout.validation.allowed_payment_methods', []);

            if (!empty($allowedMethods) && !in_array($paymentMethod->getValue(), $allowedMethods)) {
                $errors[] = 'Payment method is not allowed';
            }

            // Payment method özel validasyonları
            switch ($paymentMethod->getValue()) {
                case 'credit_card':
                    $errors = array_merge($errors, $this->validateCreditCardMethod());
                    break;
                case 'bank_transfer':
                    $errors = array_merge($errors, $this->validateBankTransferMethod());
                    break;
                case 'paypal':
                    $errors = array_merge($errors, $this->validatePayPalMethod());
                    break;
            }

            return $errors;

        } catch (\Exception $e) {
            Log::error('Payment method validation failed', [
                'payment_method' => $paymentMethod->getValue(),
                'error' => $e->getMessage()
            ]);

            return ['Payment method validation error: ' . $e->getMessage()];
        }
    }

    /**
     * Cart items stok kontrolü
     */
    private function validateCartItemsStock(Cart $cart): array
    {
        $errors = [];

        foreach ($cart->getItems() as $item) {
            // Bu basit bir implementasyon - gerçek uygulamada inventory service'den kontrol edilmeli
            $cacheKey = "product_stock:{$item->getProductId()}";
            $availableStock = Cache::get($cacheKey, 0);

            if ($item->getQuantity()->getValue() > $availableStock) {
                $errors[] = "Insufficient stock for product {$item->getProductId()}";
            }
        }

        return $errors;
    }

    /**
     * Cart items aktiflik kontrolü
     */
    private function validateCartItemsActive(Cart $cart): array
    {
        $errors = [];

        foreach ($cart->getItems() as $item) {
            $cacheKey = "product_active:{$item->getProductId()}";
            $isActive = Cache::get($cacheKey, true);

            if (!$isActive) {
                $errors[] = "Product {$item->getProductId()} is no longer available";
            }
        }

        return $errors;
    }

    /**
     * Kupon geçerliliği kontrolü
     */
    private function validateCoupon(Cart $cart): array
    {
        $errors = [];

        try {
            $couponCode = $cart->getCouponCode();
            $cacheKey = "coupon_validation:{$couponCode}";

            $couponData = Cache::remember($cacheKey, 300, function () use ($couponCode) {
                // Bu basit bir implementasyon - gerçek uygulamada coupon service'den kontrol edilmeli
                return [
                    'is_valid' => true,
                    'is_expired' => false,
                    'usage_limit_reached' => false,
                    'min_order_amount' => 0,
                ];
            });

            if (!$couponData['is_valid']) {
                $errors[] = 'Coupon code is invalid';
            }

            if ($couponData['is_expired']) {
                $errors[] = 'Coupon code has expired';
            }

            if ($couponData['usage_limit_reached']) {
                $errors[] = 'Coupon usage limit has been reached';
            }

            if ($cart->getTotal()->getTotal()->getAmount() < $couponData['min_order_amount']) {
                $errors[] = "Minimum order amount for this coupon is {$couponData['min_order_amount']}";
            }

        } catch (\Exception $e) {
            Log::error('Coupon validation failed', [
                'coupon_code' => $cart->getCouponCode(),
                'error' => $e->getMessage()
            ]);

            $errors[] = 'Coupon validation failed';
        }

        return $errors;
    }

    /**
     * Kullanıcı kısıtlamaları kontrolü
     */
    private function validateUserRestrictions(Cart $cart): array
    {
        $errors = [];

        try {
            $userId = $cart->getUserId();
            $cacheKey = "user_restrictions:{$userId}";

            $restrictions = Cache::remember($cacheKey, 600, function () use ($userId) {
                // Bu basit bir implementasyon - gerçek uygulamada user service'den kontrol edilmeli
                return [
                    'is_blocked' => false,
                    'is_verified' => true,
                    'daily_order_limit' => 10,
                    'daily_order_count' => 0,
                ];
            });

            if ($restrictions['is_blocked']) {
                $errors[] = 'User account is blocked';
            }

            if (!$restrictions['is_verified']) {
                $errors[] = 'User account is not verified';
            }

            if ($restrictions['daily_order_count'] >= $restrictions['daily_order_limit']) {
                $errors[] = 'Daily order limit exceeded';
            }

        } catch (\Exception $e) {
            Log::error('User restrictions validation failed', [
                'user_id' => $cart->getUserId(),
                'error' => $e->getMessage()
            ]);

            $errors[] = 'User validation failed';
        }

        return $errors;
    }

    /**
     * Shipping method kontrolü
     */
    private function validateShippingMethod(CheckoutProcess $checkoutProcess): array
    {
        $errors = [];

        try {
            $shippingMethod = $checkoutProcess->getShippingMethod();
            $shippingAddress = $checkoutProcess->getShippingAddress();

            // Shipping method availability kontrolü
            $availableMethods = $this->getAvailableShippingMethods($shippingAddress);

            if (!in_array($shippingMethod->getValue(), $availableMethods)) {
                $errors[] = 'Shipping method is not available for this address';
            }

            // Minimum order amount kontrolü
            $methodConfig = config("shipping.methods.{$shippingMethod->getValue()}", []);
            $minAmount = $methodConfig['min_order_amount'] ?? 0;

            if ($checkoutProcess->getCart()->getTotal()->getTotal()->getAmount() < $minAmount) {
                $errors[] = "Minimum order amount for this shipping method is {$minAmount}";
            }

        } catch (\Exception $e) {
            Log::error('Shipping method validation failed', [
                'shipping_method' => $checkoutProcess->getShippingMethod()->getValue(),
                'error' => $e->getMessage()
            ]);

            $errors[] = 'Shipping method validation failed';
        }

        return $errors;
    }

    /**
     * Checkout status kontrolü
     */
    private function validateCheckoutStatus(CheckoutProcess $checkoutProcess): array
    {
        $errors = [];

        if ($checkoutProcess->getStatus() === CheckoutProcess::STATUS_CANCELLED) {
            $errors[] = 'Checkout process has been cancelled';
        }

        if ($checkoutProcess->getStatus() === CheckoutProcess::STATUS_COMPLETED) {
            $errors[] = 'Checkout process has already been completed';
        }

        return $errors;
    }

    /**
     * Postal code format kontrolü
     */
    private function validatePostalCode(string $postalCode, string $country): array
    {
        $errors = [];

        $patterns = [
            'US' => '/^\d{5}(-\d{4})?$/',
            'TR' => '/^\d{5}$/',
            'GB' => '/^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i',
            'DE' => '/^\d{5}$/',
            'FR' => '/^\d{5}$/',
        ];

        if (isset($patterns[$country])) {
            if (!preg_match($patterns[$country], $postalCode)) {
                $errors[] = 'Invalid postal code format';
            }
        }

        return $errors;
    }

    /**
     * Phone number format kontrolü
     */
    private function validatePhoneNumber(string $phone, string $country): array
    {
        $errors = [];

        // Basit phone validation - gerçek uygulamada libphonenumber kullanılabilir
        $cleanPhone = preg_replace('/[^\d+]/', '', $phone);

        if (strlen($cleanPhone) < 10) {
            $errors[] = 'Phone number is too short';
        }

        if (strlen($cleanPhone) > 15) {
            $errors[] = 'Phone number is too long';
        }

        return $errors;
    }

    /**
     * Ülke ve eyalet kombinasyonu kontrolü
     */
    private function validateLocationCombination(string $country, ?string $state): array
    {
        $errors = [];

        $requiredStates = ['US', 'CA', 'AU']; // Eyalet zorunlu olan ülkeler

        if (in_array($country, $requiredStates) && empty($state)) {
            $errors[] = 'State is required for this country';
        }

        return $errors;
    }

    /**
     * Kargo bölgesi kontrolü
     */
    private function validateShippingZone(ShippingAddress $address): array
    {
        $errors = [];

        try {
            $cacheKey = "shipping_zone:{$address->getCountry()}:{$address->getState()}:{$address->getPostalCode()}";

            $isShippable = Cache::remember($cacheKey, 3600, function () use ($address) {
                // Bu basit bir implementasyon - gerçek uygulamada shipping service'den kontrol edilmeli
                $restrictedCountries = config('shipping.restricted_countries', []);
                return !in_array($address->getCountry(), $restrictedCountries);
            });

            if (!$isShippable) {
                $errors[] = 'Shipping is not available to this location';
            }

        } catch (\Exception $e) {
            Log::error('Shipping zone validation failed', [
                'country' => $address->getCountry(),
                'error' => $e->getMessage()
            ]);

            $errors[] = 'Shipping zone validation failed';
        }

        return $errors;
    }

    /**
     * Mevcut shipping method'ları al
     */
    private function getAvailableShippingMethods(?ShippingAddress $address): array
    {
        if (!$address) {
            return [];
        }

        $cacheKey = "available_shipping_methods:{$address->getCountry()}:{$address->getState()}";

        return Cache::remember($cacheKey, 1800, function () use ($address) {
            // Bu basit bir implementasyon - gerçek uygulamada shipping service'den alınmalı
            $allMethods = config('shipping.methods', []);
            $availableMethods = [];

            foreach ($allMethods as $method => $config) {
                if ($this->isShippingMethodAvailable($method, $address)) {
                    $availableMethods[] = $method;
                }
            }

            return $availableMethods;
        });
    }

    /**
     * Shipping method müsaitlik kontrolü
     */
    private function isShippingMethodAvailable(string $method, ShippingAddress $address): bool
    {
        $config = config("shipping.methods.{$method}", []);

        // Ülke kısıtlaması kontrolü
        if (isset($config['allowed_countries']) && !in_array($address->getCountry(), $config['allowed_countries'])) {
            return false;
        }

        // Yasaklı ülke kontrolü
        if (isset($config['restricted_countries']) && in_array($address->getCountry(), $config['restricted_countries'])) {
            return false;
        }

        return true;
    }

    /**
     * Credit card method validasyonu
     */
    private function validateCreditCardMethod(): array
    {
        $errors = [];

        // Credit card gateway müsaitlik kontrolü
        if (!config('payment.gateways.credit_card.enabled', false)) {
            $errors[] = 'Credit card payment is currently unavailable';
        }

        return $errors;
    }

    /**
     * Bank transfer method validasyonu
     */
    private function validateBankTransferMethod(): array
    {
        $errors = [];

        // Bank transfer müsaitlik kontrolü
        if (!config('payment.gateways.bank_transfer.enabled', false)) {
            $errors[] = 'Bank transfer payment is currently unavailable';
        }

        return $errors;
    }

    /**
     * PayPal method validasyonu
     */
    private function validatePayPalMethod(): array
    {
        $errors = [];

        // PayPal müsaitlik kontrolü
        if (!config('payment.gateways.paypal.enabled', false)) {
            $errors[] = 'PayPal payment is currently unavailable';
        }

        return $errors;
    }

    /**
     * Validation cache'ini temizle
     */
    public function clearValidationCache(): void
    {
        try {
            $patterns = [
                'product_stock:*',
                'product_active:*',
                'coupon_validation:*',
                'user_restrictions:*',
                'shipping_zone:*',
                'available_shipping_methods:*',
            ];

            foreach ($patterns as $pattern) {
                // Pattern-based cache clearing
                // Bu implementation cache store'a göre değişebilir
                Cache::flush(); // Geçici çözüm
            }

            Log::info('Validation cache cleared');

        } catch (\Exception $e) {
            Log::error('Failed to clear validation cache', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Validation kurallarını test et
     */
    public function testValidationRules(): array
    {
        try {
            $results = [];

            // Test 1: Postal code validation
            $testPostalCodes = [
                ['12345', 'US', true],
                ['12345-6789', 'US', true],
                ['ABCDE', 'US', false],
                ['34000', 'TR', true],
                ['340000', 'TR', false],
            ];

            foreach ($testPostalCodes as [$code, $country, $expected]) {
                $errors = $this->validatePostalCode($code, $country);
                $isValid = empty($errors);
                $results['postal_code'][] = [
                    'code' => $code,
                    'country' => $country,
                    'expected' => $expected,
                    'actual' => $isValid,
                    'status' => $expected === $isValid ? 'pass' : 'fail'
                ];
            }

            // Test 2: Phone number validation
            $testPhones = [
                ['+1234567890', 'US', true],
                ['123', 'US', false],
                ['+123456789012345678', 'US', false],
            ];

            foreach ($testPhones as [$phone, $country, $expected]) {
                $errors = $this->validatePhoneNumber($phone, $country);
                $isValid = empty($errors);
                $results['phone_number'][] = [
                    'phone' => $phone,
                    'country' => $country,
                    'expected' => $expected,
                    'actual' => $isValid,
                    'status' => $expected === $isValid ? 'pass' : 'fail'
                ];
            }

            return $results;

        } catch (\Exception $e) {
            Log::error('Validation rules test failed', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage(),
                'status' => 'fail'
            ];
        }
    }

    /**
     * Validation istatistiklerini al
     */
    public function getValidationStats(): array
    {
        try {
            return [
                'total_validations' => Cache::get('validation_stats:total', 0),
                'failed_validations' => Cache::get('validation_stats:failed', 0),
                'success_rate' => $this->calculateValidationSuccessRate(),
                'most_common_errors' => Cache::get('validation_stats:common_errors', []),
                'validation_time_avg' => Cache::get('validation_stats:avg_time', 0),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get validation stats', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validation başarı oranını hesapla
     */
    private function calculateValidationSuccessRate(): float
    {
        $total = Cache::get('validation_stats:total', 0);
        $failed = Cache::get('validation_stats:failed', 0);

        if ($total === 0) {
            return 0.0;
        }

        return round((($total - $failed) / $total) * 100, 2);
    }

    /**
     * Validation istatistiklerini güncelle
     */
    public function updateValidationStats(bool $isValid, array $errors = []): void
    {
        try {
            // Total validation sayısını artır
            $total = Cache::get('validation_stats:total', 0);
            Cache::put('validation_stats:total', $total + 1, 86400);

            // Failed validation sayısını artır
            if (!$isValid) {
                $failed = Cache::get('validation_stats:failed', 0);
                Cache::put('validation_stats:failed', $failed + 1, 86400);

                // Common errors'ı güncelle
                $commonErrors = Cache::get('validation_stats:common_errors', []);
                foreach ($errors as $error) {
                    $commonErrors[$error] = ($commonErrors[$error] ?? 0) + 1;
                }
                Cache::put('validation_stats:common_errors', $commonErrors, 86400);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to update validation stats', [
                'error' => $e->getMessage()
            ]);
        }
    }
}