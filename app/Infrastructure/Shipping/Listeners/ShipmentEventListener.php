<?php

namespace App\Infrastructure\Shipping\Listeners;

use App\Domain\Shipping\Events\ShipmentCreated;
use App\Domain\Shipping\Events\ShipmentStatusUpdated;
use App\Domain\Shipping\Events\ShipmentDelivered;
use App\Domain\Shipping\Events\ShipmentFailed;
use App\Domain\Shipping\Events\TrackingUpdated;
use App\Infrastructure\Shipping\Services\ShipmentTrackingService;
use App\Infrastructure\Shipping\Services\ShippingAnalyticsService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Notification;

/**
 * ShipmentEventListener
 * Shipping domain event'lerini dinleyen listener
 */
class ShipmentEventListener
{
    public function __construct(
        private ShipmentTrackingService $trackingService,
        private ShippingAnalyticsService $analyticsService
    ) {}

    /**
     * Shipment oluşturulduğunda
     */
    public function handleShipmentCreated(ShipmentCreated $event): void
    {
        try {
            $shipment = $event->getShipment();

            Log::info('Shipment created event handled', [
                'shipment_id' => $shipment->getId(),
                'order_id' => $shipment->getOrderId(),
                'carrier_code' => $shipment->getCarrierCode(),
                'tracking_number' => $shipment->getTrackingNumber()?->getValue(),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // Initial tracking event ekle
            if ($shipment->getId()) {
                $this->trackingService->addTrackingEvent(
                    $shipment->getId(),
                    'created',
                    'Shipment Created',
                    'Shipment has been created and is being processed',
                    $event->occurredOn(),
                    null,
                    ['source' => 'system'],
                    'system'
                );
            }

            // Cache temizleme
            $this->clearShipmentCache($shipment);

            // Analytics güncelleme (background job)
            Queue::push('update-shipping-analytics', [
                'shipment_id' => $shipment->getId(),
                'event_type' => 'shipment_created',
                'event_data' => [
                    'order_id' => $shipment->getOrderId(),
                    'carrier_code' => $shipment->getCarrierCode(),
                    'service_type' => $shipment->getServiceType(),
                    'total_cost' => $this->getPrivateProperty($shipment, 'totalCost'),
                    'destination_country' => $shipment->getDestinationAddress()->getCountry()
                ]
            ]);

            // Customer notification (background job)
            Queue::push('send-shipment-notification', [
                'shipment_id' => $shipment->getId(),
                'notification_type' => 'shipment_created',
                'tracking_number' => $shipment->getTrackingNumber()?->getValue()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle shipment created event', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Shipment status güncellendiğinde
     */
    public function handleShipmentStatusUpdated(ShipmentStatusUpdated $event): void
    {
        try {
            $shipment = $event->getShipment();
            $oldStatus = $event->getOldStatus();
            $newStatus = $event->getNewStatus();

            Log::info('Shipment status updated event handled', [
                'shipment_id' => $shipment->getId(),
                'tracking_number' => $shipment->getTrackingNumber()?->getValue(),
                'old_status' => $oldStatus->getValue(),
                'new_status' => $newStatus->getValue(),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // Status change tracking event ekle
            if ($shipment->getId()) {
                $this->trackingService->addTrackingEvent(
                    $shipment->getId(),
                    'status_changed',
                    'Status Updated',
                    "Shipment status changed from {$oldStatus->getValue()} to {$newStatus->getValue()}",
                    $event->occurredOn(),
                    null,
                    [
                        'old_status' => $oldStatus->getValue(),
                        'new_status' => $newStatus->getValue(),
                        'source' => 'system'
                    ],
                    'system'
                );
            }

            // Cache temizleme
            $this->clearShipmentCache($shipment);

            // Status-specific actions
            $this->handleStatusSpecificActions($shipment, $newStatus->getValue());

            // Analytics güncelleme
            Queue::push('update-shipping-analytics', [
                'shipment_id' => $shipment->getId(),
                'event_type' => 'status_updated',
                'event_data' => [
                    'old_status' => $oldStatus->getValue(),
                    'new_status' => $newStatus->getValue(),
                    'status_change_duration' => $this->calculateStatusDuration($shipment, $oldStatus->getValue())
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle shipment status updated event', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Shipment teslim edildiğinde
     */
    public function handleShipmentDelivered(ShipmentDelivered $event): void
    {
        try {
            $shipment = $event->getShipment();
            $deliveryInfo = $event->getDeliveryInfo();

            Log::info('Shipment delivered event handled', [
                'shipment_id' => $shipment->getId(),
                'tracking_number' => $shipment->getTrackingNumber()?->getValue(),
                'delivered_to' => $deliveryInfo['delivered_to'] ?? null,
                'delivery_time' => $deliveryInfo['delivered_at'] ?? null,
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // Delivery tracking event ekle
            if ($shipment->getId()) {
                $this->trackingService->addTrackingEvent(
                    $shipment->getId(),
                    'delivered',
                    'Package Delivered',
                    'Package has been successfully delivered',
                    $event->occurredOn(),
                    $deliveryInfo['delivery_location'] ?? null,
                    array_merge($deliveryInfo, ['source' => 'system']),
                    'system'
                );
            }

            // Cache temizleme
            $this->clearShipmentCache($shipment);

            // Delivery confirmation notification
            Queue::push('send-delivery-confirmation', [
                'shipment_id' => $shipment->getId(),
                'delivery_info' => $deliveryInfo,
                'tracking_number' => $shipment->getTrackingNumber()?->getValue()
            ]);

            // Performance metrics güncelleme
            Queue::push('update-delivery-metrics', [
                'shipment_id' => $shipment->getId(),
                'carrier_code' => $shipment->getCarrierCode(),
                'delivery_info' => $deliveryInfo,
                'delivery_duration' => $this->calculateDeliveryDuration($shipment)
            ]);

            // Order completion check
            Queue::push('check-order-completion', [
                'order_id' => $shipment->getOrderId(),
                'completed_shipment_id' => $shipment->getId()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle shipment delivered event', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Shipment başarısız olduğunda
     */
    public function handleShipmentFailed(ShipmentFailed $event): void
    {
        try {
            $shipment = $event->getShipment();
            $failureReason = $event->getFailureReason();

            Log::info('Shipment failed event handled', [
                'shipment_id' => $shipment->getId(),
                'tracking_number' => $shipment->getTrackingNumber()?->getValue(),
                'failure_reason' => $failureReason,
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // Failure tracking event ekle
            if ($shipment->getId()) {
                $this->trackingService->addTrackingEvent(
                    $shipment->getId(),
                    'delivery_failed',
                    'Delivery Failed',
                    "Delivery failed: {$failureReason}",
                    $event->occurredOn(),
                    null,
                    [
                        'failure_reason' => $failureReason,
                        'source' => 'system'
                    ],
                    'system'
                );
            }

            // Cache temizleme
            $this->clearShipmentCache($shipment);

            // Failure notification
            Queue::push('send-delivery-failure-notification', [
                'shipment_id' => $shipment->getId(),
                'failure_reason' => $failureReason,
                'tracking_number' => $shipment->getTrackingNumber()?->getValue()
            ]);

            // Incident creation
            Queue::push('create-shipping-incident', [
                'shipment_id' => $shipment->getId(),
                'incident_type' => 'delivery_failure',
                'description' => $failureReason,
                'severity' => $this->determineFailureSeverity($failureReason)
            ]);

            // Retry scheduling (if applicable)
            $this->scheduleRetryIfApplicable($shipment, $failureReason);

        } catch (\Exception $e) {
            Log::error('Failed to handle shipment failed event', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Tracking güncellendiğinde
     */
    public function handleTrackingUpdated(TrackingUpdated $event): void
    {
        try {
            $shipment = $event->getShipment();
            $trackingEvents = $event->getTrackingEvents();

            Log::info('Tracking updated event handled', [
                'shipment_id' => $shipment->getId(),
                'tracking_number' => $shipment->getTrackingNumber()?->getValue(),
                'events_count' => count($trackingEvents),
                'event_time' => $event->occurredOn()->toISOString()
            ]);

            // Cache temizleme
            $this->clearShipmentCache($shipment);

            // Real-time tracking notification (if enabled)
            if (config('shipping.real_time_notifications', false)) {
                Queue::push('send-tracking-update-notification', [
                    'shipment_id' => $shipment->getId(),
                    'tracking_events' => $trackingEvents,
                    'tracking_number' => $shipment->getTrackingNumber()?->getValue()
                ]);
            }

            // Analytics güncelleme
            Queue::push('update-tracking-analytics', [
                'shipment_id' => $shipment->getId(),
                'carrier_code' => $shipment->getCarrierCode(),
                'events_count' => count($trackingEvents),
                'latest_event' => end($trackingEvents)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle tracking updated event', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Event listener registration
     */
    public function subscribe($events): void
    {
        $events->listen(
            ShipmentCreated::class,
            [ShipmentEventListener::class, 'handleShipmentCreated']
        );

        $events->listen(
            ShipmentStatusUpdated::class,
            [ShipmentEventListener::class, 'handleShipmentStatusUpdated']
        );

        $events->listen(
            ShipmentDelivered::class,
            [ShipmentEventListener::class, 'handleShipmentDelivered']
        );

        $events->listen(
            ShipmentFailed::class,
            [ShipmentEventListener::class, 'handleShipmentFailed']
        );

        $events->listen(
            TrackingUpdated::class,
            [ShipmentEventListener::class, 'handleTrackingUpdated']
        );
    }

    /**
     * Status'a özel aksiyonları handle et
     */
    private function handleStatusSpecificActions($shipment, string $newStatus): void
    {
        switch ($newStatus) {
            case 'shipped':
                Queue::push('send-shipment-shipped-notification', [
                    'shipment_id' => $shipment->getId()
                ]);
                break;

            case 'out_for_delivery':
                Queue::push('send-out-for-delivery-notification', [
                    'shipment_id' => $shipment->getId()
                ]);
                break;

            case 'in_transit':
                // Estimated delivery güncelleme
                Queue::push('update-estimated-delivery', [
                    'shipment_id' => $shipment->getId()
                ]);
                break;
        }
    }

    /**
     * Failure severity belirle
     */
    private function determineFailureSeverity(string $failureReason): string
    {
        $criticalReasons = ['lost', 'damaged', 'stolen'];
        $highReasons = ['address_not_found', 'recipient_refused'];
        
        $reason = strtolower($failureReason);
        
        foreach ($criticalReasons as $critical) {
            if (str_contains($reason, $critical)) {
                return 'critical';
            }
        }
        
        foreach ($highReasons as $high) {
            if (str_contains($reason, $high)) {
                return 'high';
            }
        }
        
        return 'medium';
    }

    /**
     * Retry zamanlaması (uygunsa)
     */
    private function scheduleRetryIfApplicable($shipment, string $failureReason): void
    {
        $retryableReasons = ['recipient_not_available', 'address_issue', 'weather_delay'];
        
        foreach ($retryableReasons as $retryable) {
            if (str_contains(strtolower($failureReason), $retryable)) {
                Queue::push('schedule-delivery-retry', [
                    'shipment_id' => $shipment->getId(),
                    'retry_reason' => $failureReason
                ]);
                break;
            }
        }
    }

    /**
     * Status süresi hesapla
     */
    private function calculateStatusDuration($shipment, string $oldStatus): ?int
    {
        // Implementation would calculate time spent in previous status
        return null; // Placeholder
    }

    /**
     * Teslimat süresi hesapla
     */
    private function calculateDeliveryDuration($shipment): ?int
    {
        $shippedAt = $this->getPrivateProperty($shipment, 'shippedAt');
        $deliveredAt = $this->getPrivateProperty($shipment, 'actualDeliveryAt');
        
        if ($shippedAt && $deliveredAt) {
            return $shippedAt->diffInHours($deliveredAt);
        }
        
        return null;
    }

    /**
     * Shipment cache'ini temizle
     */
    private function clearShipmentCache($shipment): void
    {
        try {
            $shipmentId = $shipment->getId();
            $trackingNumber = $shipment->getTrackingNumber()?->getValue();

            $cacheKeys = [
                "shipment.{$shipmentId}",
                "shipment.summary.{$shipmentId}",
                "tracking_history_{$shipmentId}",
                "tracking_status_{$shipmentId}"
            ];

            if ($trackingNumber) {
                $cacheKeys[] = "shipment.tracking.{$trackingNumber}";
            }

            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            // Tag-based cache clearing
            if (method_exists(Cache::getStore(), 'tags')) {
                Cache::tags([
                    'shipments', 
                    "shipment.{$shipmentId}", 
                    'shipping.analytics'
                ])->flush();
            }

        } catch (\Exception $e) {
            Log::error('Failed to clear shipment cache', [
                'shipment_id' => $shipment->getId(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Private property'yi reflection ile al
     */
    private function getPrivateProperty(object $object, string $property)
    {
        try {
            $reflection = new \ReflectionClass($object);
            $prop = $reflection->getProperty($property);
            $prop->setAccessible(true);
            return $prop->getValue($object);
        } catch (\ReflectionException $e) {
            return null;
        }
    }
}
