<?php

namespace App\Infrastructure\Shipping\Mappers;

use App\Domain\Shipping\Entities\Shipment;
use App\Domain\Shipping\ValueObjects\TrackingNumber;
use App\Domain\Shipping\ValueObjects\DeliveryStatus;
use App\Domain\Shipping\ValueObjects\ShippingAddress;
use App\Domain\Shipping\ValueObjects\PackageInfo;
use App\Infrastructure\Shipping\Models\EloquentShipment;
use App\Infrastructure\Shipping\Models\EloquentCarrierIntegration;
use Carbon\Carbon;
use ReflectionClass;

/**
 * ShipmentMapper
 * Domain Shipment entity ile Eloquent model arasında mapping
 */
class ShipmentMapper
{
    /**
     * Eloquent model'i domain entity'ye çevir
     */
    public function toDomain(EloquentShipment $eloquentShipment): Shipment
    {
        // Shipping addresses oluştur
        $originAddress = $this->createShippingAddress($eloquentShipment->origin_address);
        $destinationAddress = $this->createShippingAddress($eloquentShipment->destination_address);
        $returnAddress = $eloquentShipment->return_address
            ? $this->createShippingAddress($eloquentShipment->return_address)
            : null;

        // Package info oluştur
        $packageInfo = $this->createPackageInfo($eloquentShipment->package_info);

        // Tracking number oluştur
        $trackingNumber = $eloquentShipment->tracking_number
            ? new TrackingNumber($eloquentShipment->tracking_number)
            : null;

        // Delivery status oluştur
        $deliveryStatus = new DeliveryStatus($eloquentShipment->status);

        // Shipment entity oluştur
        $shipment = Shipment::create(
            $eloquentShipment->order_id,
            $eloquentShipment->carrierIntegration->carrier_code,
            $originAddress,
            $destinationAddress,
            $packageInfo,
            $eloquentShipment->service_type,
            $deliveryStatus
        );

        // Private property'leri reflection ile set et
        $this->setPrivateProperty($shipment, 'id', $eloquentShipment->id);
        $this->setPrivateProperty($shipment, 'shipmentNumber', $eloquentShipment->shipment_number);

        if ($trackingNumber) {
            $this->setPrivateProperty($shipment, 'trackingNumber', $trackingNumber);
        }

        if ($returnAddress) {
            $this->setPrivateProperty($shipment, 'returnAddress', $returnAddress);
        }

        // Carrier tracking number
        if ($eloquentShipment->carrier_tracking_number) {
            $this->setPrivateProperty($shipment, 'carrierTrackingNumber', $eloquentShipment->carrier_tracking_number);
        }

        // Reference number
        if ($eloquentShipment->reference_number) {
            $this->setPrivateProperty($shipment, 'referenceNumber', $eloquentShipment->reference_number);
        }

        // Items
        if ($eloquentShipment->items) {
            $this->setPrivateProperty($shipment, 'items', $eloquentShipment->items);
        }

        // Declared value
        if ($eloquentShipment->declared_value) {
            $this->setPrivateProperty($shipment, 'declaredValue', $eloquentShipment->declared_value);
            $this->setPrivateProperty($shipment, 'declaredCurrency', $eloquentShipment->declared_currency);
        }

        // Delivery options
        $this->setPrivateProperty($shipment, 'deliveryType', $eloquentShipment->delivery_type);
        $this->setPrivateProperty($shipment, 'signatureRequired', $eloquentShipment->signature_required);
        $this->setPrivateProperty($shipment, 'insuranceRequired', $eloquentShipment->insurance_required);

        if ($eloquentShipment->insurance_amount) {
            $this->setPrivateProperty($shipment, 'insuranceAmount', $eloquentShipment->insurance_amount);
        }

        // Timestamps
        if ($eloquentShipment->shipped_at) {
            $this->setPrivateProperty($shipment, 'shippedAt', $eloquentShipment->shipped_at);
        }

        if ($eloquentShipment->estimated_delivery_at) {
            $this->setPrivateProperty($shipment, 'estimatedDeliveryAt', $eloquentShipment->estimated_delivery_at);
        }

        if ($eloquentShipment->actual_delivery_at) {
            $this->setPrivateProperty($shipment, 'actualDeliveryAt', $eloquentShipment->actual_delivery_at);
        }

        if ($eloquentShipment->last_tracking_update) {
            $this->setPrivateProperty($shipment, 'lastTrackingUpdate', $eloquentShipment->last_tracking_update);
        }

        // Costs
        $this->setPrivateProperty($shipment, 'shippingCost', $eloquentShipment->shipping_cost);
        $this->setPrivateProperty($shipment, 'insuranceCost', $eloquentShipment->insurance_cost);
        $this->setPrivateProperty($shipment, 'handlingCost', $eloquentShipment->handling_cost);
        $this->setPrivateProperty($shipment, 'fuelSurcharge', $eloquentShipment->fuel_surcharge);
        $this->setPrivateProperty($shipment, 'taxes', $eloquentShipment->taxes);
        $this->setPrivateProperty($shipment, 'totalCost', $eloquentShipment->total_cost);
        $this->setPrivateProperty($shipment, 'costCurrency', $eloquentShipment->cost_currency);

        // Label information
        if ($eloquentShipment->label_format) {
            $this->setPrivateProperty($shipment, 'labelFormat', $eloquentShipment->label_format);
        }

        if ($eloquentShipment->label_url) {
            $this->setPrivateProperty($shipment, 'labelUrl', $eloquentShipment->label_url);
        }

        if ($eloquentShipment->label_data) {
            $this->setPrivateProperty($shipment, 'labelData', $eloquentShipment->label_data);
        }

        // Delivery information
        if ($eloquentShipment->delivery_method) {
            $this->setPrivateProperty($shipment, 'deliveryMethod', $eloquentShipment->delivery_method);
        }

        if ($eloquentShipment->delivered_to) {
            $this->setPrivateProperty($shipment, 'deliveredTo', $eloquentShipment->delivered_to);
        }

        if ($eloquentShipment->delivery_notes) {
            $this->setPrivateProperty($shipment, 'deliveryNotes', $eloquentShipment->delivery_notes);
        }

        if ($eloquentShipment->delivery_proof) {
            $this->setPrivateProperty($shipment, 'deliveryProof', $eloquentShipment->delivery_proof);
        }

        // Failure information
        if ($eloquentShipment->failure_reason) {
            $this->setPrivateProperty($shipment, 'failureReason', $eloquentShipment->failure_reason);
        }

        $this->setPrivateProperty($shipment, 'deliveryAttempts', $eloquentShipment->delivery_attempts);

        if ($eloquentShipment->next_delivery_attempt) {
            $this->setPrivateProperty($shipment, 'nextDeliveryAttempt', $eloquentShipment->next_delivery_attempt);
        }

        // Return information
        $this->setPrivateProperty($shipment, 'returnToSender', $eloquentShipment->return_to_sender);

        if ($eloquentShipment->return_reason) {
            $this->setPrivateProperty($shipment, 'returnReason', $eloquentShipment->return_reason);
        }

        // Metadata
        if ($eloquentShipment->carrier_response) {
            $this->setPrivateProperty($shipment, 'carrierResponse', $eloquentShipment->carrier_response);
        }

        if ($eloquentShipment->tracking_events) {
            $this->setPrivateProperty($shipment, 'trackingEvents', $eloquentShipment->tracking_events);
        }

        if ($eloquentShipment->metadata) {
            $this->setPrivateProperty($shipment, 'metadata', $eloquentShipment->metadata);
        }

        // Audit fields
        $this->setPrivateProperty($shipment, 'createdAt', $eloquentShipment->created_at);
        $this->setPrivateProperty($shipment, 'updatedAt', $eloquentShipment->updated_at);

        if ($eloquentShipment->created_by) {
            $this->setPrivateProperty($shipment, 'createdBy', $eloquentShipment->created_by);
        }

        if ($eloquentShipment->updated_by) {
            $this->setPrivateProperty($shipment, 'updatedBy', $eloquentShipment->updated_by);
        }

        return $shipment;
    }

    /**
     * Domain entity'yi Eloquent model'e çevir
     */
    public function toEloquent(Shipment $shipment): EloquentShipment
    {
        $eloquentShipment = new EloquentShipment();

        // ID varsa mevcut kaydı bul
        if ($shipment->getId()) {
            $eloquentShipment = EloquentShipment::find($shipment->getId()) ?? new EloquentShipment();
        }

        // Basic properties
        $eloquentShipment->order_id = $shipment->getOrderId();
        $eloquentShipment->status = $shipment->getStatus()->getValue();
        $eloquentShipment->service_type = $shipment->getServiceType();

        // Shipment number
        $shipmentNumber = $this->getPrivateProperty($shipment, 'shipmentNumber');
        if ($shipmentNumber) {
            $eloquentShipment->shipment_number = $shipmentNumber;
        }

        // Carrier integration
        $carrierCode = $shipment->getCarrierCode();
        $carrierIntegration = EloquentCarrierIntegration::where('carrier_code', $carrierCode)->first();
        if ($carrierIntegration) {
            $eloquentShipment->carrier_integration_id = $carrierIntegration->id;
        }

        // Tracking numbers
        $trackingNumber = $shipment->getTrackingNumber();
        if ($trackingNumber) {
            $eloquentShipment->tracking_number = $trackingNumber->getValue();
        }

        $carrierTrackingNumber = $this->getPrivateProperty($shipment, 'carrierTrackingNumber');
        if ($carrierTrackingNumber) {
            $eloquentShipment->carrier_tracking_number = $carrierTrackingNumber;
        }

        $referenceNumber = $this->getPrivateProperty($shipment, 'referenceNumber');
        if ($referenceNumber) {
            $eloquentShipment->reference_number = $referenceNumber;
        }

        // Addresses
        $eloquentShipment->origin_address = $this->addressToArray($shipment->getOriginAddress());
        $eloquentShipment->destination_address = $this->addressToArray($shipment->getDestinationAddress());

        $returnAddress = $shipment->getReturnAddress();
        if ($returnAddress) {
            $eloquentShipment->return_address = $this->addressToArray($returnAddress);
        }

        // Package info
        $eloquentShipment->package_info = $this->packageInfoToArray($shipment->getPackageInfo());

        // Items
        $items = $this->getPrivateProperty($shipment, 'items');
        if ($items) {
            $eloquentShipment->items = $items;
        }

        // Declared value
        $declaredValue = $this->getPrivateProperty($shipment, 'declaredValue');
        if ($declaredValue) {
            $eloquentShipment->declared_value = $declaredValue;
            $eloquentShipment->declared_currency = $this->getPrivateProperty($shipment, 'declaredCurrency') ?? 'TRY';
        }

        // Delivery options
        $deliveryType = $this->getPrivateProperty($shipment, 'deliveryType');
        if ($deliveryType) {
            $eloquentShipment->delivery_type = $deliveryType;
        }

        $signatureRequired = $this->getPrivateProperty($shipment, 'signatureRequired');
        if ($signatureRequired !== null) {
            $eloquentShipment->signature_required = $signatureRequired;
        }

        $insuranceRequired = $this->getPrivateProperty($shipment, 'insuranceRequired');
        if ($insuranceRequired !== null) {
            $eloquentShipment->insurance_required = $insuranceRequired;
        }

        $insuranceAmount = $this->getPrivateProperty($shipment, 'insuranceAmount');
        if ($insuranceAmount) {
            $eloquentShipment->insurance_amount = $insuranceAmount;
        }

        // Timestamps
        $shippedAt = $this->getPrivateProperty($shipment, 'shippedAt');
        if ($shippedAt instanceof Carbon) {
            $eloquentShipment->shipped_at = $shippedAt;
        }

        $estimatedDeliveryAt = $this->getPrivateProperty($shipment, 'estimatedDeliveryAt');
        if ($estimatedDeliveryAt instanceof Carbon) {
            $eloquentShipment->estimated_delivery_at = $estimatedDeliveryAt;
        }

        $actualDeliveryAt = $this->getPrivateProperty($shipment, 'actualDeliveryAt');
        if ($actualDeliveryAt instanceof Carbon) {
            $eloquentShipment->actual_delivery_at = $actualDeliveryAt;
        }

        $lastTrackingUpdate = $this->getPrivateProperty($shipment, 'lastTrackingUpdate');
        if ($lastTrackingUpdate instanceof Carbon) {
            $eloquentShipment->last_tracking_update = $lastTrackingUpdate;
        }

        // Costs
        $shippingCost = $this->getPrivateProperty($shipment, 'shippingCost');
        if ($shippingCost !== null) {
            $eloquentShipment->shipping_cost = $shippingCost;
        }

        $insuranceCost = $this->getPrivateProperty($shipment, 'insuranceCost');
        if ($insuranceCost !== null) {
            $eloquentShipment->insurance_cost = $insuranceCost;
        }

        $handlingCost = $this->getPrivateProperty($shipment, 'handlingCost');
        if ($handlingCost !== null) {
            $eloquentShipment->handling_cost = $handlingCost;
        }

        $fuelSurcharge = $this->getPrivateProperty($shipment, 'fuelSurcharge');
        if ($fuelSurcharge !== null) {
            $eloquentShipment->fuel_surcharge = $fuelSurcharge;
        }

        $taxes = $this->getPrivateProperty($shipment, 'taxes');
        if ($taxes !== null) {
            $eloquentShipment->taxes = $taxes;
        }

        $totalCost = $this->getPrivateProperty($shipment, 'totalCost');
        if ($totalCost !== null) {
            $eloquentShipment->total_cost = $totalCost;
        }

        $costCurrency = $this->getPrivateProperty($shipment, 'costCurrency');
        if ($costCurrency) {
            $eloquentShipment->cost_currency = $costCurrency;
        }

        return $eloquentShipment;
    }

    /**
     * ShippingAddress oluştur
     */
    private function createShippingAddress(array $addressData): ShippingAddress
    {
        return new ShippingAddress(
            $addressData['name'] ?? '',
            $addressData['company'] ?? null,
            $addressData['address_line_1'] ?? '',
            $addressData['address_line_2'] ?? null,
            $addressData['city'] ?? '',
            $addressData['state'] ?? '',
            $addressData['postal_code'] ?? '',
            $addressData['country'] ?? '',
            $addressData['phone'] ?? null,
            $addressData['email'] ?? null
        );
    }

    /**
     * PackageInfo oluştur
     */
    private function createPackageInfo(array $packageData): PackageInfo
    {
        return new PackageInfo(
            $packageData['weight'] ?? 0,
            $packageData['weight_unit'] ?? 'kg',
            $packageData['length'] ?? 0,
            $packageData['width'] ?? 0,
            $packageData['height'] ?? 0,
            $packageData['dimension_unit'] ?? 'cm',
            $packageData['package_type'] ?? 'box'
        );
    }

    /**
     * ShippingAddress'i array'e çevir
     */
    private function addressToArray(ShippingAddress $address): array
    {
        return [
            'name' => $address->getName(),
            'company' => $address->getCompany(),
            'address_line_1' => $address->getAddressLine1(),
            'address_line_2' => $address->getAddressLine2(),
            'city' => $address->getCity(),
            'state' => $address->getState(),
            'postal_code' => $address->getPostalCode(),
            'country' => $address->getCountry(),
            'phone' => $address->getPhone(),
            'email' => $address->getEmail()
        ];
    }

    /**
     * PackageInfo'yu array'e çevir
     */
    private function packageInfoToArray(PackageInfo $packageInfo): array
    {
        return [
            'weight' => $packageInfo->getWeight(),
            'weight_unit' => $packageInfo->getWeightUnit(),
            'length' => $packageInfo->getLength(),
            'width' => $packageInfo->getWidth(),
            'height' => $packageInfo->getHeight(),
            'dimension_unit' => $packageInfo->getDimensionUnit(),
            'package_type' => $packageInfo->getPackageType()
        ];
    }

    /**
     * Shipment entity'den array'e çevir (API response için)
     */
    public function toArray(Shipment $shipment): array
    {
        return [
            'id' => $shipment->getId(),
            'shipment_number' => $this->getPrivateProperty($shipment, 'shipmentNumber'),
            'order_id' => $shipment->getOrderId(),
            'carrier_code' => $shipment->getCarrierCode(),
            'tracking_number' => $shipment->getTrackingNumber()?->getValue(),
            'carrier_tracking_number' => $this->getPrivateProperty($shipment, 'carrierTrackingNumber'),
            'reference_number' => $this->getPrivateProperty($shipment, 'referenceNumber'),
            'origin_address' => $this->addressToArray($shipment->getOriginAddress()),
            'destination_address' => $this->addressToArray($shipment->getDestinationAddress()),
            'return_address' => $shipment->getReturnAddress()
                ? $this->addressToArray($shipment->getReturnAddress())
                : null,
            'package_info' => $this->packageInfoToArray($shipment->getPackageInfo()),
            'items' => $this->getPrivateProperty($shipment, 'items'),
            'declared_value' => $this->getPrivateProperty($shipment, 'declaredValue'),
            'declared_currency' => $this->getPrivateProperty($shipment, 'declaredCurrency'),
            'service_type' => $shipment->getServiceType(),
            'delivery_type' => $this->getPrivateProperty($shipment, 'deliveryType'),
            'signature_required' => $this->getPrivateProperty($shipment, 'signatureRequired'),
            'insurance_required' => $this->getPrivateProperty($shipment, 'insuranceRequired'),
            'insurance_amount' => $this->getPrivateProperty($shipment, 'insuranceAmount'),
            'status' => $shipment->getStatus()->getValue(),
            'shipped_at' => $this->getPrivateProperty($shipment, 'shippedAt')?->toISOString(),
            'estimated_delivery_at' => $this->getPrivateProperty($shipment, 'estimatedDeliveryAt')?->toISOString(),
            'actual_delivery_at' => $this->getPrivateProperty($shipment, 'actualDeliveryAt')?->toISOString(),
            'last_tracking_update' => $this->getPrivateProperty($shipment, 'lastTrackingUpdate')?->toISOString(),
            'shipping_cost' => $this->getPrivateProperty($shipment, 'shippingCost'),
            'insurance_cost' => $this->getPrivateProperty($shipment, 'insuranceCost'),
            'handling_cost' => $this->getPrivateProperty($shipment, 'handlingCost'),
            'fuel_surcharge' => $this->getPrivateProperty($shipment, 'fuelSurcharge'),
            'taxes' => $this->getPrivateProperty($shipment, 'taxes'),
            'total_cost' => $this->getPrivateProperty($shipment, 'totalCost'),
            'cost_currency' => $this->getPrivateProperty($shipment, 'costCurrency'),
            'label_format' => $this->getPrivateProperty($shipment, 'labelFormat'),
            'label_url' => $this->getPrivateProperty($shipment, 'labelUrl'),
            'delivery_method' => $this->getPrivateProperty($shipment, 'deliveryMethod'),
            'delivered_to' => $this->getPrivateProperty($shipment, 'deliveredTo'),
            'delivery_notes' => $this->getPrivateProperty($shipment, 'deliveryNotes'),
            'failure_reason' => $this->getPrivateProperty($shipment, 'failureReason'),
            'delivery_attempts' => $this->getPrivateProperty($shipment, 'deliveryAttempts'),
            'return_to_sender' => $this->getPrivateProperty($shipment, 'returnToSender'),
            'return_reason' => $this->getPrivateProperty($shipment, 'returnReason'),
            'created_at' => $this->getPrivateProperty($shipment, 'createdAt')?->toISOString(),
            'updated_at' => $this->getPrivateProperty($shipment, 'updatedAt')?->toISOString()
        ];
    }

    /**
     * Array'den Shipment entity oluştur
     */
    public function fromArray(array $data): Shipment
    {
        // Addresses oluştur
        $originAddress = $this->createShippingAddress($data['origin_address']);
        $destinationAddress = $this->createShippingAddress($data['destination_address']);
        $returnAddress = !empty($data['return_address'])
            ? $this->createShippingAddress($data['return_address'])
            : null;

        // Package info oluştur
        $packageInfo = $this->createPackageInfo($data['package_info']);

        // Delivery status oluştur
        $deliveryStatus = new DeliveryStatus($data['status'] ?? 'pending');

        // Shipment entity oluştur
        $shipment = Shipment::create(
            $data['order_id'],
            $data['carrier_code'],
            $originAddress,
            $destinationAddress,
            $packageInfo,
            $data['service_type'] ?? 'standard',
            $deliveryStatus
        );

        // ID varsa set et
        if (!empty($data['id'])) {
            $this->setPrivateProperty($shipment, 'id', $data['id']);
        }

        // Shipment number
        if (!empty($data['shipment_number'])) {
            $this->setPrivateProperty($shipment, 'shipmentNumber', $data['shipment_number']);
        }

        // Tracking numbers
        if (!empty($data['tracking_number'])) {
            $this->setPrivateProperty($shipment, 'trackingNumber', new TrackingNumber($data['tracking_number']));
        }

        if (!empty($data['carrier_tracking_number'])) {
            $this->setPrivateProperty($shipment, 'carrierTrackingNumber', $data['carrier_tracking_number']);
        }

        if (!empty($data['reference_number'])) {
            $this->setPrivateProperty($shipment, 'referenceNumber', $data['reference_number']);
        }

        // Return address
        if ($returnAddress) {
            $this->setPrivateProperty($shipment, 'returnAddress', $returnAddress);
        }

        // Items
        if (!empty($data['items'])) {
            $this->setPrivateProperty($shipment, 'items', $data['items']);
        }

        // Declared value
        if (!empty($data['declared_value'])) {
            $this->setPrivateProperty($shipment, 'declaredValue', $data['declared_value']);
            $this->setPrivateProperty($shipment, 'declaredCurrency', $data['declared_currency'] ?? 'TRY');
        }

        // Delivery options
        if (!empty($data['delivery_type'])) {
            $this->setPrivateProperty($shipment, 'deliveryType', $data['delivery_type']);
        }

        if (isset($data['signature_required'])) {
            $this->setPrivateProperty($shipment, 'signatureRequired', $data['signature_required']);
        }

        if (isset($data['insurance_required'])) {
            $this->setPrivateProperty($shipment, 'insuranceRequired', $data['insurance_required']);
        }

        if (!empty($data['insurance_amount'])) {
            $this->setPrivateProperty($shipment, 'insuranceAmount', $data['insurance_amount']);
        }

        // Timestamps
        if (!empty($data['shipped_at'])) {
            $this->setPrivateProperty($shipment, 'shippedAt', Carbon::parse($data['shipped_at']));
        }

        if (!empty($data['estimated_delivery_at'])) {
            $this->setPrivateProperty($shipment, 'estimatedDeliveryAt', Carbon::parse($data['estimated_delivery_at']));
        }

        if (!empty($data['actual_delivery_at'])) {
            $this->setPrivateProperty($shipment, 'actualDeliveryAt', Carbon::parse($data['actual_delivery_at']));
        }

        // Costs
        if (isset($data['shipping_cost'])) {
            $this->setPrivateProperty($shipment, 'shippingCost', $data['shipping_cost']);
        }

        if (isset($data['total_cost'])) {
            $this->setPrivateProperty($shipment, 'totalCost', $data['total_cost']);
        }

        return $shipment;
    }

    /**
     * Private property'yi reflection ile set et
     */
    private function setPrivateProperty(object $object, string $property, $value): void
    {
        try {
            $reflection = new ReflectionClass($object);
            $prop = $reflection->getProperty($property);
            $prop->setAccessible(true);
            $prop->setValue($object, $value);
        } catch (\ReflectionException $e) {
            // Property bulunamazsa sessizce geç
        }
    }

    /**
     * Private property'yi reflection ile al
     */
    private function getPrivateProperty(object $object, string $property)
    {
        try {
            $reflection = new ReflectionClass($object);
            $prop = $reflection->getProperty($property);
            $prop->setAccessible(true);
            return $prop->getValue($object);
        } catch (\ReflectionException $e) {
            return null;
        }
    }
}
