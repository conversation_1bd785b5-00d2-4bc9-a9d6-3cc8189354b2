<?php

namespace App\Infrastructure\Shipping\Adapters;

/**
 * CarrierAdapterInterface
 * Kargo şirketi adapter'ları için interface
 */
interface CarrierAdapterInterface
{
    /**
     * Kargo ücret hesaplama
     */
    public function calculateRate(
        array $originAddress,
        array $destinationAddress,
        array $packageInfo,
        string $serviceType,
        ?float $declaredValue = null
    ): array;

    /**
     * Shipment oluştur
     */
    public function createShipment(
        array $originAddress,
        array $destinationAddress,
        array $packageInfo,
        string $serviceType,
        array $options = []
    ): array;

    /**
     * Tracking bilgilerini al
     */
    public function getTrackingInfo(string $trackingNumber): array;

    /**
     * Shipment iptal et
     */
    public function cancelShipment(string $trackingNumber, string $reason = ''): array;

    /**
     * Kargo etiketi oluştur
     */
    public function generateLabel(
        string $trackingNumber,
        string $format = 'pdf',
        string $size = '4x6'
    ): array;

    /**
     * Pickup zamanlama
     */
    public function schedulePickup(
        array $pickupAddress,
        array $packageInfo,
        string $pickupDate,
        array $timeWindow = []
    ): array;

    /**
     * Teslimat onayı al
     */
    public function getDeliveryConfirmation(string $trackingNumber): array;

    /**
     * Bağlantı testi
     */
    public function testConnection(): array;

    /**
     * Desteklenen servis tiplerini getir
     */
    public function getSupportedServices(): array;

    /**
     * Desteklenen ülkeleri getir
     */
    public function getSupportedCountries(): array;

    /**
     * Rate limit bilgilerini getir
     */
    public function getRateLimitInfo(): array;
}
