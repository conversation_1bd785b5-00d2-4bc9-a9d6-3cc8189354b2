<?php

namespace App\Infrastructure\Shipping\Adapters;

use App\Infrastructure\Shipping\Models\EloquentCarrierIntegration;
use App\Infrastructure\Shipping\Adapters\Implementations\ArasCargoAdapter;
use App\Infrastructure\Shipping\Adapters\Implementations\PTTCargoAdapter;
use App\Infrastructure\Shipping\Adapters\Implementations\YurticiCargoAdapter;
use App\Infrastructure\Shipping\Adapters\Implementations\UPSAdapter;
use App\Infrastructure\Shipping\Adapters\Implementations\DHLAdapter;
use Illuminate\Support\Facades\Log;

/**
 * CarrierAdapterFactory
 * Kargo şirketi adapter'larını oluşturan factory
 */
class CarrierAdapterFactory
{
    private array $adapters = [];

    /**
     * Carrier adapter oluştur
     */
    public function create(string $carrierCode): CarrierAdapterInterface
    {
        // Cache'den kontrol et
        if (isset($this->adapters[$carrierCode])) {
            return $this->adapters[$carrierCode];
        }

        // Carrier integration bilgilerini al
        $carrierIntegration = EloquentCarrierIntegration::where('carrier_code', $carrierCode)
            ->where('is_active', true)
            ->first();

        if (!$carrierIntegration) {
            throw new \InvalidArgumentException("Carrier integration not found or inactive: {$carrierCode}");
        }

        // Adapter oluştur
        $adapter = $this->createAdapter($carrierCode, $carrierIntegration);
        
        // Cache'e ekle
        $this->adapters[$carrierCode] = $adapter;

        Log::info('Carrier adapter created', [
            'carrier_code' => $carrierCode,
            'adapter_class' => get_class($adapter)
        ]);

        return $adapter;
    }

    /**
     * Carrier code'a göre adapter oluştur
     */
    private function createAdapter(string $carrierCode, EloquentCarrierIntegration $integration): CarrierAdapterInterface
    {
        return match (strtolower($carrierCode)) {
            'aras' => new ArasCargoAdapter($integration),
            'ptt' => new PTTCargoAdapter($integration),
            'yurtici' => new YurticiCargoAdapter($integration),
            'ups' => new UPSAdapter($integration),
            'dhl' => new DHLAdapter($integration),
            default => throw new \InvalidArgumentException("Unsupported carrier: {$carrierCode}")
        };
    }

    /**
     * Desteklenen carrier'ları getir
     */
    public function getSupportedCarriers(): array
    {
        return [
            'aras' => [
                'name' => 'Aras Kargo',
                'class' => ArasCargoAdapter::class,
                'country' => 'TR',
                'capabilities' => ['tracking', 'rate_calculation', 'label_generation']
            ],
            'ptt' => [
                'name' => 'PTT Kargo',
                'class' => PTTCargoAdapter::class,
                'country' => 'TR',
                'capabilities' => ['tracking', 'rate_calculation', 'label_generation']
            ],
            'yurtici' => [
                'name' => 'Yurtiçi Kargo',
                'class' => YurticiCargoAdapter::class,
                'country' => 'TR',
                'capabilities' => ['tracking', 'rate_calculation', 'label_generation', 'pickup_scheduling']
            ],
            'ups' => [
                'name' => 'UPS',
                'class' => UPSAdapter::class,
                'country' => 'GLOBAL',
                'capabilities' => ['tracking', 'rate_calculation', 'label_generation', 'pickup_scheduling', 'delivery_confirmation']
            ],
            'dhl' => [
                'name' => 'DHL Express',
                'class' => DHLAdapter::class,
                'country' => 'GLOBAL',
                'capabilities' => ['tracking', 'rate_calculation', 'label_generation', 'pickup_scheduling', 'delivery_confirmation']
            ]
        ];
    }

    /**
     * Carrier'ın desteklenip desteklenmediğini kontrol et
     */
    public function isSupported(string $carrierCode): bool
    {
        $supportedCarriers = $this->getSupportedCarriers();
        return isset($supportedCarriers[strtolower($carrierCode)]);
    }

    /**
     * Tüm aktif carrier adapter'larını oluştur
     */
    public function createAllActive(): array
    {
        $adapters = [];
        
        $activeCarriers = EloquentCarrierIntegration::where('is_active', true)->get();
        
        foreach ($activeCarriers as $carrier) {
            try {
                $adapters[$carrier->carrier_code] = $this->create($carrier->carrier_code);
            } catch (\Exception $e) {
                Log::error('Failed to create adapter for carrier', [
                    'carrier_code' => $carrier->carrier_code,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $adapters;
    }

    /**
     * Cache'i temizle
     */
    public function clearCache(): void
    {
        $this->adapters = [];
        Log::info('Carrier adapter cache cleared');
    }

    /**
     * Belirli bir carrier'ın cache'ini temizle
     */
    public function clearCarrierCache(string $carrierCode): void
    {
        unset($this->adapters[$carrierCode]);
        Log::info('Carrier adapter cache cleared', ['carrier_code' => $carrierCode]);
    }
}
