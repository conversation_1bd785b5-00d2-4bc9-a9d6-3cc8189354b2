<?php

namespace App\Infrastructure\Orders\Repositories;

use App\Domain\Orders\Entities\Order;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Illuminate\Support\Facades\Cache;

/**
 * Cache Order Repository Decorator
 * Order repository'sine cache katmanı ekler
 */
class CacheOrderRepository implements OrderRepositoryInterface
{
    private OrderRepositoryInterface $repository;
    private int $cacheTtl;
    private string $cachePrefix;

    public function __construct(
        OrderRepositoryInterface $repository,
        int $cacheTtl = 1800, // 30 dakika (orders daha dinamik)
        string $cachePrefix = 'orders'
    ) {
        $this->repository = $repository;
        $this->cacheTtl = $cacheTtl;
        $this->cachePrefix = $cachePrefix;
    }

    public function save(Order $order): Order
    {
        $result = $this->repository->save($order);
        
        // Cache'i temizle
        $this->clearOrderCache($order->getId());
        $this->clearListCaches($order->getUserId());
        
        return $result;
    }

    public function findById(int $id): ?Order
    {
        $cacheKey = $this->getCacheKey("order.{$id}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($id) {
            return $this->repository->findById($id);
        });
    }

    public function findByOrderNumber(OrderNumber $orderNumber): ?Order
    {
        $cacheKey = $this->getCacheKey("order.number.{$orderNumber->getValue()}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($orderNumber) {
            return $this->repository->findByOrderNumber($orderNumber);
        });
    }

    public function findByUserId(int $userId, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.user.{$userId}.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($userId, $limit, $offset) {
            return $this->repository->findByUserId($userId, $limit, $offset);
        });
    }

    public function findByStatus(OrderStatus $status, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.status.{$status->value}.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($status, $limit, $offset) {
            return $this->repository->findByStatus($status, $limit, $offset);
        });
    }

    public function findByPaymentStatus(PaymentStatus $paymentStatus, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.payment_status.{$paymentStatus->value}.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($paymentStatus, $limit, $offset) {
            return $this->repository->findByPaymentStatus($paymentStatus, $limit, $offset);
        });
    }

    public function findByDateRange(\DateTime $startDate, \DateTime $endDate, int $limit = 10, int $offset = 0): array
    {
        // Date range queries genellikle cache'lenmez (çok dinamik)
        return $this->repository->findByDateRange($startDate, $endDate, $limit, $offset);
    }

    public function findByUserIdAndStatus(int $userId, OrderStatus $status, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.user.{$userId}.status.{$status->value}.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($userId, $status, $limit, $offset) {
            return $this->repository->findByUserIdAndStatus($userId, $status, $limit, $offset);
        });
    }

    public function search(array $criteria, int $limit = 10, int $offset = 0): array
    {
        // Search sonuçları cache'lenmez (çok dinamik)
        return $this->repository->search($criteria, $limit, $offset);
    }

    public function count(array $criteria = []): int
    {
        if (empty($criteria)) {
            $cacheKey = $this->getCacheKey("orders.count.all");
            
            return Cache::remember($cacheKey, $this->cacheTtl, function () use ($criteria) {
                return $this->repository->count($criteria);
            });
        }
        
        return $this->repository->count($criteria);
    }

    public function countByUserId(int $userId): int
    {
        $cacheKey = $this->getCacheKey("orders.count.user.{$userId}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($userId) {
            return $this->repository->countByUserId($userId);
        });
    }

    public function countByStatus(OrderStatus $status): int
    {
        $cacheKey = $this->getCacheKey("orders.count.status.{$status->value}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($status) {
            return $this->repository->countByStatus($status);
        });
    }

    public function delete(Order $order): bool
    {
        $result = $this->repository->delete($order);
        
        if ($result) {
            $this->clearOrderCache($order->getId());
            $this->clearListCaches($order->getUserId());
        }
        
        return $result;
    }

    public function deleteById(int $id): bool
    {
        $result = $this->repository->deleteById($id);
        
        if ($result) {
            $this->clearOrderCache($id);
            $this->clearListCaches();
        }
        
        return $result;
    }

    public function exists(int $id): bool
    {
        $cacheKey = $this->getCacheKey("order.exists.{$id}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($id) {
            return $this->repository->exists($id);
        });
    }

    public function existsByOrderNumber(OrderNumber $orderNumber): bool
    {
        $cacheKey = $this->getCacheKey("order.exists.number.{$orderNumber->getValue()}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($orderNumber) {
            return $this->repository->existsByOrderNumber($orderNumber);
        });
    }

    public function findPendingOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.pending.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findPendingOrders($limit, $offset);
        });
    }

    public function findProcessingOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.processing.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findProcessingOrders($limit, $offset);
        });
    }

    public function findReadyToShipOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.ready_to_ship.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findReadyToShipOrders($limit, $offset);
        });
    }

    public function findShippedOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.shipped.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findShippedOrders($limit, $offset);
        });
    }

    public function findDeliveredOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.delivered.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findDeliveredOrders($limit, $offset);
        });
    }

    public function findCancelledOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.cancelled.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findCancelledOrders($limit, $offset);
        });
    }

    public function findRefundedOrders(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("orders.refunded.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findRefundedOrders($limit, $offset);
        });
    }

    public function findOrdersRequiringAction(int $limit = 10, int $offset = 0): array
    {
        // Action gerektiren siparişler cache'lenmez (real-time olmalı)
        return $this->repository->findOrdersRequiringAction($limit, $offset);
    }

    public function findOverdueOrders(int $limit = 10, int $offset = 0): array
    {
        // Overdue siparişler cache'lenmez (real-time olmalı)
        return $this->repository->findOverdueOrders($limit, $offset);
    }

    public function getOrderStatistics(): array
    {
        $cacheKey = $this->getCacheKey("orders.statistics");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            return $this->repository->getOrderStatistics();
        });
    }

    public function getRevenueStatistics(\DateTime $startDate, \DateTime $endDate): array
    {
        // Revenue statistics genellikle cache'lenmez (real-time reporting)
        return $this->repository->getRevenueStatistics($startDate, $endDate);
    }

    /**
     * Cache key oluştur
     */
    private function getCacheKey(string $key): string
    {
        return "{$this->cachePrefix}.{$key}";
    }

    /**
     * Sipariş cache'ini temizle
     */
    private function clearOrderCache(?int $orderId): void
    {
        if (!$orderId) {
            return;
        }

        $patterns = [
            "order.{$orderId}",
            "order.exists.{$orderId}",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($this->getCacheKey($pattern));
        }
    }

    /**
     * Liste cache'lerini temizle
     */
    private function clearListCaches(?int $userId = null): void
    {
        $patterns = [
            'orders.pending.*',
            'orders.processing.*',
            'orders.ready_to_ship.*',
            'orders.shipped.*',
            'orders.delivered.*',
            'orders.cancelled.*',
            'orders.refunded.*',
            'orders.count.*',
            'orders.statistics',
        ];

        if ($userId) {
            $patterns[] = "orders.user.{$userId}.*";
        }

        foreach ($patterns as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }
}
