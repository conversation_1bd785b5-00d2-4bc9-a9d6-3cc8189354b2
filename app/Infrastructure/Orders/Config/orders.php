<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Orders Module Configuration
    |--------------------------------------------------------------------------
    |
    | Bu dosya Orders modülü için infrastructure ayarlarını içerir.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Order cache ayarları
    |
    */
    'cache' => [
        'enabled' => env('ORDERS_CACHE_ENABLED', true),
        'ttl' => env('ORDERS_CACHE_TTL', 1800), // 30 dakika
        'prefix' => env('ORDERS_CACHE_PREFIX', 'orders'),
        'tags' => [
            'orders',
            'order_status',
            'order_analytics',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Order Number Settings
    |--------------------------------------------------------------------------
    |
    | Sipariş numarası ayarları
    |
    */
    'order_number' => [
        'prefix' => env('ORDER_NUMBER_PREFIX', 'ORD'),
        'length' => env('ORDER_NUMBER_LENGTH', 8),
        'format' => env('ORDER_NUMBER_FORMAT', '{prefix}-{year}{month}{day}-{sequence}'),
        'start_sequence' => env('ORDER_NUMBER_START_SEQUENCE', 1000),
        'reset_sequence' => env('ORDER_NUMBER_RESET_SEQUENCE', 'yearly'), // daily, monthly, yearly, never
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Management
    |--------------------------------------------------------------------------
    |
    | Sipariş durum yönetimi ayarları
    |
    */
    'status' => [
        'auto_transitions' => env('ORDERS_AUTO_TRANSITIONS', true),
        'transition_delays' => [
            'payment_to_processing' => env('ORDERS_PAYMENT_TO_PROCESSING_DELAY', 0), // dakika
            'processing_to_preparing' => env('ORDERS_PROCESSING_TO_PREPARING_DELAY', 60),
            'preparing_to_ready' => env('ORDERS_PREPARING_TO_READY_DELAY', 120),
        ],
        'auto_cancel_after' => env('ORDERS_AUTO_CANCEL_AFTER', 7), // gün
        'auto_complete_after' => env('ORDERS_AUTO_COMPLETE_AFTER', 30), // gün
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    |
    | Ödeme ayarları
    |
    */
    'payment' => [
        'default_currency' => env('ORDERS_DEFAULT_CURRENCY', 'TRY'),
        'allowed_currencies' => ['TRY', 'USD', 'EUR'],
        'payment_timeout' => env('ORDERS_PAYMENT_TIMEOUT', 30), // dakika
        'refund_timeout' => env('ORDERS_REFUND_TIMEOUT', 7), // gün
        'partial_refund_enabled' => env('ORDERS_PARTIAL_REFUND_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Shipping Settings
    |--------------------------------------------------------------------------
    |
    | Kargo ayarları
    |
    */
    'shipping' => [
        'default_method' => env('ORDERS_DEFAULT_SHIPPING_METHOD', 'standard'),
        'free_shipping_threshold' => env('ORDERS_FREE_SHIPPING_THRESHOLD', 150.00),
        'same_day_delivery_enabled' => env('ORDERS_SAME_DAY_DELIVERY', false),
        'international_shipping_enabled' => env('ORDERS_INTERNATIONAL_SHIPPING', false),
        'tracking_enabled' => env('ORDERS_TRACKING_ENABLED', true),
        'delivery_estimation_enabled' => env('ORDERS_DELIVERY_ESTIMATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Bildirim ayarları
    |
    */
    'notifications' => [
        'email_enabled' => env('ORDERS_EMAIL_NOTIFICATIONS', true),
        'sms_enabled' => env('ORDERS_SMS_NOTIFICATIONS', false),
        'push_enabled' => env('ORDERS_PUSH_NOTIFICATIONS', false),
        'admin_emails' => explode(',', env('ORDERS_ADMIN_EMAILS', '<EMAIL>')),
        'customer_notifications' => [
            'order_created' => true,
            'payment_confirmed' => true,
            'order_shipped' => true,
            'order_delivered' => true,
            'order_cancelled' => true,
            'delivery_delay' => true,
        ],
        'admin_notifications' => [
            'new_order' => true,
            'payment_failed' => true,
            'high_value_order' => true,
            'fraud_alert' => true,
            'stock_alert' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fraud Detection
    |--------------------------------------------------------------------------
    |
    | Fraud detection ayarları
    |
    */
    'fraud_detection' => [
        'enabled' => env('ORDERS_FRAUD_DETECTION', true),
        'high_value_threshold' => env('ORDERS_HIGH_VALUE_THRESHOLD', 5000.00),
        'velocity_check_enabled' => env('ORDERS_VELOCITY_CHECK', true),
        'max_orders_per_hour' => env('ORDERS_MAX_PER_HOUR', 5),
        'max_orders_per_day' => env('ORDERS_MAX_PER_DAY', 20),
        'blacklist_check_enabled' => env('ORDERS_BLACKLIST_CHECK', true),
        'geolocation_check_enabled' => env('ORDERS_GEOLOCATION_CHECK', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Settings
    |--------------------------------------------------------------------------
    |
    | Analitik ayarları
    |
    */
    'analytics' => [
        'enabled' => env('ORDERS_ANALYTICS_ENABLED', true),
        'track_conversion' => env('ORDERS_TRACK_CONVERSION', true),
        'track_abandonment' => env('ORDERS_TRACK_ABANDONMENT', true),
        'track_customer_journey' => env('ORDERS_TRACK_CUSTOMER_JOURNEY', true),
        'retention_days' => env('ORDERS_ANALYTICS_RETENTION', 365),
        'real_time_dashboard' => env('ORDERS_REAL_TIME_DASHBOARD', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Stock Management
    |--------------------------------------------------------------------------
    |
    | Stok yönetimi ayarları
    |
    */
    'stock' => [
        'reserve_on_order' => env('ORDERS_RESERVE_STOCK', true),
        'reservation_timeout' => env('ORDERS_STOCK_RESERVATION_TIMEOUT', 24), // saat
        'auto_release_cancelled' => env('ORDERS_AUTO_RELEASE_CANCELLED_STOCK', true),
        'backorder_enabled' => env('ORDERS_BACKORDER_ENABLED', false),
        'oversell_protection' => env('ORDERS_OVERSELL_PROTECTION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Invoice Settings
    |--------------------------------------------------------------------------
    |
    | Fatura ayarları
    |
    */
    'invoice' => [
        'auto_generate' => env('ORDERS_AUTO_GENERATE_INVOICE', true),
        'generate_on_status' => env('ORDERS_INVOICE_ON_STATUS', 'payment_confirmed'),
        'number_format' => env('ORDERS_INVOICE_NUMBER_FORMAT', 'INV-{year}-{sequence}'),
        'include_tax_breakdown' => env('ORDERS_INVOICE_TAX_BREAKDOWN', true),
        'pdf_generation' => env('ORDERS_INVOICE_PDF', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Return & Refund Settings
    |--------------------------------------------------------------------------
    |
    | İade ayarları
    |
    */
    'returns' => [
        'enabled' => env('ORDERS_RETURNS_ENABLED', true),
        'return_window_days' => env('ORDERS_RETURN_WINDOW', 14),
        'auto_approve_returns' => env('ORDERS_AUTO_APPROVE_RETURNS', false),
        'return_shipping_cost' => env('ORDERS_RETURN_SHIPPING_COST', 'customer'), // customer, merchant, free
        'refund_processing_days' => env('ORDERS_REFUND_PROCESSING_DAYS', 3),
        'partial_returns_enabled' => env('ORDERS_PARTIAL_RETURNS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    |
    | Dış entegrasyon ayarları
    |
    */
    'integrations' => [
        'crm' => [
            'enabled' => env('ORDERS_CRM_INTEGRATION', false),
            'sync_on_create' => env('ORDERS_CRM_SYNC_CREATE', true),
            'sync_on_status_change' => env('ORDERS_CRM_SYNC_STATUS', true),
        ],
        'erp' => [
            'enabled' => env('ORDERS_ERP_INTEGRATION', false),
            'endpoint' => env('ORDERS_ERP_ENDPOINT'),
            'api_key' => env('ORDERS_ERP_API_KEY'),
            'sync_interval' => env('ORDERS_ERP_SYNC_INTERVAL', 15), // dakika
        ],
        'accounting' => [
            'enabled' => env('ORDERS_ACCOUNTING_INTEGRATION', false),
            'sync_invoices' => env('ORDERS_ACCOUNTING_SYNC_INVOICES', true),
            'sync_refunds' => env('ORDERS_ACCOUNTING_SYNC_REFUNDS', true),
        ],
        'marketing' => [
            'enabled' => env('ORDERS_MARKETING_INTEGRATION', false),
            'track_conversions' => env('ORDERS_MARKETING_TRACK_CONVERSIONS', true),
            'sync_customer_data' => env('ORDERS_MARKETING_SYNC_CUSTOMERS', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Performans ayarları
    |
    */
    'performance' => [
        'eager_load_relations' => ['items', 'addresses', 'notes'],
        'pagination_size' => env('ORDERS_PAGINATION_SIZE', 20),
        'max_pagination_size' => env('ORDERS_MAX_PAGINATION_SIZE', 100),
        'bulk_operations_batch_size' => env('ORDERS_BULK_BATCH_SIZE', 100),
        'export_batch_size' => env('ORDERS_EXPORT_BATCH_SIZE', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Güvenlik ayarları
    |
    */
    'security' => [
        'encrypt_sensitive_data' => env('ORDERS_ENCRYPT_SENSITIVE_DATA', true),
        'mask_payment_info' => env('ORDERS_MASK_PAYMENT_INFO', true),
        'audit_trail_enabled' => env('ORDERS_AUDIT_TRAIL', true),
        'access_log_enabled' => env('ORDERS_ACCESS_LOG', true),
        'data_retention_days' => env('ORDERS_DATA_RETENTION', 2555), // 7 yıl
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | API ayarları
    |
    */
    'api' => [
        'rate_limit' => env('ORDERS_API_RATE_LIMIT', 100), // per minute
        'webhook_enabled' => env('ORDERS_WEBHOOK_ENABLED', false),
        'webhook_events' => [
            'order.created',
            'order.status_changed',
            'order.shipped',
            'order.delivered',
            'order.cancelled',
        ],
        'webhook_retry_attempts' => env('ORDERS_WEBHOOK_RETRY_ATTEMPTS', 3),
        'webhook_timeout' => env('ORDERS_WEBHOOK_TIMEOUT', 30), // saniye
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Doğrulama kuralları
    |
    */
    'validation' => [
        'min_order_amount' => env('ORDERS_MIN_ORDER_AMOUNT', 10.00),
        'max_order_amount' => env('ORDERS_MAX_ORDER_AMOUNT', 50000.00),
        'max_items_per_order' => env('ORDERS_MAX_ITEMS_PER_ORDER', 50),
        'required_fields' => [
            'billing_address',
            'shipping_address',
            'payment_method',
            'shipping_method',
        ],
        'address_validation' => env('ORDERS_ADDRESS_VALIDATION', true),
        'phone_validation' => env('ORDERS_PHONE_VALIDATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Özellik bayrakları
    |
    */
    'features' => [
        'guest_checkout' => env('ORDERS_GUEST_CHECKOUT', true),
        'save_payment_methods' => env('ORDERS_SAVE_PAYMENT_METHODS', true),
        'order_notes' => env('ORDERS_CUSTOMER_NOTES', true),
        'gift_messages' => env('ORDERS_GIFT_MESSAGES', true),
        'delivery_instructions' => env('ORDERS_DELIVERY_INSTRUCTIONS', true),
        'order_tracking' => env('ORDERS_ORDER_TRACKING', true),
        'reorder_functionality' => env('ORDERS_REORDER_FUNCTIONALITY', true),
        'order_cancellation' => env('ORDERS_CUSTOMER_CANCELLATION', true),
    ],
];
