<?php

namespace App\Infrastructure\Orders\Listeners;

use App\Domain\Orders\Events\OrderCreated;
use App\Infrastructure\Orders\Services\OrderNotificationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

/**
 * Order Created Event Listener
 * Sipariş oluşturulduğunda çalışan infrastructure listener
 */
class OrderCreatedListener
{
    private OrderNotificationService $notificationService;

    public function __construct(OrderNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Event'i handle et
     */
    public function handle(OrderCreated $event): void
    {
        $order = $event->getOrder();

        try {
            // 1. Cache'i temizle
            $this->clearRelatedCaches($order);

            // 2. Bildirim gönder
            $this->sendNotifications($order);

            // 3. Stok rezervasyonu
            $this->reserveStock($order);

            // 4. Analytics'e kaydet
            $this->recordAnalytics($order);

            // 5. External service'lere bildir
            $this->notifyExternalServices($order);

            // 6. Fraud detection
            $this->performFraudDetection($order);

            // 7. Inventory management
            $this->updateInventory($order);

            // 8. Customer segmentation
            $this->updateCustomerSegmentation($order);

            Log::info('Order created successfully processed', [
                'order_id' => $order->getId(),
                'order_number' => $order->getOrderNumber()->getValue(),
                'user_id' => $order->getUserId(),
                'total_amount' => $order->getTotalAmount()->getAmount(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing order created event', [
                'order_id' => $order->getId(),
                'order_number' => $order->getOrderNumber()->getValue(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Hata durumunda retry mekanizması
            $this->scheduleRetry($event);
        }
    }

    /**
     * İlgili cache'leri temizle
     */
    private function clearRelatedCaches($order): void
    {
        $cacheKeys = [
            'orders.user.' . $order->getUserId() . '.*',
            'orders.pending.*',
            'orders.count.*',
            'orders.statistics',
        ];

        foreach ($cacheKeys as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }

    /**
     * Bildirim gönder
     */
    private function sendNotifications($order): void
    {
        // Müşteri ve admin bildirimlerini gönder
        $this->notificationService->sendOrderCreatedNotification($order);

        // Depo bildirimini gönder
        Queue::push('notify-warehouse', [
            'order_id' => $order->getId(),
            'action' => 'new_order',
            'priority' => $this->calculateOrderPriority($order),
        ]);

        // Muhasebe bildirimini gönder
        Queue::push('notify-accounting', [
            'order_id' => $order->getId(),
            'total_amount' => $order->getTotalAmount()->getAmount(),
            'payment_method' => $order->getPaymentMethod(),
        ]);
    }

    /**
     * Stok rezervasyonu
     */
    private function reserveStock($order): void
    {
        foreach ($order->getItems() as $item) {
            Queue::push('reserve-product-stock', [
                'product_id' => $item->getProductId(),
                'quantity' => $item->getQuantity(),
                'order_id' => $order->getId(),
                'reservation_expires_at' => now()->addHours(24)->toISOString(),
            ]);
        }

        Log::info('Stock reservation queued for order', [
            'order_id' => $order->getId(),
            'items_count' => count($order->getItems()),
        ]);
    }

    /**
     * Analytics'e kaydet
     */
    private function recordAnalytics($order): void
    {
        $analyticsData = [
            'event' => 'order_created',
            'order_id' => $order->getId(),
            'order_number' => $order->getOrderNumber()->getValue(),
            'user_id' => $order->getUserId(),
            'total_amount' => $order->getTotalAmount()->getAmount(),
            'item_count' => count($order->getItems()),
            'payment_method' => $order->getPaymentMethod(),
            'shipping_method' => $order->getShippingMethod(),
            'coupon_code' => $order->getCouponCode(),
            'created_at' => now()->toISOString(),
        ];

        Queue::push('record-analytics', $analyticsData);

        // E-commerce tracking
        Queue::push('track-ecommerce-event', [
            'event' => 'purchase',
            'order_id' => $order->getId(),
            'value' => $order->getTotalAmount()->getAmount(),
            'currency' => $order->getTotalAmount()->getCurrency(),
            'items' => $this->formatItemsForTracking($order->getItems()),
        ]);
    }

    /**
     * External service'lere bildir
     */
    private function notifyExternalServices($order): void
    {
        // CRM sistemine bildir
        if (config('integrations.crm.enabled', false)) {
            Queue::push('sync-crm-order', [
                'order_id' => $order->getId(),
                'action' => 'create',
            ]);
        }

        // ERP sistemine bildir
        if (config('integrations.erp.enabled', false)) {
            Queue::push('sync-erp-order', [
                'order_id' => $order->getId(),
                'action' => 'create',
            ]);
        }

        // Accounting sistemine bildir
        if (config('integrations.accounting.enabled', false)) {
            Queue::push('sync-accounting-order', [
                'order_id' => $order->getId(),
                'action' => 'create',
            ]);
        }

        // Marketing automation
        if (config('integrations.marketing.enabled', false)) {
            Queue::push('trigger-marketing-automation', [
                'event' => 'order_created',
                'user_id' => $order->getUserId(),
                'order_id' => $order->getId(),
                'order_value' => $order->getTotalAmount()->getAmount(),
            ]);
        }
    }

    /**
     * Fraud detection
     */
    private function performFraudDetection($order): void
    {
        $riskFactors = [
            'high_value' => $order->getTotalAmount()->getAmount() > 5000,
            'new_customer' => $this->isNewCustomer($order->getUserId()),
            'multiple_items' => count($order->getItems()) > 10,
            'international_shipping' => $this->isInternationalShipping($order),
        ];

        $riskScore = array_sum($riskFactors);

        if ($riskScore >= 2) {
            Queue::push('perform-fraud-check', [
                'order_id' => $order->getId(),
                'risk_score' => $riskScore,
                'risk_factors' => array_keys(array_filter($riskFactors)),
            ]);

            Log::warning('High risk order detected', [
                'order_id' => $order->getId(),
                'risk_score' => $riskScore,
                'risk_factors' => array_keys(array_filter($riskFactors)),
            ]);
        }
    }

    /**
     * Inventory management
     */
    private function updateInventory($order): void
    {
        // Inventory seviyelerini güncelle
        Queue::push('update-inventory-levels', [
            'order_id' => $order->getId(),
            'items' => array_map(function ($item) {
                return [
                    'product_id' => $item->getProductId(),
                    'quantity' => $item->getQuantity(),
                ];
            }, $order->getItems()),
        ]);

        // Low stock kontrolü
        Queue::push('check-low-stock-alerts', [
            'product_ids' => array_map(fn($item) => $item->getProductId(), $order->getItems()),
        ]);
    }

    /**
     * Customer segmentation
     */
    private function updateCustomerSegmentation($order): void
    {
        Queue::push('update-customer-segment', [
            'user_id' => $order->getUserId(),
            'order_value' => $order->getTotalAmount()->getAmount(),
            'order_count' => $this->getCustomerOrderCount($order->getUserId()),
        ]);
    }

    /**
     * Sipariş önceliğini hesapla
     */
    private function calculateOrderPriority($order): string
    {
        $amount = $order->getTotalAmount()->getAmount();
        
        if ($amount > 1000) {
            return 'high';
        } elseif ($amount > 500) {
            return 'medium';
        } else {
            return 'normal';
        }
    }

    /**
     * Tracking için item'ları formatla
     */
    private function formatItemsForTracking(array $items): array
    {
        return array_map(function ($item) {
            return [
                'item_id' => $item->getProductId(),
                'item_name' => $item->getProductName(),
                'quantity' => $item->getQuantity(),
                'price' => $item->getUnitPrice()->getAmount(),
            ];
        }, $items);
    }

    /**
     * Yeni müşteri mi kontrol et
     */
    private function isNewCustomer(int $userId): bool
    {
        // Bu bilgiyi customer service'den alabiliriz
        return false; // Simplified implementation
    }

    /**
     * Uluslararası kargo mı kontrol et
     */
    private function isInternationalShipping($order): bool
    {
        $shippingAddress = $order->getShippingAddress();
        return $shippingAddress && $shippingAddress->getCountry() !== 'TR';
    }

    /**
     * Müşteri sipariş sayısını al
     */
    private function getCustomerOrderCount(int $userId): int
    {
        // Bu bilgiyi order repository'den alabiliriz
        return 1; // Simplified implementation
    }

    /**
     * Retry mekanizması
     */
    private function scheduleRetry(OrderCreated $event): void
    {
        Queue::later(now()->addMinutes(5), 'retry-order-created', [
            'event' => serialize($event),
            'attempt' => 1,
        ]);
    }
}
