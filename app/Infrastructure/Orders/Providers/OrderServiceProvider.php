<?php

namespace App\Infrastructure\Orders\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Contracts\Foundation\Application;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Infrastructure\Orders\Repositories\EloquentOrderRepository;
use App\Infrastructure\Orders\Repositories\CacheOrderRepository;
use App\Infrastructure\Orders\Services\OrderStatusService;
use App\Infrastructure\Orders\Services\OrderNotificationService;
use App\Infrastructure\Orders\Listeners\OrderCreatedListener;
use App\Infrastructure\Orders\Listeners\OrderStatusChangedListener;
use App\Domain\Orders\Events\OrderCreated;
use App\Domain\Orders\Events\OrderStatusChanged;
use Illuminate\Support\Facades\Event;

/**
 * Order Infrastructure Service Provider
 * Orders modülü infrastructure katmanı için DI konfigürasyonu
 */
class OrderServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerRepositories();
        $this->registerServices();
        $this->registerEventListeners();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->bootEventListeners();
        $this->publishAssets();
    }

    /**
     * Repository'leri kaydet
     */
    private function registerRepositories(): void
    {
        // Base Eloquent Repository
        $this->app->bind(
            'order.repository.eloquent',
            EloquentOrderRepository::class
        );

        // Cache Decorator ile Repository
        $this->app->bind(OrderRepositoryInterface::class, function (Application $app) {
            $eloquentRepository = $app->make('order.repository.eloquent');

            // Cache kullanılacaksa decorator ile sar
            if (config('cache.default') !== 'array' && config('orders.cache.enabled', true)) {
                return new CacheOrderRepository(
                    $eloquentRepository,
                    config('orders.cache.ttl', 1800),
                    config('orders.cache.prefix', 'orders')
                );
            }

            return $eloquentRepository;
        });
    }

    /**
     * Infrastructure servislerini kaydet
     */
    private function registerServices(): void
    {
        // Order Status Service
        $this->app->singleton(OrderStatusService::class, function (Application $app) {
            return new OrderStatusService(
                $app->make(OrderRepositoryInterface::class)
            );
        });

        // Order Notification Service
        $this->app->singleton(OrderNotificationService::class, function (Application $app) {
            return new OrderNotificationService();
        });
    }

    /**
     * Event listener'ları kaydet
     */
    private function registerEventListeners(): void
    {
        $this->app->singleton(OrderCreatedListener::class, function (Application $app) {
            return new OrderCreatedListener(
                $app->make(OrderNotificationService::class)
            );
        });

        $this->app->singleton(OrderStatusChangedListener::class, function (Application $app) {
            return new OrderStatusChangedListener(
                $app->make(OrderNotificationService::class)
            );
        });
    }

    /**
     * Event listener'ları boot et
     */
    private function bootEventListeners(): void
    {
        Event::listen(OrderCreated::class, OrderCreatedListener::class);
        Event::listen(OrderStatusChanged::class, OrderStatusChangedListener::class);
    }

    /**
     * Asset'leri publish et
     */
    private function publishAssets(): void
    {
        // Config dosyalarını publish et
        $this->publishes([
            __DIR__ . '/../Config/orders.php' => config_path('orders.php'),
        ], 'orders-config');

        // Migration dosyalarını publish et
        $this->publishes([
            __DIR__ . '/../Database/Migrations' => database_path('migrations'),
        ], 'orders-migrations');

        // View dosyalarını publish et
        $this->publishes([
            __DIR__ . '/../Resources/views' => resource_path('views/orders'),
        ], 'orders-views');

        // Email template'lerini publish et
        $this->publishes([
            __DIR__ . '/../Resources/views/emails' => resource_path('views/emails/orders'),
        ], 'orders-email-templates');
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            OrderRepositoryInterface::class,
            OrderStatusService::class,
            OrderNotificationService::class,
            OrderCreatedListener::class,
            OrderStatusChangedListener::class,
        ];
    }
}
