<?php

namespace App\Infrastructure\Categories\Services;

use App\Domain\Categories\Repositories\CategoryRepositoryInterface;
use App\Domain\Categories\Entities\Category;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Category Tree Service
 * Kategori ağaç yapısı yönetimi için infrastructure service
 */
class CategoryTreeService
{
    private CategoryRepositoryInterface $repository;

    public function __construct(CategoryRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    /**
     * <PERSON>gori ağacını JSON formatında al
     */
    public function getTreeAsJson(?int $parentId = null, int $maxDepth = 10): string
    {
        $tree = $this->repository->getTree($parentId, $maxDepth);
        return json_encode($this->formatTreeForJson($tree));
    }

    /**
     * Kategori ağacını HTML select için formatla
     */
    public function getTreeForSelect(?int $parentId = null, int $maxDepth = 10): array
    {
        $tree = $this->repository->getTree($parentId, $maxDepth);
        return $this->formatTreeForSelect($tree);
    }

    /**
     * Kategori ağacını breadcrumb için formatla
     */
    public function getBreadcrumb(int $categoryId): array
    {
        $path = $this->repository->getPath($categoryId);
        
        return array_map(function ($category) {
            return [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'slug' => $category->getSlug()->getValue(),
                'url' => "/categories/{$category->getSlug()->getValue()}",
            ];
        }, $path);
    }

    /**
     * Kategori ağacını menu için formatla
     */
    public function getMenuTree(?int $parentId = null, int $maxDepth = 3): array
    {
        $categories = $this->repository->findVisibleInMenu();
        return $this->buildMenuTree($categories, $parentId, $maxDepth);
    }

    /**
     * Kategoriyi taşı
     */
    public function moveCategory(int $categoryId, ?int $newParentId, int $newPosition = 0): bool
    {
        try {
            DB::beginTransaction();

            // Geçerlilik kontrolü
            if (!$this->canMoveCategory($categoryId, $newParentId)) {
                throw new \InvalidArgumentException('Kategori bu konuma taşınamaz');
            }

            // Kategoriyi taşı
            $moved = $this->repository->move($categoryId, $newParentId, $newPosition);

            if ($moved) {
                // Path'leri yeniden hesapla
                $this->recalculatePaths($categoryId);
                
                // Level'ları yeniden hesapla
                $this->recalculateLevels($categoryId);
                
                DB::commit();
                
                Log::info('Category moved successfully', [
                    'category_id' => $categoryId,
                    'new_parent_id' => $newParentId,
                    'new_position' => $newPosition,
                ]);
                
                return true;
            }

            DB::rollBack();
            return false;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Error moving category', [
                'category_id' => $categoryId,
                'new_parent_id' => $newParentId,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Kardeş kategorileri yeniden sırala
     */
    public function reorderSiblings(array $categoryIds): bool
    {
        try {
            DB::beginTransaction();

            $result = $this->repository->reorderSiblings($categoryIds);

            if ($result) {
                DB::commit();
                
                Log::info('Categories reordered successfully', [
                    'category_ids' => $categoryIds,
                ]);
                
                return true;
            }

            DB::rollBack();
            return false;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Error reordering categories', [
                'category_ids' => $categoryIds,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Kategori ağacını yeniden oluştur
     */
    public function rebuildTree(): bool
    {
        try {
            DB::beginTransaction();

            // Path'leri yeniden hesapla
            $pathsRebuilt = $this->repository->rebuildPaths();
            
            // Level'ları yeniden hesapla
            $levelsRebuilt = $this->repository->rebuildLevels();
            
            // Tree'yi yeniden oluştur
            $treeRebuilt = $this->repository->rebuildTree();

            if ($pathsRebuilt && $levelsRebuilt && $treeRebuilt) {
                DB::commit();
                
                Log::info('Category tree rebuilt successfully');
                return true;
            }

            DB::rollBack();
            return false;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Error rebuilding category tree', [
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Kategori ağacı istatistiklerini al
     */
    public function getTreeStatistics(): array
    {
        $stats = $this->repository->getStatistics();
        $levelStats = $this->repository->getLevelStatistics();

        return [
            'total_categories' => $stats['total'] ?? 0,
            'active_categories' => $stats['active'] ?? 0,
            'inactive_categories' => $stats['inactive'] ?? 0,
            'featured_categories' => $stats['featured'] ?? 0,
            'empty_categories' => count($this->repository->findEmpty()),
            'max_depth' => $this->repository->getMaxLevel(),
            'level_distribution' => $levelStats,
            'tree_health' => $this->checkTreeHealth(),
        ];
    }

    /**
     * Kategori ağacının sağlığını kontrol et
     */
    public function checkTreeHealth(): array
    {
        $issues = [];

        // Orphaned categories (parent'ı olmayan ama root olmayan)
        $orphaned = $this->findOrphanedCategories();
        if (!empty($orphaned)) {
            $issues[] = [
                'type' => 'orphaned_categories',
                'count' => count($orphaned),
                'description' => 'Parent kategorisi olmayan kategoriler',
            ];
        }

        // Circular references
        $circular = $this->findCircularReferences();
        if (!empty($circular)) {
            $issues[] = [
                'type' => 'circular_references',
                'count' => count($circular),
                'description' => 'Döngüsel referanslar',
            ];
        }

        // Incorrect levels
        $incorrectLevels = $this->findIncorrectLevels();
        if (!empty($incorrectLevels)) {
            $issues[] = [
                'type' => 'incorrect_levels',
                'count' => count($incorrectLevels),
                'description' => 'Yanlış seviye değerleri',
            ];
        }

        return [
            'healthy' => empty($issues),
            'issues' => $issues,
            'total_issues' => count($issues),
        ];
    }

    /**
     * JSON formatı için ağacı formatla
     */
    private function formatTreeForJson(array $tree): array
    {
        return array_map(function ($category) {
            return [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'slug' => $category->getSlug()->getValue(),
                'level' => $category->getLevel(),
                'position' => $category->getPosition(),
                'status' => $category->getStatus(),
                'featured' => $category->isFeatured(),
                'product_count' => $category->getProductCount(),
                'children' => $this->formatTreeForJson($category->getChildren()),
            ];
        }, $tree);
    }

    /**
     * Select formatı için ağacı formatla
     */
    private function formatTreeForSelect(array $tree, int $level = 0): array
    {
        $options = [];
        
        foreach ($tree as $category) {
            $indent = str_repeat('— ', $level);
            $options[] = [
                'value' => $category->getId(),
                'label' => $indent . $category->getName(),
                'level' => $level,
            ];
            
            if (!empty($category->getChildren())) {
                $childOptions = $this->formatTreeForSelect($category->getChildren(), $level + 1);
                $options = array_merge($options, $childOptions);
            }
        }
        
        return $options;
    }

    /**
     * Menu ağacını oluştur
     */
    private function buildMenuTree(array $categories, ?int $parentId, int $maxDepth, int $currentDepth = 0): array
    {
        if ($currentDepth >= $maxDepth) {
            return [];
        }

        $tree = [];
        
        foreach ($categories as $category) {
            if ($category->getParentId() === $parentId) {
                $children = $this->buildMenuTree($categories, $category->getId(), $maxDepth, $currentDepth + 1);
                
                $tree[] = [
                    'id' => $category->getId(),
                    'name' => $category->getName(),
                    'slug' => $category->getSlug()->getValue(),
                    'url' => "/categories/{$category->getSlug()->getValue()}",
                    'children' => $children,
                ];
            }
        }
        
        return $tree;
    }

    /**
     * Kategori taşınabilir mi kontrol et
     */
    private function canMoveCategory(int $categoryId, ?int $newParentId): bool
    {
        if ($newParentId === null) {
            return true; // Root'a taşınabilir
        }

        if ($categoryId === $newParentId) {
            return false; // Kendisine taşınamaz
        }

        // Alt kategorilerinden birine taşınamaz
        $descendants = $this->repository->getDescendants($categoryId);
        foreach ($descendants as $descendant) {
            if ($descendant->getId() === $newParentId) {
                return false;
            }
        }

        return true;
    }

    /**
     * Path'leri yeniden hesapla
     */
    private function recalculatePaths(int $categoryId): void
    {
        $descendants = $this->repository->getDescendants($categoryId);
        
        foreach ($descendants as $descendant) {
            // Her descendant için path'i yeniden hesapla
            $path = $this->repository->getPath($descendant->getId());
            // Path güncelleme logic'i burada olacak
        }
    }

    /**
     * Level'ları yeniden hesapla
     */
    private function recalculateLevels(int $categoryId): void
    {
        $descendants = $this->repository->getDescendants($categoryId);
        
        foreach ($descendants as $descendant) {
            // Her descendant için level'ı yeniden hesapla
            $level = $this->repository->getLevel($descendant->getId());
            // Level güncelleme logic'i burada olacak
        }
    }

    /**
     * Orphaned kategorileri bul
     */
    private function findOrphanedCategories(): array
    {
        // Bu method'un implementasyonu repository'de olmalı
        return [];
    }

    /**
     * Circular reference'ları bul
     */
    private function findCircularReferences(): array
    {
        // Bu method'un implementasyonu repository'de olmalı
        return [];
    }

    /**
     * Yanlış level'ları bul
     */
    private function findIncorrectLevels(): array
    {
        // Bu method'un implementasyonu repository'de olmalı
        return [];
    }
}
