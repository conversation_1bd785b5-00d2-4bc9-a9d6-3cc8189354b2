<?php

namespace App\Infrastructure\Categories\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Contracts\Foundation\Application;
use App\Domain\Categories\Repositories\CategoryRepositoryInterface;
use App\Infrastructure\Categories\Repositories\EloquentCategoryRepository;
use App\Infrastructure\Categories\Repositories\CacheCategoryRepository;
use App\Infrastructure\Categories\Services\CategoryTreeService;
use App\Infrastructure\Categories\Services\CategoryBulkService;
use App\Infrastructure\Categories\Listeners\CategoryCreatedListener;
use App\Infrastructure\Categories\Listeners\CategoryMovedListener;
use App\Infrastructure\Categories\Listeners\CategoryUpdatedListener;
use App\Domain\Categories\Events\CategoryCreated;
use App\Domain\Categories\Events\CategoryMoved;
use App\Domain\Categories\Events\CategoryUpdated;
use Illuminate\Support\Facades\Event;

/**
 * Category Infrastructure Service Provider
 * Categories modülü infrastructure katmanı için DI konfigürasyonu
 */
class CategoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerRepositories();
        $this->registerServices();
        $this->registerEventListeners();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->bootEventListeners();
        $this->publishAssets();
    }

    /**
     * Repository'leri kaydet
     */
    private function registerRepositories(): void
    {
        // Base Eloquent Repository
        $this->app->bind(
            'category.repository.eloquent',
            EloquentCategoryRepository::class
        );

        // Cache Decorator ile Repository
        $this->app->bind(CategoryRepositoryInterface::class, function (Application $app) {
            $eloquentRepository = $app->make('category.repository.eloquent');

            // Cache kullanılacaksa decorator ile sar
            if (config('cache.default') !== 'array' && config('categories.cache.enabled', true)) {
                return new CacheCategoryRepository(
                    $eloquentRepository,
                    config('categories.cache.ttl', 3600),
                    config('categories.cache.prefix', 'categories')
                );
            }

            return $eloquentRepository;
        });
    }

    /**
     * Infrastructure servislerini kaydet
     */
    private function registerServices(): void
    {
        // Category Tree Service
        $this->app->singleton(CategoryTreeService::class, function (Application $app) {
            return new CategoryTreeService(
                $app->make(CategoryRepositoryInterface::class)
            );
        });

        // Category Bulk Service
        $this->app->singleton(CategoryBulkService::class, function (Application $app) {
            return new CategoryBulkService(
                $app->make(CategoryRepositoryInterface::class)
            );
        });
    }

    /**
     * Event listener'ları kaydet
     */
    private function registerEventListeners(): void
    {
        $this->app->singleton(CategoryCreatedListener::class, function (Application $app) {
            return new CategoryCreatedListener(
                $app->make(CategoryTreeService::class)
            );
        });

        $this->app->singleton(CategoryMovedListener::class, function (Application $app) {
            return new CategoryMovedListener(
                $app->make(CategoryTreeService::class)
            );
        });

        $this->app->singleton(CategoryUpdatedListener::class, function (Application $app) {
            return new CategoryUpdatedListener(
                $app->make(CategoryTreeService::class)
            );
        });
    }

    /**
     * Event listener'ları boot et
     */
    private function bootEventListeners(): void
    {
        Event::listen(CategoryCreated::class, CategoryCreatedListener::class);
        Event::listen(CategoryMoved::class, CategoryMovedListener::class);
        Event::listen(CategoryUpdated::class, CategoryUpdatedListener::class);
    }

    /**
     * Asset'leri publish et
     */
    private function publishAssets(): void
    {
        // Config dosyalarını publish et
        $this->publishes([
            __DIR__ . '/../Config/categories.php' => config_path('categories.php'),
        ], 'categories-config');

        // Migration dosyalarını publish et
        $this->publishes([
            __DIR__ . '/../Database/Migrations' => database_path('migrations'),
        ], 'categories-migrations');

        // View dosyalarını publish et
        $this->publishes([
            __DIR__ . '/../Resources/views' => resource_path('views/categories'),
        ], 'categories-views');
    }

    /**
     * Provides
     */
    public function provides(): array
    {
        return [
            CategoryRepositoryInterface::class,
            CategoryTreeService::class,
            CategoryBulkService::class,
            CategoryCreatedListener::class,
            CategoryMovedListener::class,
            CategoryUpdatedListener::class,
        ];
    }
}
