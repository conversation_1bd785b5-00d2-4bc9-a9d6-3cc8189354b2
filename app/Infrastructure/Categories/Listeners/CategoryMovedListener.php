<?php

namespace App\Infrastructure\Categories\Listeners;

use App\Domain\Categories\Events\CategoryMoved;
use App\Infrastructure\Categories\Services\CategoryTreeService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

/**
 * Category Moved Event Listener
 * Kate<PERSON>i taşındığında çalışan infrastructure listener
 */
class CategoryMovedListener
{
    private CategoryTreeService $treeService;

    public function __construct(CategoryTreeService $treeService)
    {
        $this->treeService = $treeService;
    }

    /**
     * Event'i handle et
     */
    public function handle(CategoryMoved $event): void
    {
        $category = $event->getCategory();
        $oldParentId = $event->getOldParentId();
        $newParentId = $event->getNewParentId();

        try {
            // 1. Cache'i temizle
            $this->clearRelatedCaches($category, $oldParentId, $newParentId);

            // 2. Tree yapısını yeniden hesapla
            $this->recalculateTreeStructure($category, $oldParentId, $newParentId);

            // 3. Path'leri güncelle
            $this->updatePaths($category);

            // 4. Level'ları güncelle
            $this->updateLevels($category);

            // 5. Parent kategorilerin ürün sayılarını güncelle
            $this->updateParentProductCounts($oldParentId, $newParentId);

            // 6. URL'leri güncelle
            $this->updateUrls($category);

            // 7. Analytics'e kaydet
            $this->recordAnalytics($category, $oldParentId, $newParentId);

            // 8. SEO güncellemeleri
            $this->updateSeoData($category);

            // 9. Menu cache'ini güncelle
            $this->updateMenuCache($category, $oldParentId, $newParentId);

            Log::info('Category moved successfully processed', [
                'category_id' => $category->getId(),
                'category_name' => $category->getName(),
                'old_parent_id' => $oldParentId,
                'new_parent_id' => $newParentId,
                'new_level' => $category->getLevel(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing category moved event', [
                'category_id' => $category->getId(),
                'old_parent_id' => $oldParentId,
                'new_parent_id' => $newParentId,
                'error' => $e->getMessage(),
            ]);

            // Hata durumunda retry mekanizması
            $this->scheduleRetry($event);
        }
    }

    /**
     * İlgili cache'leri temizle
     */
    private function clearRelatedCaches($category, $oldParentId, $newParentId): void
    {
        $cacheKeys = [
            'categories.tree.*',
            'categories.root.*',
            'categories.active.*',
            'categories.menu.*',
            'category.' . $category->getId(),
            'category.path.' . $category->getId(),
            'category.ancestors.' . $category->getId(),
            'category.descendants.' . $category->getId(),
            'category.level.' . $category->getId(),
        ];

        // Eski parent cache'ini temizle
        if ($oldParentId) {
            $cacheKeys[] = 'categories.parent.' . $oldParentId . '.*';
            $cacheKeys[] = 'category.has_children.' . $oldParentId;
            $cacheKeys[] = 'category.descendants.' . $oldParentId;
        }

        // Yeni parent cache'ini temizle
        if ($newParentId) {
            $cacheKeys[] = 'categories.parent.' . $newParentId . '.*';
            $cacheKeys[] = 'category.has_children.' . $newParentId;
            $cacheKeys[] = 'category.descendants.' . $newParentId;
        }

        foreach ($cacheKeys as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }

    /**
     * Tree yapısını yeniden hesapla
     */
    private function recalculateTreeStructure($category, $oldParentId, $newParentId): void
    {
        // Eski parent'ın children count'unu güncelle
        if ($oldParentId) {
            Queue::push('update-category-children-count', [
                'category_id' => $oldParentId,
            ]);
        }

        // Yeni parent'ın children count'unu güncelle
        if ($newParentId) {
            Queue::push('update-category-children-count', [
                'category_id' => $newParentId,
            ]);
        }

        // Tree istatistiklerini güncelle
        Queue::push('update-tree-statistics', [
            'action' => 'category_moved',
            'category_id' => $category->getId(),
            'old_parent_id' => $oldParentId,
            'new_parent_id' => $newParentId,
        ]);
    }

    /**
     * Path'leri güncelle
     */
    private function updatePaths($category): void
    {
        // Kategori ve tüm alt kategorilerinin path'lerini güncelle
        Queue::push('update-category-paths', [
            'category_id' => $category->getId(),
            'update_descendants' => true,
        ]);
    }

    /**
     * Level'ları güncelle
     */
    private function updateLevels($category): void
    {
        // Kategori ve tüm alt kategorilerinin level'larını güncelle
        Queue::push('update-category-levels', [
            'category_id' => $category->getId(),
            'update_descendants' => true,
        ]);
    }

    /**
     * Parent kategorilerin ürün sayılarını güncelle
     */
    private function updateParentProductCounts($oldParentId, $newParentId): void
    {
        // Eski parent'ın ürün sayısını güncelle
        if ($oldParentId) {
            Queue::push('recalculate-parent-product-count', [
                'parent_id' => $oldParentId,
            ]);
        }

        // Yeni parent'ın ürün sayısını güncelle
        if ($newParentId) {
            Queue::push('recalculate-parent-product-count', [
                'parent_id' => $newParentId,
            ]);
        }
    }

    /**
     * URL'leri güncelle
     */
    private function updateUrls($category): void
    {
        // Kategori URL'ini güncelle
        Queue::push('update-category-url', [
            'category_id' => $category->getId(),
        ]);

        // Alt kategorilerin URL'lerini güncelle
        Queue::push('update-descendant-urls', [
            'category_id' => $category->getId(),
        ]);

        // Ürün URL'lerini güncelle (kategori path değiştiği için)
        Queue::push('update-product-urls-for-category', [
            'category_id' => $category->getId(),
        ]);
    }

    /**
     * Analytics'e kaydet
     */
    private function recordAnalytics($category, $oldParentId, $newParentId): void
    {
        $analyticsData = [
            'event' => 'category_moved',
            'category_id' => $category->getId(),
            'category_name' => $category->getName(),
            'old_parent_id' => $oldParentId,
            'new_parent_id' => $newParentId,
            'old_level' => $this->calculateOldLevel($oldParentId),
            'new_level' => $category->getLevel(),
            'moved_at' => now()->toISOString(),
        ];

        Queue::push('record-analytics', $analyticsData);
    }

    /**
     * SEO verilerini güncelle
     */
    private function updateSeoData($category): void
    {
        // Kategori SEO verilerini güncelle
        Queue::push('update-category-seo', [
            'category_id' => $category->getId(),
            'reason' => 'moved',
        ]);

        // Sitemap'i güncelle
        Queue::push('update-sitemap', [
            'type' => 'category',
            'action' => 'update',
            'category_id' => $category->getId(),
            'url' => "/categories/{$category->getSlug()->getValue()}",
            'last_modified' => now()->toISOString(),
        ]);

        // Alt kategorilerin SEO verilerini güncelle
        Queue::push('update-descendant-seo', [
            'category_id' => $category->getId(),
        ]);
    }

    /**
     * Menu cache'ini güncelle
     */
    private function updateMenuCache($category, $oldParentId, $newParentId): void
    {
        if ($category->isShowInMenu()) {
            // Menu cache'ini temizle
            Cache::forget('menu.categories');
            Cache::forget('menu.tree');
            
            // Yeni menu'yu pre-generate et
            Queue::push('regenerate-menu-cache', [
                'trigger' => 'category_moved',
                'category_id' => $category->getId(),
                'old_parent_id' => $oldParentId,
                'new_parent_id' => $newParentId,
            ]);
        }
    }

    /**
     * Eski level'ı hesapla
     */
    private function calculateOldLevel($oldParentId): int
    {
        if ($oldParentId === null) {
            return 0; // Root level
        }

        // Bu bilgiyi event'ten alabiliriz veya hesaplayabiliriz
        return 1; // Simplified calculation
    }

    /**
     * Retry mekanizması
     */
    private function scheduleRetry(CategoryMoved $event): void
    {
        Queue::later(now()->addMinutes(5), 'retry-category-moved', [
            'event' => serialize($event),
            'attempt' => 1,
        ]);
    }
}
