<?php

namespace App\Infrastructure\Categories\Listeners;

use App\Domain\Categories\Events\CategoryCreated;
use App\Infrastructure\Categories\Services\CategoryTreeService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

/**
 * Category Created Event Listener
 * Kategori oluşturulduğunda çalışan infrastructure listener
 */
class CategoryCreatedListener
{
    private CategoryTreeService $treeService;

    public function __construct(CategoryTreeService $treeService)
    {
        $this->treeService = $treeService;
    }

    /**
     * Event'i handle et
     */
    public function handle(CategoryCreated $event): void
    {
        $category = $event->getCategory();

        try {
            // 1. Cache'i temizle
            $this->clearRelatedCaches($category);

            // 2. Tree yapısını güncelle
            $this->updateTreeStructure($category);

            // 3. Parent kategori ürün sayısını güncelle
            $this->updateParentProductCount($category);

            // 4. Notification gönder
            $this->sendNotifications($category);

            // 5. Analytics'e kaydet
            $this->recordAnalytics($category);

            // 6. SEO sitemap'i güncelle
            $this->updateSitemap($category);

            // 7. Menu cache'ini güncelle
            $this->updateMenuCache($category);

            Log::info('Category created successfully processed', [
                'category_id' => $category->getId(),
                'category_name' => $category->getName(),
                'parent_id' => $category->getParentId(),
                'level' => $category->getLevel(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing category created event', [
                'category_id' => $category->getId(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Hata durumunda retry mekanizması
            $this->scheduleRetry($event);
        }
    }

    /**
     * İlgili cache'leri temizle
     */
    private function clearRelatedCaches($category): void
    {
        $cacheKeys = [
            'categories.tree.*',
            'categories.root.*',
            'categories.active.*',
            'categories.featured.*',
            'categories.menu.*',
            'categories.count.*',
        ];

        // Parent kategorisi varsa onun cache'ini de temizle
        if ($category->getParentId()) {
            $cacheKeys[] = 'categories.parent.' . $category->getParentId() . '.*';
            $cacheKeys[] = 'category.has_children.' . $category->getParentId();
        }

        foreach ($cacheKeys as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }

    /**
     * Tree yapısını güncelle
     */
    private function updateTreeStructure($category): void
    {
        // Parent kategorisi varsa children count'unu güncelle
        if ($category->getParentId()) {
            Queue::push('update-category-children-count', [
                'category_id' => $category->getParentId(),
            ]);
        }

        // Tree istatistiklerini güncelle
        Queue::push('update-tree-statistics', [
            'action' => 'category_added',
            'category_id' => $category->getId(),
            'level' => $category->getLevel(),
        ]);
    }

    /**
     * Parent kategori ürün sayısını güncelle
     */
    private function updateParentProductCount($category): void
    {
        if ($category->getParentId()) {
            Queue::push('recalculate-parent-product-count', [
                'parent_id' => $category->getParentId(),
            ]);
        }
    }

    /**
     * Bildirim gönder
     */
    private function sendNotifications($category): void
    {
        // Admin'lere yeni kategori bildirimi
        Queue::push('send-admin-notification', [
            'type' => 'category_created',
            'category_id' => $category->getId(),
            'category_name' => $category->getName(),
            'parent_id' => $category->getParentId(),
            'message' => "Yeni kategori eklendi: {$category->getName()}",
        ]);

        // Eğer kategori featured ise, özel bildirim gönder
        if ($category->isFeatured()) {
            Queue::push('send-featured-category-notification', [
                'category_id' => $category->getId(),
                'category_name' => $category->getName(),
            ]);
        }

        // Eğer kategori menüde gösterilecekse, menu güncellemesi bildir
        if ($category->isShowInMenu()) {
            Queue::push('notify-menu-update', [
                'action' => 'category_added',
                'category_id' => $category->getId(),
            ]);
        }
    }

    /**
     * Analytics'e kaydet
     */
    private function recordAnalytics($category): void
    {
        $analyticsData = [
            'event' => 'category_created',
            'category_id' => $category->getId(),
            'category_name' => $category->getName(),
            'parent_id' => $category->getParentId(),
            'level' => $category->getLevel(),
            'is_featured' => $category->isFeatured(),
            'show_in_menu' => $category->isShowInMenu(),
            'created_at' => now()->toISOString(),
        ];

        Queue::push('record-analytics', $analyticsData);
    }

    /**
     * SEO sitemap'i güncelle
     */
    private function updateSitemap($category): void
    {
        // Sitemap güncellemesini queue'ya ekle
        Queue::push('update-sitemap', [
            'type' => 'category',
            'action' => 'add',
            'category_id' => $category->getId(),
            'url' => "/categories/{$category->getSlug()->getValue()}",
            'last_modified' => now()->toISOString(),
            'priority' => $this->calculateSitemapPriority($category),
        ]);
    }

    /**
     * Menu cache'ini güncelle
     */
    private function updateMenuCache($category): void
    {
        if ($category->isShowInMenu()) {
            // Menu cache'ini temizle
            Cache::forget('menu.categories');
            Cache::forget('menu.tree');
            
            // Yeni menu'yu pre-generate et
            Queue::push('regenerate-menu-cache', [
                'trigger' => 'category_created',
                'category_id' => $category->getId(),
            ]);
        }
    }

    /**
     * Sitemap priority hesapla
     */
    private function calculateSitemapPriority($category): float
    {
        $priority = 0.5; // Base priority

        // Root kategoriler daha yüksek priority
        if ($category->getLevel() === 0) {
            $priority += 0.3;
        }

        // Featured kategoriler daha yüksek priority
        if ($category->isFeatured()) {
            $priority += 0.2;
        }

        // Menu'de gösterilen kategoriler daha yüksek priority
        if ($category->isShowInMenu()) {
            $priority += 0.1;
        }

        return min(1.0, $priority);
    }

    /**
     * Retry mekanizması
     */
    private function scheduleRetry(CategoryCreated $event): void
    {
        Queue::later(now()->addMinutes(5), 'retry-category-created', [
            'event' => serialize($event),
            'attempt' => 1,
        ]);
    }
}
