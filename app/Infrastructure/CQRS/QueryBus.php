<?php

namespace App\Infrastructure\CQRS;

use App\Core\CQRS\Contracts\QueryBusInterface;
use App\Core\CQRS\Contracts\QueryInterface;
use App\Core\CQRS\Contracts\QueryHandlerInterface;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Query Bus Implementation
 * Query'leri ilgili handler'lara yönlendirir
 */
class QueryBus implements QueryBusInterface
{
    protected Container $container;
    protected array $handlers = [];
    protected array $middlewares = [];

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * Query'yi dispatch et
     *
     * @param QueryInterface $query
     * @return mixed
     * @throws \Exception
     */
    public function dispatch(QueryInterface $query)
    {
        $queryClass = get_class($query);
        
        if (!isset($this->handlers[$queryClass])) {
            throw new \InvalidArgumentException("No handler registered for query: {$queryClass}");
        }

        $handlerClass = $this->handlers[$queryClass];
        $handler = $this->container->make($handlerClass);

        if (!$handler instanceof QueryHandlerInterface) {
            throw new \InvalidArgumentException("Handler {$handlerClass} must implement QueryHandlerInterface");
        }

        // Cache kontrolü
        if ($handler->isCacheable() && $query->getCacheKey()) {
            $cached = $this->getFromCache($query);
            if ($cached !== null) {
                $this->logCacheHit($query, $handler);
                return $cached;
            }
        }

        // Middleware'leri çalıştır
        $pipeline = array_reduce(
            array_reverse($this->middlewares),
            function ($next, $middleware) {
                return function ($query) use ($middleware, $next) {
                    return $middleware($query, $next);
                };
            },
            function ($query) use ($handler) {
                return $handler->handle($query);
            }
        );

        $startTime = microtime(true);
        
        try {
            $result = $pipeline($query);
            
            // Sonucu cache'le
            if ($handler->isCacheable() && $query->getCacheKey() && $query->getCacheTtl()) {
                $this->putToCache($query, $result);
            }
            
            $this->logQuerySuccess($query, $handler, microtime(true) - $startTime);
            
            return $result;
        } catch (\Exception $e) {
            $this->logQueryFailure($query, $handler, $e, microtime(true) - $startTime);
            throw $e;
        }
    }

    /**
     * Query handler'ı kaydet
     *
     * @param string $queryClass
     * @param string $handlerClass
     * @return void
     */
    public function registerHandler(string $queryClass, string $handlerClass): void
    {
        $this->handlers[$queryClass] = $handlerClass;
        
        Log::debug("Query handler registered", [
            'query' => $queryClass,
            'handler' => $handlerClass
        ]);
    }

    /**
     * Middleware ekle
     *
     * @param callable $middleware
     * @return void
     */
    public function addMiddleware(callable $middleware): void
    {
        $this->middlewares[] = $middleware;
    }

    /**
     * Kayıtlı handler'ları al
     *
     * @return array
     */
    public function getRegisteredHandlers(): array
    {
        return $this->handlers;
    }

    /**
     * Cache'den sonuç al
     *
     * @param QueryInterface $query
     * @return mixed|null
     */
    public function getFromCache(QueryInterface $query)
    {
        if (!$query->getCacheKey()) {
            return null;
        }

        return Cache::get($query->getCacheKey());
    }

    /**
     * Sonucu cache'le
     *
     * @param QueryInterface $query
     * @param mixed $result
     * @return void
     */
    public function putToCache(QueryInterface $query, $result): void
    {
        if (!$query->getCacheKey() || !$query->getCacheTtl()) {
            return;
        }

        Cache::put($query->getCacheKey(), $result, $query->getCacheTtl());
        
        Log::debug("Query result cached", [
            'query_id' => $query->getId(),
            'cache_key' => $query->getCacheKey(),
            'cache_ttl' => $query->getCacheTtl(),
        ]);
    }

    /**
     * Cache hit'i logla
     *
     * @param QueryInterface $query
     * @param QueryHandlerInterface $handler
     * @return void
     */
    protected function logCacheHit(QueryInterface $query, QueryHandlerInterface $handler): void
    {
        Log::info("Query cache hit", [
            'query_id' => $query->getId(),
            'query_class' => get_class($query),
            'handler_class' => get_class($handler),
            'cache_key' => $query->getCacheKey(),
        ]);
    }

    /**
     * Query başarısını logla
     *
     * @param QueryInterface $query
     * @param QueryHandlerInterface $handler
     * @param float $executionTime
     * @return void
     */
    protected function logQuerySuccess(
        QueryInterface $query, 
        QueryHandlerInterface $handler, 
        float $executionTime
    ): void {
        Log::info("Query dispatched successfully", [
            'query_id' => $query->getId(),
            'query_class' => get_class($query),
            'handler_class' => get_class($handler),
            'execution_time_ms' => round($executionTime * 1000, 2),
            'cached' => false,
        ]);
    }

    /**
     * Query hatasını logla
     *
     * @param QueryInterface $query
     * @param QueryHandlerInterface $handler
     * @param \Exception $exception
     * @param float $executionTime
     * @return void
     */
    protected function logQueryFailure(
        QueryInterface $query, 
        QueryHandlerInterface $handler, 
        \Exception $exception,
        float $executionTime
    ): void {
        Log::error("Query dispatch failed", [
            'query_id' => $query->getId(),
            'query_class' => get_class($query),
            'handler_class' => get_class($handler),
            'execution_time_ms' => round($executionTime * 1000, 2),
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
        ]);
    }
}
