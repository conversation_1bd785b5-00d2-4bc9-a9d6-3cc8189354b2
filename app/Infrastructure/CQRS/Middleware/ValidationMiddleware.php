<?php

namespace App\Infrastructure\CQRS\Middleware;

use App\Core\CQRS\Contracts\CommandInterface;
use App\Core\CQRS\Contracts\QueryInterface;
use Illuminate\Support\Facades\Log;

/**
 * Validation Middleware
 * Command ve Query'leri validate eder
 */
class ValidationMiddleware
{
    /**
     * Middleware'i çalıştır
     *
     * @param CommandInterface|QueryInterface $message
     * @param callable $next
     * @return mixed
     * @throws \InvalidArgumentException
     */
    public function __invoke($message, callable $next)
    {
        $startTime = microtime(true);
        
        try {
            // Message'ı validate et
            $this->validateMessage($message);
            
            $result = $next($message);
            
            $this->logValidationSuccess($message, microtime(true) - $startTime);
            
            return $result;
        } catch (\InvalidArgumentException $e) {
            $this->logValidationFailure($message, $e, microtime(true) - $startTime);
            throw $e;
        }
    }

    /**
     * Message'ı validate et
     *
     * @param CommandInterface|QueryInterface $message
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateMessage($message): void
    {
        if (!($message instanceof CommandInterface) && !($message instanceof QueryInterface)) {
            throw new \InvalidArgumentException('Message must implement CommandInterface or QueryInterface');
        }

        // Message'ın kendi validation'ını çalıştır
        if (method_exists($message, 'validate')) {
            $message->validate();
        }

        // Ek validation kuralları
        $this->validateMessageStructure($message);
    }

    /**
     * Message yapısını validate et
     *
     * @param CommandInterface|QueryInterface $message
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateMessageStructure($message): void
    {
        // ID kontrolü
        if (empty($message->getId())) {
            throw new \InvalidArgumentException('Message ID cannot be empty');
        }

        // Created at kontrolü
        if (!$message->getCreatedAt() instanceof \DateTimeInterface) {
            throw new \InvalidArgumentException('Message created_at must be a valid DateTime');
        }

        // Rules kontrolü
        if (!is_array($message->rules())) {
            throw new \InvalidArgumentException('Message rules() must return an array');
        }
    }

    /**
     * Validation başarısını logla
     *
     * @param CommandInterface|QueryInterface $message
     * @param float $executionTime
     * @return void
     */
    protected function logValidationSuccess($message, float $executionTime): void
    {
        Log::debug('Message validation successful', [
            'message_id' => $message->getId(),
            'message_class' => get_class($message),
            'validation_time_ms' => round($executionTime * 1000, 2),
        ]);
    }

    /**
     * Validation hatasını logla
     *
     * @param CommandInterface|QueryInterface $message
     * @param \InvalidArgumentException $exception
     * @param float $executionTime
     * @return void
     */
    protected function logValidationFailure($message, \InvalidArgumentException $exception, float $executionTime): void
    {
        Log::warning('Message validation failed', [
            'message_id' => $message->getId(),
            'message_class' => get_class($message),
            'validation_time_ms' => round($executionTime * 1000, 2),
            'error_message' => $exception->getMessage(),
        ]);
    }
}
