<?php

namespace App\Infrastructure\Products\Repositories;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Models\Product as EloquentProduct;
use Carbon\Carbon;

class EloquentProductRepository implements ProductRepositoryInterface
{
    protected EloquentProduct $model;

    public function __construct(EloquentProduct $model)
    {
        $this->model = $model;
    }

    public function save(Product $product): Product
    {
        $eloquentProduct = $this->toEloquentModel($product);
        $eloquentProduct->save();

        return $this->toDomainEntity($eloquentProduct);
    }

    public function findById(int $id): ?Product
    {
        $eloquentProduct = $this->model->find($id);

        return $eloquentProduct ? $this->toDomainEntity($eloquentProduct) : null;
    }

    public function findBySlug(string $slug): ?Product
    {
        $eloquentProduct = $this->model->where('slug', $slug)->first();

        return $eloquentProduct ? $this->toDomainEntity($eloquentProduct) : null;
    }

    public function findBySKU(SKU $sku): ?Product
    {
        $eloquentProduct = $this->model->where('stock_code', $sku->getValue())->first();

        return $eloquentProduct ? $this->toDomainEntity($eloquentProduct) : null;
    }

    public function findByCategoryId(int $categoryId, int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('category_id', $categoryId)
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findActive(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findFeatured(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('is_featured', true)
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findOnSale(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->onSale()
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findInStock(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('stock', '>', 0)
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findOutOfStock(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('stock', '<=', 0)
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findLowStock(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('stock', '>', 0)
            ->where('stock', '<=', 10)
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function search(array $criteria, int $limit = 10, int $offset = 0): array
    {
        $query = $this->model->where('status', true);

        if (isset($criteria['name'])) {
            $query->where('name', 'like', '%' . $criteria['name'] . '%');
        }

        if (isset($criteria['category_id'])) {
            $query->where('category_id', $criteria['category_id']);
        }

        if (isset($criteria['min_price'])) {
            $query->where('price', '>=', $criteria['min_price']);
        }

        if (isset($criteria['max_price'])) {
            $query->where('price', '<=', $criteria['max_price']);
        }

        if (isset($criteria['in_stock']) && $criteria['in_stock']) {
            $query->where('stock', '>', 0);
        }

        $eloquentProducts = $query
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function count(array $criteria = []): int
    {
        $query = $this->model->where('status', true);

        if (isset($criteria['category_id'])) {
            $query->where('category_id', $criteria['category_id']);
        }

        return $query->count();
    }

    public function countByCategoryId(int $categoryId): int
    {
        return $this->model
            ->where('category_id', $categoryId)
            ->where('status', true)
            ->count();
    }

    public function delete(Product $product): bool
    {
        if (!$product->getId()) {
            return false;
        }

        return $this->model->destroy($product->getId());
    }

    public function deleteById(int $id): bool
    {
        return $this->model->destroy($id);
    }

    public function exists(int $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    public function existsBySKU(SKU $sku): bool
    {
        return $this->model->where('stock_code', $sku->getValue())->exists();
    }

    public function existsBySlug(string $slug): bool
    {
        return $this->model->where('slug', $slug)->exists();
    }

    public function findMostViewed(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->orderBy('view_count', 'desc')
            ->where('status', true)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findLatest(int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('status', true)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findByPriceRange(float $minPrice, float $maxPrice, int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('status', true)
            ->whereBetween('price', [$minPrice, $maxPrice])
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findSimilar(Product $product, int $limit = 10): array
    {
        $eloquentProducts = $this->model
            ->where('category_id', $product->getCategoryId())
            ->where('id', '!=', $product->getId())
            ->where('status', true)
            ->limit($limit)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findRelated(Product $product, int $limit = 10): array
    {
        return $this->findSimilar($product, $limit);
    }

    public function findRandom(int $limit = 10): array
    {
        $eloquentProducts = $this->model
            ->where('status', true)
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findUpdatedAfter(\DateTime $date, int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('status', true)
            ->where('updated_at', '>', $date)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function findCreatedAfter(\DateTime $date, int $limit = 10, int $offset = 0): array
    {
        $eloquentProducts = $this->model
            ->where('status', true)
            ->where('created_at', '>', $date)
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $eloquentProducts->map(fn($product) => $this->toDomainEntity($product))->toArray();
    }

    public function incrementViewCount(int $productId): void
    {
        $this->model->where('id', $productId)->increment('view_count');
    }

    public function bulkUpdateStock(array $stockUpdates): bool
    {
        foreach ($stockUpdates as $update) {
            $this->model->where('id', $update['id'])->update(['stock' => $update['stock']]);
        }
        return true;
    }

    public function bulkUpdatePrices(array $priceUpdates): bool
    {
        foreach ($priceUpdates as $update) {
            $this->model->where('id', $update['id'])->update(['price' => $update['price']]);
        }
        return true;
    }

    public function bulkUpdateStatus(array $productIds, bool $status): bool
    {
        return $this->model->whereIn('id', $productIds)->update(['status' => $status]);
    }

    public function bulkUpdateCategory(array $productIds, int $categoryId): bool
    {
        return $this->model->whereIn('id', $productIds)->update(['category_id' => $categoryId]);
    }

    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'active' => $this->model->where('status', true)->count(),
            'featured' => $this->model->where('is_featured', true)->count(),
            'on_sale' => $this->model->where('is_on_sale', true)->count(),
            'in_stock' => $this->model->where('stock', '>', 0)->count(),
            'out_of_stock' => $this->model->where('stock', '<=', 0)->count(),
        ];
    }

    public function getCategoryStatistics(int $categoryId): array
    {
        return [
            'total' => $this->model->where('category_id', $categoryId)->count(),
            'active' => $this->model->where('category_id', $categoryId)->where('status', true)->count(),
            'in_stock' => $this->model->where('category_id', $categoryId)->where('stock', '>', 0)->count(),
        ];
    }

    public function getStockStatistics(): array
    {
        return [
            'in_stock' => $this->model->where('stock', '>', 0)->count(),
            'low_stock' => $this->model->where('stock', '>', 0)->where('stock', '<=', 10)->count(),
            'out_of_stock' => $this->model->where('stock', '<=', 0)->count(),
            'total_stock' => $this->model->sum('stock'),
        ];
    }

    public function getPriceStatistics(): array
    {
        return [
            'min_price' => $this->model->where('status', true)->min('price'),
            'max_price' => $this->model->where('status', true)->max('price'),
            'avg_price' => $this->model->where('status', true)->avg('price'),
            'on_sale_count' => $this->model->where('is_on_sale', true)->count(),
        ];
    }

    private function toEloquentModel(Product $product): EloquentProduct
    {
        $eloquentProduct = $product->getId()
            ? $this->model->find($product->getId()) ?? new EloquentProduct()
            : new EloquentProduct();

        $eloquentProduct->fill([
            'name' => $product->getName(),
            'slug' => $product->getSlug(),
            'description' => $product->getDescription(),
            'stock_code' => $product->getSku()->getValue(),
            'price' => $product->getPrice()->getAmount(),
            'sale_price' => $product->getSalePrice()?->getAmount(),
            'stock' => $product->getStock()->getQuantity(),
            'category_id' => $product->getCategoryId(),
            'status' => $product->getStatus(),
            'is_featured' => $product->isFeatured(),
            'is_on_sale' => $product->isOnSale(),
            'sale_starts_at' => $product->getSaleStartsAt(),
            'sale_ends_at' => $product->getSaleEndsAt(),
            'view_count' => $product->getViewCount(),
            'weight' => $product->getWeight()?->getValue(),
        ]);

        if ($product->getId()) {
            $eloquentProduct->id = $product->getId();
        }

        return $eloquentProduct;
    }

    private function toDomainEntity(EloquentProduct $eloquentProduct): Product
    {
        $sku = new SKU($eloquentProduct->stock_code ?? '');
        $price = new Price($eloquentProduct->price, 'TRY');
        $stock = new Stock($eloquentProduct->stock);

        $product = new Product(
            $eloquentProduct->name,
            $eloquentProduct->slug,
            $sku,
            $price,
            $stock,
            $eloquentProduct->category_id,
            $eloquentProduct->description,
            $eloquentProduct->status,
            $eloquentProduct->is_featured
        );

        $reflection = new \ReflectionClass($product);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($product, $eloquentProduct->id);

        if ($eloquentProduct->sale_price) {
            $salePrice = new Price($eloquentProduct->sale_price, 'TRY');
            $product->setSalePrice($salePrice, $eloquentProduct->sale_starts_at, $eloquentProduct->sale_ends_at);
        }

        if ($eloquentProduct->weight) {
            $weight = new Weight($eloquentProduct->weight, 'kg');
            $product->setWeight($weight);
        }

        $viewCountProperty = $reflection->getProperty('viewCount');
        $viewCountProperty->setAccessible(true);
        $viewCountProperty->setValue($product, $eloquentProduct->view_count);

        $createdAtProperty = $reflection->getProperty('createdAt');
        $createdAtProperty->setAccessible(true);
        $createdAtProperty->setValue($product, Carbon::parse($eloquentProduct->created_at));

        $updatedAtProperty = $reflection->getProperty('updatedAt');
        $updatedAtProperty->setAccessible(true);
        $updatedAtProperty->setValue($product, Carbon::parse($eloquentProduct->updated_at));

        return $product;
    }
}
