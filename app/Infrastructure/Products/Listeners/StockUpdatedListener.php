<?php

namespace App\Infrastructure\Products\Listeners;

use App\Domain\Products\Events\StockUpdated;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;

/**
 * Stock Updated Event Listener
 * Stok güncellendiğinde çalışan infrastructure listener
 */
class StockUpdatedListener
{
    /**
     * Event'i handle et
     */
    public function handle(StockUpdated $event): void
    {
        $product = $event->getProduct();
        $oldStock = $event->getOldStock();
        $newStock = $event->getNewStock();

        try {
            // 1. Cache'i temizle
            $this->clearStockCaches($product);

            // 2. Low stock kontrolü
            $this->checkLowStock($product, $newStock);

            // 3. Out of stock kontrolü
            $this->checkOutOfStock($product, $oldStock, $newStock);

            // 4. Back in stock kontrolü
            $this->checkBackInStock($product, $oldStock, $newStock);

            // 5. Analytics'e kaydet
            $this->recordStockAnalytics($product, $oldStock, $newStock);

            // 6. External service'lere bildir
            $this->notifyExternalServices($product, $newStock);

            Log::info('Stock updated successfully processed', [
                'product_id' => $product->getId(),
                'old_stock' => $oldStock->getQuantity(),
                'new_stock' => $newStock->getQuantity(),
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing stock updated event', [
                'product_id' => $product->getId(),
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Stok cache'lerini temizle
     */
    private function clearStockCaches($product): void
    {
        $cacheKeys = [
            'products.in_stock.*',
            'products.out_of_stock.*',
            'products.low_stock.*',
            'product.' . $product->getId(),
        ];

        foreach ($cacheKeys as $pattern) {
            Cache::flush(); // Production'da daha spesifik olmalı
        }
    }

    /**
     * Düşük stok kontrolü
     */
    private function checkLowStock($product, $newStock): void
    {
        $lowStockThreshold = config('products.low_stock_threshold', 10);

        if ($newStock->getQuantity() <= $lowStockThreshold && $newStock->getQuantity() > 0) {
            // Admin'lere düşük stok bildirimi
            Queue::push('send-low-stock-notification', [
                'product_id' => $product->getId(),
                'product_name' => $product->getName(),
                'current_stock' => $newStock->getQuantity(),
                'threshold' => $lowStockThreshold,
            ]);

            Log::warning('Low stock detected', [
                'product_id' => $product->getId(),
                'current_stock' => $newStock->getQuantity(),
                'threshold' => $lowStockThreshold,
            ]);
        }
    }

    /**
     * Stok tükendi kontrolü
     */
    private function checkOutOfStock($product, $oldStock, $newStock): void
    {
        if ($oldStock->getQuantity() > 0 && $newStock->getQuantity() <= 0) {
            // Stok tükendi bildirimi
            Queue::push('send-out-of-stock-notification', [
                'product_id' => $product->getId(),
                'product_name' => $product->getName(),
            ]);

            // Müşterilere "stok geldiğinde bildir" seçeneği sun
            Queue::push('enable-stock-notification', [
                'product_id' => $product->getId(),
            ]);

            Log::warning('Product out of stock', [
                'product_id' => $product->getId(),
                'product_name' => $product->getName(),
            ]);
        }
    }

    /**
     * Stok geldi kontrolü
     */
    private function checkBackInStock($product, $oldStock, $newStock): void
    {
        if ($oldStock->getQuantity() <= 0 && $newStock->getQuantity() > 0) {
            // Stok geldi bildirimi
            Queue::push('send-back-in-stock-notification', [
                'product_id' => $product->getId(),
                'product_name' => $product->getName(),
                'new_stock' => $newStock->getQuantity(),
            ]);

            // Bekleyen müşterilere bildirim gönder
            Queue::push('notify-waiting-customers', [
                'product_id' => $product->getId(),
            ]);

            Log::info('Product back in stock', [
                'product_id' => $product->getId(),
                'new_stock' => $newStock->getQuantity(),
            ]);
        }
    }

    /**
     * Stok analytics'i kaydet
     */
    private function recordStockAnalytics($product, $oldStock, $newStock): void
    {
        $analyticsData = [
            'event' => 'stock_updated',
            'product_id' => $product->getId(),
            'old_stock' => $oldStock->getQuantity(),
            'new_stock' => $newStock->getQuantity(),
            'stock_change' => $newStock->getQuantity() - $oldStock->getQuantity(),
            'timestamp' => now()->toISOString(),
        ];

        Queue::push('record-analytics', $analyticsData);
    }

    /**
     * External service'lere stok güncellemesi bildir
     */
    private function notifyExternalServices($product, $newStock): void
    {
        // Google Merchant Center'a stok bildir
        if (config('integrations.google_merchant.enabled', false)) {
            Queue::push('sync-google-merchant-stock', [
                'product_id' => $product->getId(),
                'stock' => $newStock->getQuantity(),
            ]);
        }

        // ERP sistemine stok bildir
        if (config('integrations.erp.enabled', false)) {
            Queue::push('sync-erp-stock', [
                'product_id' => $product->getId(),
                'stock' => $newStock->getQuantity(),
            ]);
        }

        // Marketplace'lere stok bildir
        if (config('integrations.marketplaces.enabled', false)) {
            Queue::push('sync-marketplace-stock', [
                'product_id' => $product->getId(),
                'stock' => $newStock->getQuantity(),
            ]);
        }
    }
}
