<?php

namespace App\Modules\Products\Infrastructure\Repositories;

use App\Core\Infrastructure\Repository\EloquentRepository;
use App\Core\Domain\Contracts\EntityInterface;
use App\Core\Domain\Exceptions\EntityNotFoundException;
use App\Modules\Products\Domain\Interfaces\ProductRepositoryInterface;
use App\Modules\Products\Domain\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Product Repository Implementation
 * Ürün repository implementasyonu
 */
class ProductRepository extends EloquentRepository implements ProductRepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct(new Product());
    }

    /**
     * Slug'a göre ürün getir
     *
     * @param string $slug
     * @return Product|null
     */
    public function findBySlug(string $slug): ?Product
    {
        return $this->model->where('slug', $slug)->first();
    }

    /**
     * SKU'ya göre ürün getir
     *
     * @param string $sku
     * @return Product|null
     */
    public function findBySku(string $sku): ?Product
    {
        return $this->model->where('sku', $sku)->first();
    }

    /**
     * Kategoriye göre ürünleri getir
     *
     * @param int $categoryId
     * @param array $columns
     * @return Collection
     */
    public function findByCategory(int $categoryId, array $columns = ['*']): Collection
    {
        return $this->model->where('category_id', $categoryId)
            ->where('status', true)
            ->get($columns);
    }

    /**
     * Aktif ürünleri getir
     *
     * @param array $columns
     * @return Collection
     */
    public function findActive(array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)->get($columns);
    }

    /**
     * Öne çıkarılmış ürünleri getir
     *
     * @param int $limit
     * @param array $columns
     * @return Collection
     */
    public function findFeatured(int $limit = 10, array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)
            ->where('is_featured', true)
            ->limit($limit)
            ->get($columns);
    }

    /**
     * İndirimli ürünleri getir
     *
     * @param int $limit
     * @param array $columns
     * @return Collection
     */
    public function findOnSale(int $limit = 10, array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)
            ->where('is_on_sale', true)
            ->whereNotNull('sale_price')
            ->where(function ($query) {
                $query->whereNull('sale_starts_at')
                    ->orWhere('sale_starts_at', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('sale_ends_at')
                    ->orWhere('sale_ends_at', '>=', now());
            })
            ->limit($limit)
            ->get($columns);
    }

    /**
     * Stokta olan ürünleri getir
     *
     * @param array $columns
     * @return Collection
     */
    public function findInStock(array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)
            ->where('stock', '>', 0)
            ->get($columns);
    }

    /**
     * Ürün arama
     *
     * @param string $query
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function search(string $query, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $queryBuilder = $this->model->where('status', true);

        // Arama terimi
        if (!empty($query)) {
            $queryBuilder->where(function ($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('sku', 'LIKE', "%{$query}%");
            });
        }

        // Kategori filtresi
        if (!empty($filters['category_id'])) {
            $queryBuilder->where('category_id', $filters['category_id']);
        }

        // Fiyat aralığı filtresi
        if (!empty($filters['min_price'])) {
            $queryBuilder->where('price', '>=', $filters['min_price']);
        }
        if (!empty($filters['max_price'])) {
            $queryBuilder->where('price', '<=', $filters['max_price']);
        }

        // Stok durumu filtresi
        if (!empty($filters['in_stock'])) {
            $queryBuilder->where('stock', '>', 0);
        }

        // İndirim filtresi
        if (!empty($filters['on_sale'])) {
            $queryBuilder->where('is_on_sale', true);
        }

        // Sıralama
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $queryBuilder->orderBy($sortBy, $sortOrder);

        return $queryBuilder->paginate($perPage);
    }

    /**
     * Fiyat aralığına göre ürünleri getir
     *
     * @param float $minPrice
     * @param float $maxPrice
     * @param array $columns
     * @return Collection
     */
    public function findByPriceRange(float $minPrice, float $maxPrice, array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)
            ->whereBetween('price', [$minPrice, $maxPrice])
            ->get($columns);
    }

    /**
     * En çok görüntülenen ürünleri getir
     *
     * @param int $limit
     * @param array $columns
     * @return Collection
     */
    public function findMostViewed(int $limit = 10, array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)
            ->orderBy('view_count', 'desc')
            ->limit($limit)
            ->get($columns);
    }

    /**
     * Son eklenen ürünleri getir
     *
     * @param int $limit
     * @param array $columns
     * @return Collection
     */
    public function findLatest(int $limit = 10, array $columns = ['*']): Collection
    {
        return $this->model->where('status', true)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get($columns);
    }

    /**
     * Slug'ın benzersiz olup olmadığını kontrol et
     *
     * @param string $slug
     * @param int|null $excludeId
     * @return bool
     */
    public function isSlugUnique(string $slug, ?int $excludeId = null): bool
    {
        $query = $this->model->where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * SKU'nun benzersiz olup olmadığını kontrol et
     *
     * @param string $sku
     * @param int|null $excludeId
     * @return bool
     */
    public function isSkuUnique(string $sku, ?int $excludeId = null): bool
    {
        $query = $this->model->where('sku', $sku);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * ID'ye göre ürün getir veya exception fırlat
     *
     * @param mixed $id
     * @param array $columns
     * @return EntityInterface
     * @throws EntityNotFoundException
     */
    public function findByIdOrFail($id, array $columns = ['*']): EntityInterface
    {
        $product = $this->findById($id, $columns);

        if (!$product) {
            throw EntityNotFoundException::forEntity('Product', $id);
        }

        return $product;
    }
}
