<?php

namespace App\Modules\Products\Application\DTOs;

use App\Core\Application\DTO\DataTransferObject;

/**
 * Search Products DTO
 * Ürün arama için veri transfer objesi
 */
class SearchProductsDTO extends DataTransferObject
{
    public ?string $query = null;
    public ?int $categoryId = null;
    public ?float $minPrice = null;
    public ?float $maxPrice = null;
    public bool $inStock = false;
    public bool $onSale = false;
    public bool $featured = false;
    public string $sortBy = 'created_at';
    public string $sortOrder = 'desc';
    public int $perPage = 15;

    /**
     * Geçerli sıralama alanları
     *
     * @var array
     */
    private const VALID_SORT_FIELDS = [
        'name',
        'price',
        'created_at',
        'updated_at',
        'view_count',
        'stock'
    ];

    /**
     * Geçerli sıralama yönleri
     *
     * @var array
     */
    private const VALID_SORT_ORDERS = ['asc', 'desc'];

    /**
     * DTO'yu validate et
     *
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validate(): void
    {
        // Fiyat aralığı kontrolü
        if ($this->minPrice !== null && $this->minPrice < 0) {
            throw new \InvalidArgumentException('Minimum price cannot be negative');
        }

        if ($this->maxPrice !== null && $this->maxPrice < 0) {
            throw new \InvalidArgumentException('Maximum price cannot be negative');
        }

        if ($this->minPrice !== null && $this->maxPrice !== null && $this->minPrice > $this->maxPrice) {
            throw new \InvalidArgumentException('Minimum price cannot be greater than maximum price');
        }

        // Sıralama kontrolü
        if (!in_array($this->sortBy, self::VALID_SORT_FIELDS)) {
            throw new \InvalidArgumentException('Invalid sort field: ' . $this->sortBy);
        }

        if (!in_array($this->sortOrder, self::VALID_SORT_ORDERS)) {
            throw new \InvalidArgumentException('Invalid sort order: ' . $this->sortOrder);
        }

        // Sayfa başına kayıt kontrolü
        if ($this->perPage < 1 || $this->perPage > 100) {
            throw new \InvalidArgumentException('Per page must be between 1 and 100');
        }

        // Kategori ID kontrolü
        if ($this->categoryId !== null && $this->categoryId <= 0) {
            throw new \InvalidArgumentException('Category ID must be positive');
        }
    }

    /**
     * Array'den DTO oluştur
     *
     * @param array $data
     * @return static
     */
    public static function fromArray(array $data): static
    {
        return new static([
            'query' => $data['query'] ?? null,
            'categoryId' => isset($data['category_id']) ? (int) $data['category_id'] : null,
            'minPrice' => isset($data['min_price']) ? (float) $data['min_price'] : null,
            'maxPrice' => isset($data['max_price']) ? (float) $data['max_price'] : null,
            'inStock' => (bool) ($data['in_stock'] ?? false),
            'onSale' => (bool) ($data['on_sale'] ?? false),
            'featured' => (bool) ($data['featured'] ?? false),
            'sortBy' => $data['sort_by'] ?? 'created_at',
            'sortOrder' => $data['sort_order'] ?? 'desc',
            'perPage' => (int) ($data['per_page'] ?? 15),
        ]);
    }

    /**
     * Arama parametrelerini array olarak getir
     *
     * @return array
     */
    public function toSearchArray(): array
    {
        $data = [];

        if ($this->query) $data['query'] = $this->query;
        if ($this->categoryId) $data['category_id'] = $this->categoryId;
        if ($this->minPrice !== null) $data['min_price'] = $this->minPrice;
        if ($this->maxPrice !== null) $data['max_price'] = $this->maxPrice;
        if ($this->inStock) $data['in_stock'] = true;
        if ($this->onSale) $data['on_sale'] = true;
        if ($this->featured) $data['featured'] = true;
        
        $data['sort_by'] = $this->sortBy;
        $data['sort_order'] = $this->sortOrder;
        $data['per_page'] = $this->perPage;

        return $data;
    }

    /**
     * Aktif filtre var mı kontrol et
     *
     * @return bool
     */
    public function hasActiveFilters(): bool
    {
        return !empty($this->query) ||
               $this->categoryId !== null ||
               $this->minPrice !== null ||
               $this->maxPrice !== null ||
               $this->inStock ||
               $this->onSale ||
               $this->featured;
    }
}
