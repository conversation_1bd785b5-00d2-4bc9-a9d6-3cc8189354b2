<?php

namespace App\Modules\Products\Application\DTOs;

use App\Core\Application\DTO\DataTransferObject;

/**
 * Create Product DTO
 * Ürün oluşturma için veri transfer objesi
 */
class CreateProductDTO extends DataTransferObject
{
    public string $name;
    public ?string $description = null;
    public float $price;
    public int $stock = 0;
    public ?int $categoryId = null;
    public bool $status = true;
    public ?string $sku = null;
    public ?float $weight = null;
    public ?float $desi = null;
    public bool $isFeatured = false;
    public bool $isOnSale = false;
    public ?float $salePrice = null;
    public ?string $saleStartsAt = null;
    public ?string $saleEndsAt = null;

    /**
     * DTO'yu validate et
     *
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validate(): void
    {
        if (empty($this->name)) {
            throw new \InvalidArgumentException('Product name is required');
        }

        if (strlen($this->name) < 3 || strlen($this->name) > 255) {
            throw new \InvalidArgumentException('Product name must be between 3 and 255 characters');
        }

        if ($this->price < 0) {
            throw new \InvalidArgumentException('Product price cannot be negative');
        }

        if ($this->stock < 0) {
            throw new \InvalidArgumentException('Product stock cannot be negative');
        }

        if ($this->isOnSale && (!$this->salePrice || $this->salePrice <= 0)) {
            throw new \InvalidArgumentException('Sale price is required when product is on sale');
        }

        if ($this->isOnSale && $this->salePrice >= $this->price) {
            throw new \InvalidArgumentException('Sale price must be less than regular price');
        }

        if ($this->weight && $this->weight < 0) {
            throw new \InvalidArgumentException('Product weight cannot be negative');
        }

        if ($this->desi && $this->desi < 0) {
            throw new \InvalidArgumentException('Product desi cannot be negative');
        }

        // Tarih validasyonu
        if ($this->saleStartsAt && $this->saleEndsAt) {
            $startDate = new \DateTime($this->saleStartsAt);
            $endDate = new \DateTime($this->saleEndsAt);
            
            if ($endDate <= $startDate) {
                throw new \InvalidArgumentException('Sale end date must be after start date');
            }
        }
    }

    /**
     * Array'den DTO oluştur
     *
     * @param array $data
     * @return static
     */
    public static function fromArray(array $data): static
    {
        return new static([
            'name' => $data['name'] ?? '',
            'description' => $data['description'] ?? null,
            'price' => (float) ($data['price'] ?? 0),
            'stock' => (int) ($data['stock'] ?? 0),
            'categoryId' => isset($data['category_id']) ? (int) $data['category_id'] : null,
            'status' => (bool) ($data['status'] ?? true),
            'sku' => $data['sku'] ?? null,
            'weight' => isset($data['weight']) ? (float) $data['weight'] : null,
            'desi' => isset($data['desi']) ? (float) $data['desi'] : null,
            'isFeatured' => (bool) ($data['is_featured'] ?? false),
            'isOnSale' => (bool) ($data['is_on_sale'] ?? false),
            'salePrice' => isset($data['sale_price']) ? (float) $data['sale_price'] : null,
            'saleStartsAt' => $data['sale_starts_at'] ?? null,
            'saleEndsAt' => $data['sale_ends_at'] ?? null,
        ]);
    }

    /**
     * Model için array'e çevir
     *
     * @return array
     */
    public function toModelArray(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'stock' => $this->stock,
            'category_id' => $this->categoryId,
            'status' => $this->status,
            'sku' => $this->sku,
            'weight' => $this->weight,
            'desi' => $this->desi,
            'is_featured' => $this->isFeatured,
            'is_on_sale' => $this->isOnSale,
            'sale_price' => $this->salePrice,
            'sale_starts_at' => $this->saleStartsAt,
            'sale_ends_at' => $this->saleEndsAt,
        ];
    }
}
