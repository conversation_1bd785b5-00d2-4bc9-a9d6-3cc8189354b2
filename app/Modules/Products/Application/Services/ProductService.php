<?php

namespace App\Modules\Products\Application\Services;

use App\Modules\Products\Application\DTOs\CreateProductDTO;
use App\Modules\Products\Application\DTOs\UpdateProductDTO;
use App\Modules\Products\Application\DTOs\SearchProductsDTO;
use App\Modules\Products\Application\UseCases\CreateProductUseCase;
use App\Modules\Products\Application\UseCases\UpdateProductUseCase;
use App\Modules\Products\Application\UseCases\DeleteProductUseCase;
use App\Modules\Products\Application\UseCases\GetProductUseCase;
use App\Modules\Products\Application\UseCases\SearchProductsUseCase;
use App\Modules\Products\Application\UseCases\GetFeaturedProductsUseCase;
use App\Modules\Products\Application\UseCases\ManageStockUseCase;
use App\Modules\Products\Application\UseCases\CheckStockAvailabilityUseCase;
use App\Modules\Products\Application\UseCases\CalculatePriceUseCase;
use App\Modules\Products\Application\DTOs\ManageStockDTO;
use App\Modules\Products\Domain\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Product Application Service
 * Ürün uygulama servisi - Use Case'leri orchestrate eder
 */
class ProductService
{
    /**
     * Use Case'ler
     */
    private CreateProductUseCase $createProductUseCase;
    private UpdateProductUseCase $updateProductUseCase;
    private DeleteProductUseCase $deleteProductUseCase;
    private GetProductUseCase $getProductUseCase;
    private SearchProductsUseCase $searchProductsUseCase;
    private GetFeaturedProductsUseCase $getFeaturedProductsUseCase;
    private ManageStockUseCase $manageStockUseCase;
    private CheckStockAvailabilityUseCase $checkStockAvailabilityUseCase;
    private CalculatePriceUseCase $calculatePriceUseCase;

    /**
     * Constructor
     */
    public function __construct(
        CreateProductUseCase $createProductUseCase,
        UpdateProductUseCase $updateProductUseCase,
        DeleteProductUseCase $deleteProductUseCase,
        GetProductUseCase $getProductUseCase,
        SearchProductsUseCase $searchProductsUseCase,
        GetFeaturedProductsUseCase $getFeaturedProductsUseCase,
        ManageStockUseCase $manageStockUseCase,
        CheckStockAvailabilityUseCase $checkStockAvailabilityUseCase,
        CalculatePriceUseCase $calculatePriceUseCase
    ) {
        $this->createProductUseCase = $createProductUseCase;
        $this->updateProductUseCase = $updateProductUseCase;
        $this->deleteProductUseCase = $deleteProductUseCase;
        $this->getProductUseCase = $getProductUseCase;
        $this->searchProductsUseCase = $searchProductsUseCase;
        $this->getFeaturedProductsUseCase = $getFeaturedProductsUseCase;
        $this->manageStockUseCase = $manageStockUseCase;
        $this->checkStockAvailabilityUseCase = $checkStockAvailabilityUseCase;
        $this->calculatePriceUseCase = $calculatePriceUseCase;
    }

    /**
     * Ürün oluştur
     *
     * @param array $data
     * @return Product
     */
    public function createProduct(array $data): Product
    {
        $dto = CreateProductDTO::fromArray($data);
        return $this->createProductUseCase->execute($dto);
    }

    /**
     * Ürün güncelle
     *
     * @param int $id
     * @param array $data
     * @return Product
     */
    public function updateProduct(int $id, array $data): Product
    {
        $dto = UpdateProductDTO::fromArrayWithId($id, $data);
        return $this->updateProductUseCase->execute($dto);
    }

    /**
     * Ürün sil
     *
     * @param int $id
     * @return bool
     */
    public function deleteProduct(int $id): bool
    {
        return $this->deleteProductUseCase->execute($id);
    }

    /**
     * ID'ye göre ürün getir
     *
     * @param int $id
     * @return Product
     */
    public function getProductById(int $id): Product
    {
        return $this->getProductUseCase->execute($id);
    }

    /**
     * Slug'a göre ürün getir
     *
     * @param string $slug
     * @return Product
     */
    public function getProductBySlug(string $slug): Product
    {
        return $this->getProductUseCase->getBySlug($slug);
    }

    /**
     * SKU'ya göre ürün getir
     *
     * @param string $sku
     * @return Product
     */
    public function getProductBySku(string $sku): Product
    {
        return $this->getProductUseCase->getBySku($sku);
    }

    /**
     * Ürün arama
     *
     * @param array $searchParams
     * @return LengthAwarePaginator
     */
    public function searchProducts(array $searchParams): LengthAwarePaginator
    {
        $dto = SearchProductsDTO::fromArray($searchParams);
        return $this->searchProductsUseCase->execute($dto);
    }

    /**
     * Öne çıkarılmış ürünleri getir
     *
     * @param int $limit
     * @return Collection
     */
    public function getFeaturedProducts(int $limit = 10): Collection
    {
        return $this->getFeaturedProductsUseCase->execute($limit);
    }

    /**
     * İndirimli ürünleri getir
     *
     * @param int $limit
     * @return Collection
     */
    public function getOnSaleProducts(int $limit = 10): Collection
    {
        return $this->getFeaturedProductsUseCase->getOnSaleProducts($limit);
    }

    /**
     * En çok görüntülenen ürünleri getir
     *
     * @param int $limit
     * @return Collection
     */
    public function getMostViewedProducts(int $limit = 10): Collection
    {
        return $this->getFeaturedProductsUseCase->getMostViewedProducts($limit);
    }

    /**
     * Son eklenen ürünleri getir
     *
     * @param int $limit
     * @return Collection
     */
    public function getLatestProducts(int $limit = 10): Collection
    {
        return $this->getFeaturedProductsUseCase->getLatestProducts($limit);
    }

    /**
     * Kategoriye göre ürünleri getir
     *
     * @param int $categoryId
     * @param int $limit
     * @return Collection
     */
    public function getProductsByCategory(int $categoryId, int $limit = 20): Collection
    {
        return $this->getFeaturedProductsUseCase->getProductsByCategory($categoryId, $limit);
    }

    // Backward compatibility için eski metodlar

    // === STOCK MANAGEMENT METHODS ===

    /**
     * Stok yönetimi
     *
     * @param array $data
     * @return Product
     */
    public function manageStock(array $data): Product
    {
        $dto = ManageStockDTO::fromArray($data);
        return $this->manageStockUseCase->execute($dto);
    }

    /**
     * Stok artır
     *
     * @param int $productId
     * @param int $quantity
     * @param string|null $reason
     * @param int|null $userId
     * @return Product
     */
    public function increaseStock(int $productId, int $quantity, ?string $reason = null, ?int $userId = null): Product
    {
        $dto = ManageStockDTO::forIncrease($productId, $quantity, $reason, $userId);
        return $this->manageStockUseCase->execute($dto);
    }

    /**
     * Stok azalt
     *
     * @param int $productId
     * @param int $quantity
     * @param string|null $reason
     * @param int|null $userId
     * @return Product
     */
    public function decreaseStock(int $productId, int $quantity, ?string $reason = null, ?int $userId = null): Product
    {
        $dto = ManageStockDTO::forDecrease($productId, $quantity, $reason, $userId);
        return $this->manageStockUseCase->execute($dto);
    }

    /**
     * Stok belirle
     *
     * @param int $productId
     * @param int $quantity
     * @param string|null $reason
     * @param int|null $userId
     * @return Product
     */
    public function setStock(int $productId, int $quantity, ?string $reason = null, ?int $userId = null): Product
    {
        $dto = ManageStockDTO::forSet($productId, $quantity, $reason, $userId);
        return $this->manageStockUseCase->execute($dto);
    }

    /**
     * Stok rezervasyonu
     *
     * @param int $productId
     * @param int $quantity
     * @param string $reservationId
     * @param int|null $userId
     * @return Product
     */
    public function reserveStock(int $productId, int $quantity, string $reservationId, ?int $userId = null): Product
    {
        return $this->manageStockUseCase->reserveStock($productId, $quantity, $reservationId, $userId);
    }

    /**
     * Stok rezervasyonunu iptal et
     *
     * @param int $productId
     * @param int $quantity
     * @param string $reservationId
     * @param int|null $userId
     * @return Product
     */
    public function cancelReservation(int $productId, int $quantity, string $reservationId, ?int $userId = null): Product
    {
        return $this->manageStockUseCase->cancelReservation($productId, $quantity, $reservationId, $userId);
    }

    // === STOCK AVAILABILITY METHODS ===

    /**
     * Stok müsaitlik kontrolü
     *
     * @param int $productId
     * @param int $quantity
     * @return array
     */
    public function checkStockAvailability(int $productId, int $quantity): array
    {
        return $this->checkStockAvailabilityUseCase->checkSingleProduct($productId, $quantity);
    }

    /**
     * Çoklu ürün stok kontrolü
     *
     * @param array $items
     * @return array
     */
    public function checkMultipleProductsStock(array $items): array
    {
        return $this->checkStockAvailabilityUseCase->checkMultipleProducts($items);
    }

    /**
     * SKU ile stok kontrolü
     *
     * @param string $sku
     * @param int $quantity
     * @return array
     */
    public function checkStockBySku(string $sku, int $quantity): array
    {
        return $this->checkStockAvailabilityUseCase->checkBySku($sku, $quantity);
    }

    /**
     * Düşük stoklu ürünleri getir
     *
     * @param int $threshold
     * @return array
     */
    public function getLowStockProducts(int $threshold = 10): array
    {
        return $this->checkStockAvailabilityUseCase->getLowStockProducts($threshold);
    }

    // === PRICING METHODS ===

    /**
     * Ürün fiyatı hesapla
     *
     * @param int $productId
     * @param int $quantity
     * @param array $options
     * @return array
     */
    public function calculatePrice(int $productId, int $quantity = 1, array $options = []): array
    {
        return $this->calculatePriceUseCase->calculateSingleProductPrice($productId, $quantity, $options);
    }

    /**
     * Çoklu ürün fiyatı hesapla
     *
     * @param array $items
     * @param array $options
     * @return array
     */
    public function calculateMultipleProductsPrice(array $items, array $options = []): array
    {
        return $this->calculatePriceUseCase->calculateMultipleProductsPrice($items, $options);
    }

    // === BACKWARD COMPATIBILITY ===

    /**
     * Tüm ürünleri getir (backward compatibility)
     *
     * @return Collection
     * @deprecated Use searchProducts() instead
     */
    public function getAllProducts(): Collection
    {
        return $this->searchProducts(['per_page' => 1000])->getCollection();
    }
}
