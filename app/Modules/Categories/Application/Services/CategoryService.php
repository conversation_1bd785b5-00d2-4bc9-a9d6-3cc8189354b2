<?php

namespace App\Modules\Categories\Application\Services;

use App\Modules\Categories\Domain\Interfaces\CategoryRepositoryInterface;
use Illuminate\Support\Str;

class CategoryService
{
    /**
     * Kategori repository
     *
     * @var \App\Modules\Categories\Domain\Interfaces\CategoryRepositoryInterface
     */
    protected $categoryRepository;

    /**
     * Yapılandırıcı
     *
     * @param \App\Modules\Categories\Domain\Interfaces\CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(CategoryRepositoryInterface $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * Tüm kategorileri getir
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllCategories()
    {
        return $this->categoryRepository->all();
    }

    /**
     * ID'ye göre kategori getir
     *
     * @param int $id
     * @return \App\Modules\Categories\Domain\Models\Category|null
     */
    public function getCategoryById($id)
    {
        return $this->categoryRepository->findById($id);
    }

    /**
     * Kategori oluştur
     *
     * @param array $data
     * @return \App\Modules\Categories\Domain\Models\Category
     */
    public function createCategory(array $data)
    {
        // Slug oluştur
        $data['slug'] = Str::slug($data['name']);
        
        return $this->categoryRepository->create($data);
    }

    /**
     * Kategori güncelle
     *
     * @param int $id
     * @param array $data
     * @return \App\Modules\Categories\Domain\Models\Category
     */
    public function updateCategory($id, array $data)
    {
        // İsim değiştiyse slug güncelle
        if (isset($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }
        
        return $this->categoryRepository->update($id, $data);
    }

    /**
     * Kategori sil
     *
     * @param int $id
     * @return bool
     */
    public function deleteCategory($id)
    {
        return $this->categoryRepository->delete($id);
    }

    /**
     * Üst kategorileri getir
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getParentCategories()
    {
        return $this->categoryRepository->getParentCategories();
    }

    /**
     * Kategori ağacını getir
     *
     * @return array
     */
    public function getCategoryTree()
    {
        return $this->categoryRepository->getCategoryTree();
    }
}
