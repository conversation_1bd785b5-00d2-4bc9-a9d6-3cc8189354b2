<?php

namespace App\Modules\Categories\Domain\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;
use Illuminate\Support\Str;

/**
 * Category Slug Value Object
 * Kategori slug'ını temsil eder
 */
class CategorySlug extends ValueObject
{
    /**
     * Slug değeri
     *
     * @var string
     */
    private string $value;

    /**
     * Constructor
     *
     * @param string $value
     * @throws \InvalidArgumentException
     */
    public function __construct(string $value)
    {
        $this->value = trim(strtolower($value));
        $this->validate();
    }

    /**
     * Slug değerini getir
     *
     * @return string
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * Value object'in geçerli olup olmadığını kontrol et
     *
     * @return bool
     */
    public function isValid(): bool
    {
        // Slug en az 2, en fazla 255 karakter olmalı
        if (strlen($this->value) < 2 || strlen($this->value) > 255) {
            return false;
        }

        // Slug sadece küçük harf, rakam ve tire içerebilir
        // Başında ve sonunda tire olamaz
        return preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*$/', $this->value);
    }

    /**
     * Value object'i validate et
     *
     * @throws \InvalidArgumentException
     */
    protected function validate(): void
    {
        if (!$this->isValid()) {
            throw new \InvalidArgumentException(
                "Invalid category slug format: {$this->value}. Slug must be 2-255 characters long and contain only lowercase letters, numbers and hyphens."
            );
        }
    }

    /**
     * Value object'i array'e çevir
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'value' => $this->value,
        ];
    }

    /**
     * Value object'in string temsilini döndür
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->value;
    }

    /**
     * String'den slug oluştur
     *
     * @param string $text
     * @return CategorySlug
     */
    public static function fromString(string $text): CategorySlug
    {
        $slug = Str::slug($text);
        return new CategorySlug($slug);
    }

    /**
     * Benzersiz slug oluştur
     *
     * @param string $text
     * @param callable|null $uniqueChecker
     * @return CategorySlug
     */
    public static function createUnique(string $text, ?callable $uniqueChecker = null): CategorySlug
    {
        $baseSlug = Str::slug($text);
        $slug = $baseSlug;
        $counter = 1;

        // Eğer uniqueness checker verilmişse kullan
        if ($uniqueChecker) {
            while ($uniqueChecker($slug)) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }
        }

        return new CategorySlug($slug);
    }
}
