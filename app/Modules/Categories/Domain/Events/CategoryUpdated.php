<?php

namespace App\Modules\Categories\Domain\Events;

use App\Core\Domain\Events\DomainEvent;
use App\Modules\Categories\Domain\Models\Category;

/**
 * Category Updated Domain Event
 * Kategori güncellendiğinde fırlatılır
 */
class CategoryUpdated extends DomainEvent
{
    /**
     * Güncellenen kategori
     *
     * @var Category
     */
    public Category $category;

    /**
     * Değişen alanlar
     *
     * @var array
     */
    public array $changes;

    /**
     * Constructor
     *
     * @param Category $category
     */
    public function __construct(Category $category)
    {
        parent::__construct();
        $this->category = $category;
        $this->changes = $category->getChanges();
    }

    /**
     * Event'in payload'ını döndür
     *
     * @return array
     */
    public function getPayload(): array
    {
        return [
            'category_id' => $this->category->getId(),
            'category_name' => $this->category->name,
            'category_slug' => $this->category->slug,
            'changes' => $this->changes,
            'updated_at' => $this->category->updated_at?->toISOString(),
        ];
    }
}
