<?php

namespace App\Modules\Categories\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Categories\Domain\Interfaces\CategoryRepositoryInterface;
use App\Modules\Categories\Infrastructure\Repositories\EloquentCategoryRepository;
// Cache repository will be implemented later
// use App\Modules\Categories\Infrastructure\Cache\CacheCategoryRepository;

class CategoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Base repository binding
        $this->app->bind(CategoryRepositoryInterface::class, EloquentCategoryRepository::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Rotaları yükle
        $this->loadRoutesFrom(__DIR__ . '/../Routes/api.php');

        // Migrasyonları yükleme - birleştirilmiş migration dosyaları kullanılıyor
        // $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
    }
}
