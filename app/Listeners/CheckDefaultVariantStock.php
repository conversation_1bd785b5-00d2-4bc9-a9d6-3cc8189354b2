<?php

namespace App\Listeners;

use App\Events\VariantStockUpdated;
use App\Models\ProductVariant;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckDefaultVariantStock implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(VariantStockUpdated $event): void
    {
        $variant = $event->variant;
        $oldStock = $event->oldStock;

        // Stok 0'a düştüyse ve bu varyant varsayılan ise yeni varsayılan varyant seç
        if ($variant->stock == 0 && $oldStock > 0 && $variant->is_default) {
            Log::info('Default variant stock is now zero. Finding new default variant.', [
                'variant_id' => $variant->id,
                'product_id' => $variant->product_id
            ]);

            // Varyantı yeniden yükle (refresh) - bu <PERSON><PERSON><PERSON>, çü<PERSON><PERSON> başka bir işlem tarafından değiştirilmiş olabilir
            $variant->refresh();

            // Hala varsayılan mı kontrol et
            if ($variant->is_default) {
                $this->updateDefaultVariant($variant->product_id);
            } else {
                Log::info('Variant is no longer default, skipping default variant update', [
                    'variant_id' => $variant->id,
                    'product_id' => $variant->product_id
                ]);
            }
        }
    }

    /**
     * Ürün için yeni bir varsayılan varyant seç
     *
     * @param int $productId
     * @return void
     */
    protected function updateDefaultVariant(int $productId): void
    {
        try {
            // Önce tüm varyantları getir
            $variants = ProductVariant::where('product_id', $productId)->get();

            if ($variants->isEmpty()) {
                Log::warning('No variants found for product', ['product_id' => $productId]);
                return;
            }

            // Stokta olan ve aktif varyantları bul
            $inStockVariants = $variants->filter(function($variant) {
                return $variant->stock > 0 && $variant->status === 'in_stock';
            });

            Log::info('Checking variants for new default', [
                'product_id' => $productId,
                'total_variants' => $variants->count(),
                'in_stock_variants' => $inStockVariants->count()
            ]);

            DB::beginTransaction();

            // Önce tüm varyantların varsayılan durumunu kaldır
            ProductVariant::where('product_id', $productId)
                ->update(['is_default' => false]);

            // Stokta olan ve aktif bir varyant varsa onu seç
            if ($inStockVariants->isNotEmpty()) {
                $newDefaultVariant = $inStockVariants->first();
                $newDefaultVariant->is_default = true;
                $newDefaultVariant->save();

                Log::info('New default variant set successfully', [
                    'product_id' => $productId,
                    'new_default_variant_id' => $newDefaultVariant->id,
                    'new_default_variant_stock' => $newDefaultVariant->stock,
                    'new_default_variant_status' => $newDefaultVariant->status
                ]);
            } else {
                Log::warning('No suitable variant found for new default. Using first available variant.', [
                    'product_id' => $productId
                ]);

                // Stokta olan varyant yoksa, ilk varyantı varsayılan yap
                $firstVariant = $variants->first();
                $firstVariant->is_default = true;
                $firstVariant->save();

                Log::info('First variant set as default', [
                    'product_id' => $productId,
                    'variant_id' => $firstVariant->id,
                    'variant_stock' => $firstVariant->stock,
                    'variant_status' => $firstVariant->status
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating default variant', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
