<?php

namespace App\Domain\Cart\Entities;

use App\Core\Domain\Entity;
use App\Domain\Cart\ValueObjects\ItemQuantity;
use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

/**
 * CartItem Domain Entity
 * Sepet öğesi domain entity'si
 */
class CartItem extends Entity
{
    private ?int $id;
    private int $cartId;
    private int $productId;
    private ?int $productVariantId;
    private ItemQuantity $quantity;
    private Money $unitPrice;
    private Money $totalPrice;
    private array $options;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    private function __construct(
        int $productId,
        ItemQuantity $quantity,
        Money $unitPrice,
        ?int $productVariantId = null,
        array $options = []
    ) {
        $this->productId = $productId;
        $this->productVariantId = $productVariantId;
        $this->quantity = $quantity;
        $this->unitPrice = $unitPrice;
        $this->options = $options;
        $this->totalPrice = $this->calculateTotalPrice();
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();
    }

    /**
     * Yeni CartItem oluştur
     */
    public static function create(
        int $productId,
        ItemQuantity $quantity,
        Money $unitPrice,
        ?int $productVariantId = null,
        array $options = []
    ): self {
        return new self($productId, $quantity, $unitPrice, $productVariantId, $options);
    }

    /**
     * ID'yi set et
     */
    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    /**
     * Cart ID'yi set et
     */
    public function setCartId(int $cartId): void
    {
        $this->cartId = $cartId;
    }

    /**
     * ID'yi getir
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Cart ID'yi getir
     */
    public function getCartId(): int
    {
        return $this->cartId;
    }

    /**
     * Product ID'yi getir
     */
    public function getProductId(): int
    {
        return $this->productId;
    }

    /**
     * Product Variant ID'yi getir
     */
    public function getProductVariantId(): ?int
    {
        return $this->productVariantId;
    }

    /**
     * Miktarı getir
     */
    public function getQuantity(): ItemQuantity
    {
        return $this->quantity;
    }

    /**
     * Birim fiyatı getir
     */
    public function getUnitPrice(): Money
    {
        return $this->unitPrice;
    }

    /**
     * Toplam fiyatı getir
     */
    public function getTotalPrice(): Money
    {
        return $this->totalPrice;
    }

    /**
     * Seçenekleri getir
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Miktarı güncelle
     */
    public function updateQuantity(ItemQuantity $newQuantity): void
    {
        $this->quantity = $newQuantity;
        $this->totalPrice = $this->calculateTotalPrice();
        $this->updatedAt = Carbon::now();
    }

    /**
     * Miktar artır
     */
    public function increaseQuantity(int $amount = 1): void
    {
        $this->updateQuantity($this->quantity->increase($amount));
    }

    /**
     * Miktar azalt
     */
    public function decreaseQuantity(int $amount = 1): void
    {
        $this->updateQuantity($this->quantity->decrease($amount));
    }

    /**
     * Birim fiyatı güncelle
     */
    public function updateUnitPrice(Money $newPrice): void
    {
        $this->unitPrice = $newPrice;
        $this->totalPrice = $this->calculateTotalPrice();
        $this->updatedAt = Carbon::now();
    }

    /**
     * Seçenekleri güncelle
     */
    public function updateOptions(array $options): void
    {
        $this->options = $options;
        $this->updatedAt = Carbon::now();
    }

    /**
     * Aynı ürün mü kontrol et
     */
    public function isSameProduct(int $productId, ?int $productVariantId = null): bool
    {
        return $this->productId === $productId 
            && $this->productVariantId === $productVariantId;
    }

    /**
     * Miktar sıfır mı?
     */
    public function hasZeroQuantity(): bool
    {
        return $this->quantity->isZero();
    }

    /**
     * Toplam fiyatı hesapla
     */
    private function calculateTotalPrice(): Money
    {
        return $this->unitPrice->multiply($this->quantity->getValue());
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'cart_id' => $this->cartId,
            'product_id' => $this->productId,
            'product_variant_id' => $this->productVariantId,
            'quantity' => $this->quantity->getValue(),
            'unit_price' => $this->unitPrice->getAmount(),
            'total_price' => $this->totalPrice->getAmount(),
            'options' => $this->options,
            'created_at' => $this->createdAt->toISOString(),
            'updated_at' => $this->updatedAt->toISOString(),
        ];
    }
}
