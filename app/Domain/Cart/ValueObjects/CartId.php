<?php

namespace App\Domain\Cart\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * CartId Value Object
 * Sepet kimliği için immutable value object
 */
class CartId extends ValueObject
{
    private int $value;

    private function __construct(int $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    /**
     * Integer değerden CartId oluştur
     */
    public static function fromInt(int $value): self
    {
        return new self($value);
    }

    /**
     * String değerden CartId oluştur
     */
    public static function fromString(string $value): self
    {
        $intValue = (int) $value;
        return new self($intValue);
    }

    /**
     * Değeri integer olarak getir
     */
    public function getValue(): int
    {
        return $this->value;
    }

    /**
     * String temsilini getir
     */
    public function toString(): string
    {
        return (string) $this->value;
    }

    /**
     * Değeri validate et
     */
    protected function validate(int $value): void
    {
        if ($value <= 0) {
            throw new \InvalidArgumentException('Cart ID must be a positive integer');
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && $this->value === $other->value;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return ['cart_id' => $this->value];
    }
}
