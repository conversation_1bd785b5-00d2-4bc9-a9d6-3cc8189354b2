<?php

namespace App\Domain\Cart\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * CheckoutStep Value Object
 * Checkout adımları için immutable value object
 */
class CheckoutStep extends ValueObject
{
    public const SHIPPING_ADDRESS = 'shipping_address';
    public const BILLING_ADDRESS = 'billing_address';
    public const SHIPPING_METHOD = 'shipping_method';
    public const PAYMENT_METHOD = 'payment_method';
    public const REVIEW = 'review';

    private string $value;

    private function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    /**
     * String değerden CheckoutStep oluştur
     */
    public static function fromString(string $value): self
    {
        return new self($value);
    }

    /**
     * Kargo adresi adımı
     */
    public static function shippingAddress(): self
    {
        return new self(self::SHIPPING_ADDRESS);
    }

    /**
     * Fatura adresi adımı
     */
    public static function billingAddress(): self
    {
        return new self(self::BILLING_ADDRESS);
    }

    /**
     * Kargo yöntemi adımı
     */
    public static function shippingMethod(): self
    {
        return new self(self::SHIPPING_METHOD);
    }

    /**
     * Ödeme yöntemi adımı
     */
    public static function paymentMethod(): self
    {
        return new self(self::PAYMENT_METHOD);
    }

    /**
     * İnceleme adımı
     */
    public static function review(): self
    {
        return new self(self::REVIEW);
    }

    /**
     * Değeri string olarak getir
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * String temsilini getir
     */
    public function toString(): string
    {
        return $this->value;
    }

    /**
     * Adım adını getir
     */
    public function getDisplayName(): string
    {
        return match ($this->value) {
            self::SHIPPING_ADDRESS => 'Kargo Adresi',
            self::BILLING_ADDRESS => 'Fatura Adresi',
            self::SHIPPING_METHOD => 'Kargo Yöntemi',
            self::PAYMENT_METHOD => 'Ödeme Yöntemi',
            self::REVIEW => 'İnceleme',
            default => 'Bilinmeyen Adım'
        };
    }

    /**
     * Adım sırasını getir
     */
    public function getOrder(): int
    {
        return match ($this->value) {
            self::SHIPPING_ADDRESS => 1,
            self::BILLING_ADDRESS => 2,
            self::SHIPPING_METHOD => 3,
            self::PAYMENT_METHOD => 4,
            self::REVIEW => 5,
            default => 0
        };
    }

    /**
     * Bir sonraki adımı getir
     */
    public function getNext(): ?self
    {
        return match ($this->value) {
            self::SHIPPING_ADDRESS => self::billingAddress(),
            self::BILLING_ADDRESS => self::shippingMethod(),
            self::SHIPPING_METHOD => self::paymentMethod(),
            self::PAYMENT_METHOD => self::review(),
            self::REVIEW => null,
            default => null
        };
    }

    /**
     * Bir önceki adımı getir
     */
    public function getPrevious(): ?self
    {
        return match ($this->value) {
            self::SHIPPING_ADDRESS => null,
            self::BILLING_ADDRESS => self::shippingAddress(),
            self::SHIPPING_METHOD => self::billingAddress(),
            self::PAYMENT_METHOD => self::shippingMethod(),
            self::REVIEW => self::paymentMethod(),
            default => null
        };
    }

    /**
     * Belirli bir adımdan sonra mı kontrol et
     */
    public function isAfter(self $other): bool
    {
        return $this->getOrder() > $other->getOrder();
    }

    /**
     * Belirli bir adımdan önce mi kontrol et
     */
    public function isBefore(self $other): bool
    {
        return $this->getOrder() < $other->getOrder();
    }

    /**
     * Son adım mı kontrol et
     */
    public function isLast(): bool
    {
        return $this->value === self::REVIEW;
    }

    /**
     * İlk adım mı kontrol et
     */
    public function isFirst(): bool
    {
        return $this->value === self::SHIPPING_ADDRESS;
    }

    /**
     * Tüm adımları getir
     */
    public static function getAllSteps(): array
    {
        return [
            self::shippingAddress(),
            self::billingAddress(),
            self::shippingMethod(),
            self::paymentMethod(),
            self::review(),
        ];
    }

    /**
     * Geçerli adımları getir
     */
    public static function getValidSteps(): array
    {
        return [
            self::SHIPPING_ADDRESS,
            self::BILLING_ADDRESS,
            self::SHIPPING_METHOD,
            self::PAYMENT_METHOD,
            self::REVIEW,
        ];
    }

    /**
     * Değeri validate et
     */
    protected function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new \InvalidArgumentException('Checkout step cannot be empty');
        }

        $validSteps = self::getValidSteps();
        
        if (!in_array($value, $validSteps)) {
            throw new \InvalidArgumentException(
                "Invalid checkout step: {$value}. Valid steps are: " . implode(', ', $validSteps)
            );
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && $this->value === $other->value;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'value' => $this->value,
            'display_name' => $this->getDisplayName(),
            'order' => $this->getOrder(),
            'is_first' => $this->isFirst(),
            'is_last' => $this->isLast(),
        ];
    }
}
