<?php

namespace App\Domain\Cart\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * ShippingAddress Value Object
 * Kargo adresi için immutable value object
 */
class ShippingAddress extends ValueObject
{
    private string $firstName;
    private string $lastName;
    private string $company;
    private string $addressLine1;
    private string $addressLine2;
    private string $city;
    private string $state;
    private string $postalCode;
    private string $country;
    private string $phone;
    private ?string $email;
    private ?string $notes;

    private function __construct(
        string $firstName,
        string $lastName,
        string $company,
        string $addressLine1,
        string $addressLine2,
        string $city,
        string $state,
        string $postalCode,
        string $country,
        string $phone,
        ?string $email = null,
        ?string $notes = null
    ) {
        $this->validate($firstName, $lastName, $addressLine1, $city, $postalCode, $country, $phone);
        
        $this->firstName = trim($firstName);
        $this->lastName = trim($lastName);
        $this->company = trim($company);
        $this->addressLine1 = trim($addressLine1);
        $this->addressLine2 = trim($addressLine2);
        $this->city = trim($city);
        $this->state = trim($state);
        $this->postalCode = trim($postalCode);
        $this->country = trim($country);
        $this->phone = trim($phone);
        $this->email = $email ? trim($email) : null;
        $this->notes = $notes ? trim($notes) : null;
    }

    /**
     * Array'den ShippingAddress oluştur
     */
    public static function fromArray(array $data): self
    {
        return new self(
            firstName: $data['first_name'] ?? '',
            lastName: $data['last_name'] ?? '',
            company: $data['company'] ?? '',
            addressLine1: $data['address_line_1'] ?? '',
            addressLine2: $data['address_line_2'] ?? '',
            city: $data['city'] ?? '',
            state: $data['state'] ?? '',
            postalCode: $data['postal_code'] ?? '',
            country: $data['country'] ?? 'TR',
            phone: $data['phone'] ?? '',
            email: $data['email'] ?? null,
            notes: $data['notes'] ?? null
        );
    }

    /**
     * Parametrelerle ShippingAddress oluştur
     */
    public static function create(
        string $firstName,
        string $lastName,
        string $addressLine1,
        string $city,
        string $postalCode,
        string $country,
        string $phone,
        string $company = '',
        string $addressLine2 = '',
        string $state = '',
        ?string $email = null,
        ?string $notes = null
    ): self {
        return new self(
            $firstName,
            $lastName,
            $company,
            $addressLine1,
            $addressLine2,
            $city,
            $state,
            $postalCode,
            $country,
            $phone,
            $email,
            $notes
        );
    }

    /**
     * Tam adı getir
     */
    public function getFullName(): string
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }

    /**
     * Tam adresi getir
     */
    public function getFullAddress(): string
    {
        $parts = array_filter([
            $this->addressLine1,
            $this->addressLine2,
            $this->city,
            $this->state,
            $this->postalCode,
            $this->country
        ]);

        return implode(', ', $parts);
    }

    /**
     * Şirket adresi mi kontrol et
     */
    public function isCompanyAddress(): bool
    {
        return !empty($this->company);
    }

    /**
     * Türkiye adresi mi kontrol et
     */
    public function isTurkishAddress(): bool
    {
        return strtoupper($this->country) === 'TR' || strtoupper($this->country) === 'TURKEY';
    }

    /**
     * Uluslararası adres mi kontrol et
     */
    public function isInternationalAddress(): bool
    {
        return !$this->isTurkishAddress();
    }

    // Getters
    public function getFirstName(): string { return $this->firstName; }
    public function getLastName(): string { return $this->lastName; }
    public function getCompany(): string { return $this->company; }
    public function getAddressLine1(): string { return $this->addressLine1; }
    public function getAddressLine2(): string { return $this->addressLine2; }
    public function getCity(): string { return $this->city; }
    public function getState(): string { return $this->state; }
    public function getPostalCode(): string { return $this->postalCode; }
    public function getCountry(): string { return $this->country; }
    public function getPhone(): string { return $this->phone; }
    public function getEmail(): ?string { return $this->email; }
    public function getNotes(): ?string { return $this->notes; }

    /**
     * Değerleri validate et
     */
    private function validate(
        string $firstName,
        string $lastName,
        string $addressLine1,
        string $city,
        string $postalCode,
        string $country,
        string $phone
    ): void {
        if (empty(trim($firstName))) {
            throw new \InvalidArgumentException('First name is required');
        }

        if (empty(trim($lastName))) {
            throw new \InvalidArgumentException('Last name is required');
        }

        if (empty(trim($addressLine1))) {
            throw new \InvalidArgumentException('Address line 1 is required');
        }

        if (empty(trim($city))) {
            throw new \InvalidArgumentException('City is required');
        }

        if (empty(trim($postalCode))) {
            throw new \InvalidArgumentException('Postal code is required');
        }

        if (empty(trim($country))) {
            throw new \InvalidArgumentException('Country is required');
        }

        if (empty(trim($phone))) {
            throw new \InvalidArgumentException('Phone is required');
        }

        // Telefon numarası format kontrolü
        if (!preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', trim($phone))) {
            throw new \InvalidArgumentException('Invalid phone number format');
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self &&
            $this->firstName === $other->firstName &&
            $this->lastName === $other->lastName &&
            $this->addressLine1 === $other->addressLine1 &&
            $this->city === $other->city &&
            $this->postalCode === $other->postalCode &&
            $this->country === $other->country;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'full_name' => $this->getFullName(),
            'company' => $this->company,
            'address_line_1' => $this->addressLine1,
            'address_line_2' => $this->addressLine2,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postalCode,
            'country' => $this->country,
            'phone' => $this->phone,
            'email' => $this->email,
            'notes' => $this->notes,
            'full_address' => $this->getFullAddress(),
            'is_company' => $this->isCompanyAddress(),
            'is_turkish' => $this->isTurkishAddress(),
        ];
    }
}
