<?php

namespace App\Domain\Cart\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * SessionId Value Object
 * Oturum kimliği için immutable value object
 */
class SessionId extends ValueObject
{
    private string $value;

    private function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    /**
     * String değerden SessionId oluştur
     */
    public static function fromString(string $value): self
    {
        return new self($value);
    }

    /**
     * Yeni session ID oluştur
     */
    public static function generate(): self
    {
        return new self(bin2hex(random_bytes(16)));
    }

    /**
     * Değeri string olarak getir
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * String temsilini getir
     */
    public function toString(): string
    {
        return $this->value;
    }

    /**
     * Değeri validate et
     */
    protected function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new \InvalidArgumentException('Session ID cannot be empty');
        }

        if (strlen($value) < 8) {
            throw new \InvalidArgumentException('Session ID must be at least 8 characters long');
        }

        if (strlen($value) > 255) {
            throw new \InvalidArgumentException('Session ID cannot be longer than 255 characters');
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && $this->value === $other->value;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return ['session_id' => $this->value];
    }
}
