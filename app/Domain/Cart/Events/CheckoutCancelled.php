<?php

namespace App\Domain\Cart\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Cart\Entities\CheckoutProcess;
use Carbon\Carbon;

/**
 * CheckoutCancelled Domain Event
 * Checkout süreci iptal edildiğinde tetiklenir
 */
class CheckoutCancelled implements DomainEvent
{
    private CheckoutProcess $checkoutProcess;
    private ?string $reason;
    private Carbon $occurredOn;

    public function __construct(CheckoutProcess $checkoutProcess, ?string $reason = null)
    {
        $this->checkoutProcess = $checkoutProcess;
        $this->reason = $reason;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Checkout process'i getir
     */
    public function getCheckoutProcess(): CheckoutProcess
    {
        return $this->checkoutProcess;
    }

    /**
     * İptal nedenini getir
     */
    public function getReason(): ?string
    {
        return $this->reason;
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'checkout.cancelled';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        $cart = $this->checkoutProcess->getCart();
        
        return [
            'checkout_id' => $this->checkoutProcess->getId(),
            'cart_id' => $cart->getId(),
            'user_id' => $cart->getUserId(),
            'session_id' => $cart->getSessionId()?->getValue(),
            'cancellation_reason' => $this->reason,
            'completed_steps' => $this->checkoutProcess->getCompletedSteps(),
            'current_step' => $this->checkoutProcess->getCurrentStep()?->getValue(),
            'checkout_duration' => $this->occurredOn->diffInSeconds($this->checkoutProcess->getCreatedAt()),
            'item_count' => $cart->getTotalItems(),
            'cart_total' => $cart->getTotal()->getTotal()->getAmount(),
            'cancelled_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->checkoutProcess->getId();
    }
}
