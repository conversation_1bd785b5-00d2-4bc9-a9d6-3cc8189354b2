<?php

namespace App\Domain\Cart\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Cart\Entities\CheckoutProcess;
use Carbon\Carbon;

/**
 * CheckoutRestarted Domain Event
 * Checkout süreci yeniden başlatıldığında tetiklenir
 */
class CheckoutRestarted implements DomainEvent
{
    private CheckoutProcess $checkoutProcess;
    private Carbon $occurredOn;

    public function __construct(CheckoutProcess $checkoutProcess)
    {
        $this->checkoutProcess = $checkoutProcess;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Checkout process'i getir
     */
    public function getCheckoutProcess(): CheckoutProcess
    {
        return $this->checkoutProcess;
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'checkout.restarted';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        $cart = $this->checkoutProcess->getCart();
        
        return [
            'checkout_id' => $this->checkoutProcess->getId(),
            'cart_id' => $cart->getId(),
            'user_id' => $cart->getUserId(),
            'session_id' => $cart->getSessionId()?->getValue(),
            'item_count' => $cart->getTotalItems(),
            'cart_total' => $cart->getTotal()->getTotal()->getAmount(),
            'restarted_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->checkoutProcess->getId();
    }
}
