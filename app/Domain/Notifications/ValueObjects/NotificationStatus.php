<?php

namespace App\Domain\Notifications\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * NotificationStatus Value Object
 * Bildirim durumu için immutable value object
 */
class NotificationStatus extends ValueObject
{
    public const PENDING = 'pending';
    public const SENDING = 'sending';
    public const SENT = 'sent';
    public const DELIVERED = 'delivered';
    public const READ = 'read';
    public const FAILED = 'failed';
    public const RETRYING = 'retrying';
    public const CANCELLED = 'cancelled';
    public const EXPIRED = 'expired';

    private string $value;

    private function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    /**
     * String değerden NotificationStatus oluştur
     */
    public static function fromString(string $value): self
    {
        return new self($value);
    }

    /**
     * Beklemede durumu
     */
    public static function pending(): self
    {
        return new self(self::PENDING);
    }

    /**
     * Gönderiliyor durumu
     */
    public static function sending(): self
    {
        return new self(self::SENDING);
    }

    /**
     * G<PERSON><PERSON>ildi durumu
     */
    public static function sent(): self
    {
        return new self(self::SENT);
    }

    /**
     * Teslim edildi durumu
     */
    public static function delivered(): self
    {
        return new self(self::DELIVERED);
    }

    /**
     * Okundu durumu
     */
    public static function read(): self
    {
        return new self(self::READ);
    }

    /**
     * Başarısız durumu
     */
    public static function failed(): self
    {
        return new self(self::FAILED);
    }

    /**
     * Yeniden deneniyor durumu
     */
    public static function retrying(): self
    {
        return new self(self::RETRYING);
    }

    /**
     * İptal edildi durumu
     */
    public static function cancelled(): self
    {
        return new self(self::CANCELLED);
    }

    /**
     * Süresi doldu durumu
     */
    public static function expired(): self
    {
        return new self(self::EXPIRED);
    }

    /**
     * Değeri string olarak getir
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * String temsilini getir
     */
    public function toString(): string
    {
        return $this->value;
    }

    /**
     * Durum adını getir
     */
    public function getDisplayName(): string
    {
        return match ($this->value) {
            self::PENDING => 'Beklemede',
            self::SENDING => 'Gönderiliyor',
            self::SENT => 'Gönderildi',
            self::DELIVERED => 'Teslim Edildi',
            self::READ => 'Okundu',
            self::FAILED => 'Başarısız',
            self::RETRYING => 'Yeniden Deneniyor',
            self::CANCELLED => 'İptal Edildi',
            self::EXPIRED => 'Süresi Doldu',
            default => 'Bilinmeyen Durum'
        };
    }

    /**
     * Durum açıklamasını getir
     */
    public function getDescription(): string
    {
        return match ($this->value) {
            self::PENDING => 'Bildirim gönderilmeyi bekliyor',
            self::SENDING => 'Bildirim şu anda gönderiliyor',
            self::SENT => 'Bildirim başarıyla gönderildi',
            self::DELIVERED => 'Bildirim alıcıya teslim edildi',
            self::READ => 'Bildirim alıcı tarafından okundu',
            self::FAILED => 'Bildirim gönderilemedi',
            self::RETRYING => 'Bildirim yeniden gönderilmeye çalışılıyor',
            self::CANCELLED => 'Bildirim gönderimi iptal edildi',
            self::EXPIRED => 'Bildirim gönderim süresi doldu',
            default => 'Durum bilgisi mevcut değil'
        };
    }

    /**
     * Durum rengini getir
     */
    public function getColor(): string
    {
        return match ($this->value) {
            self::PENDING => 'gray',
            self::SENDING => 'blue',
            self::SENT => 'green',
            self::DELIVERED => 'green',
            self::READ => 'green',
            self::FAILED => 'red',
            self::RETRYING => 'yellow',
            self::CANCELLED => 'red',
            self::EXPIRED => 'orange',
            default => 'gray'
        };
    }

    /**
     * Durum ikonunu getir
     */
    public function getIcon(): string
    {
        return match ($this->value) {
            self::PENDING => 'clock',
            self::SENDING => 'send',
            self::SENT => 'check',
            self::DELIVERED => 'check-circle',
            self::READ => 'eye',
            self::FAILED => 'x-circle',
            self::RETRYING => 'refresh-cw',
            self::CANCELLED => 'x',
            self::EXPIRED => 'clock',
            default => 'bell'
        };
    }

    /**
     * Durum sırasını getir
     */
    public function getOrder(): int
    {
        return match ($this->value) {
            self::PENDING => 1,
            self::SENDING => 2,
            self::SENT => 3,
            self::DELIVERED => 4,
            self::READ => 5,
            self::RETRYING => 6,
            self::FAILED => 7,
            self::CANCELLED => 8,
            self::EXPIRED => 9,
            default => 0
        };
    }

    /**
     * Aktif durum mu kontrol et
     */
    public function isActive(): bool
    {
        return in_array($this->value, [
            self::PENDING,
            self::SENDING,
            self::RETRYING
        ]);
    }

    /**
     * Tamamlanmış durum mu kontrol et
     */
    public function isCompleted(): bool
    {
        return in_array($this->value, [
            self::SENT,
            self::DELIVERED,
            self::READ,
            self::FAILED,
            self::CANCELLED,
            self::EXPIRED
        ]);
    }

    /**
     * Başarılı durum mu kontrol et
     */
    public function isSuccessful(): bool
    {
        return in_array($this->value, [
            self::SENT,
            self::DELIVERED,
            self::READ
        ]);
    }

    /**
     * Başarısız durum mu kontrol et
     */
    public function isFailed(): bool
    {
        return in_array($this->value, [
            self::FAILED,
            self::CANCELLED,
            self::EXPIRED
        ]);
    }

    /**
     * Beklemede mi kontrol et
     */
    public function isPending(): bool
    {
        return $this->value === self::PENDING;
    }

    /**
     * Gönderiliyor mu kontrol et
     */
    public function isSending(): bool
    {
        return $this->value === self::SENDING;
    }

    /**
     * Gönderildi mi kontrol et
     */
    public function isSent(): bool
    {
        return $this->value === self::SENT;
    }

    /**
     * Teslim edildi mi kontrol et
     */
    public function isDelivered(): bool
    {
        return $this->value === self::DELIVERED;
    }

    /**
     * Okundu mu kontrol et
     */
    public function isRead(): bool
    {
        return $this->value === self::READ;
    }

    /**
     * Başarısız mı kontrol et
     */
    public function isFailedStatus(): bool
    {
        return $this->value === self::FAILED;
    }

    /**
     * Yeniden deneniyor mu kontrol et
     */
    public function isRetrying(): bool
    {
        return $this->value === self::RETRYING;
    }

    /**
     * İptal edildi mi kontrol et
     */
    public function isCancelled(): bool
    {
        return $this->value === self::CANCELLED;
    }

    /**
     * Süresi doldu mu kontrol et
     */
    public function isExpired(): bool
    {
        return $this->value === self::EXPIRED;
    }

    /**
     * Yeniden deneme yapılabilir mi kontrol et
     */
    public function canRetry(): bool
    {
        return in_array($this->value, [
            self::FAILED,
            self::RETRYING
        ]);
    }

    /**
     * İptal edilebilir mi kontrol et
     */
    public function canCancel(): bool
    {
        return in_array($this->value, [
            self::PENDING,
            self::RETRYING
        ]);
    }

    /**
     * Yeniden planlanabilir mi kontrol et
     */
    public function canReschedule(): bool
    {
        return in_array($this->value, [
            self::PENDING,
            self::FAILED,
            self::RETRYING,
            self::CANCELLED
        ]);
    }

    /**
     * Bir sonraki olası durumları getir
     */
    public function getNextPossibleStatuses(): array
    {
        return match ($this->value) {
            self::PENDING => [self::SENDING, self::CANCELLED, self::EXPIRED],
            self::SENDING => [self::SENT, self::FAILED],
            self::SENT => [self::DELIVERED, self::READ],
            self::DELIVERED => [self::READ],
            self::FAILED => [self::RETRYING, self::CANCELLED],
            self::RETRYING => [self::SENDING, self::FAILED, self::CANCELLED],
            default => []
        };
    }

    /**
     * Belirli duruma geçiş yapılabilir mi kontrol et
     */
    public function canTransitionTo(self $newStatus): bool
    {
        $possibleStatuses = $this->getNextPossibleStatuses();
        return in_array($newStatus->getValue(), $possibleStatuses);
    }

    /**
     * Geçerli durumları getir
     */
    public static function getValidStatuses(): array
    {
        return [
            self::PENDING,
            self::SENDING,
            self::SENT,
            self::DELIVERED,
            self::READ,
            self::FAILED,
            self::RETRYING,
            self::CANCELLED,
            self::EXPIRED,
        ];
    }

    /**
     * Tüm durumları getir
     */
    public static function getAllStatuses(): array
    {
        return [
            self::pending(),
            self::sending(),
            self::sent(),
            self::delivered(),
            self::read(),
            self::failed(),
            self::retrying(),
            self::cancelled(),
            self::expired(),
        ];
    }

    /**
     * Durum kategorilerini getir
     */
    public static function getStatusCategories(): array
    {
        return [
            'active' => [self::PENDING, self::SENDING, self::RETRYING],
            'successful' => [self::SENT, self::DELIVERED, self::READ],
            'failed' => [self::FAILED, self::CANCELLED, self::EXPIRED],
        ];
    }

    /**
     * Değeri validate et
     */
    protected function validate(string $value): void
    {
        if (empty(trim($value))) {
            throw new \InvalidArgumentException('Notification status cannot be empty');
        }

        $validStatuses = self::getValidStatuses();
        
        if (!in_array($value, $validStatuses)) {
            throw new \InvalidArgumentException(
                "Invalid notification status: {$value}. Valid statuses are: " . implode(', ', $validStatuses)
            );
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && $this->value === $other->value;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'value' => $this->value,
            'display_name' => $this->getDisplayName(),
            'description' => $this->getDescription(),
            'color' => $this->getColor(),
            'icon' => $this->getIcon(),
            'order' => $this->getOrder(),
            'is_active' => $this->isActive(),
            'is_completed' => $this->isCompleted(),
            'is_successful' => $this->isSuccessful(),
            'is_failed' => $this->isFailed(),
            'can_retry' => $this->canRetry(),
            'can_cancel' => $this->canCancel(),
            'can_reschedule' => $this->canReschedule(),
            'next_possible_statuses' => $this->getNextPossibleStatuses(),
        ];
    }
}
