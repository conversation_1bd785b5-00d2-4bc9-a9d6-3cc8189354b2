<?php

namespace App\Domain\Notifications\Repositories;

use App\Domain\Notifications\Entities\Notification;
use App\Domain\Notifications\ValueObjects\NotificationType;
use App\Domain\Notifications\ValueObjects\NotificationChannel;
use App\Domain\Notifications\ValueObjects\NotificationStatus;
use App\Domain\Notifications\ValueObjects\NotificationPriority;
use App\Domain\Notifications\ValueObjects\RecipientInfo;
use Carbon\Carbon;

/**
 * NotificationRepositoryInterface
 * Bildirim repository interface'i
 */
interface NotificationRepositoryInterface
{
    /**
     * Bildirim kaydet
     */
    public function save(Notification $notification): Notification;

    /**
     * ID ile bildirim bul
     */
    public function findById(int $id): ?Notification;

    /**
     * Bildirimi sil
     */
    public function delete(Notification $notification): bool;

    /**
     * ID ile bildirimi sil
     */
    public function deleteById(int $id): bool;

    /**
     * Bildirim var mı kontrol et
     */
    public function exists(int $id): bool;

    /**
     * Alıcıya göre bildirimleri al
     */
    public function findByRecipient(RecipientInfo $recipient, int $limit = 10, int $offset = 0): array;

    /**
     * Duruma göre bildirimleri al
     */
    public function findByStatus(NotificationStatus $status, int $limit = 10, int $offset = 0): array;

    /**
     * Kanala göre bildirimleri al
     */
    public function findByChannel(NotificationChannel $channel, int $limit = 10, int $offset = 0): array;

    /**
     * Tipe göre bildirimleri al
     */
    public function findByType(NotificationType $type, int $limit = 10, int $offset = 0): array;

    /**
     * Önceliğe göre bildirimleri al
     */
    public function findByPriority(NotificationPriority $priority, int $limit = 10, int $offset = 0): array;

    /**
     * Bekleyen bildirimleri al
     */
    public function findPending(int $limit = 10, int $offset = 0): array;

    /**
     * Gönderilmeyi bekleyen bildirimleri al (zamanlanmış)
     */
    public function findScheduledForDelivery(Carbon $before = null, int $limit = 10, int $offset = 0): array;

    /**
     * Yeniden deneme gereken bildirimleri al
     */
    public function findForRetry(int $limit = 10, int $offset = 0): array;

    /**
     * Başarısız bildirimleri al
     */
    public function findFailed(int $limit = 10, int $offset = 0): array;

    /**
     * Süresi dolmuş bildirimleri al
     */
    public function findExpired(int $limit = 10, int $offset = 0): array;

    /**
     * Yüksek öncelikli bildirimleri al
     */
    public function findHighPriority(int $limit = 10, int $offset = 0): array;

    /**
     * Okunmamış bildirimleri al
     */
    public function findUnread(RecipientInfo $recipient, int $limit = 10, int $offset = 0): array;

    /**
     * Tarih aralığında bildirimleri al
     */
    public function findByDateRange(Carbon $from, Carbon $to, int $limit = 10, int $offset = 0): array;

    /**
     * Template'e göre bildirimleri al
     */
    public function findByTemplate(string $templateId, int $limit = 10, int $offset = 0): array;

    /**
     * Toplu bildirim kaydet
     */
    public function saveBatch(array $notifications): array;

    /**
     * Toplu durum güncelleme
     */
    public function updateStatusBatch(array $notificationIds, NotificationStatus $status): int;

    /**
     * Eski bildirimleri temizle
     */
    public function deleteOlderThan(Carbon $date): int;

    /**
     * İstatistikleri al
     */
    public function getStatistics(Carbon $from = null, Carbon $to = null): array;

    /**
     * Kanal istatistikleri al
     */
    public function getChannelStatistics(Carbon $from = null, Carbon $to = null): array;

    /**
     * Tip istatistikleri al
     */
    public function getTypeStatistics(Carbon $from = null, Carbon $to = null): array;

    /**
     * Başarı oranı al
     */
    public function getSuccessRate(Carbon $from = null, Carbon $to = null): float;

    /**
     * Ortalama teslimat süresi al
     */
    public function getAverageDeliveryTime(Carbon $from = null, Carbon $to = null): ?float;

    /**
     * Alıcı bildirim sayısı al
     */
    public function getRecipientNotificationCount(RecipientInfo $recipient): int;

    /**
     * Günlük bildirim limiti kontrol et
     */
    public function checkDailyLimit(RecipientInfo $recipient, int $limit): bool;

    /**
     * Arama yap
     */
    public function search(array $criteria, int $limit = 10, int $offset = 0): array;

    /**
     * Toplam sayı al
     */
    public function count(array $criteria = []): int;
}
