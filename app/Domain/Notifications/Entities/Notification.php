<?php

namespace App\Domain\Notifications\Entities;

use App\Core\Domain\Entity;
use App\Domain\Shared\Traits\AggregateRoot;
use App\Domain\Notifications\ValueObjects\NotificationType;
use App\Domain\Notifications\ValueObjects\NotificationChannel;
use App\Domain\Notifications\ValueObjects\NotificationStatus;
use App\Domain\Notifications\ValueObjects\NotificationPriority;
use App\Domain\Notifications\ValueObjects\RecipientInfo;
use App\Domain\Notifications\Events\NotificationCreated;
use App\Domain\Notifications\Events\NotificationSent;
use App\Domain\Notifications\Events\NotificationFailed;
use App\Domain\Notifications\Events\NotificationRead;
use Carbon\Carbon;

/**
 * Notification Domain Entity (Aggregate Root)
 * Bildirim domain entity'si
 */
class Notification extends Entity
{
    use AggregateRoot;

    private ?int $id;
    private NotificationType $type;
    private NotificationChannel $channel;
    private NotificationStatus $status;
    private NotificationPriority $priority;
    private RecipientInfo $recipient;
    private string $title;
    private string $message;
    private array $data;
    private ?string $templateId;
    private array $templateVariables;
    private ?Carbon $scheduledAt;
    private ?Carbon $sentAt;
    private ?Carbon $readAt;
    private ?Carbon $expiresAt;
    private int $retryCount;
    private int $maxRetries;
    private ?string $failureReason;
    private array $deliveryAttempts;
    private array $metadata;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    private function __construct(
        NotificationType $type,
        NotificationChannel $channel,
        RecipientInfo $recipient,
        string $title,
        string $message,
        array $data = [],
        NotificationPriority $priority = null,
        ?string $templateId = null,
        array $templateVariables = [],
        ?Carbon $scheduledAt = null,
        ?Carbon $expiresAt = null,
        int $maxRetries = 3,
        array $metadata = []
    ) {
        $this->type = $type;
        $this->channel = $channel;
        $this->recipient = $recipient;
        $this->title = trim($title);
        $this->message = trim($message);
        $this->data = $data;
        $this->priority = $priority ?: NotificationPriority::normal();
        $this->templateId = $templateId;
        $this->templateVariables = $templateVariables;
        $this->scheduledAt = $scheduledAt;
        $this->expiresAt = $expiresAt;
        $this->maxRetries = $maxRetries;
        $this->metadata = $metadata;
        
        $this->status = NotificationStatus::pending();
        $this->retryCount = 0;
        $this->deliveryAttempts = [];
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();

        // Bildirim oluşturulduğunda event kaydet
        $this->recordEvent(new NotificationCreated($this));
    }

    /**
     * Yeni bildirim oluştur
     */
    public static function create(
        NotificationType $type,
        NotificationChannel $channel,
        RecipientInfo $recipient,
        string $title,
        string $message,
        array $data = [],
        NotificationPriority $priority = null,
        ?string $templateId = null,
        array $templateVariables = [],
        ?Carbon $scheduledAt = null,
        ?Carbon $expiresAt = null,
        int $maxRetries = 3,
        array $metadata = []
    ): self {
        return new self(
            $type,
            $channel,
            $recipient,
            $title,
            $message,
            $data,
            $priority,
            $templateId,
            $templateVariables,
            $scheduledAt,
            $expiresAt,
            $maxRetries,
            $metadata
        );
    }

    /**
     * Template'den bildirim oluştur
     */
    public static function fromTemplate(
        string $templateId,
        NotificationChannel $channel,
        RecipientInfo $recipient,
        array $templateVariables = [],
        NotificationPriority $priority = null,
        ?Carbon $scheduledAt = null,
        ?Carbon $expiresAt = null,
        array $metadata = []
    ): self {
        // Template'den title ve message alınacak (template service'den)
        return new self(
            NotificationType::fromTemplate($templateId),
            $channel,
            $recipient,
            '', // Template'den doldurulacak
            '', // Template'den doldurulacak
            [],
            $priority,
            $templateId,
            $templateVariables,
            $scheduledAt,
            $expiresAt,
            3,
            $metadata
        );
    }

    /**
     * Bildirimi gönder olarak işaretle
     */
    public function markAsSent(): void
    {
        if (!$this->status->isPending() && !$this->status->isRetrying()) {
            throw new \DomainException('Only pending or retrying notifications can be marked as sent');
        }

        $this->status = NotificationStatus::sent();
        $this->sentAt = Carbon::now();
        $this->updatedAt = Carbon::now();

        // Teslimat denemesi kaydet
        $this->addDeliveryAttempt('sent', 'Successfully sent');

        // Domain event kaydet
        $this->recordEvent(new NotificationSent($this));
    }

    /**
     * Bildirimi başarısız olarak işaretle
     */
    public function markAsFailed(string $reason): void
    {
        $this->failureReason = $reason;
        $this->retryCount++;
        $this->updatedAt = Carbon::now();

        // Teslimat denemesi kaydet
        $this->addDeliveryAttempt('failed', $reason);

        // Maksimum deneme sayısına ulaşıldı mı kontrol et
        if ($this->retryCount >= $this->maxRetries) {
            $this->status = NotificationStatus::failed();
        } else {
            $this->status = NotificationStatus::retrying();
        }

        // Domain event kaydet
        $this->recordEvent(new NotificationFailed($this, $reason));
    }

    /**
     * Bildirimi okundu olarak işaretle
     */
    public function markAsRead(): void
    {
        if (!$this->status->isSent()) {
            throw new \DomainException('Only sent notifications can be marked as read');
        }

        $this->readAt = Carbon::now();
        $this->updatedAt = Carbon::now();

        // Domain event kaydet
        $this->recordEvent(new NotificationRead($this));
    }

    /**
     * Bildirimi iptal et
     */
    public function cancel(string $reason = null): void
    {
        if ($this->status->isSent() || $this->status->isFailed()) {
            throw new \DomainException('Cannot cancel sent or failed notifications');
        }

        $this->status = NotificationStatus::cancelled();
        $this->failureReason = $reason ?: 'Cancelled by user';
        $this->updatedAt = Carbon::now();

        // Teslimat denemesi kaydet
        $this->addDeliveryAttempt('cancelled', $this->failureReason);
    }

    /**
     * Bildirimi yeniden planla
     */
    public function reschedule(Carbon $newScheduledAt): void
    {
        if ($this->status->isSent()) {
            throw new \DomainException('Cannot reschedule sent notifications');
        }

        $this->scheduledAt = $newScheduledAt;
        $this->status = NotificationStatus::pending();
        $this->retryCount = 0;
        $this->failureReason = null;
        $this->updatedAt = Carbon::now();
    }

    /**
     * Teslimat denemesi ekle
     */
    private function addDeliveryAttempt(string $status, string $message): void
    {
        $this->deliveryAttempts[] = [
            'attempt' => count($this->deliveryAttempts) + 1,
            'status' => $status,
            'message' => $message,
            'timestamp' => Carbon::now()->toISOString(),
        ];
    }

    /**
     * Bildirim gönderilmeye hazır mı kontrol et
     */
    public function isReadyToSend(): bool
    {
        if (!$this->status->isPending() && !$this->status->isRetrying()) {
            return false;
        }

        // Zamanlanmış bildirim ise zamanı geldi mi kontrol et
        if ($this->scheduledAt && Carbon::now()->isBefore($this->scheduledAt)) {
            return false;
        }

        // Süresi dolmuş mu kontrol et
        if ($this->isExpired()) {
            return false;
        }

        return true;
    }

    /**
     * Bildirim süresi dolmuş mu kontrol et
     */
    public function isExpired(): bool
    {
        return $this->expiresAt && Carbon::now()->isAfter($this->expiresAt);
    }

    /**
     * Bildirim okunmuş mu kontrol et
     */
    public function isRead(): bool
    {
        return $this->readAt !== null;
    }

    /**
     * Bildirim zamanlanmış mı kontrol et
     */
    public function isScheduled(): bool
    {
        return $this->scheduledAt !== null && Carbon::now()->isBefore($this->scheduledAt);
    }

    /**
     * Yeniden deneme yapılabilir mi kontrol et
     */
    public function canRetry(): bool
    {
        return $this->retryCount < $this->maxRetries && !$this->isExpired();
    }

    /**
     * Son teslimat denemesini getir
     */
    public function getLastDeliveryAttempt(): ?array
    {
        return empty($this->deliveryAttempts) ? null : end($this->deliveryAttempts);
    }

    /**
     * Başarılı teslimat denemelerini getir
     */
    public function getSuccessfulAttempts(): array
    {
        return array_filter($this->deliveryAttempts, function($attempt) {
            return $attempt['status'] === 'sent';
        });
    }

    /**
     * Başarısız teslimat denemelerini getir
     */
    public function getFailedAttempts(): array
    {
        return array_filter($this->deliveryAttempts, function($attempt) {
            return $attempt['status'] === 'failed';
        });
    }

    /**
     * Bildirim yaşını getir (saniye)
     */
    public function getAge(): int
    {
        return $this->createdAt->diffInSeconds(Carbon::now());
    }

    /**
     * Teslimat süresini getir (saniye)
     */
    public function getDeliveryDuration(): ?int
    {
        if (!$this->sentAt) {
            return null;
        }

        return $this->createdAt->diffInSeconds($this->sentAt);
    }

    /**
     * Kalan süreyi getir (saniye)
     */
    public function getTimeToExpiry(): ?int
    {
        if (!$this->expiresAt) {
            return null;
        }

        $now = Carbon::now();
        if ($now->isAfter($this->expiresAt)) {
            return 0;
        }

        return $now->diffInSeconds($this->expiresAt);
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getType(): NotificationType { return $this->type; }
    public function getChannel(): NotificationChannel { return $this->channel; }
    public function getStatus(): NotificationStatus { return $this->status; }
    public function getPriority(): NotificationPriority { return $this->priority; }
    public function getRecipient(): RecipientInfo { return $this->recipient; }
    public function getTitle(): string { return $this->title; }
    public function getMessage(): string { return $this->message; }
    public function getData(): array { return $this->data; }
    public function getTemplateId(): ?string { return $this->templateId; }
    public function getTemplateVariables(): array { return $this->templateVariables; }
    public function getScheduledAt(): ?Carbon { return $this->scheduledAt; }
    public function getSentAt(): ?Carbon { return $this->sentAt; }
    public function getReadAt(): ?Carbon { return $this->readAt; }
    public function getExpiresAt(): ?Carbon { return $this->expiresAt; }
    public function getRetryCount(): int { return $this->retryCount; }
    public function getMaxRetries(): int { return $this->maxRetries; }
    public function getFailureReason(): ?string { return $this->failureReason; }
    public function getDeliveryAttempts(): array { return $this->deliveryAttempts; }
    public function getMetadata(): array { return $this->metadata; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // Setters (for infrastructure)
    public function setId(int $id): void { $this->id = $id; }
    public function setTitle(string $title): void { $this->title = trim($title); }
    public function setMessage(string $message): void { $this->message = trim($message); }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type->toArray(),
            'channel' => $this->channel->toArray(),
            'status' => $this->status->toArray(),
            'priority' => $this->priority->toArray(),
            'recipient' => $this->recipient->toArray(),
            'title' => $this->title,
            'message' => $this->message,
            'data' => $this->data,
            'template_id' => $this->templateId,
            'template_variables' => $this->templateVariables,
            'scheduled_at' => $this->scheduledAt?->toISOString(),
            'sent_at' => $this->sentAt?->toISOString(),
            'read_at' => $this->readAt?->toISOString(),
            'expires_at' => $this->expiresAt?->toISOString(),
            'retry_count' => $this->retryCount,
            'max_retries' => $this->maxRetries,
            'failure_reason' => $this->failureReason,
            'delivery_attempts' => $this->deliveryAttempts,
            'is_ready_to_send' => $this->isReadyToSend(),
            'is_expired' => $this->isExpired(),
            'is_read' => $this->isRead(),
            'is_scheduled' => $this->isScheduled(),
            'can_retry' => $this->canRetry(),
            'age_seconds' => $this->getAge(),
            'delivery_duration_seconds' => $this->getDeliveryDuration(),
            'time_to_expiry_seconds' => $this->getTimeToExpiry(),
            'metadata' => $this->metadata,
            'created_at' => $this->createdAt->toISOString(),
            'updated_at' => $this->updatedAt->toISOString(),
        ];
    }
}
