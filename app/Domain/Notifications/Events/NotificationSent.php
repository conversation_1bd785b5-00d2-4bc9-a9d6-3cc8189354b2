<?php

namespace App\Domain\Notifications\Events;

use App\Core\Domain\Events\DomainEvent;
use App\Domain\Notifications\Entities\Notification;
use Carbon\Carbon;

/**
 * NotificationSent Domain Event
 * Bildirim gönderildiğinde tetiklenen domain event
 */
class NotificationSent extends DomainEvent
{
    public function __construct(
        private Notification $notification
    ) {
        parent::__construct();
    }

    /**
     * Bildirimi getir
     */
    public function getNotification(): Notification
    {
        return $this->notification;
    }

    /**
     * Event adını getir
     */
    public function getEventName(): string
    {
        return 'notification.sent';
    }

    /**
     * Event verilerini getir
     */
    public function getEventData(): array
    {
        return [
            'notification_id' => $this->notification->getId(),
            'type' => $this->notification->getType()->getValue(),
            'channel' => $this->notification->getChannel()->getValue(),
            'priority' => $this->notification->getPriority()->getValue(),
            'recipient_type' => $this->notification->getRecipient()->getType(),
            'recipient_id' => $this->notification->getRecipient()->getId(),
            'title' => $this->notification->getTitle(),
            'sent_at' => $this->notification->getSentAt()?->toISOString(),
            'delivery_duration' => $this->notification->getDeliveryDuration(),
            'retry_count' => $this->notification->getRetryCount(),
            'template_id' => $this->notification->getTemplateId(),
        ];
    }

    /**
     * Event payload'ını getir
     */
    public function getPayload(): array
    {
        return [
            'event_id' => $this->getEventId(),
            'event_name' => $this->getEventName(),
            'occurred_at' => $this->getOccurredAt()->toISOString(),
            'notification' => [
                'id' => $this->notification->getId(),
                'type' => $this->notification->getType()->toArray(),
                'channel' => $this->notification->getChannel()->toArray(),
                'status' => $this->notification->getStatus()->toArray(),
                'priority' => $this->notification->getPriority()->toArray(),
                'recipient' => $this->notification->getRecipient()->toArray(),
                'title' => $this->notification->getTitle(),
                'sent_at' => $this->notification->getSentAt()?->toISOString(),
                'delivery_duration' => $this->notification->getDeliveryDuration(),
                'retry_count' => $this->notification->getRetryCount(),
                'delivery_attempts' => $this->notification->getDeliveryAttempts(),
                'template_id' => $this->notification->getTemplateId(),
            ],
            'context' => [
                'aggregate_id' => $this->notification->getId(),
                'aggregate_type' => 'notification',
                'event_version' => 1,
                'delivery_metrics' => [
                    'duration_seconds' => $this->notification->getDeliveryDuration(),
                    'attempt_count' => $this->notification->getRetryCount() + 1,
                    'channel_type' => $this->notification->getChannel()->getValue(),
                    'priority_level' => $this->notification->getPriority()->getLevel(),
                ]
            ]
        ];
    }

    /**
     * Event'in kritik olup olmadığını belirle
     */
    public function isCritical(): bool
    {
        return $this->notification->getPriority()->isCritical();
    }

    /**
     * Event'in anında işlenmesi gerekip gerekmediğini belirle
     */
    public function requiresImmediateProcessing(): bool
    {
        return $this->notification->getPriority()->isHighPriority();
    }

    /**
     * Teslimat başarılı mı
     */
    public function isSuccessfulDelivery(): bool
    {
        return $this->notification->getStatus()->isSent();
    }

    /**
     * Yeniden deneme sonucu mu
     */
    public function isRetryAttempt(): bool
    {
        return $this->notification->getRetryCount() > 0;
    }

    /**
     * Teslimat süresini getir (saniye)
     */
    public function getDeliveryDuration(): ?int
    {
        return $this->notification->getDeliveryDuration();
    }

    /**
     * Event kategorisini getir
     */
    public function getCategory(): string
    {
        return 'notification';
    }

    /**
     * Event alt kategorisini getir
     */
    public function getSubCategory(): string
    {
        return 'delivery';
    }

    /**
     * Event etiketlerini getir
     */
    public function getTags(): array
    {
        $tags = [
            'notification',
            'sent',
            'delivery',
            $this->notification->getType()->getCategory(),
            $this->notification->getChannel()->getValue(),
            $this->notification->getPriority()->getValue(),
        ];

        if ($this->isRetryAttempt()) {
            $tags[] = 'retry';
        }

        if ($this->isSuccessfulDelivery()) {
            $tags[] = 'success';
        }

        return $tags;
    }

    /**
     * Event'in serialize edilebilir verilerini getir
     */
    public function toArray(): array
    {
        return [
            'event_id' => $this->getEventId(),
            'event_name' => $this->getEventName(),
            'occurred_at' => $this->getOccurredAt()->toISOString(),
            'data' => $this->getEventData(),
            'payload' => $this->getPayload(),
            'is_critical' => $this->isCritical(),
            'requires_immediate_processing' => $this->requiresImmediateProcessing(),
            'is_successful_delivery' => $this->isSuccessfulDelivery(),
            'is_retry_attempt' => $this->isRetryAttempt(),
            'delivery_duration' => $this->getDeliveryDuration(),
            'category' => $this->getCategory(),
            'sub_category' => $this->getSubCategory(),
            'tags' => $this->getTags(),
        ];
    }
}
