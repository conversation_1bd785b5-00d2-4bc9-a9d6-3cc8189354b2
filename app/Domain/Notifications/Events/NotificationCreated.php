<?php

namespace App\Domain\Notifications\Events;

use App\Core\Domain\Events\DomainEvent;
use App\Domain\Notifications\Entities\Notification;
use Carbon\Carbon;

/**
 * NotificationCreated Domain Event
 * Bildirim oluşturulduğunda tetiklenen domain event
 */
class NotificationCreated extends DomainEvent
{
    public function __construct(
        private Notification $notification
    ) {
        parent::__construct();
    }

    /**
     * Bildirimi getir
     */
    public function getNotification(): Notification
    {
        return $this->notification;
    }

    /**
     * Event adını getir
     */
    public function getEventName(): string
    {
        return 'notification.created';
    }

    /**
     * Event verilerini getir
     */
    public function getEventData(): array
    {
        return [
            'notification_id' => $this->notification->getId(),
            'type' => $this->notification->getType()->getValue(),
            'channel' => $this->notification->getChannel()->getValue(),
            'priority' => $this->notification->getPriority()->getValue(),
            'recipient_type' => $this->notification->getRecipient()->getType(),
            'recipient_id' => $this->notification->getRecipient()->getId(),
            'title' => $this->notification->getTitle(),
            'scheduled_at' => $this->notification->getScheduledAt()?->toISOString(),
            'expires_at' => $this->notification->getExpiresAt()?->toISOString(),
            'template_id' => $this->notification->getTemplateId(),
            'created_at' => $this->notification->getCreatedAt()->toISOString(),
        ];
    }

    /**
     * Event payload'ını getir
     */
    public function getPayload(): array
    {
        return [
            'event_id' => $this->getEventId(),
            'event_name' => $this->getEventName(),
            'occurred_at' => $this->getOccurredAt()->toISOString(),
            'notification' => [
                'id' => $this->notification->getId(),
                'type' => $this->notification->getType()->toArray(),
                'channel' => $this->notification->getChannel()->toArray(),
                'status' => $this->notification->getStatus()->toArray(),
                'priority' => $this->notification->getPriority()->toArray(),
                'recipient' => $this->notification->getRecipient()->toArray(),
                'title' => $this->notification->getTitle(),
                'message' => $this->notification->getMessage(),
                'data' => $this->notification->getData(),
                'template_id' => $this->notification->getTemplateId(),
                'template_variables' => $this->notification->getTemplateVariables(),
                'scheduled_at' => $this->notification->getScheduledAt()?->toISOString(),
                'expires_at' => $this->notification->getExpiresAt()?->toISOString(),
                'max_retries' => $this->notification->getMaxRetries(),
                'metadata' => $this->notification->getMetadata(),
                'created_at' => $this->notification->getCreatedAt()->toISOString(),
            ],
            'context' => [
                'aggregate_id' => $this->notification->getId(),
                'aggregate_type' => 'notification',
                'event_version' => 1,
            ]
        ];
    }

    /**
     * Event'in kritik olup olmadığını belirle
     */
    public function isCritical(): bool
    {
        return $this->notification->getPriority()->isCritical();
    }

    /**
     * Event'in anında işlenmesi gerekip gerekmediğini belirle
     */
    public function requiresImmediateProcessing(): bool
    {
        return $this->notification->getPriority()->requiresImmediateDelivery();
    }

    /**
     * Event kategorisini getir
     */
    public function getCategory(): string
    {
        return 'notification';
    }

    /**
     * Event alt kategorisini getir
     */
    public function getSubCategory(): string
    {
        return 'lifecycle';
    }

    /**
     * Event etiketlerini getir
     */
    public function getTags(): array
    {
        return [
            'notification',
            'created',
            $this->notification->getType()->getCategory(),
            $this->notification->getChannel()->getValue(),
            $this->notification->getPriority()->getValue(),
        ];
    }

    /**
     * Event'in serialize edilebilir verilerini getir
     */
    public function toArray(): array
    {
        return [
            'event_id' => $this->getEventId(),
            'event_name' => $this->getEventName(),
            'occurred_at' => $this->getOccurredAt()->toISOString(),
            'data' => $this->getEventData(),
            'payload' => $this->getPayload(),
            'is_critical' => $this->isCritical(),
            'requires_immediate_processing' => $this->requiresImmediateProcessing(),
            'category' => $this->getCategory(),
            'sub_category' => $this->getSubCategory(),
            'tags' => $this->getTags(),
        ];
    }
}
