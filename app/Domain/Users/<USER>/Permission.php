<?php

namespace App\Domain\Users\Entities;

use App\Domain\Users\ValueObjects\PermissionSlug;
use Carbon\Carbon;

class Permission
{
    private ?int $id;
    private string $name;
    private PermissionSlug $slug;
    private ?string $description;
    private ?string $group;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    public function __construct(
        string $name,
        PermissionSlug $slug,
        ?string $description = null,
        ?string $group = null
    ) {
        $this->name = $name;
        $this->slug = $slug;
        $this->description = $description;
        $this->group = $group;
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();
    }

    public static function create(
        string $name,
        PermissionSlug $slug,
        ?string $description = null,
        ?string $group = null
    ): self {
        return new self($name, $slug, $description, $group);
    }

    public function updateInfo(string $name, ?string $description = null, ?string $group = null): void
    {
        $this->name = $name;
        $this->description = $description;
        $this->group = $group;
        $this->updatedAt = Carbon::now();
    }

    public function equals(Permission $other): bool
    {
        return $this->slug->equals($other->slug);
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getName(): string { return $this->name; }
    public function getSlug(): PermissionSlug { return $this->slug; }
    public function getDescription(): ?string { return $this->description; }
    public function getGroup(): ?string { return $this->group; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
}
