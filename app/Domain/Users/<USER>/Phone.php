<?php

namespace App\Domain\Users\ValueObjects;

use InvalidArgumentException;

class Phone
{
    private string $value;
    private string $countryCode;
    private string $nationalNumber;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $this->normalize($value);
        $this->parseNumber();
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public static function fromParts(string $countryCode, string $nationalNumber): self
    {
        $fullNumber = $countryCode . $nationalNumber;
        return new self($fullNumber);
    }

    private function validate(string $value): void
    {
        $value = trim($value);
        
        if (empty($value)) {
            throw new InvalidArgumentException('Phone number cannot be empty');
        }

        // Remove all non-digit characters for validation
        $digitsOnly = preg_replace('/[^0-9]/', '', $value);
        
        if (empty($digitsOnly)) {
            throw new InvalidArgumentException('Phone number must contain digits');
        }

        if (strlen($digitsOnly) < 10) {
            throw new InvalidArgumentException('Phone number must be at least 10 digits');
        }

        if (strlen($digitsOnly) > 15) {
            throw new InvalidArgumentException('Phone number cannot be longer than 15 digits');
        }

        // Check for valid format patterns
        $validPatterns = [
            '/^\+?[1-9]\d{1,14}$/',           // International format
            '/^0[1-9]\d{8,9}$/',              // Turkish national format
            '/^\([0-9]{3}\)\s?[0-9]{3}-?[0-9]{4}$/', // US format
        ];

        $normalizedValue = $this->normalize($value);
        $isValid = false;
        
        foreach ($validPatterns as $pattern) {
            if (preg_match($pattern, $normalizedValue)) {
                $isValid = true;
                break;
            }
        }

        if (!$isValid) {
            throw new InvalidArgumentException('Invalid phone number format');
        }
    }

    private function normalize(string $value): string
    {
        // Remove all spaces, dashes, parentheses, and other formatting
        $normalized = preg_replace('/[\s\-\(\)\.]/', '', $value);
        
        // Handle Turkish numbers starting with 0
        if (str_starts_with($normalized, '0') && strlen($normalized) === 11) {
            $normalized = '+90' . substr($normalized, 1);
        }
        
        // Add + if missing for international numbers
        if (!str_starts_with($normalized, '+') && strlen($normalized) > 10) {
            $normalized = '+' . $normalized;
        }
        
        return $normalized;
    }

    private function parseNumber(): void
    {
        $number = $this->value;
        
        if (str_starts_with($number, '+')) {
            // International format
            if (str_starts_with($number, '+90')) {
                $this->countryCode = '+90';
                $this->nationalNumber = substr($number, 3);
            } elseif (str_starts_with($number, '+1')) {
                $this->countryCode = '+1';
                $this->nationalNumber = substr($number, 2);
            } else {
                // Try to extract country code (1-3 digits after +)
                preg_match('/^\+(\d{1,3})(\d+)$/', $number, $matches);
                if ($matches) {
                    $this->countryCode = '+' . $matches[1];
                    $this->nationalNumber = $matches[2];
                } else {
                    $this->countryCode = '';
                    $this->nationalNumber = substr($number, 1);
                }
            }
        } else {
            // National format
            $this->countryCode = '';
            $this->nationalNumber = $number;
        }
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function getNationalNumber(): string
    {
        return $this->nationalNumber;
    }

    public function getFormattedValue(): string
    {
        if ($this->countryCode === '+90') {
            // Turkish format: +90 (5XX) XXX XX XX
            $national = $this->nationalNumber;
            if (strlen($national) === 10) {
                return sprintf('+90 (%s) %s %s %s',
                    substr($national, 0, 3),
                    substr($national, 3, 3),
                    substr($national, 6, 2),
                    substr($national, 8, 2)
                );
            }
        } elseif ($this->countryCode === '+1') {
            // US format: +1 (XXX) XXX-XXXX
            $national = $this->nationalNumber;
            if (strlen($national) === 10) {
                return sprintf('+1 (%s) %s-%s',
                    substr($national, 0, 3),
                    substr($national, 3, 3),
                    substr($national, 6, 4)
                );
            }
        }
        
        // Default international format
        return $this->countryCode . ' ' . $this->nationalNumber;
    }

    public function isTurkish(): bool
    {
        return $this->countryCode === '+90';
    }

    public function isUS(): bool
    {
        return $this->countryCode === '+1';
    }

    public function isMobile(): bool
    {
        if ($this->isTurkish()) {
            // Turkish mobile numbers start with 5
            return str_starts_with($this->nationalNumber, '5');
        }
        
        // For other countries, assume all are mobile for now
        return true;
    }

    public function obfuscate(): string
    {
        $formatted = $this->getFormattedValue();
        $length = strlen($formatted);
        
        if ($length <= 4) {
            return str_repeat('*', $length);
        }
        
        // Show first 2 and last 2 characters
        return substr($formatted, 0, 2) . str_repeat('*', $length - 4) . substr($formatted, -2);
    }

    public function equals(Phone $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->getFormattedValue();
    }
}
