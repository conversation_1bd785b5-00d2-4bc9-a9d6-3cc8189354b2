<?php

namespace App\Domain\Users\ValueObjects;

use InvalidArgumentException;

class Email
{
    private string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = strtolower(trim($value));
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    private function validate(string $value): void
    {
        $value = trim($value);
        
        if (empty($value)) {
            throw new InvalidArgumentException('Email cannot be empty');
        }

        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Invalid email format');
        }

        if (strlen($value) > 254) {
            throw new InvalidArgumentException('Email cannot be longer than 254 characters');
        }

        // Check for common disposable email domains
        $disposableDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'throwaway.email'
        ];

        $domain = substr(strrchr($value, '@'), 1);
        if (in_array(strtolower($domain), $disposableDomains)) {
            throw new InvalidArgumentException('Disposable email addresses are not allowed');
        }

        // Check for valid domain format
        if (!$this->isValidDomain($domain)) {
            throw new InvalidArgumentException('Invalid email domain');
        }
    }

    private function isValidDomain(string $domain): bool
    {
        // Basic domain validation
        if (empty($domain)) {
            return false;
        }

        // Check for valid characters
        if (!preg_match('/^[a-zA-Z0-9.-]+$/', $domain)) {
            return false;
        }

        // Check for proper format
        if (strpos($domain, '..') !== false) {
            return false;
        }

        if (str_starts_with($domain, '.') || str_ends_with($domain, '.')) {
            return false;
        }

        return true;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getDomain(): string
    {
        return substr(strrchr($this->value, '@'), 1);
    }

    public function getLocalPart(): string
    {
        return substr($this->value, 0, strpos($this->value, '@'));
    }

    public function equals(Email $other): bool
    {
        return $this->value === $other->value;
    }

    public function isGmail(): bool
    {
        return $this->getDomain() === 'gmail.com';
    }

    public function isYahoo(): bool
    {
        return in_array($this->getDomain(), ['yahoo.com', 'yahoo.co.uk', 'yahoo.fr']);
    }

    public function isOutlook(): bool
    {
        return in_array($this->getDomain(), ['outlook.com', 'hotmail.com', 'live.com']);
    }

    public function isCorporate(): bool
    {
        $commonProviders = [
            'gmail.com', 'yahoo.com', 'yahoo.co.uk', 'yahoo.fr',
            'outlook.com', 'hotmail.com', 'live.com', 'aol.com'
        ];

        return !in_array($this->getDomain(), $commonProviders);
    }

    public function obfuscate(): string
    {
        $localPart = $this->getLocalPart();
        $domain = $this->getDomain();
        
        if (strlen($localPart) <= 2) {
            return $localPart[0] . '*@' . $domain;
        }
        
        return $localPart[0] . str_repeat('*', strlen($localPart) - 2) . $localPart[-1] . '@' . $domain;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
