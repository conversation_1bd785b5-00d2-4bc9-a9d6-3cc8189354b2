<?php

namespace App\Domain\Users\Events;

use App\Domain\Users\Entities\User;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class UserLoggedOut implements DomainEvent
{
    private User $user;
    private Carbon $occurredOn;

    public function __construct(User $user)
    {
        $this->user = $user;
        $this->occurredOn = Carbon::now();
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'user.logged_out';
    }

    public function getEventData(): array
    {
        return [
            'user_id' => $this->user->getId(),
            'name' => $this->user->getName(),
            'email' => $this->user->getEmail()->getValue(),
            'logged_out_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->user->getId();
    }

    public function getAggregateType(): string
    {
        return 'User';
    }
}
