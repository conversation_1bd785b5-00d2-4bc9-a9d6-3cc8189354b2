<?php

namespace App\Domain\Categories\Services;

use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategoryPath;
use App\Domain\Categories\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\Services\BaseDomainService;

/**
 * CategoryHierarchyService
 * Kategori hiyerarşisi business logic'ini yönetir
 */
class CategoryHierarchyService extends BaseDomainService
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    /**
     * Kategori ağacını oluştur
     */
    public function buildCategoryTree(?int $parentId = null, int $maxDepth = null): array
    {
        $categories = $this->categoryRepository->findByParentId($parentId);
        $tree = [];

        foreach ($categories as $category) {
            $node = [
                'category' => $category,
                'children' => []
            ];

            // Maksimum derinlik kontrolü
            if ($maxDepth === null || $category->getLevel() < $maxDepth) {
                $node['children'] = $this->buildCategoryTree($category->getId(), $maxDepth);
            }

            $tree[] = $node;
        }

        return $this->sortTreeByPosition($tree);
    }

    /**
     * Kategori yolunu hesapla
     */
    public function calculateCategoryPath(int $categoryId): CategoryPath
    {
        $category = $this->categoryRepository->findById($categoryId);
        
        if (!$category) {
            throw new \InvalidArgumentException("Kategori bulunamadı: {$categoryId}");
        }

        $path = [];
        $currentCategory = $category;

        // Root'a kadar çık
        while ($currentCategory) {
            array_unshift($path, $currentCategory->getId());
            
            if ($currentCategory->getParentId()) {
                $currentCategory = $this->categoryRepository->findById($currentCategory->getParentId());
            } else {
                break;
            }
        }

        return CategoryPath::fromArray($path);
    }

    /**
     * Kategori seviyesini hesapla
     */
    public function calculateCategoryLevel(int $categoryId): int
    {
        $path = $this->calculateCategoryPath($categoryId);
        return count($path->getPath()) - 1; // Root level = 0
    }

    /**
     * Ancestor kategorileri getir
     */
    public function getAncestors(int $categoryId, bool $includeRoot = true): array
    {
        $path = $this->calculateCategoryPath($categoryId);
        $ancestorIds = $path->getPath();
        
        // Kendisini çıkar
        array_pop($ancestorIds);
        
        if (!$includeRoot && !empty($ancestorIds)) {
            array_shift($ancestorIds); // Root'u çıkar
        }

        $ancestors = [];
        foreach ($ancestorIds as $ancestorId) {
            $ancestor = $this->categoryRepository->findById($ancestorId);
            if ($ancestor) {
                $ancestors[] = $ancestor;
            }
        }

        return $ancestors;
    }

    /**
     * Descendant kategorileri getir
     */
    public function getDescendants(int $categoryId, int $maxDepth = null): array
    {
        return $this->categoryRepository->findDescendants($categoryId, $maxDepth);
    }

    /**
     * Sibling kategorileri getir
     */
    public function getSiblings(int $categoryId, bool $includeSelf = false): array
    {
        $category = $this->categoryRepository->findById($categoryId);
        
        if (!$category) {
            return [];
        }

        $siblings = $this->categoryRepository->findByParentId($category->getParentId());
        
        if (!$includeSelf) {
            $siblings = array_filter($siblings, fn($sibling) => $sibling->getId() !== $categoryId);
        }

        return $siblings;
    }

    /**
     * Breadcrumb oluştur
     */
    public function generateBreadcrumb(int $categoryId): array
    {
        $ancestors = $this->getAncestors($categoryId, true);
        $category = $this->categoryRepository->findById($categoryId);
        
        if ($category) {
            $ancestors[] = $category;
        }

        return array_map(function ($cat) {
            return [
                'id' => $cat->getId(),
                'name' => $cat->getName(),
                'slug' => $cat->getSlug()->getValue(),
                'url' => $this->generateCategoryUrl($cat)
            ];
        }, $ancestors);
    }

    /**
     * Kategori URL'i oluştur
     */
    public function generateCategoryUrl(Category $category): string
    {
        $ancestors = $this->getAncestors($category->getId(), false);
        $slugs = array_map(fn($cat) => $cat->getSlug()->getValue(), $ancestors);
        $slugs[] = $category->getSlug()->getValue();

        return '/categories/' . implode('/', $slugs);
    }

    /**
     * Kategori derinliğini kontrol et
     */
    public function isWithinMaxDepth(int $categoryId, int $maxDepth = 10): bool
    {
        $level = $this->calculateCategoryLevel($categoryId);
        return $level <= $maxDepth;
    }

    /**
     * İki kategori arasındaki ilişkiyi kontrol et
     */
    public function getRelationship(int $categoryId1, int $categoryId2): string
    {
        if ($categoryId1 === $categoryId2) {
            return 'same';
        }

        $path1 = $this->calculateCategoryPath($categoryId1);
        $path2 = $this->calculateCategoryPath($categoryId2);

        // Ancestor-descendant kontrolü
        if ($path1->isAncestorOf($path2)) {
            return 'ancestor';
        }

        if ($path2->isAncestorOf($path1)) {
            return 'descendant';
        }

        // Sibling kontrolü
        $category1 = $this->categoryRepository->findById($categoryId1);
        $category2 = $this->categoryRepository->findById($categoryId2);

        if ($category1 && $category2 && $category1->getParentId() === $category2->getParentId()) {
            return 'sibling';
        }

        return 'unrelated';
    }

    /**
     * Kategori ağacını düzleştir
     */
    public function flattenTree(array $tree): array
    {
        $flattened = [];

        foreach ($tree as $node) {
            $flattened[] = $node['category'];
            
            if (!empty($node['children'])) {
                $childrenFlattened = $this->flattenTree($node['children']);
                $flattened = array_merge($flattened, $childrenFlattened);
            }
        }

        return $flattened;
    }

    /**
     * Kategori ağacını position'a göre sırala
     */
    private function sortTreeByPosition(array $tree): array
    {
        usort($tree, function ($a, $b) {
            return $a['category']->getPosition() <=> $b['category']->getPosition();
        });

        // Alt kategorileri de sırala
        foreach ($tree as &$node) {
            if (!empty($node['children'])) {
                $node['children'] = $this->sortTreeByPosition($node['children']);
            }
        }

        return $tree;
    }

    /**
     * Kategori ağacını yeniden düzenle
     */
    public function reorderCategories(array $categoryOrders): void
    {
        foreach ($categoryOrders as $categoryId => $newPosition) {
            $category = $this->categoryRepository->findById($categoryId);
            
            if ($category) {
                $category->updatePosition($newPosition);
                $this->categoryRepository->save($category);
            }
        }
    }

    /**
     * Boş kategorileri temizle
     */
    public function cleanupEmptyCategories(): array
    {
        $allCategories = $this->categoryRepository->findAll();
        $emptyCategories = [];

        foreach ($allCategories as $category) {
            if ($category->getProductCount() === 0 && !$category->hasChildren()) {
                $emptyCategories[] = $category;
            }
        }

        return $emptyCategories;
    }

    /**
     * Kategori istatistiklerini hesapla
     */
    public function calculateCategoryStats(int $categoryId): array
    {
        $category = $this->categoryRepository->findById($categoryId);
        
        if (!$category) {
            return [];
        }

        $descendants = $this->getDescendants($categoryId);
        $totalProducts = $category->getProductCount();
        
        // Alt kategorilerdeki ürünleri de say
        foreach ($descendants as $descendant) {
            $totalProducts += $descendant->getProductCount();
        }

        return [
            'direct_products' => $category->getProductCount(),
            'total_products' => $totalProducts,
            'subcategory_count' => count($this->categoryRepository->findByParentId($categoryId)),
            'total_descendants' => count($descendants),
            'level' => $category->getLevel(),
            'max_descendant_level' => $this->getMaxDescendantLevel($categoryId),
        ];
    }

    /**
     * En derin descendant seviyesini getir
     */
    private function getMaxDescendantLevel(int $categoryId): int
    {
        $descendants = $this->getDescendants($categoryId);
        
        if (empty($descendants)) {
            return $this->calculateCategoryLevel($categoryId);
        }

        $maxLevel = 0;
        foreach ($descendants as $descendant) {
            $maxLevel = max($maxLevel, $descendant->getLevel());
        }

        return $maxLevel;
    }

    /**
     * Kategori ağacını JSON formatında export et
     */
    public function exportTreeAsJson(?int $parentId = null, int $maxDepth = null): string
    {
        $tree = $this->buildCategoryTree($parentId, $maxDepth);
        
        $exportData = $this->convertTreeForExport($tree);
        
        return json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Export için ağacı dönüştür
     */
    private function convertTreeForExport(array $tree): array
    {
        $exported = [];

        foreach ($tree as $node) {
            $category = $node['category'];
            
            $exportedNode = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'slug' => $category->getSlug()->getValue(),
                'description' => $category->getDescription(),
                'level' => $category->getLevel(),
                'position' => $category->getPosition(),
                'status' => $category->getStatus(),
                'featured' => $category->isFeatured(),
                'show_in_menu' => $category->isVisibleInMenu(),
                'product_count' => $category->getProductCount(),
                'children' => []
            ];

            if (!empty($node['children'])) {
                $exportedNode['children'] = $this->convertTreeForExport($node['children']);
            }

            $exported[] = $exportedNode;
        }

        return $exported;
    }
}
