<?php

namespace App\Domain\Categories\Services;

use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\Entities\CategoryAttribute;
use App\Domain\Categories\ValueObjects\CategorySlug;
use App\Domain\Categories\ValueObjects\CategoryPath;
use App\Domain\Categories\ValueObjects\SEOData;
use App\Domain\Categories\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\Services\BaseDomainService;
use App\Domain\Categories\Exceptions\CategoryNotFoundException;

/**
 * CategoryDomainService
 * Kategori domain business logic'ini yönetir
 */
class CategoryDomainService extends BaseDomainService
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    /**
     * Kategori oluştur
     */
    public function createCategory(
        string $name,
        CategorySlug $slug,
        ?int $parentId = null,
        ?string $description = null,
        int $position = 0,
        bool $status = true,
        bool $featured = false,
        bool $showInMenu = true
    ): Category {
        // Slug benzersizlik kontrolü
        $this->ensureUniqueSlug($slug);
        
        // Parent kategori kontrolü
        if ($parentId) {
            $this->ensureParentExists($parentId);
            $this->ensureMaxDepthNotExceeded($parentId);
        }

        // Position hesapla
        if ($position === 0) {
            $position = $this->calculateNextPosition($parentId);
        }

        $category = Category::create(
            $name,
            $slug,
            $parentId,
            $description,
            $position,
            $status,
            $featured,
            $showInMenu
        );

        // SEO verilerini otomatik oluştur
        if ($description) {
            $parentCategory = $parentId ? $this->categoryRepository->findById($parentId) : null;
            $seoData = SEOData::fromCategory(
                $name,
                $description,
                null,
                $parentCategory?->getName()
            );
            $category->setSEOData($seoData);
        }

        return $category;
    }

    /**
     * Kategori güncelle
     */
    public function updateCategory(
        Category $category,
        array $updateData
    ): Category {
        // Slug değişiyorsa benzersizlik kontrolü
        if (isset($updateData['slug'])) {
            $newSlug = is_string($updateData['slug']) ? 
                CategorySlug::fromString($updateData['slug']) : 
                $updateData['slug'];
            
            if (!$category->getSlug()->equals($newSlug)) {
                $this->ensureUniqueSlug($newSlug, $category->getId());
            }
        }

        // Temel bilgileri güncelle
        if (isset($updateData['name']) || isset($updateData['slug']) || isset($updateData['description'])) {
            $category->updateBasicInfo(
                $updateData['name'] ?? $category->getName(),
                isset($updateData['slug']) ? 
                    (is_string($updateData['slug']) ? CategorySlug::fromString($updateData['slug']) : $updateData['slug']) :
                    $category->getSlug(),
                $updateData['description'] ?? $category->getDescription()
            );
        }

        // Position güncelle
        if (isset($updateData['position'])) {
            $category->updatePosition($updateData['position']);
        }

        // Durum güncelle
        if (isset($updateData['status'])) {
            if ($updateData['status']) {
                $category->activate();
            } else {
                $category->deactivate();
            }
        }

        // Featured durumu
        if (isset($updateData['featured'])) {
            $category->setFeatured($updateData['featured']);
        }

        // Menu görünürlüğü
        if (isset($updateData['show_in_menu'])) {
            $category->setMenuVisibility($updateData['show_in_menu']);
        }

        // Resim güncelle
        if (isset($updateData['image'])) {
            $category->setImage($updateData['image']);
        }

        // İkon güncelle
        if (isset($updateData['icon'])) {
            $category->setIcon($updateData['icon']);
        }

        // SEO verileri güncelle
        if (isset($updateData['seo'])) {
            $seoData = SEOData::create(
                $updateData['seo']['meta_title'] ?? null,
                $updateData['seo']['meta_description'] ?? null,
                $updateData['seo']['meta_keywords'] ?? null,
                $updateData['seo']['og_title'] ?? null,
                $updateData['seo']['og_description'] ?? null,
                $updateData['seo']['og_image'] ?? null
            );
            $category->setSEOData($seoData);
        }

        return $category;
    }

    /**
     * Kategori sil
     */
    public function deleteCategory(Category $category): void
    {
        // Alt kategorileri kontrol et
        if ($category->hasChildren()) {
            throw new \DomainException('Alt kategorileri olan kategori silinemez. Önce alt kategorileri silin veya taşıyın.');
        }

        // Ürünleri kontrol et
        if ($category->getProductCount() > 0) {
            throw new \DomainException('Ürünleri olan kategori silinemez. Önce ürünleri başka kategoriye taşıyın.');
        }

        $category->delete();
    }

    /**
     * Kategori taşı
     */
    public function moveCategory(Category $category, ?int $newParentId, int $newPosition = 0): Category
    {
        // Kendisini kendi alt kategorisine taşımaya çalışıyor mu kontrol et
        if ($newParentId && $this->isDescendantOf($newParentId, $category->getId())) {
            throw new \DomainException('Kategori kendi alt kategorisine taşınamaz.');
        }

        // Parent kategori kontrolü
        if ($newParentId) {
            $this->ensureParentExists($newParentId);
            $this->ensureMaxDepthNotExceeded($newParentId);
        }

        // Position hesapla
        if ($newPosition === 0) {
            $newPosition = $this->calculateNextPosition($newParentId);
        }

        $category->moveToParent($newParentId);
        $category->updatePosition($newPosition);

        return $category;
    }

    /**
     * Kategori ürün sayısını güncelle
     */
    public function updateProductCount(Category $category): void
    {
        $productCount = $this->calculateProductCount($category->getId());
        $category->updateProductCount($productCount);
    }

    /**
     * Kategori attribute'u ekle
     */
    public function addAttribute(Category $category, CategoryAttribute $attribute): void
    {
        $category->addAttribute($attribute);
    }

    /**
     * Slug benzersizlik kontrolü
     */
    private function ensureUniqueSlug(CategorySlug $slug, ?int $excludeCategoryId = null): void
    {
        $existingCategory = $this->categoryRepository->findBySlug($slug);
        
        if ($existingCategory && ($excludeCategoryId === null || $existingCategory->getId() !== $excludeCategoryId)) {
            throw new \DomainException("Slug '{$slug->getValue()}' zaten kullanımda.");
        }
    }

    /**
     * Parent kategori varlık kontrolü
     */
    private function ensureParentExists(int $parentId): void
    {
        $parentCategory = $this->categoryRepository->findById($parentId);
        
        if (!$parentCategory) {
            throw new CategoryNotFoundException("Parent kategori bulunamadı: {$parentId}");
        }

        if (!$parentCategory->getStatus()) {
            throw new \DomainException("Pasif kategori parent olarak seçilemez.");
        }
    }

    /**
     * Maksimum derinlik kontrolü
     */
    private function ensureMaxDepthNotExceeded(int $parentId): void
    {
        $parentCategory = $this->categoryRepository->findById($parentId);
        
        if ($parentCategory && $parentCategory->getLevel() >= 9) { // Max 10 level
            throw new \DomainException("Maksimum kategori derinliği aşıldı.");
        }
    }

    /**
     * Sonraki position hesapla
     */
    private function calculateNextPosition(?int $parentId): int
    {
        $siblings = $this->categoryRepository->findByParentId($parentId);
        
        if (empty($siblings)) {
            return 1;
        }

        $maxPosition = max(array_map(fn($category) => $category->getPosition(), $siblings));
        return $maxPosition + 1;
    }

    /**
     * Kategori descendant kontrolü
     */
    private function isDescendantOf(int $potentialAncestorId, int $categoryId): bool
    {
        $descendants = $this->categoryRepository->findDescendants($categoryId);
        
        foreach ($descendants as $descendant) {
            if ($descendant->getId() === $potentialAncestorId) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Ürün sayısını hesapla
     */
    private function calculateProductCount(int $categoryId): int
    {
        // Bu metod Infrastructure layer'da implement edilecek
        // Şimdilik 0 döndürüyoruz
        return 0;
    }
}
