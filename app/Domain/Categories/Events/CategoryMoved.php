<?php

namespace App\Domain\Categories\Events;

use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategoryPath;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class CategoryMoved implements DomainEvent
{
    private Category $category;
    private ?int $oldParentId;
    private ?int $newParentId;
    private CategoryPath $oldPath;
    private CategoryPath $newPath;
    private Carbon $occurredOn;

    public function __construct(
        Category $category,
        ?int $oldParentId,
        ?int $newParentId,
        CategoryPath $oldPath,
        CategoryPath $newPath
    ) {
        $this->category = $category;
        $this->oldParentId = $oldParentId;
        $this->newParentId = $newParentId;
        $this->oldPath = $oldPath;
        $this->newPath = $newPath;
        $this->occurredOn = Carbon::now();
    }

    public function getCategory(): Category
    {
        return $this->category;
    }

    public function getOldParentId(): ?int
    {
        return $this->oldParentId;
    }

    public function getNewParentId(): ?int
    {
        return $this->newParentId;
    }

    public function getOldPath(): CategoryPath
    {
        return $this->oldPath;
    }

    public function getNewPath(): CategoryPath
    {
        return $this->newPath;
    }

    public function isMovedToRoot(): bool
    {
        return $this->newParentId === null;
    }

    public function isMovedFromRoot(): bool
    {
        return $this->oldParentId === null;
    }

    public function getLevelChange(): int
    {
        return $this->newPath->getDepth() - $this->oldPath->getDepth();
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'category.moved';
    }

    public function getEventData(): array
    {
        return [
            'category_id' => $this->category->getId(),
            'category_name' => $this->category->getName(),
            'old_parent_id' => $this->oldParentId,
            'new_parent_id' => $this->newParentId,
            'old_path' => $this->oldPath->getPathString(),
            'new_path' => $this->newPath->getPathString(),
            'old_level' => $this->oldPath->getDepth(),
            'new_level' => $this->newPath->getDepth(),
            'level_change' => $this->getLevelChange(),
            'moved_to_root' => $this->isMovedToRoot(),
            'moved_from_root' => $this->isMovedFromRoot(),
            'moved_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->category->getId();
    }

    public function getAggregateType(): string
    {
        return 'Category';
    }
}
