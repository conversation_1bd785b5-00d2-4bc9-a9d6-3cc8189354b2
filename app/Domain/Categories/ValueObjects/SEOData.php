<?php

namespace App\Domain\Categories\ValueObjects;

use InvalidArgumentException;

class SEOData
{
    private string $metaTitle;
    private string $metaDescription;
    private ?string $metaKeywords;
    private ?string $ogTitle;
    private ?string $ogDescription;
    private ?string $ogImage;
    private ?string $canonicalUrl;
    private array $structuredData;
    private bool $noIndex;
    private bool $noFollow;

    public function __construct(
        string $metaTitle,
        string $metaDescription,
        ?string $metaKeywords = null,
        ?string $ogTitle = null,
        ?string $ogDescription = null,
        ?string $ogImage = null,
        ?string $canonicalUrl = null,
        array $structuredData = [],
        bool $noIndex = false,
        bool $noFollow = false
    ) {
        $this->validate($metaTitle, $metaDescription);
        
        $this->metaTitle = trim($metaTitle);
        $this->metaDescription = trim($metaDescription);
        $this->metaKeywords = $metaKeywords ? trim($metaKeywords) : null;
        $this->ogTitle = $ogTitle ? trim($ogTitle) : $this->metaTitle;
        $this->ogDescription = $ogDescription ? trim($ogDescription) : $this->metaDescription;
        $this->ogImage = $ogImage;
        $this->canonicalUrl = $canonicalUrl;
        $this->structuredData = $structuredData;
        $this->noIndex = $noIndex;
        $this->noFollow = $noFollow;
    }

    public static function create(string $metaTitle, string $metaDescription): self
    {
        return new self($metaTitle, $metaDescription);
    }

    public static function fromCategory(
        string $categoryName,
        string $categoryDescription,
        ?string $categoryImage = null,
        ?string $parentCategoryName = null
    ): self {
        $metaTitle = self::generateCategoryMetaTitle($categoryName, $parentCategoryName);
        $metaDescription = self::generateCategoryMetaDescription($categoryDescription, $categoryName);
        
        $structuredData = self::generateCategoryStructuredData(
            $categoryName,
            $categoryDescription,
            $categoryImage,
            $parentCategoryName
        );

        return new self(
            metaTitle: $metaTitle,
            metaDescription: $metaDescription,
            ogTitle: $metaTitle,
            ogDescription: $metaDescription,
            ogImage: $categoryImage,
            structuredData: $structuredData
        );
    }

    private function validate(string $metaTitle, string $metaDescription): void
    {
        if (empty(trim($metaTitle))) {
            throw new InvalidArgumentException('Meta title cannot be empty');
        }

        if (strlen($metaTitle) > 60) {
            throw new InvalidArgumentException('Meta title should not exceed 60 characters for optimal SEO');
        }

        if (empty(trim($metaDescription))) {
            throw new InvalidArgumentException('Meta description cannot be empty');
        }

        if (strlen($metaDescription) > 160) {
            throw new InvalidArgumentException('Meta description should not exceed 160 characters for optimal SEO');
        }
    }

    public function updateMetaTitle(string $metaTitle): SEOData
    {
        return new self(
            $metaTitle,
            $this->metaDescription,
            $this->metaKeywords,
            $this->ogTitle,
            $this->ogDescription,
            $this->ogImage,
            $this->canonicalUrl,
            $this->structuredData,
            $this->noIndex,
            $this->noFollow
        );
    }

    public function updateMetaDescription(string $metaDescription): SEOData
    {
        return new self(
            $this->metaTitle,
            $metaDescription,
            $this->metaKeywords,
            $this->ogTitle,
            $this->ogDescription,
            $this->ogImage,
            $this->canonicalUrl,
            $this->structuredData,
            $this->noIndex,
            $this->noFollow
        );
    }

    public function setOpenGraphData(string $ogTitle, string $ogDescription, ?string $ogImage = null): SEOData
    {
        return new self(
            $this->metaTitle,
            $this->metaDescription,
            $this->metaKeywords,
            $ogTitle,
            $ogDescription,
            $ogImage,
            $this->canonicalUrl,
            $this->structuredData,
            $this->noIndex,
            $this->noFollow
        );
    }

    public function setCanonicalUrl(string $canonicalUrl): SEOData
    {
        return new self(
            $this->metaTitle,
            $this->metaDescription,
            $this->metaKeywords,
            $this->ogTitle,
            $this->ogDescription,
            $this->ogImage,
            $canonicalUrl,
            $this->structuredData,
            $this->noIndex,
            $this->noFollow
        );
    }

    public function setStructuredData(array $structuredData): SEOData
    {
        return new self(
            $this->metaTitle,
            $this->metaDescription,
            $this->metaKeywords,
            $this->ogTitle,
            $this->ogDescription,
            $this->ogImage,
            $this->canonicalUrl,
            $structuredData,
            $this->noIndex,
            $this->noFollow
        );
    }

    public function setRobotsMeta(bool $noIndex, bool $noFollow): SEOData
    {
        return new self(
            $this->metaTitle,
            $this->metaDescription,
            $this->metaKeywords,
            $this->ogTitle,
            $this->ogDescription,
            $this->ogImage,
            $this->canonicalUrl,
            $this->structuredData,
            $noIndex,
            $noFollow
        );
    }

    public function getRobotsContent(): string
    {
        $robots = [];
        
        if ($this->noIndex) {
            $robots[] = 'noindex';
        } else {
            $robots[] = 'index';
        }
        
        if ($this->noFollow) {
            $robots[] = 'nofollow';
        } else {
            $robots[] = 'follow';
        }
        
        return implode(', ', $robots);
    }

    public function getStructuredDataJson(): string
    {
        if (empty($this->structuredData)) {
            return '';
        }
        
        return json_encode($this->structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    private static function generateCategoryMetaTitle(string $categoryName, ?string $parentCategoryName = null): string
    {
        $title = $categoryName;
        
        if ($parentCategoryName) {
            $title = "{$categoryName} - {$parentCategoryName}";
        }
        
        // Truncate if too long
        if (strlen($title) > 60) {
            $title = substr($title, 0, 57) . '...';
        }
        
        return $title;
    }

    private static function generateCategoryMetaDescription(string $description, string $categoryName): string
    {
        if (empty($description)) {
            $metaDescription = "{$categoryName} kategorisindeki ürünleri keşfedin.";
        } else {
            $metaDescription = substr(strip_tags($description), 0, 140);
        }
        
        // Truncate if too long
        if (strlen($metaDescription) > 160) {
            $metaDescription = substr($metaDescription, 0, 157) . '...';
        }
        
        return $metaDescription;
    }

    private static function generateCategoryStructuredData(
        string $categoryName,
        string $categoryDescription,
        ?string $categoryImage = null,
        ?string $parentCategoryName = null
    ): array {
        $structuredData = [
            '@context' => 'https://schema.org/',
            '@type' => 'ProductCategory',
            'name' => $categoryName,
            'description' => strip_tags($categoryDescription),
        ];

        if ($categoryImage) {
            $structuredData['image'] = $categoryImage;
        }

        if ($parentCategoryName) {
            $structuredData['parentCategory'] = [
                '@type' => 'ProductCategory',
                'name' => $parentCategoryName
            ];
        }

        return $structuredData;
    }

    // Getters
    public function getMetaTitle(): string { return $this->metaTitle; }
    public function getMetaDescription(): string { return $this->metaDescription; }
    public function getMetaKeywords(): ?string { return $this->metaKeywords; }
    public function getOgTitle(): ?string { return $this->ogTitle; }
    public function getOgDescription(): ?string { return $this->ogDescription; }
    public function getOgImage(): ?string { return $this->ogImage; }
    public function getCanonicalUrl(): ?string { return $this->canonicalUrl; }
    public function getStructuredData(): array { return $this->structuredData; }
    public function isNoIndex(): bool { return $this->noIndex; }
    public function isNoFollow(): bool { return $this->noFollow; }

    public function toArray(): array
    {
        return [
            'meta_title' => $this->metaTitle,
            'meta_description' => $this->metaDescription,
            'meta_keywords' => $this->metaKeywords,
            'og_title' => $this->ogTitle,
            'og_description' => $this->ogDescription,
            'og_image' => $this->ogImage,
            'canonical_url' => $this->canonicalUrl,
            'structured_data' => $this->structuredData,
            'robots_content' => $this->getRobotsContent(),
            'no_index' => $this->noIndex,
            'no_follow' => $this->noFollow,
        ];
    }

    public function equals(SEOData $other): bool
    {
        return $this->metaTitle === $other->metaTitle &&
               $this->metaDescription === $other->metaDescription &&
               $this->metaKeywords === $other->metaKeywords &&
               $this->ogTitle === $other->ogTitle &&
               $this->ogDescription === $other->ogDescription &&
               $this->ogImage === $other->ogImage &&
               $this->canonicalUrl === $other->canonicalUrl &&
               $this->structuredData === $other->structuredData &&
               $this->noIndex === $other->noIndex &&
               $this->noFollow === $other->noFollow;
    }
}
