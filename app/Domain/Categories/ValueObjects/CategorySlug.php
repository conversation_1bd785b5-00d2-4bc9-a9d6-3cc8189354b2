<?php

namespace App\Domain\Categories\ValueObjects;

use InvalidArgumentException;

class CategorySlug
{
    private string $value;

    public function __construct(string $value)
    {
        $this->validate($value);
        $this->value = $this->normalize($value);
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public static function generate(string $name): self
    {
        $slug = self::slugify($name);
        return new self($slug);
    }

    private function validate(string $value): void
    {
        $value = trim($value);
        
        if (empty($value)) {
            throw new InvalidArgumentException('Category slug cannot be empty');
        }

        if (strlen($value) < 2) {
            throw new InvalidArgumentException('Category slug must be at least 2 characters long');
        }

        if (strlen($value) > 100) {
            throw new InvalidArgumentException('Category slug cannot be longer than 100 characters');
        }

        // Slug formatını kontrol et
        if (!preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*$/', $value)) {
            throw new InvalidArgumentException('Category slug can only contain lowercase letters, numbers and hyphens');
        }

        // Başlangıç ve bitiş kontrolü
        if (str_starts_with($value, '-') || str_ends_with($value, '-')) {
            throw new InvalidArgumentException('Category slug cannot start or end with hyphen');
        }

        // Çift tire kontrolü
        if (str_contains($value, '--')) {
            throw new InvalidArgumentException('Category slug cannot contain consecutive hyphens');
        }

        // Rezerve edilmiş kelimeler
        $reserved = ['admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'root', 'null', 'undefined'];
        if (in_array($value, $reserved)) {
            throw new InvalidArgumentException("Category slug '{$value}' is reserved");
        }
    }

    private function normalize(string $value): string
    {
        return strtolower(trim($value));
    }

    private static function slugify(string $text): string
    {
        // Türkçe karakterleri dönüştür
        $turkishChars = [
            'ç' => 'c', 'Ç' => 'c',
            'ğ' => 'g', 'Ğ' => 'g',
            'ı' => 'i', 'I' => 'i',
            'İ' => 'i', 'i' => 'i',
            'ö' => 'o', 'Ö' => 'o',
            'ş' => 's', 'Ş' => 's',
            'ü' => 'u', 'Ü' => 'u',
        ];

        $text = strtr($text, $turkishChars);

        // Küçük harfe çevir
        $text = strtolower($text);

        // Özel karakterleri kaldır ve boşlukları tire ile değiştir
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
        $text = preg_replace('/[\s-]+/', '-', $text);

        // Başlangıç ve bitişteki tireleri kaldır
        $text = trim($text, '-');

        return $text;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function equals(CategorySlug $other): bool
    {
        return $this->value === $other->value;
    }

    public function withSuffix(string $suffix): CategorySlug
    {
        return new self($this->value . '-' . $suffix);
    }

    public function withPrefix(string $prefix): CategorySlug
    {
        return new self($prefix . '-' . $this->value);
    }

    public function getLength(): int
    {
        return strlen($this->value);
    }

    public function contains(string $substring): bool
    {
        return str_contains($this->value, $substring);
    }

    public function startsWith(string $prefix): bool
    {
        return str_starts_with($this->value, $prefix);
    }

    public function endsWith(string $suffix): bool
    {
        return str_ends_with($this->value, $suffix);
    }

    public function toUrl(): string
    {
        return '/' . $this->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
