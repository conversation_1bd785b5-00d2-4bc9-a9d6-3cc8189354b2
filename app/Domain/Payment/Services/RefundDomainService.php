<?php

namespace App\Domain\Payment\Services;

use App\Domain\Payment\Entities\Payment;
use App\Domain\Payment\Entities\Refund;
use App\Domain\Payment\ValueObjects\RefundAmount;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Domain\Payment\ValueObjects\PaymentStatus;
use App\Domain\Payment\ValueObjects\PaymentMetadata;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Shared\Events\DomainEventDispatcher;
use Carbon\Carbon;

/**
 * RefundDomainService
 * İade işlemleri domain business logic'ini yönetir
 */
class RefundDomainService
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private PaymentValidationService $validationService,
        private PaymentCalculationService $calculationService,
        private DomainEventDispatcher $eventDispatcher
    ) {}

    /**
     * Tam iade işlemi
     */
    public function processFullRefund(
        Payment $payment,
        string $reason,
        ?int $refundedByUserId = null,
        array $additionalMetadata = []
    ): RefundAmount {
        // Validate refund eligibility
        $this->validateRefundEligibility($payment);

        // Create full refund amount
        $originalAmount = PaymentAmount::create(
            $payment->getAmount()->getAmount(),
            $payment->getAmount()->getFeeAmount()
        );
        
        $refundAmount = RefundAmount::createFull($originalAmount);

        // Validate refund amount
        $this->validationService->validateRefundAmount($payment, $refundAmount);

        // Process refund
        $this->executeRefund($payment, $refundAmount, $reason, $refundedByUserId, $additionalMetadata);

        // Update payment status
        $payment->refund($reason);

        return $refundAmount;
    }

    /**
     * Kısmi iade işlemi
     */
    public function processPartialRefund(
        Payment $payment,
        Money $refundAmount,
        string $reason,
        bool $refundFeeProportionally = true,
        ?int $refundedByUserId = null,
        array $additionalMetadata = []
    ): RefundAmount {
        // Validate refund eligibility
        $this->validateRefundEligibility($payment);

        // Create partial refund amount
        $originalAmount = PaymentAmount::create(
            $payment->getAmount()->getAmount(),
            $payment->getAmount()->getFeeAmount()
        );
        
        $refund = RefundAmount::createPartial($refundAmount, $originalAmount, $refundFeeProportionally);

        // Validate refund amount
        $this->validationService->validateRefundAmount($payment, $refund);

        // Check if this would exceed total refunded amount
        $this->validateTotalRefundLimit($payment, $refund);

        // Process refund
        $this->executeRefund($payment, $refund, $reason, $refundedByUserId, $additionalMetadata);

        // Update payment status
        $totalRefunded = $this->calculateTotalRefundedAmount($payment, $refund);
        $originalTotal = $payment->getAmount()->getAmount()->add($payment->getAmount()->getFeeAmount());
        
        if ($totalRefunded->equals($originalTotal)) {
            $payment->refund($reason);
        } else {
            $payment->partialRefund($reason);
        }

        return $refund;
    }

    /**
     * Yüzde bazlı iade işlemi
     */
    public function processPercentageRefund(
        Payment $payment,
        float $percentage,
        string $reason,
        bool $refundFeeProportionally = true,
        ?int $refundedByUserId = null,
        array $additionalMetadata = []
    ): RefundAmount {
        // Validate percentage
        if ($percentage <= 0 || $percentage > 100) {
            throw new \DomainException('Refund percentage must be between 0 and 100');
        }

        // Validate refund eligibility
        $this->validateRefundEligibility($payment);

        // Create percentage-based refund
        $originalAmount = PaymentAmount::create(
            $payment->getAmount()->getAmount(),
            $payment->getAmount()->getFeeAmount()
        );
        
        $refund = RefundAmount::createByPercentage($percentage, $originalAmount, $refundFeeProportionally);

        // Validate refund amount
        $this->validationService->validateRefundAmount($payment, $refund);

        // Check if this would exceed total refunded amount
        $this->validateTotalRefundLimit($payment, $refund);

        // Process refund
        $this->executeRefund($payment, $refund, $reason, $refundedByUserId, $additionalMetadata);

        // Update payment status
        if ($percentage >= 100) {
            $payment->refund($reason);
        } else {
            $payment->partialRefund($reason);
        }

        return $refund;
    }

    /**
     * Sadece komisyon iadesi
     */
    public function processFeeOnlyRefund(
        Payment $payment,
        string $reason,
        ?int $refundedByUserId = null,
        array $additionalMetadata = []
    ): RefundAmount {
        // Validate refund eligibility
        $this->validateRefundEligibility($payment);

        // Check if payment has fee
        if ($payment->getAmount()->getFeeAmount()->isZero()) {
            throw new \DomainException('Payment has no fee to refund');
        }

        // Create fee-only refund
        $originalAmount = PaymentAmount::create(
            $payment->getAmount()->getAmount(),
            $payment->getAmount()->getFeeAmount()
        );
        
        $refund = RefundAmount::createFeeOnly($originalAmount);

        // Validate refund amount
        $this->validationService->validateRefundAmount($payment, $refund);

        // Process refund
        $this->executeRefund($payment, $refund, $reason, $refundedByUserId, $additionalMetadata);

        // Update payment status (partial refund since only fee is refunded)
        $payment->partialRefund($reason);

        return $refund;
    }

    /**
     * Sadece tutar iadesi (komisyon hariç)
     */
    public function processAmountOnlyRefund(
        Payment $payment,
        Money $refundAmount,
        string $reason,
        ?int $refundedByUserId = null,
        array $additionalMetadata = []
    ): RefundAmount {
        // Validate refund eligibility
        $this->validateRefundEligibility($payment);

        // Create amount-only refund
        $originalAmount = PaymentAmount::create(
            $payment->getAmount()->getAmount(),
            $payment->getAmount()->getFeeAmount()
        );
        
        $refund = RefundAmount::createAmountOnly($refundAmount, $originalAmount);

        // Validate refund amount
        $this->validationService->validateRefundAmount($payment, $refund);

        // Check if this would exceed total refunded amount
        $this->validateTotalRefundLimit($payment, $refund);

        // Process refund
        $this->executeRefund($payment, $refund, $reason, $refundedByUserId, $additionalMetadata);

        // Update payment status
        if ($refundAmount->equals($payment->getAmount()->getAmount())) {
            $payment->partialRefund($reason); // Still partial because fee is not refunded
        } else {
            $payment->partialRefund($reason);
        }

        return $refund;
    }

    /**
     * İade işlemini iptal et
     */
    public function cancelRefund(
        Payment $payment,
        string $refundId,
        string $reason,
        ?int $cancelledByUserId = null
    ): void {
        // Find refund in payment metadata
        $metadata = PaymentMetadata::fromArray($payment->getMetadata());
        $refunds = $metadata->get('refunds', []);
        
        $refundIndex = null;
        foreach ($refunds as $index => $refund) {
            if ($refund['id'] === $refundId) {
                $refundIndex = $index;
                break;
            }
        }

        if ($refundIndex === null) {
            throw new \DomainException("Refund not found: {$refundId}");
        }

        $refund = $refunds[$refundIndex];
        
        // Check if refund can be cancelled
        if ($refund['status'] !== 'pending') {
            throw new \DomainException("Only pending refunds can be cancelled. Current status: {$refund['status']}");
        }

        // Update refund status
        $refunds[$refundIndex]['status'] = 'cancelled';
        $refunds[$refundIndex]['cancelled_at'] = Carbon::now()->toISOString();
        $refunds[$refundIndex]['cancelled_by_user_id'] = $cancelledByUserId;
        $refunds[$refundIndex]['cancellation_reason'] = $reason;

        // Update payment metadata
        $updatedMetadata = $metadata->with('refunds', $refunds);
        $payment->updateMetadata($updatedMetadata->getData());

        // Recalculate payment status based on remaining refunds
        $this->recalculatePaymentStatusAfterRefundCancellation($payment);
    }

    /**
     * İade geçmişini getir
     */
    public function getRefundHistory(Payment $payment): array
    {
        $metadata = PaymentMetadata::fromArray($payment->getMetadata());
        return $metadata->get('refunds', []);
    }

    /**
     * Toplam iade edilen tutarı hesapla
     */
    public function getTotalRefundedAmount(Payment $payment): Money
    {
        $refunds = $this->getRefundHistory($payment);
        $totalAmount = 0;
        $currency = $payment->getAmount()->getCurrency();

        foreach ($refunds as $refund) {
            if ($refund['status'] === 'completed') {
                $totalAmount += $refund['net_refund_amount'];
            }
        }

        return Money::fromAmount($totalAmount / 100, $currency); // Convert from minor units
    }

    /**
     * Kalan iade edilebilir tutarı hesapla
     */
    public function getRemainingRefundableAmount(Payment $payment): Money
    {
        $originalAmount = $payment->getAmount()->getAmount()->add($payment->getAmount()->getFeeAmount());
        $totalRefunded = $this->getTotalRefundedAmount($payment);
        
        return $originalAmount->subtract($totalRefunded);
    }

    /**
     * İade uygunluğunu kontrol et
     */
    private function validateRefundEligibility(Payment $payment): void
    {
        // Payment must be completed
        if (!$payment->isCompleted()) {
            throw new \DomainException('Only completed payments can be refunded');
        }

        // Check if payment is already fully refunded
        if ($payment->isRefunded()) {
            throw new \DomainException('Payment is already fully refunded');
        }

        // Check refund time limit (e.g., 30 days)
        $refundTimeLimit = config('payment.refund_time_limit_days', 30);
        $completedAt = $payment->getCompletedAt();
        
        if ($completedAt && Carbon::now()->diffInDays($completedAt) > $refundTimeLimit) {
            throw new \DomainException("Refund time limit exceeded. Limit: {$refundTimeLimit} days");
        }

        // Check if gateway supports refunds
        if (!$payment->getGateway()->supportsRefund()) {
            throw new \DomainException('Payment gateway does not support refunds');
        }
    }

    /**
     * Toplam iade limitini kontrol et
     */
    private function validateTotalRefundLimit(Payment $payment, RefundAmount $newRefund): void
    {
        $currentTotalRefunded = $this->getTotalRefundedAmount($payment);
        $newRefundTotal = $newRefund->getNetRefundAmount();
        $proposedTotal = $currentTotalRefunded->add($newRefundTotal);
        
        $originalTotal = $payment->getAmount()->getAmount()->add($payment->getAmount()->getFeeAmount());
        
        if ($proposedTotal->isGreaterThan($originalTotal)) {
            throw new \DomainException(
                'Total refund amount would exceed original payment amount'
            );
        }
    }

    /**
     * İade işlemini gerçekleştir
     */
    private function executeRefund(
        Payment $payment,
        RefundAmount $refundAmount,
        string $reason,
        ?int $refundedByUserId,
        array $additionalMetadata
    ): void {
        // Generate refund ID
        $refundId = 'ref_' . uniqid() . '_' . time();

        // Create refund record
        $refundData = [
            'id' => $refundId,
            'amount' => $refundAmount->getRefundAmount()->getAmount(),
            'fee_amount' => $refundAmount->getRefundFee()->getAmount(),
            'net_refund_amount' => $refundAmount->getNetRefundAmount()->getAmount(),
            'type' => $refundAmount->getType(),
            'percentage' => $refundAmount->getPercentage(),
            'reason' => $reason,
            'status' => 'pending',
            'refunded_by_user_id' => $refundedByUserId,
            'created_at' => Carbon::now()->toISOString(),
            'metadata' => $additionalMetadata,
        ];

        // Add to payment metadata
        $metadata = PaymentMetadata::fromArray($payment->getMetadata());
        $refunds = $metadata->get('refunds', []);
        $refunds[] = $refundData;
        
        $updatedMetadata = $metadata->with('refunds', $refunds);
        $payment->updateMetadata($updatedMetadata->getData());
    }

    /**
     * Toplam iade edilen tutarı hesapla (yeni iade dahil)
     */
    private function calculateTotalRefundedAmount(Payment $payment, RefundAmount $newRefund): Money
    {
        $currentTotal = $this->getTotalRefundedAmount($payment);
        return $currentTotal->add($newRefund->getNetRefundAmount());
    }

    /**
     * İade iptali sonrası ödeme durumunu yeniden hesapla
     */
    private function recalculatePaymentStatusAfterRefundCancellation(Payment $payment): void
    {
        $remainingRefundable = $this->getRemainingRefundableAmount($payment);
        $originalTotal = $payment->getAmount()->getAmount()->add($payment->getAmount()->getFeeAmount());
        
        if ($remainingRefundable->equals($originalTotal)) {
            // No refunds processed, back to completed
            $payment->updateStatus(PaymentStatus::COMPLETED, 'Refund cancelled - back to completed');
        } elseif ($remainingRefundable->isZero()) {
            // Still fully refunded
            $payment->updateStatus(PaymentStatus::REFUNDED, 'Still fully refunded');
        } else {
            // Partially refunded
            $payment->updateStatus(PaymentStatus::PARTIALLY_REFUNDED, 'Partially refunded after cancellation');
        }
    }
}
