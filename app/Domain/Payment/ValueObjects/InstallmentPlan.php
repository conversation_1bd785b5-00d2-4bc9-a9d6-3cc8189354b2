<?php

namespace App\Domain\Payment\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;
use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

/**
 * InstallmentPlan Value Object
 * Taksit planı için immutable value object
 */
class InstallmentPlan extends ValueObject
{
    // Installment Types
    public const TYPE_EQUAL = 'equal';
    public const TYPE_DECREASING = 'decreasing';
    public const TYPE_BALLOON = 'balloon';
    public const TYPE_CUSTOM = 'custom';

    // Interest Types
    public const INTEREST_SIMPLE = 'simple';
    public const INTEREST_COMPOUND = 'compound';
    public const INTEREST_NONE = 'none';

    private int $installmentCount;
    private Money $totalAmount;
    private Money $principalAmount;
    private Money $totalInterest;
    private float $interestRate;
    private string $type;
    private string $interestType;
    private array $installments;
    private Carbon $startDate;
    private string $currency;

    private function __construct(
        int $installmentCount,
        Money $principalAmount,
        float $interestRate = 0.0,
        string $type = self::TYPE_EQUAL,
        string $interestType = self::INTEREST_SIMPLE,
        ?Carbon $startDate = null
    ) {
        $this->installmentCount = $installmentCount;
        $this->principalAmount = $principalAmount;
        $this->interestRate = $interestRate;
        $this->type = $type;
        $this->interestType = $interestType;
        $this->startDate = $startDate ?? Carbon::now();
        $this->currency = $principalAmount->getCurrency();
        
        $this->calculateInstallments();
        $this->validate();
    }

    /**
     * Eşit taksitli plan oluştur
     */
    public static function createEqual(
        int $installmentCount,
        Money $principalAmount,
        float $interestRate = 0.0,
        ?Carbon $startDate = null
    ): self {
        return new self(
            $installmentCount,
            $principalAmount,
            $interestRate,
            self::TYPE_EQUAL,
            self::INTEREST_SIMPLE,
            $startDate
        );
    }

    /**
     * Azalan taksitli plan oluştur
     */
    public static function createDecreasing(
        int $installmentCount,
        Money $principalAmount,
        float $interestRate = 0.0,
        ?Carbon $startDate = null
    ): self {
        return new self(
            $installmentCount,
            $principalAmount,
            $interestRate,
            self::TYPE_DECREASING,
            self::INTEREST_SIMPLE,
            $startDate
        );
    }

    /**
     * Balon ödemeli plan oluştur
     */
    public static function createBalloon(
        int $installmentCount,
        Money $principalAmount,
        float $balloonPercentage = 50.0,
        float $interestRate = 0.0,
        ?Carbon $startDate = null
    ): self {
        $plan = new self(
            $installmentCount,
            $principalAmount,
            $interestRate,
            self::TYPE_BALLOON,
            self::INTEREST_SIMPLE,
            $startDate
        );
        
        $plan->calculateBalloonInstallments($balloonPercentage);
        return $plan;
    }

    /**
     * Özel taksit planı oluştur
     */
    public static function createCustom(
        array $customInstallments,
        Money $principalAmount,
        ?Carbon $startDate = null
    ): self {
        $plan = new self(
            count($customInstallments),
            $principalAmount,
            0.0,
            self::TYPE_CUSTOM,
            self::INTEREST_NONE,
            $startDate
        );
        
        $plan->setCustomInstallments($customInstallments);
        return $plan;
    }

    /**
     * Tek çekim (taksitsiz) plan oluştur
     */
    public static function createSingle(Money $amount, ?Carbon $startDate = null): self
    {
        return new self(1, $amount, 0.0, self::TYPE_EQUAL, self::INTEREST_NONE, $startDate);
    }

    /**
     * Taksit sayısını getir
     */
    public function getInstallmentCount(): int
    {
        return $this->installmentCount;
    }

    /**
     * Ana parayı getir
     */
    public function getPrincipalAmount(): Money
    {
        return $this->principalAmount;
    }

    /**
     * Toplam tutarı getir
     */
    public function getTotalAmount(): Money
    {
        return $this->totalAmount;
    }

    /**
     * Toplam faizi getir
     */
    public function getTotalInterest(): Money
    {
        return $this->totalInterest;
    }

    /**
     * Faiz oranını getir
     */
    public function getInterestRate(): float
    {
        return $this->interestRate;
    }

    /**
     * Plan tipini getir
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Faiz tipini getir
     */
    public function getInterestType(): string
    {
        return $this->interestType;
    }

    /**
     * Başlangıç tarihini getir
     */
    public function getStartDate(): Carbon
    {
        return $this->startDate;
    }

    /**
     * Para birimini getir
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * Tüm taksitleri getir
     */
    public function getInstallments(): array
    {
        return $this->installments;
    }

    /**
     * Belirli bir taksiti getir
     */
    public function getInstallment(int $number): ?array
    {
        return $this->installments[$number - 1] ?? null;
    }

    /**
     * İlk taksiti getir
     */
    public function getFirstInstallment(): array
    {
        return $this->installments[0];
    }

    /**
     * Son taksiti getir
     */
    public function getLastInstallment(): array
    {
        return $this->installments[$this->installmentCount - 1];
    }

    /**
     * Aylık ödeme tutarını getir (eşit taksitler için)
     */
    public function getMonthlyPayment(): Money
    {
        if ($this->type === self::TYPE_EQUAL) {
            return Money::fromMajorUnit(
                $this->installments[0]['amount'],
                $this->currency
            );
        }
        
        throw new \LogicException('Monthly payment is only available for equal installment plans');
    }

    /**
     * Bitiş tarihini getir
     */
    public function getEndDate(): Carbon
    {
        return $this->getLastInstallment()['due_date'];
    }

    /**
     * Plan süresini getir (ay cinsinden)
     */
    public function getDurationInMonths(): int
    {
        return $this->installmentCount;
    }

    /**
     * Tek çekim mi kontrol et
     */
    public function isSinglePayment(): bool
    {
        return $this->installmentCount === 1;
    }

    /**
     * Faizli mi kontrol et
     */
    public function hasInterest(): bool
    {
        return $this->interestRate > 0 && $this->interestType !== self::INTEREST_NONE;
    }

    /**
     * Eşit taksitli mi kontrol et
     */
    public function isEqualInstallments(): bool
    {
        return $this->type === self::TYPE_EQUAL;
    }

    /**
     * Azalan taksitli mi kontrol et
     */
    public function isDecreasingInstallments(): bool
    {
        return $this->type === self::TYPE_DECREASING;
    }

    /**
     * Balon ödemeli mi kontrol et
     */
    public function isBalloonPayment(): bool
    {
        return $this->type === self::TYPE_BALLOON;
    }

    /**
     * Özel plan mı kontrol et
     */
    public function isCustomPlan(): bool
    {
        return $this->type === self::TYPE_CUSTOM;
    }

    /**
     * Taksitleri hesapla
     */
    private function calculateInstallments(): void
    {
        $this->installments = [];
        
        switch ($this->type) {
            case self::TYPE_EQUAL:
                $this->calculateEqualInstallments();
                break;
            case self::TYPE_DECREASING:
                $this->calculateDecreasingInstallments();
                break;
            case self::TYPE_BALLOON:
                // Balon ödemeli hesaplama ayrı method'da yapılacak
                $this->calculateEqualInstallments(); // Geçici olarak eşit taksit
                break;
            case self::TYPE_CUSTOM:
                // Özel taksitler ayrı method'da set edilecek
                break;
        }
        
        $this->calculateTotals();
    }

    /**
     * Eşit taksitleri hesapla
     */
    private function calculateEqualInstallments(): void
    {
        $monthlyInterestRate = $this->interestRate / 100 / 12;
        $principalAmount = $this->principalAmount->getAmountInMajorUnit();
        
        if ($this->hasInterest()) {
            // Faizli eşit taksit hesaplama
            $monthlyPayment = $principalAmount * 
                ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $this->installmentCount)) /
                (pow(1 + $monthlyInterestRate, $this->installmentCount) - 1);
        } else {
            // Faizsiz eşit taksit
            $monthlyPayment = $principalAmount / $this->installmentCount;
        }
        
        for ($i = 1; $i <= $this->installmentCount; $i++) {
            $dueDate = $this->startDate->copy()->addMonths($i);
            
            $this->installments[] = [
                'number' => $i,
                'amount' => round($monthlyPayment, 2),
                'principal' => $this->hasInterest() ? 
                    $this->calculatePrincipalForInstallment($i, $monthlyPayment, $monthlyInterestRate) :
                    round($principalAmount / $this->installmentCount, 2),
                'interest' => $this->hasInterest() ? 
                    $this->calculateInterestForInstallment($i, $monthlyPayment, $monthlyInterestRate) : 0,
                'due_date' => $dueDate,
                'remaining_balance' => $this->calculateRemainingBalance($i, $principalAmount, $monthlyInterestRate),
            ];
        }
    }

    /**
     * Azalan taksitleri hesapla
     */
    private function calculateDecreasingInstallments(): void
    {
        $principalAmount = $this->principalAmount->getAmountInMajorUnit();
        $monthlyPrincipal = $principalAmount / $this->installmentCount;
        $remainingBalance = $principalAmount;
        
        for ($i = 1; $i <= $this->installmentCount; $i++) {
            $monthlyInterest = $this->hasInterest() ? 
                ($remainingBalance * $this->interestRate / 100 / 12) : 0;
            $monthlyPayment = $monthlyPrincipal + $monthlyInterest;
            $dueDate = $this->startDate->copy()->addMonths($i);
            
            $this->installments[] = [
                'number' => $i,
                'amount' => round($monthlyPayment, 2),
                'principal' => round($monthlyPrincipal, 2),
                'interest' => round($monthlyInterest, 2),
                'due_date' => $dueDate,
                'remaining_balance' => round($remainingBalance - $monthlyPrincipal, 2),
            ];
            
            $remainingBalance -= $monthlyPrincipal;
        }
    }

    /**
     * Balon ödemeli taksitleri hesapla
     */
    private function calculateBalloonInstallments(float $balloonPercentage): void
    {
        $principalAmount = $this->principalAmount->getAmountInMajorUnit();
        $balloonAmount = $principalAmount * ($balloonPercentage / 100);
        $regularAmount = $principalAmount - $balloonAmount;
        $monthlyPayment = $regularAmount / ($this->installmentCount - 1);
        
        $this->installments = [];
        
        // Normal taksitler
        for ($i = 1; $i < $this->installmentCount; $i++) {
            $dueDate = $this->startDate->copy()->addMonths($i);
            
            $this->installments[] = [
                'number' => $i,
                'amount' => round($monthlyPayment, 2),
                'principal' => round($monthlyPayment, 2),
                'interest' => 0,
                'due_date' => $dueDate,
                'remaining_balance' => round($principalAmount - ($monthlyPayment * $i), 2),
            ];
        }
        
        // Balon ödeme
        $dueDate = $this->startDate->copy()->addMonths($this->installmentCount);
        $this->installments[] = [
            'number' => $this->installmentCount,
            'amount' => round($balloonAmount, 2),
            'principal' => round($balloonAmount, 2),
            'interest' => 0,
            'due_date' => $dueDate,
            'remaining_balance' => 0,
        ];
    }

    /**
     * Özel taksitleri set et
     */
    private function setCustomInstallments(array $customInstallments): void
    {
        $this->installments = [];
        
        foreach ($customInstallments as $index => $installment) {
            $number = $index + 1;
            $dueDate = $this->startDate->copy()->addMonths($number);
            
            $this->installments[] = [
                'number' => $number,
                'amount' => $installment['amount'],
                'principal' => $installment['principal'] ?? $installment['amount'],
                'interest' => $installment['interest'] ?? 0,
                'due_date' => $dueDate,
                'remaining_balance' => $installment['remaining_balance'] ?? 0,
            ];
        }
    }

    /**
     * Toplam tutarları hesapla
     */
    private function calculateTotals(): void
    {
        $totalAmount = 0;
        $totalInterest = 0;
        
        foreach ($this->installments as $installment) {
            $totalAmount += $installment['amount'];
            $totalInterest += $installment['interest'];
        }
        
        $this->totalAmount = Money::fromMajorUnit($totalAmount, $this->currency);
        $this->totalInterest = Money::fromMajorUnit($totalInterest, $this->currency);
    }

    /**
     * Belirli taksit için ana para hesapla
     */
    private function calculatePrincipalForInstallment(int $installmentNumber, float $monthlyPayment, float $monthlyInterestRate): float
    {
        $principalAmount = $this->principalAmount->getAmountInMajorUnit();
        $remainingBalance = $principalAmount;
        
        for ($i = 1; $i < $installmentNumber; $i++) {
            $interest = $remainingBalance * $monthlyInterestRate;
            $principal = $monthlyPayment - $interest;
            $remainingBalance -= $principal;
        }
        
        $interest = $remainingBalance * $monthlyInterestRate;
        return round($monthlyPayment - $interest, 2);
    }

    /**
     * Belirli taksit için faiz hesapla
     */
    private function calculateInterestForInstallment(int $installmentNumber, float $monthlyPayment, float $monthlyInterestRate): float
    {
        $principalAmount = $this->principalAmount->getAmountInMajorUnit();
        $remainingBalance = $principalAmount;
        
        for ($i = 1; $i < $installmentNumber; $i++) {
            $interest = $remainingBalance * $monthlyInterestRate;
            $principal = $monthlyPayment - $interest;
            $remainingBalance -= $principal;
        }
        
        return round($remainingBalance * $monthlyInterestRate, 2);
    }

    /**
     * Kalan bakiyeyi hesapla
     */
    private function calculateRemainingBalance(int $installmentNumber, float $principalAmount, float $monthlyInterestRate): float
    {
        if (!$this->hasInterest()) {
            return round($principalAmount - ($principalAmount / $this->installmentCount * $installmentNumber), 2);
        }
        
        $remainingBalance = $principalAmount;
        $monthlyPayment = $this->installments[0]['amount'];
        
        for ($i = 1; $i <= $installmentNumber; $i++) {
            $interest = $remainingBalance * $monthlyInterestRate;
            $principal = $monthlyPayment - $interest;
            $remainingBalance -= $principal;
        }
        
        return round(max(0, $remainingBalance), 2);
    }

    /**
     * Plan tipinin açıklamasını getir
     */
    public function getTypeDisplayName(): string
    {
        return match ($this->type) {
            self::TYPE_EQUAL => 'Eşit Taksit',
            self::TYPE_DECREASING => 'Azalan Taksit',
            self::TYPE_BALLOON => 'Balon Ödeme',
            self::TYPE_CUSTOM => 'Özel Plan',
            default => 'Bilinmeyen',
        };
    }

    /**
     * Validation
     */
    protected function validate(): void
    {
        if ($this->installmentCount < 1) {
            throw new \InvalidArgumentException('Installment count must be at least 1');
        }
        
        if ($this->installmentCount > 36) {
            throw new \InvalidArgumentException('Installment count cannot exceed 36');
        }
        
        if ($this->interestRate < 0 || $this->interestRate > 100) {
            throw new \InvalidArgumentException('Interest rate must be between 0 and 100');
        }
        
        if ($this->principalAmount->getAmount() <= 0) {
            throw new \InvalidArgumentException('Principal amount must be positive');
        }
    }

    /**
     * String temsilini getir
     */
    public function __toString(): string
    {
        return sprintf(
            '%d taksit - %s (%s)',
            $this->installmentCount,
            $this->totalAmount->__toString(),
            $this->getTypeDisplayName()
        );
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'installment_count' => $this->installmentCount,
            'principal_amount' => $this->principalAmount->getAmountInMajorUnit(),
            'total_amount' => $this->totalAmount->getAmountInMajorUnit(),
            'total_interest' => $this->totalInterest->getAmountInMajorUnit(),
            'interest_rate' => $this->interestRate,
            'type' => $this->type,
            'type_display_name' => $this->getTypeDisplayName(),
            'interest_type' => $this->interestType,
            'currency' => $this->currency,
            'start_date' => $this->startDate->toDateString(),
            'end_date' => $this->getEndDate()->toDateString(),
            'duration_months' => $this->getDurationInMonths(),
            'monthly_payment' => $this->isEqualInstallments() ? $this->getMonthlyPayment()->getAmountInMajorUnit() : null,
            'is_single_payment' => $this->isSinglePayment(),
            'has_interest' => $this->hasInterest(),
            'installments' => array_map(function ($installment) {
                return [
                    'number' => $installment['number'],
                    'amount' => $installment['amount'],
                    'principal' => $installment['principal'],
                    'interest' => $installment['interest'],
                    'due_date' => $installment['due_date']->toDateString(),
                    'remaining_balance' => $installment['remaining_balance'],
                ];
            }, $this->installments),
        ];
    }
}
