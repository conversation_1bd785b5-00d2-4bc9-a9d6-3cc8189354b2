<?php

namespace App\Domain\Payment\Repositories;

use App\Domain\Payment\Entities\PaymentMethod;

/**
 * PaymentMethodRepositoryInterface
 * PaymentMethod domain repository interface
 */
interface PaymentMethodRepositoryInterface
{
    /**
     * Ödeme yöntemini kaydet
     */
    public function save(PaymentMethod $paymentMethod): PaymentMethod;

    /**
     * ID ile ödeme yöntemi bul
     */
    public function findById(int $id): ?PaymentMethod;

    /**
     * Kullanıcının ödeme yöntemlerini bul
     */
    public function findByUserId(int $userId, bool $activeOnly = true): array;

    /**
     * Kullanıcının varsayılan ödeme yöntemini bul
     */
    public function findDefaultByUserId(int $userId): ?PaymentMethod;

    /**
     * Kullanıcının aktif ödeme yöntemlerini bul
     */
    public function findActiveByUserId(int $userId): array;

    /**
     * Tipe göre ödeme yöntemlerini bul
     */
    public function findByType(string $type, int $limit = 100, int $offset = 0): array;

    /**
     * Kullanıcının belirli tipteki ödeme yöntemlerini bul
     */
    public function findByUserIdAndType(int $userId, string $type): array;

    /**
     * Süresi dolan ödeme yöntemlerini bul
     */
    public function findExpired(int $limit = 100, int $offset = 0): array;

    /**
     * Yakında süresi dolacak ödeme yöntemlerini bul
     */
    public function findExpiringSoon(int $days = 30, int $limit = 100, int $offset = 0): array;

    /**
     * Token ile ödeme yöntemi bul
     */
    public function findByToken(string $token): ?PaymentMethod;

    /**
     * Ödeme yöntemini sil
     */
    public function delete(PaymentMethod $paymentMethod): bool;

    /**
     * ID ile ödeme yöntemini sil
     */
    public function deleteById(int $id): bool;

    /**
     * Kullanıcının tüm ödeme yöntemlerini sil
     */
    public function deleteByUserId(int $userId): bool;

    /**
     * Süresi dolan ödeme yöntemlerini sil
     */
    public function deleteExpired(): int;

    /**
     * Ödeme yöntemi var mı kontrol et
     */
    public function exists(int $id): bool;

    /**
     * Kullanıcının ödeme yöntemi var mı kontrol et
     */
    public function existsForUser(int $userId): bool;

    /**
     * Kullanıcının varsayılan ödeme yöntemi var mı kontrol et
     */
    public function hasDefaultForUser(int $userId): bool;

    /**
     * Kullanıcının belirli tipteki ödeme yöntemi var mı kontrol et
     */
    public function existsForUserAndType(int $userId, string $type): bool;

    /**
     * Ödeme yöntemi sayısını al
     */
    public function count(array $criteria = []): int;

    /**
     * Kullanıcının ödeme yöntemi sayısını al
     */
    public function countByUserId(int $userId, bool $activeOnly = true): int;

    /**
     * Tipe göre ödeme yöntemi sayısını al
     */
    public function countByType(string $type): int;

    /**
     * Aktif ödeme yöntemi sayısını al
     */
    public function countActive(): int;

    /**
     * Süresi dolan ödeme yöntemi sayısını al
     */
    public function countExpired(): int;

    /**
     * Ödeme yöntemi istatistiklerini al
     */
    public function getPaymentMethodStatistics(): array;

    /**
     * Tip bazlı istatistikleri al
     */
    public function getTypeStatistics(): array;

    /**
     * Kullanım istatistiklerini al
     */
    public function getUsageStatistics(): array;

    /**
     * Kullanıcının en çok kullandığı ödeme yöntemini al
     */
    public function getMostUsedByUser(int $userId): ?PaymentMethod;

    /**
     * En popüler ödeme yöntemini al
     */
    public function getMostPopularType(): ?string;

    /**
     * Kullanıcının varsayılan ödeme yöntemini değiştir
     */
    public function setDefaultForUser(int $userId, int $paymentMethodId): bool;

    /**
     * Kullanıcının tüm ödeme yöntemlerini varsayılan olmaktan çıkar
     */
    public function unsetAllDefaultsForUser(int $userId): bool;

    /**
     * Toplu durum güncelleme
     */
    public function bulkUpdateStatus(array $paymentMethodIds, bool $isActive): bool;

    /**
     * Kullanım sayısını artır
     */
    public function incrementUsageCount(int $paymentMethodId): bool;

    /**
     * Son kullanım tarihini güncelle
     */
    public function updateLastUsedAt(int $paymentMethodId): bool;

    /**
     * Gelişmiş arama
     */
    public function search(array $criteria, int $limit = 100, int $offset = 0): array;
}
