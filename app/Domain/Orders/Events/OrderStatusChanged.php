<?php

namespace App\Domain\Orders\Events;

use App\Domain\Orders\Entities\Order;
use App\Domain\Shared\Events\DomainEvent;
use App\Enums\OrderStatus;
use Carbon\Carbon;

class OrderStatusChanged implements DomainEvent
{
    private Order $order;
    private OrderStatus $previousStatus;
    private OrderStatus $newStatus;
    private Carbon $occurredOn;

    public function __construct(Order $order, OrderStatus $previousStatus, OrderStatus $newStatus)
    {
        $this->order = $order;
        $this->previousStatus = $previousStatus;
        $this->newStatus = $newStatus;
        $this->occurredOn = Carbon::now();
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getPreviousStatus(): OrderStatus
    {
        return $this->previousStatus;
    }

    public function getNewStatus(): OrderStatus
    {
        return $this->newStatus;
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'order.status_changed';
    }

    public function getEventData(): array
    {
        return [
            'order_id' => $this->order->getId(),
            'order_number' => $this->order->getOrderNumber()->getValue(),
            'user_id' => $this->order->getUserId(),
            'previous_status' => $this->previousStatus->value,
            'new_status' => $this->newStatus->value,
            'previous_status_label' => $this->previousStatus->label(),
            'new_status_label' => $this->newStatus->label(),
            'changed_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->order->getId();
    }

    public function getAggregateType(): string
    {
        return 'Order';
    }
}
