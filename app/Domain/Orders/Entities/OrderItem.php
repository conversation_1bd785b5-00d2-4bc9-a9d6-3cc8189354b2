<?php

namespace App\Domain\Orders\Entities;

use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

class OrderItem
{
    private ?int $id;
    private int $orderId;
    private int $productId;
    private string $productName;
    private Money $price;
    private int $quantity;
    private Money $subtotal;
    private array $options;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    public function __construct(
        int $productId,
        string $productName,
        Money $price,
        int $quantity,
        array $options = [],
        ?int $orderId = null
    ) {
        $this->productId = $productId;
        $this->productName = $productName;
        $this->price = $price;
        $this->quantity = $quantity;
        $this->options = $options;
        $this->orderId = $orderId ?? 0;
        $this->subtotal = new Money($price->getAmount() * $quantity);
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();
    }

    public static function create(
        int $productId,
        string $productName,
        Money $price,
        int $quantity,
        array $options = []
    ): self {
        return new self($productId, $productName, $price, $quantity, $options);
    }

    public function updateQuantity(int $quantity): void
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than 0');
        }

        $this->quantity = $quantity;
        $this->subtotal = $this->price->multiply($quantity);
        $this->updatedAt = Carbon::now();
    }

    public function updatePrice(Money $price): void
    {
        $this->price = $price;
        $this->subtotal = $price->multiply($this->quantity);
        $this->updatedAt = Carbon::now();
    }

    public function updateOptions(array $options): void
    {
        $this->options = $options;
        $this->updatedAt = Carbon::now();
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getOrderId(): int { return $this->orderId; }
    public function getProductId(): int { return $this->productId; }
    public function getProductName(): string { return $this->productName; }
    public function getPrice(): Money { return $this->price; }
    public function getQuantity(): int { return $this->quantity; }
    public function getSubtotal(): Money { return $this->subtotal; }
    public function getOptions(): array { return $this->options; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
    public function setOrderId(int $orderId): void { $this->orderId = $orderId; }
}
