<?php

namespace App\Domain\Orders\Collections;

/**
 * OrderItemCollection
 * Sipariş öğeleri koleksiyonu
 */
class OrderItemCollection
{
    private array $items;

    public function __construct(array $items = [])
    {
        $this->items = $items;
    }

    public function isEmpty(): bool
    {
        return empty($this->items);
    }

    public function count(): int
    {
        return count($this->items);
    }

    public function clear(): void
    {
        $this->items = [];
    }

    public function add($item): void
    {
        $this->items[] = $item;
    }

    public function toArray(): array
    {
        return $this->items;
    }

    public function getIterator(): \ArrayIterator
    {
        return new \ArrayIterator($this->items);
    }
}
