<?php

namespace App\Domain\Inventory\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use Carbon\Carbon;

/**
 * StockReplenished Domain Event
 * Stok yenilendiğinde tetiklenir
 */
class StockReplenished implements DomainEvent
{
    private Stock $stock;
    private StockLevel $replenishedQuantity;
    private string $reason;
    private Carbon $occurredOn;

    public function __construct(
        Stock $stock,
        StockLevel $replenishedQuantity,
        string $reason
    ) {
        $this->stock = $stock;
        $this->replenishedQuantity = $replenishedQuantity;
        $this->reason = $reason;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Stock entity'sini getir
     */
    public function getStock(): Stock
    {
        return $this->stock;
    }

    /**
     * Yenilenen miktarı getir
     */
    public function getReplenishedQuantity(): StockLevel
    {
        return $this->replenishedQuantity;
    }

    /**
     * Yenilenme nedenini getir
     */
    public function getReason(): string
    {
        return $this->reason;
    }

    /**
     * Yenilenme sonrası mevcut stok
     */
    public function getAvailableStockAfterReplenishment(): StockLevel
    {
        return $this->stock->getAvailableQuantity();
    }

    /**
     * Toplam stok seviyesi
     */
    public function getTotalStockAfterReplenishment(): StockLevel
    {
        return $this->stock->getTotalQuantity();
    }

    /**
     * Otomatik yenilenme mi kontrol et
     */
    public function isAutomaticReplenishment(): bool
    {
        return in_array($this->reason, [
            'automatic_reorder',
            'scheduled_replenishment',
            'supplier_delivery'
        ]);
    }

    /**
     * Manuel yenilenme mi kontrol et
     */
    public function isManualReplenishment(): bool
    {
        return in_array($this->reason, [
            'manual_adjustment',
            'inventory_count',
            'stock_correction'
        ]);
    }

    /**
     * Tedarikçi teslimatı mı kontrol et
     */
    public function isSupplierDelivery(): bool
    {
        return $this->reason === 'supplier_delivery';
    }

    /**
     * İade nedeniyle yenilenme mi kontrol et
     */
    public function isReturnReplenishment(): bool
    {
        return in_array($this->reason, [
            'customer_return',
            'damaged_return',
            'cancelled_order'
        ]);
    }

    /**
     * Düşük stok uyarısı çözüldü mü kontrol et
     */
    public function isLowStockResolved(): bool
    {
        return !$this->stock->isLowStock();
    }

    /**
     * Yeniden sipariş ihtiyacı çözüldü mü kontrol et
     */
    public function isReorderNeedResolved(): bool
    {
        return !$this->stock->needsReorder();
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'inventory.stock_replenished';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'stock_id' => $this->stock->getId(),
            'product_id' => $this->stock->getProductId(),
            'product_variant_id' => $this->stock->getProductVariantId(),
            'replenished_quantity' => $this->replenishedQuantity->getValue(),
            'reason' => $this->reason,
            'available_after_replenishment' => $this->getAvailableStockAfterReplenishment()->getValue(),
            'total_after_replenishment' => $this->getTotalStockAfterReplenishment()->getValue(),
            'reserved_quantity' => $this->stock->getReservedQuantity()->getValue(),
            'low_stock_threshold' => $this->stock->getLowStockThreshold()->getValue(),
            'reorder_level' => $this->stock->getReorderLevel()->getValue(),
            'location' => $this->stock->getLocation()?->toArray(),
            'is_automatic' => $this->isAutomaticReplenishment(),
            'is_manual' => $this->isManualReplenishment(),
            'is_supplier_delivery' => $this->isSupplierDelivery(),
            'is_return_replenishment' => $this->isReturnReplenishment(),
            'is_low_stock_resolved' => $this->isLowStockResolved(),
            'is_reorder_need_resolved' => $this->isReorderNeedResolved(),
            'is_still_low_stock' => $this->stock->isLowStock(),
            'still_needs_reorder' => $this->stock->needsReorder(),
            'replenished_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->stock->getId();
    }
}
