<?php

namespace App\Domain\Inventory\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use App\Domain\Inventory\ValueObjects\ReservationId;
use Carbon\Carbon;

/**
 * StockReserved Domain Event
 * Stok rezerve edildiğinde tetiklenir
 */
class StockReserved implements DomainEvent
{
    private Stock $stock;
    private StockLevel $reservedQuantity;
    private ReservationId $reservationId;
    private string $reason;
    private Carbon $occurredOn;

    public function __construct(
        Stock $stock,
        StockLevel $reservedQuantity,
        ReservationId $reservationId,
        string $reason
    ) {
        $this->stock = $stock;
        $this->reservedQuantity = $reservedQuantity;
        $this->reservationId = $reservationId;
        $this->reason = $reason;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Stock entity'sini getir
     */
    public function getStock(): Stock
    {
        return $this->stock;
    }

    /**
     * Rezerve edilen miktarı getir
     */
    public function getReservedQuantity(): StockLevel
    {
        return $this->reservedQuantity;
    }

    /**
     * Rezervasyon ID'sini getir
     */
    public function getReservationId(): ReservationId
    {
        return $this->reservationId;
    }

    /**
     * Rezervasyon nedenini getir
     */
    public function getReason(): string
    {
        return $this->reason;
    }

    /**
     * Rezervasyon tipini getir
     */
    public function getReservationType(): string
    {
        return $this->reservationId->getType();
    }

    /**
     * Sipariş rezervasyonu mu kontrol et
     */
    public function isOrderReservation(): bool
    {
        return $this->reservationId->isOrderReservation();
    }

    /**
     * Cart rezervasyonu mu kontrol et
     */
    public function isCartReservation(): bool
    {
        return $this->reservationId->isCartReservation();
    }

    /**
     * Checkout rezervasyonu mu kontrol et
     */
    public function isCheckoutReservation(): bool
    {
        return $this->reservationId->isCheckoutReservation();
    }

    /**
     * Manuel rezervasyon mu kontrol et
     */
    public function isManualReservation(): bool
    {
        return $this->reservationId->isManualReservation();
    }

    /**
     * Rezervasyon sonrası kalan stok
     */
    public function getRemainingStock(): StockLevel
    {
        return $this->stock->getAvailableQuantity();
    }

    /**
     * Toplam rezerve stok
     */
    public function getTotalReservedStock(): StockLevel
    {
        return $this->stock->getReservedQuantity();
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'inventory.stock_reserved';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'stock_id' => $this->stock->getId(),
            'product_id' => $this->stock->getProductId(),
            'product_variant_id' => $this->stock->getProductVariantId(),
            'reserved_quantity' => $this->reservedQuantity->getValue(),
            'reservation_id' => $this->reservationId->getValue(),
            'reservation_type' => $this->getReservationType(),
            'reason' => $this->reason,
            'remaining_available' => $this->getRemainingStock()->getValue(),
            'total_reserved' => $this->getTotalReservedStock()->getValue(),
            'total_stock' => $this->stock->getTotalQuantity()->getValue(),
            'entity_id' => $this->reservationId->extractEntityId(),
            'location' => $this->stock->getLocation()?->toArray(),
            'is_order_reservation' => $this->isOrderReservation(),
            'is_cart_reservation' => $this->isCartReservation(),
            'is_checkout_reservation' => $this->isCheckoutReservation(),
            'is_manual_reservation' => $this->isManualReservation(),
            'reserved_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->stock->getId();
    }
}
