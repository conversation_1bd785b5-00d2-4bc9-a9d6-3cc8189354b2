<?php

namespace App\Domain\Inventory\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\StockLevel;
use App\Domain\Inventory\ValueObjects\ReservationId;
use Carbon\Carbon;

/**
 * StockReleased Domain Event
 * Stok rezervasyonu serbest bırakıldığında tetiklenir
 */
class StockReleased implements DomainEvent
{
    private Stock $stock;
    private StockLevel $releasedQuantity;
    private ReservationId $reservationId;
    private Carbon $occurredOn;

    public function __construct(
        Stock $stock,
        StockLevel $releasedQuantity,
        ReservationId $reservationId
    ) {
        $this->stock = $stock;
        $this->releasedQuantity = $releasedQuantity;
        $this->reservationId = $reservationId;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Stock entity'sini getir
     */
    public function getStock(): Stock
    {
        return $this->stock;
    }

    /**
     * Serbest bırakılan miktarı getir
     */
    public function getReleasedQuantity(): StockLevel
    {
        return $this->releasedQuantity;
    }

    /**
     * Rezervasyon ID'sini getir
     */
    public function getReservationId(): ReservationId
    {
        return $this->reservationId;
    }

    /**
     * Rezervasyon tipini getir
     */
    public function getReservationType(): string
    {
        return $this->reservationId->getType();
    }

    /**
     * Sipariş rezervasyonu mu kontrol et
     */
    public function isOrderReservation(): bool
    {
        return $this->reservationId->isOrderReservation();
    }

    /**
     * Cart rezervasyonu mu kontrol et
     */
    public function isCartReservation(): bool
    {
        return $this->reservationId->isCartReservation();
    }

    /**
     * Checkout rezervasyonu mu kontrol et
     */
    public function isCheckoutReservation(): bool
    {
        return $this->reservationId->isCheckoutReservation();
    }

    /**
     * Manuel rezervasyon mu kontrol et
     */
    public function isManualReservation(): bool
    {
        return $this->reservationId->isManualReservation();
    }

    /**
     * Serbest bırakma sonrası mevcut stok
     */
    public function getAvailableStockAfterRelease(): StockLevel
    {
        return $this->stock->getAvailableQuantity();
    }

    /**
     * Kalan rezerve stok
     */
    public function getRemainingReservedStock(): StockLevel
    {
        return $this->stock->getReservedQuantity();
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'inventory.stock_released';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'stock_id' => $this->stock->getId(),
            'product_id' => $this->stock->getProductId(),
            'product_variant_id' => $this->stock->getProductVariantId(),
            'released_quantity' => $this->releasedQuantity->getValue(),
            'reservation_id' => $this->reservationId->getValue(),
            'reservation_type' => $this->getReservationType(),
            'available_after_release' => $this->getAvailableStockAfterRelease()->getValue(),
            'remaining_reserved' => $this->getRemainingReservedStock()->getValue(),
            'total_stock' => $this->stock->getTotalQuantity()->getValue(),
            'entity_id' => $this->reservationId->extractEntityId(),
            'location' => $this->stock->getLocation()?->toArray(),
            'is_order_reservation' => $this->isOrderReservation(),
            'is_cart_reservation' => $this->isCartReservation(),
            'is_checkout_reservation' => $this->isCheckoutReservation(),
            'is_manual_reservation' => $this->isManualReservation(),
            'released_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->stock->getId();
    }
}
