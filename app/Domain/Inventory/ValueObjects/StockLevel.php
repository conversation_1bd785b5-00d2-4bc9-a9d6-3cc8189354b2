<?php

namespace App\Domain\Inventory\ValueObjects;

use App\Core\Domain\ValueObjects\ValueObject;

/**
 * StockLevel Value Object
 * Stok seviyesi için immutable value object
 */
class StockLevel extends ValueObject
{
    private int $value;

    private function __construct(int $value)
    {
        $this->validate($value);
        $this->value = $value;
    }

    /**
     * Integer değerden StockLevel oluştur
     */
    public static function fromInt(int $value): self
    {
        return new self($value);
    }

    /**
     * String değerden StockLevel oluştur
     */
    public static function fromString(string $value): self
    {
        $intValue = (int) $value;
        return new self($intValue);
    }

    /**
     * Sıfır stok seviyesi
     */
    public static function zero(): self
    {
        return new self(0);
    }

    /**
     * Değeri integer olarak getir
     */
    public function getValue(): int
    {
        return $this->value;
    }

    /**
     * String temsilini getir
     */
    public function toString(): string
    {
        return (string) $this->value;
    }

    /**
     * Stok seviyesi ekle
     */
    public function add(self $other): self
    {
        return new self($this->value + $other->value);
    }

    /**
     * Stok seviyesi çıkar
     */
    public function subtract(self $other): self
    {
        $newValue = $this->value - $other->value;
        return new self(max(0, $newValue)); // Negatif değer olamaz
    }

    /**
     * Stok seviyesi çarp
     */
    public function multiply(int $multiplier): self
    {
        return new self($this->value * $multiplier);
    }

    /**
     * Stok seviyesi böl
     */
    public function divide(int $divisor): self
    {
        if ($divisor === 0) {
            throw new \InvalidArgumentException('Cannot divide by zero');
        }
        
        return new self((int) ($this->value / $divisor));
    }

    /**
     * Yüzde hesapla
     */
    public function percentage(float $percentage): self
    {
        $result = (int) ($this->value * ($percentage / 100));
        return new self($result);
    }

    /**
     * Sıfır mı kontrol et
     */
    public function isZero(): bool
    {
        return $this->value === 0;
    }

    /**
     * Pozitif mi kontrol et
     */
    public function isPositive(): bool
    {
        return $this->value > 0;
    }

    /**
     * Negatif mi kontrol et
     */
    public function isNegative(): bool
    {
        return $this->value < 0;
    }

    /**
     * Belirli bir değerden büyük mü kontrol et
     */
    public function isGreaterThan(self $other): bool
    {
        return $this->value > $other->value;
    }

    /**
     * Belirli bir değerden büyük veya eşit mi kontrol et
     */
    public function isGreaterThanOrEqual(self $other): bool
    {
        return $this->value >= $other->value;
    }

    /**
     * Belirli bir değerden küçük mü kontrol et
     */
    public function isLessThan(self $other): bool
    {
        return $this->value < $other->value;
    }

    /**
     * Belirli bir değerden küçük veya eşit mi kontrol et
     */
    public function isLessThanOrEqual(self $other): bool
    {
        return $this->value <= $other->value;
    }

    /**
     * Belirli bir aralıkta mı kontrol et
     */
    public function isBetween(self $min, self $max): bool
    {
        return $this->isGreaterThanOrEqual($min) && $this->isLessThanOrEqual($max);
    }

    /**
     * Minimum değeri getir
     */
    public function min(self $other): self
    {
        return $this->value <= $other->value ? $this : $other;
    }

    /**
     * Maksimum değeri getir
     */
    public function max(self $other): self
    {
        return $this->value >= $other->value ? $this : $other;
    }

    /**
     * Mutlak değeri getir
     */
    public function abs(): self
    {
        return new self(abs($this->value));
    }

    /**
     * Değeri belirli bir değere yuvarla
     */
    public function roundTo(int $nearest): self
    {
        if ($nearest <= 0) {
            throw new \InvalidArgumentException('Nearest value must be positive');
        }
        
        $rounded = (int) (round($this->value / $nearest) * $nearest);
        return new self($rounded);
    }

    /**
     * Değeri yukarı yuvarla
     */
    public function ceilTo(int $nearest): self
    {
        if ($nearest <= 0) {
            throw new \InvalidArgumentException('Nearest value must be positive');
        }
        
        $ceiled = (int) (ceil($this->value / $nearest) * $nearest);
        return new self($ceiled);
    }

    /**
     * Değeri aşağı yuvarla
     */
    public function floorTo(int $nearest): self
    {
        if ($nearest <= 0) {
            throw new \InvalidArgumentException('Nearest value must be positive');
        }
        
        $floored = (int) (floor($this->value / $nearest) * $nearest);
        return new self($floored);
    }

    /**
     * Stok durumu açıklaması getir
     */
    public function getStatusDescription(): string
    {
        if ($this->isZero()) {
            return 'Stokta Yok';
        } elseif ($this->value <= 5) {
            return 'Kritik Seviye';
        } elseif ($this->value <= 20) {
            return 'Düşük Stok';
        } elseif ($this->value <= 100) {
            return 'Normal Stok';
        } else {
            return 'Yüksek Stok';
        }
    }

    /**
     * Stok durumu kodu getir
     */
    public function getStatusCode(): string
    {
        if ($this->isZero()) {
            return 'out_of_stock';
        } elseif ($this->value <= 5) {
            return 'critical';
        } elseif ($this->value <= 20) {
            return 'low';
        } elseif ($this->value <= 100) {
            return 'normal';
        } else {
            return 'high';
        }
    }

    /**
     * Formatlanmış string getir
     */
    public function format(): string
    {
        return number_format($this->value) . ' adet';
    }

    /**
     * Değeri validate et
     */
    protected function validate(int $value): void
    {
        if ($value < 0) {
            throw new \InvalidArgumentException('Stock level cannot be negative');
        }

        if ($value > 999999) {
            throw new \InvalidArgumentException('Stock level cannot exceed 999,999');
        }
    }

    /**
     * Eşitlik kontrolü
     */
    public function equals(ValueObject $other): bool
    {
        return $other instanceof self && $this->value === $other->value;
    }

    /**
     * Array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'value' => $this->value,
            'formatted' => $this->format(),
            'status_code' => $this->getStatusCode(),
            'status_description' => $this->getStatusDescription(),
            'is_zero' => $this->isZero(),
            'is_positive' => $this->isPositive(),
        ];
    }
}
