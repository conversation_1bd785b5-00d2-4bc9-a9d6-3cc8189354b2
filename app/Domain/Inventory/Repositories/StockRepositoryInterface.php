<?php

namespace App\Domain\Inventory\Repositories;

use App\Domain\Inventory\Entities\Stock;
use App\Domain\Inventory\ValueObjects\ReservationId;
use App\Domain\Inventory\ValueObjects\StockLocation;

/**
 * StockRepositoryInterface
 * Stok repository interface
 */
interface StockRepositoryInterface
{
    /**
     * ID ile stok bul
     */
    public function findById(int $id): ?Stock;

    /**
     * Ürün için stok bul
     */
    public function findByProduct(int $productId, ?int $productVariantId = null, ?StockLocation $location = null): ?Stock;

    /**
     * Ürün için tüm stokları bul
     */
    public function findAllByProduct(int $productId, ?int $productVariantId = null): array;

    /**
     * Ürün için mevcut stokları bul
     */
    public function findAvailableByProduct(int $productId, ?int $productVariantId = null): array;

    /**
     * Rezervasyon ID ile stok bul
     */
    public function findByReservationId(ReservationId $reservationId): ?Stock;

    /**
     * Lokasyon ile stokları bul
     */
    public function findByLocation(StockLocation $location): array;

    /**
     * Düşük stok seviyesindeki stokları bul
     */
    public function findLowStock(): array;

    /**
     * Yeniden sipariş gereken stokları bul
     */
    public function findNeedingReorder(): array;

    /**
     * Stokta olmayan ürünleri bul
     */
    public function findOutOfStock(): array;

    /**
     * Süresi dolmuş rezervasyonları bul
     */
    public function findExpiredReservations(int $maxAgeSeconds = 3600): array;

    /**
     * Aktif rezervasyonları bul
     */
    public function findActiveReservations(): array;

    /**
     * Belirli ürünler için stok seviyelerini bul
     */
    public function findStockLevelsForProducts(array $productIds): array;

    /**
     * Stok kaydet
     */
    public function save(Stock $stock): void;

    /**
     * Stok sil
     */
    public function delete(Stock $stock): void;

    /**
     * Toplu stok kaydet
     */
    public function saveBatch(array $stocks): void;

    /**
     * Stok sayısını getir
     */
    public function count(): int;

    /**
     * Sayfalı stok listesi getir
     */
    public function paginate(int $page = 1, int $perPage = 20, array $filters = []): array;

    /**
     * Stok arama
     */
    public function search(string $query, array $filters = []): array;

    /**
     * Stok istatistikleri getir
     */
    public function getStatistics(): array;

    /**
     * Lokasyon bazlı stok özeti getir
     */
    public function getLocationSummary(): array;

    /**
     * Ürün bazlı stok özeti getir
     */
    public function getProductSummary(int $productId, ?int $productVariantId = null): array;

    /**
     * Stok hareketleri getir
     */
    public function getStockMovements(int $stockId, int $limit = 50): array;

    /**
     * Rezervasyon geçmişi getir
     */
    public function getReservationHistory(int $stockId, int $limit = 50): array;

    /**
     * Düşük stok uyarıları getir
     */
    public function getLowStockAlerts(): array;

    /**
     * Yeniden sipariş önerileri getir
     */
    public function getReorderSuggestions(): array;

    /**
     * Stok değeri hesapla
     */
    public function calculateStockValue(array $productPrices = []): array;

    /**
     * Stok devir hızı hesapla
     */
    public function calculateStockTurnover(int $days = 30): array;

    /**
     * ABC analizi yap
     */
    public function performABCAnalysis(): array;

    /**
     * Stok yaşlandırma raporu
     */
    public function getStockAgingReport(): array;

    /**
     * Kritik stok seviyesi raporu
     */
    public function getCriticalStockReport(): array;

    /**
     * Stok hareket raporu
     */
    public function getStockMovementReport(\DateTime $startDate, \DateTime $endDate): array;

    /**
     * Rezervasyon raporu
     */
    public function getReservationReport(\DateTime $startDate, \DateTime $endDate): array;

    /**
     * Stok performans raporu
     */
    public function getStockPerformanceReport(): array;
}
