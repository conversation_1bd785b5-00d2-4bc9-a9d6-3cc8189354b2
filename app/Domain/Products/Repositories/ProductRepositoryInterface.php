<?php

namespace App\Domain\Products\Repositories;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\SKU;

interface ProductRepositoryInterface
{
    /**
     * Ürün kaydet
     */
    public function save(Product $product): Product;

    /**
     * ID ile ürün bul
     */
    public function findById(int $id): ?Product;

    /**
     * Slug ile ürün bul
     */
    public function findBySlug(string $slug): ?Product;

    /**
     * SKU ile ürün bul
     */
    public function findBySKU(SKU $sku): ?Product;

    /**
     * Kategori ID'sine göre ürünleri al
     */
    public function findByCategoryId(int $categoryId, int $limit = 10, int $offset = 0): array;

    /**
     * Aktif ürünleri al
     */
    public function findActive(int $limit = 10, int $offset = 0): array;

    /**
     * Öne çıkan ürünleri al
     */
    public function findFeatured(int $limit = 10, int $offset = 0): array;

    /**
     * İndirimli ürünleri al
     */
    public function findOnSale(int $limit = 10, int $offset = 0): array;

    /**
     * Stokta olan ürünleri al
     */
    public function findInStock(int $limit = 10, int $offset = 0): array;

    /**
     * Stokta olmayan ürünleri al
     */
    public function findOutOfStock(int $limit = 10, int $offset = 0): array;

    /**
     * Düşük stoklu ürünleri al
     */
    public function findLowStock(int $limit = 10, int $offset = 0): array;

    /**
     * Arama kriterlerine göre ürünleri al
     */
    public function search(array $criteria, int $limit = 10, int $offset = 0): array;

    /**
     * Ürün sayısını al
     */
    public function count(array $criteria = []): int;

    /**
     * Kategori ID'sine göre ürün sayısını al
     */
    public function countByCategoryId(int $categoryId): int;

    /**
     * Ürünü sil
     */
    public function delete(Product $product): bool;

    /**
     * ID ile ürünü sil
     */
    public function deleteById(int $id): bool;

    /**
     * Ürün var mı kontrol et
     */
    public function exists(int $id): bool;

    /**
     * SKU var mı kontrol et
     */
    public function existsBySKU(SKU $sku): bool;

    /**
     * Slug var mı kontrol et
     */
    public function existsBySlug(string $slug): bool;

    /**
     * En çok görüntülenen ürünleri al
     */
    public function findMostViewed(int $limit = 10, int $offset = 0): array;

    /**
     * Son eklenen ürünleri al
     */
    public function findLatest(int $limit = 10, int $offset = 0): array;

    /**
     * Fiyat aralığına göre ürünleri al
     */
    public function findByPriceRange(float $minPrice, float $maxPrice, int $limit = 10, int $offset = 0): array;

    /**
     * Benzer ürünleri al
     */
    public function findSimilar(Product $product, int $limit = 10): array;

    /**
     * İlgili ürünleri al
     */
    public function findRelated(Product $product, int $limit = 10): array;

    /**
     * Rastgele ürünleri al
     */
    public function findRandom(int $limit = 10): array;

    /**
     * Belirli bir tarihten sonra güncellenen ürünleri al
     */
    public function findUpdatedAfter(\DateTime $date, int $limit = 10, int $offset = 0): array;

    /**
     * Belirli bir tarihten sonra oluşturulan ürünleri al
     */
    public function findCreatedAfter(\DateTime $date, int $limit = 10, int $offset = 0): array;

    /**
     * Ürün görüntüleme sayısını artır
     */
    public function incrementViewCount(int $productId): void;

    /**
     * Toplu stok güncelleme
     */
    public function bulkUpdateStock(array $stockUpdates): bool;

    /**
     * Toplu fiyat güncelleme
     */
    public function bulkUpdatePrices(array $priceUpdates): bool;

    /**
     * Toplu durum güncelleme
     */
    public function bulkUpdateStatus(array $productIds, bool $status): bool;

    /**
     * Kategori değiştirme
     */
    public function bulkUpdateCategory(array $productIds, int $categoryId): bool;

    /**
     * Ürün istatistiklerini al
     */
    public function getStatistics(): array;

    /**
     * Kategori bazlı istatistikleri al
     */
    public function getCategoryStatistics(int $categoryId): array;

    /**
     * Stok istatistiklerini al
     */
    public function getStockStatistics(): array;

    /**
     * Fiyat istatistiklerini al
     */
    public function getPriceStatistics(): array;
}
