<?php

namespace App\Domain\Products\Entities;

use Carbon\Carbon;

class ProductAttribute
{
    private ?int $id;
    private int $productId;
    private int $attributeId;
    private mixed $value;
    private bool $isVariantGenerator;
    private bool $isRequired;
    private ?int $position;
    private Carbon $createdAt;
    private Carbon $updatedAt;

    public function __construct(
        int $productId,
        int $attributeId,
        mixed $value,
        bool $isVariantGenerator = false,
        bool $isRequired = false,
        ?int $position = null
    ) {
        $this->productId = $productId;
        $this->attributeId = $attributeId;
        $this->value = $value;
        $this->isVariantGenerator = $isVariantGenerator;
        $this->isRequired = $isRequired;
        $this->position = $position;
        $this->createdAt = Carbon::now();
        $this->updatedAt = Carbon::now();
    }

    public static function create(
        int $productId,
        int $attributeId,
        mixed $value,
        bool $isVariantGenerator = false,
        bool $isRequired = false,
        ?int $position = null
    ): self {
        return new self(
            $productId,
            $attributeId,
            $value,
            $isVariantGenerator,
            $isRequired,
            $position
        );
    }

    public function updateValue(mixed $value): void
    {
        $this->value = $value;
        $this->updatedAt = Carbon::now();
    }

    public function setAsVariantGenerator(bool $isVariantGenerator = true): void
    {
        $this->isVariantGenerator = $isVariantGenerator;
        $this->updatedAt = Carbon::now();
    }

    public function setAsRequired(bool $isRequired = true): void
    {
        $this->isRequired = $isRequired;
        $this->updatedAt = Carbon::now();
    }

    public function setPosition(int $position): void
    {
        $this->position = $position;
        $this->updatedAt = Carbon::now();
    }

    public function isVariantGenerator(): bool
    {
        return $this->isVariantGenerator;
    }

    public function isRequired(): bool
    {
        return $this->isRequired;
    }

    public function hasValue(): bool
    {
        return $this->value !== null && $this->value !== '';
    }

    public function getDisplayValue(): string
    {
        if (is_array($this->value)) {
            return implode(', ', $this->value);
        }
        
        if (is_bool($this->value)) {
            return $this->value ? 'Evet' : 'Hayır';
        }
        
        return (string) $this->value;
    }

    // Getters
    public function getId(): ?int { return $this->id; }
    public function getProductId(): int { return $this->productId; }
    public function getAttributeId(): int { return $this->attributeId; }
    public function getValue(): mixed { return $this->value; }
    public function getPosition(): ?int { return $this->position; }
    public function getCreatedAt(): Carbon { return $this->createdAt; }
    public function getUpdatedAt(): Carbon { return $this->updatedAt; }

    // Setters
    public function setId(int $id): void { $this->id = $id; }
}
