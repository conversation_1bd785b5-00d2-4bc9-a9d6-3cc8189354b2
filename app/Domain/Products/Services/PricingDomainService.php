<?php

namespace App\Domain\Products\Services;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Shared\Services\BaseDomainService;
use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

/**
 * PricingDomainService
 * Ürün fiyatlandırma business logic'ini yönetir
 */
class PricingDomainService extends BaseDomainService
{
    /**
     * Ürünün geçerli fiyatını hesapla
     */
    public function calculateCurrentPrice(Product $product, ?ProductVariant $variant = null): Price
    {
        $basePrice = $product->getCurrentPrice();
        
        if ($variant) {
            return $this->calculateVariantPrice($basePrice, $variant);
        }
        
        return $basePrice;
    }

    /**
     * Varyant fiyatını hesapla
     */
    public function calculateVariantPrice(Price $basePrice, ProductVariant $variant): Price
    {
        $additionalPrice = $variant->getAdditionalPrice();
        
        if ($additionalPrice->getAmount() === 0.0) {
            return $basePrice;
        }
        
        return $basePrice->add($additionalPrice);
    }

    /**
     * İndirim yüzdesini hesapla
     */
    public function calculateDiscountPercentage(Product $product): float
    {
        if (!$product->isOnSale() || !$product->isSaleActive()) {
            return 0.0;
        }

        $originalPrice = $product->getPrice();
        $salePrice = $product->getSalePrice();

        if (!$salePrice || $originalPrice->getAmount() === 0) {
            return 0.0;
        }

        $discount = $originalPrice->subtract($salePrice);
        return ($discount->getAmount() / $originalPrice->getAmount()) * 100;
    }

    /**
     * İndirim tutarını hesapla
     */
    public function calculateDiscountAmount(Product $product): Price
    {
        if (!$product->isOnSale() || !$product->isSaleActive()) {
            return Price::zero($product->getPrice()->getCurrency());
        }

        $originalPrice = $product->getPrice();
        $salePrice = $product->getSalePrice();

        if (!$salePrice) {
            return Price::zero($originalPrice->getCurrency());
        }

        return $originalPrice->subtract($salePrice);
    }

    /**
     * Toplu fiyat hesapla (quantity-based pricing)
     */
    public function calculateBulkPrice(Product $product, int $quantity, ?ProductVariant $variant = null): Price
    {
        $unitPrice = $this->calculateCurrentPrice($product, $variant);
        $bulkDiscountRate = $this->getBulkDiscountRate($quantity);
        
        if ($bulkDiscountRate > 0) {
            $discountAmount = $unitPrice->multiply($bulkDiscountRate);
            $unitPrice = $unitPrice->subtract($discountAmount);
        }
        
        return $unitPrice->multiply($quantity);
    }

    /**
     * Toplu alım indirim oranını getir
     */
    private function getBulkDiscountRate(int $quantity): float
    {
        return match (true) {
            $quantity >= 100 => 0.15, // %15 indirim
            $quantity >= 50 => 0.10,  // %10 indirim
            $quantity >= 20 => 0.05,  // %5 indirim
            default => 0.0
        };
    }

    /**
     * Vergi dahil fiyat hesapla
     */
    public function calculatePriceWithTax(Price $price, float $taxRate = 0.18): Price
    {
        $taxAmount = $price->multiply($taxRate);
        return $price->add($taxAmount);
    }

    /**
     * Vergi tutarını hesapla
     */
    public function calculateTaxAmount(Price $price, float $taxRate = 0.18): Price
    {
        return $price->multiply($taxRate);
    }

    /**
     * Fiyat karşılaştırması yap
     */
    public function comparePrices(Price $price1, Price $price2): int
    {
        if ($price1->getCurrency() !== $price2->getCurrency()) {
            throw new \InvalidArgumentException('Farklı para birimlerindeki fiyatlar karşılaştırılamaz');
        }

        return $price1->compareTo($price2);
    }

    /**
     * En düşük fiyatı bul
     */
    public function findLowestPrice(array $prices): Price
    {
        if (empty($prices)) {
            throw new \InvalidArgumentException('Fiyat listesi boş olamaz');
        }

        $lowestPrice = $prices[0];
        
        foreach ($prices as $price) {
            if ($this->comparePrices($price, $lowestPrice) < 0) {
                $lowestPrice = $price;
            }
        }
        
        return $lowestPrice;
    }

    /**
     * En yüksek fiyatı bul
     */
    public function findHighestPrice(array $prices): Price
    {
        if (empty($prices)) {
            throw new \InvalidArgumentException('Fiyat listesi boş olamaz');
        }

        $highestPrice = $prices[0];
        
        foreach ($prices as $price) {
            if ($this->comparePrices($price, $highestPrice) > 0) {
                $highestPrice = $price;
            }
        }
        
        return $highestPrice;
    }

    /**
     * Ortalama fiyat hesapla
     */
    public function calculateAveragePrice(array $prices): Price
    {
        if (empty($prices)) {
            throw new \InvalidArgumentException('Fiyat listesi boş olamaz');
        }

        $currency = $prices[0]->getCurrency();
        $totalAmount = 0.0;
        
        foreach ($prices as $price) {
            if ($price->getCurrency() !== $currency) {
                throw new \InvalidArgumentException('Tüm fiyatlar aynı para biriminde olmalı');
            }
            $totalAmount += $price->getAmount();
        }
        
        $averageAmount = $totalAmount / count($prices);
        return Price::fromAmount($averageAmount, $currency);
    }

    /**
     * Fiyat aralığını kontrol et
     */
    public function isPriceInRange(Price $price, Price $minPrice, Price $maxPrice): bool
    {
        return $this->comparePrices($price, $minPrice) >= 0 && 
               $this->comparePrices($price, $maxPrice) <= 0;
    }

    /**
     * Dinamik fiyatlandırma hesapla (stok durumuna göre)
     */
    public function calculateDynamicPrice(Product $product, ?ProductVariant $variant = null): Price
    {
        $basePrice = $this->calculateCurrentPrice($product, $variant);
        
        // Stok durumuna göre fiyat ayarlaması
        $stock = $variant ? $variant->getStock() : $product->getStock();
        $stockRatio = $stock->getQuantity() / max($stock->getLowStockThreshold(), 1);
        
        if ($stockRatio < 0.1) { // %10'dan az stok varsa
            // %5 fiyat artışı
            $increase = $basePrice->multiply(0.05);
            return $basePrice->add($increase);
        } elseif ($stockRatio > 5.0) { // Çok fazla stok varsa
            // %3 fiyat indirimi
            $decrease = $basePrice->multiply(0.03);
            return $basePrice->subtract($decrease);
        }
        
        return $basePrice;
    }

    /**
     * Rekabetçi fiyat öner
     */
    public function suggestCompetitivePrice(Price $currentPrice, array $competitorPrices): Price
    {
        if (empty($competitorPrices)) {
            return $currentPrice;
        }
        
        $averageCompetitorPrice = $this->calculateAveragePrice($competitorPrices);
        $lowestCompetitorPrice = $this->findLowestPrice($competitorPrices);
        
        // Ortalama rakip fiyatının %5 altında öner
        $suggestedPrice = $averageCompetitorPrice->multiply(0.95);
        
        // Ama en düşük rakip fiyatından %10 fazla olmasın
        $minimumPrice = $lowestCompetitorPrice->multiply(1.10);
        
        if ($this->comparePrices($suggestedPrice, $minimumPrice) < 0) {
            return $minimumPrice;
        }
        
        return $suggestedPrice;
    }

    /**
     * Fiyat geçmişi analizi
     */
    public function analyzePriceHistory(Product $product, int $days = 30): array
    {
        // Bu metod Infrastructure layer'da implement edilecek
        // Şimdilik basit bir analiz döndürüyoruz
        return [
            'current_price' => $product->getCurrentPrice(),
            'average_price' => $product->getPrice(),
            'lowest_price' => $product->getCurrentPrice(),
            'highest_price' => $product->getPrice(),
            'price_changes' => 0,
            'trend' => 'stable'
        ];
    }
}
