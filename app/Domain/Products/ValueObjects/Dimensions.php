<?php

namespace App\Domain\Products\ValueObjects;

use InvalidArgumentException;

class Dimensions
{
    private float $length;
    private float $width;
    private float $height;
    private string $unit;

    // Supported units
    public const UNIT_CENTIMETER = 'cm';
    public const UNIT_METER = 'm';
    public const UNIT_INCH = 'in';
    public const UNIT_FOOT = 'ft';

    private const SUPPORTED_UNITS = [
        self::UNIT_CENTIMETER,
        self::UNIT_METER,
        self::UNIT_INCH,
        self::UNIT_FOOT,
    ];

    // Conversion rates to centimeters
    private const CONVERSION_RATES = [
        self::UNIT_CENTIMETER => 1,
        self::UNIT_METER => 100,
        self::UNIT_INCH => 2.54,
        self::UNIT_FOOT => 30.48,
    ];

    public function __construct(
        float $length,
        float $width,
        float $height,
        string $unit = self::UNIT_CENTIMETER
    ) {
        $this->validate($length, $width, $height, $unit);
        $this->length = round($length, 2);
        $this->width = round($width, 2);
        $this->height = round($height, 2);
        $this->unit = $unit;
    }

    public static function fromCentimeters(float $length, float $width, float $height): self
    {
        return new self($length, $width, $height, self::UNIT_CENTIMETER);
    }

    public static function fromMeters(float $length, float $width, float $height): self
    {
        return new self($length, $width, $height, self::UNIT_METER);
    }

    public static function fromInches(float $length, float $width, float $height): self
    {
        return new self($length, $width, $height, self::UNIT_INCH);
    }

    public static function fromFeet(float $length, float $width, float $height): self
    {
        return new self($length, $width, $height, self::UNIT_FOOT);
    }

    private function validate(float $length, float $width, float $height, string $unit): void
    {
        if ($length < 0 || $width < 0 || $height < 0) {
            throw new InvalidArgumentException('Dimension values cannot be negative');
        }

        if (!in_array($unit, self::SUPPORTED_UNITS)) {
            throw new InvalidArgumentException("Unsupported dimension unit: {$unit}");
        }
    }

    public function convertTo(string $targetUnit): Dimensions
    {
        if (!in_array($targetUnit, self::SUPPORTED_UNITS)) {
            throw new InvalidArgumentException("Unsupported dimension unit: {$targetUnit}");
        }

        if ($this->unit === $targetUnit) {
            return $this;
        }

        // Convert to centimeters first, then to target unit
        $lengthCm = $this->length * self::CONVERSION_RATES[$this->unit];
        $widthCm = $this->width * self::CONVERSION_RATES[$this->unit];
        $heightCm = $this->height * self::CONVERSION_RATES[$this->unit];

        $conversionRate = self::CONVERSION_RATES[$targetUnit];
        
        return new Dimensions(
            $lengthCm / $conversionRate,
            $widthCm / $conversionRate,
            $heightCm / $conversionRate,
            $targetUnit
        );
    }

    public function getVolume(): float
    {
        return $this->length * $this->width * $this->height;
    }

    public function getVolumeInCubicCentimeters(): float
    {
        $cmDimensions = $this->convertTo(self::UNIT_CENTIMETER);
        return $cmDimensions->getVolume();
    }

    public function getVolumeInCubicMeters(): float
    {
        return $this->getVolumeInCubicCentimeters() / 1000000; // 1m³ = 1,000,000 cm³
    }

    public function getVolumeInCubicInches(): float
    {
        $inchDimensions = $this->convertTo(self::UNIT_INCH);
        return $inchDimensions->getVolume();
    }

    public function getVolumeInCubicFeet(): float
    {
        return $this->getVolumeInCubicInches() / 1728; // 1ft³ = 1,728 in³
    }

    public function getSurfaceArea(): float
    {
        return 2 * (
            ($this->length * $this->width) +
            ($this->length * $this->height) +
            ($this->width * $this->height)
        );
    }

    public function getLongestSide(): float
    {
        return max($this->length, $this->width, $this->height);
    }

    public function getShortestSide(): float
    {
        return min($this->length, $this->width, $this->height);
    }

    public function isSquare(): bool
    {
        return abs($this->length - $this->width) < 0.01;
    }

    public function isCube(): bool
    {
        return $this->isSquare() && abs($this->length - $this->height) < 0.01;
    }

    public function fitsIn(Dimensions $container): bool
    {
        // Convert both to same unit for comparison
        $containerCm = $container->convertTo(self::UNIT_CENTIMETER);
        $thisCm = $this->convertTo(self::UNIT_CENTIMETER);

        // Check all possible orientations
        $dimensions = [$thisCm->length, $thisCm->width, $thisCm->height];
        $containerDims = [$containerCm->length, $containerCm->width, $containerCm->height];
        
        sort($dimensions);
        sort($containerDims);

        return $dimensions[0] <= $containerDims[0] &&
               $dimensions[1] <= $containerDims[1] &&
               $dimensions[2] <= $containerDims[2];
    }

    public function getLength(): float
    {
        return $this->length;
    }

    public function getWidth(): float
    {
        return $this->width;
    }

    public function getHeight(): float
    {
        return $this->height;
    }

    public function getUnit(): string
    {
        return $this->unit;
    }

    public function format(): string
    {
        $l = number_format($this->length, 2);
        $w = number_format($this->width, 2);
        $h = number_format($this->height, 2);
        
        // Remove trailing zeros
        $l = rtrim(rtrim($l, '0'), '.');
        $w = rtrim(rtrim($w, '0'), '.');
        $h = rtrim(rtrim($h, '0'), '.');
        
        return "{$l} × {$w} × {$h} {$this->unit}";
    }

    public function getUnitLabel(): string
    {
        return match($this->unit) {
            self::UNIT_CENTIMETER => 'santimetre',
            self::UNIT_METER => 'metre',
            self::UNIT_INCH => 'inç',
            self::UNIT_FOOT => 'feet',
            default => $this->unit,
        };
    }

    public function toArray(): array
    {
        return [
            'length' => $this->length,
            'width' => $this->width,
            'height' => $this->height,
            'unit' => $this->unit,
            'unit_label' => $this->getUnitLabel(),
            'formatted' => $this->format(),
            'volume' => $this->getVolume(),
            'volume_cubic_cm' => $this->getVolumeInCubicCentimeters(),
            'surface_area' => $this->getSurfaceArea(),
            'longest_side' => $this->getLongestSide(),
            'shortest_side' => $this->getShortestSide(),
            'is_square' => $this->isSquare(),
            'is_cube' => $this->isCube(),
        ];
    }

    public function __toString(): string
    {
        return $this->format();
    }

    public function equals(Dimensions $other): bool
    {
        $thisCm = $this->convertTo(self::UNIT_CENTIMETER);
        $otherCm = $other->convertTo(self::UNIT_CENTIMETER);

        return abs($thisCm->length - $otherCm->length) < 0.01 &&
               abs($thisCm->width - $otherCm->width) < 0.01 &&
               abs($thisCm->height - $otherCm->height) < 0.01;
    }
}
