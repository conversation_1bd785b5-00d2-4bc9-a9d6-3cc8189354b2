<?php

namespace App\Domain\Products\ValueObjects;

use InvalidArgumentException;

class Weight
{
    private float $value;
    private string $unit;

    // Supported units
    public const UNIT_GRAM = 'g';
    public const UNIT_KILOGRAM = 'kg';
    public const UNIT_POUND = 'lb';
    public const UNIT_OUNCE = 'oz';

    private const SUPPORTED_UNITS = [
        self::UNIT_GRAM,
        self::UNIT_KILOGRAM,
        self::UNIT_POUND,
        self::UNIT_OUNCE,
    ];

    // Conversion rates to grams
    private const CONVERSION_RATES = [
        self::UNIT_GRAM => 1,
        self::UNIT_KILOGRAM => 1000,
        self::UNIT_POUND => 453.592,
        self::UNIT_OUNCE => 28.3495,
    ];

    public function __construct(float $value, string $unit = self::UNIT_GRAM)
    {
        $this->validate($value, $unit);
        $this->value = round($value, 3);
        $this->unit = $unit;
    }

    public static function fromGrams(float $grams): self
    {
        return new self($grams, self::UNIT_GRAM);
    }

    public static function fromKilograms(float $kilograms): self
    {
        return new self($kilograms, self::UNIT_KILOGRAM);
    }

    public static function fromPounds(float $pounds): self
    {
        return new self($pounds, self::UNIT_POUND);
    }

    public static function fromOunces(float $ounces): self
    {
        return new self($ounces, self::UNIT_OUNCE);
    }

    private function validate(float $value, string $unit): void
    {
        if ($value < 0) {
            throw new InvalidArgumentException('Weight value cannot be negative');
        }

        if (!in_array($unit, self::SUPPORTED_UNITS)) {
            throw new InvalidArgumentException("Unsupported weight unit: {$unit}");
        }
    }

    public function convertTo(string $targetUnit): Weight
    {
        if (!in_array($targetUnit, self::SUPPORTED_UNITS)) {
            throw new InvalidArgumentException("Unsupported weight unit: {$targetUnit}");
        }

        if ($this->unit === $targetUnit) {
            return $this;
        }

        // Convert to grams first, then to target unit
        $grams = $this->toGrams();
        $targetValue = $grams / self::CONVERSION_RATES[$targetUnit];

        return new Weight($targetValue, $targetUnit);
    }

    public function toGrams(): float
    {
        return $this->value * self::CONVERSION_RATES[$this->unit];
    }

    public function toKilograms(): float
    {
        return $this->toGrams() / self::CONVERSION_RATES[self::UNIT_KILOGRAM];
    }

    public function toPounds(): float
    {
        return $this->toGrams() / self::CONVERSION_RATES[self::UNIT_POUND];
    }

    public function toOunces(): float
    {
        return $this->toGrams() / self::CONVERSION_RATES[self::UNIT_OUNCE];
    }

    public function add(Weight $other): Weight
    {
        $thisInGrams = $this->toGrams();
        $otherInGrams = $other->toGrams();
        $totalGrams = $thisInGrams + $otherInGrams;

        // Return in the same unit as this weight
        $totalInThisUnit = $totalGrams / self::CONVERSION_RATES[$this->unit];
        return new Weight($totalInThisUnit, $this->unit);
    }

    public function subtract(Weight $other): Weight
    {
        $thisInGrams = $this->toGrams();
        $otherInGrams = $other->toGrams();
        
        if ($otherInGrams > $thisInGrams) {
            throw new InvalidArgumentException('Cannot subtract larger weight from smaller weight');
        }

        $resultGrams = $thisInGrams - $otherInGrams;
        $resultInThisUnit = $resultGrams / self::CONVERSION_RATES[$this->unit];
        
        return new Weight($resultInThisUnit, $this->unit);
    }

    public function multiply(float $multiplier): Weight
    {
        if ($multiplier < 0) {
            throw new InvalidArgumentException('Multiplier cannot be negative');
        }

        return new Weight($this->value * $multiplier, $this->unit);
    }

    public function isGreaterThan(Weight $other): bool
    {
        return $this->toGrams() > $other->toGrams();
    }

    public function isLessThan(Weight $other): bool
    {
        return $this->toGrams() < $other->toGrams();
    }

    public function isEqualTo(Weight $other): bool
    {
        return abs($this->toGrams() - $other->toGrams()) < 0.001; // 1mg tolerance
    }

    public function isZero(): bool
    {
        return $this->value < 0.001;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getUnit(): string
    {
        return $this->unit;
    }

    public function format(): string
    {
        $formattedValue = number_format($this->value, 3);
        $formattedValue = rtrim(rtrim($formattedValue, '0'), '.');
        
        return $formattedValue . ' ' . $this->unit;
    }

    public function getUnitLabel(): string
    {
        return match($this->unit) {
            self::UNIT_GRAM => 'gram',
            self::UNIT_KILOGRAM => 'kilogram',
            self::UNIT_POUND => 'pound',
            self::UNIT_OUNCE => 'ounce',
            default => $this->unit,
        };
    }

    public function toArray(): array
    {
        return [
            'value' => $this->value,
            'unit' => $this->unit,
            'unit_label' => $this->getUnitLabel(),
            'formatted' => $this->format(),
            'grams' => $this->toGrams(),
            'kilograms' => $this->toKilograms(),
        ];
    }

    public function __toString(): string
    {
        return $this->format();
    }

    public function equals(Weight $other): bool
    {
        return $this->isEqualTo($other);
    }
}
