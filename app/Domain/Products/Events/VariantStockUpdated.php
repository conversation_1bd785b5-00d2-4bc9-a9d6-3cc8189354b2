<?php

namespace App\Domain\Products\Events;

use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class VariantStockUpdated implements DomainEvent
{
    private ProductVariant $variant;
    private Stock $oldStock;
    private Stock $newStock;
    private Carbon $occurredOn;

    public function __construct(ProductVariant $variant, Stock $oldStock, Stock $newStock)
    {
        $this->variant = $variant;
        $this->oldStock = $oldStock;
        $this->newStock = $newStock;
        $this->occurredOn = Carbon::now();
    }

    public function getVariant(): ProductVariant
    {
        return $this->variant;
    }

    public function getOldStock(): Stock
    {
        return $this->oldStock;
    }

    public function getNewStock(): Stock
    {
        return $this->newStock;
    }

    public function getQuantityChange(): int
    {
        return $this->newStock->getQuantity() - $this->oldStock->getQuantity();
    }

    public function isStockIncreased(): bool
    {
        return $this->getQuantityChange() > 0;
    }

    public function isStockDecreased(): bool
    {
        return $this->getQuantityChange() < 0;
    }

    public function isNowOutOfStock(): bool
    {
        return !$this->oldStock->isOutOfStock() && $this->newStock->isOutOfStock();
    }

    public function isNowInStock(): bool
    {
        return $this->oldStock->isOutOfStock() && !$this->newStock->isOutOfStock();
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'product.variant_stock_updated';
    }

    public function getEventData(): array
    {
        return [
            'variant_id' => $this->variant->getId(),
            'product_id' => $this->variant->getProductId(),
            'sku' => $this->variant->getSKU()->getValue(),
            'old_quantity' => $this->oldStock->getQuantity(),
            'new_quantity' => $this->newStock->getQuantity(),
            'quantity_change' => $this->getQuantityChange(),
            'old_status' => $this->oldStock->getStatus(),
            'new_status' => $this->newStock->getStatus(),
            'is_stock_increased' => $this->isStockIncreased(),
            'is_stock_decreased' => $this->isStockDecreased(),
            'is_now_out_of_stock' => $this->isNowOutOfStock(),
            'is_now_in_stock' => $this->isNowInStock(),
            'updated_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->variant->getId();
    }

    public function getAggregateType(): string
    {
        return 'ProductVariant';
    }
}
