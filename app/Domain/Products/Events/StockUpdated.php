<?php

namespace App\Domain\Products\Events;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class StockUpdated implements DomainEvent
{
    private Product $product;
    private Stock $oldStock;
    private Stock $newStock;
    private Carbon $occurredOn;

    public function __construct(Product $product, Stock $oldStock, Stock $newStock)
    {
        $this->product = $product;
        $this->oldStock = $oldStock;
        $this->newStock = $newStock;
        $this->occurredOn = Carbon::now();
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function getOldStock(): Stock
    {
        return $this->oldStock;
    }

    public function getNewStock(): Stock
    {
        return $this->newStock;
    }

    public function getQuantityChange(): int
    {
        return $this->newStock->getQuantity() - $this->oldStock->getQuantity();
    }

    public function isStockIncreased(): bool
    {
        return $this->getQuantityChange() > 0;
    }

    public function isStockDecreased(): bool
    {
        return $this->getQuantityChange() < 0;
    }

    public function isNowOutOfStock(): bool
    {
        return !$this->oldStock->isOutOfStock() && $this->newStock->isOutOfStock();
    }

    public function isNowInStock(): bool
    {
        return $this->oldStock->isOutOfStock() && !$this->newStock->isOutOfStock();
    }

    public function isNowLowStock(): bool
    {
        return !$this->oldStock->isLowStock() && $this->newStock->isLowStock();
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'product.stock_updated';
    }

    public function getEventData(): array
    {
        return [
            'product_id' => $this->product->getId(),
            'product_name' => $this->product->getName(),
            'sku' => $this->product->getSKU()->getValue(),
            'old_quantity' => $this->oldStock->getQuantity(),
            'new_quantity' => $this->newStock->getQuantity(),
            'quantity_change' => $this->getQuantityChange(),
            'old_status' => $this->oldStock->getStatus(),
            'new_status' => $this->newStock->getStatus(),
            'is_stock_increased' => $this->isStockIncreased(),
            'is_stock_decreased' => $this->isStockDecreased(),
            'is_now_out_of_stock' => $this->isNowOutOfStock(),
            'is_now_in_stock' => $this->isNowInStock(),
            'is_now_low_stock' => $this->isNowLowStock(),
            'updated_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->product->getId();
    }

    public function getAggregateType(): string
    {
        return 'Product';
    }
}
