<?php

namespace App\Domain\Products\Events;

use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Shared\Events\DomainEvent;
use Carbon\Carbon;

class VariantPriceChanged implements DomainEvent
{
    private ProductVariant $variant;
    private Price $oldPrice;
    private Price $newPrice;
    private Carbon $occurredOn;

    public function __construct(ProductVariant $variant, Price $oldPrice, Price $newPrice)
    {
        $this->variant = $variant;
        $this->oldPrice = $oldPrice;
        $this->newPrice = $newPrice;
        $this->occurredOn = Carbon::now();
    }

    public function getVariant(): ProductVariant
    {
        return $this->variant;
    }

    public function getOldPrice(): Price
    {
        return $this->oldPrice;
    }

    public function getNewPrice(): Price
    {
        return $this->newPrice;
    }

    public function getPriceChange(): Price
    {
        if ($this->newPrice->isGreaterThan($this->oldPrice)) {
            return $this->newPrice->subtract($this->oldPrice);
        } else {
            return $this->oldPrice->subtract($this->newPrice);
        }
    }

    public function isPriceIncreased(): bool
    {
        return $this->newPrice->isGreaterThan($this->oldPrice);
    }

    public function isPriceDecreased(): bool
    {
        return $this->newPrice->isLessThan($this->oldPrice);
    }

    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    public function getEventName(): string
    {
        return 'product.variant_price_changed';
    }

    public function getEventData(): array
    {
        return [
            'variant_id' => $this->variant->getId(),
            'product_id' => $this->variant->getProductId(),
            'sku' => $this->variant->getSKU()->getValue(),
            'old_price' => $this->oldPrice->getAmount(),
            'new_price' => $this->newPrice->getAmount(),
            'currency' => $this->newPrice->getCurrency(),
            'price_change' => $this->getPriceChange()->getAmount(),
            'is_price_increased' => $this->isPriceIncreased(),
            'is_price_decreased' => $this->isPriceDecreased(),
            'updated_at' => $this->occurredOn->toISOString(),
        ];
    }

    public function getAggregateId(): string
    {
        return (string) $this->variant->getId();
    }

    public function getAggregateType(): string
    {
        return 'ProductVariant';
    }
}
