<?php

namespace App\Domain\Shared\Rules\Pricing;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRuleResult;
use App\Core\Domain\ValueObjects\Money;

/**
 * PricingRuleInterface
 * Fiyatlandırma kuralları için interface
 */
interface PricingRuleInterface
{
    /**
     * Fiyatlandırma kuralını uygula
     */
    public function applyRule(EntityInterface $entity, Money $basePrice, array $context = []): PricingRuleResult;

    /**
     * Kural bu entity için geçerli mi
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool;

    /**
     * Kuralın önceliğini getir
     */
    public function getPriority(): int;

    /**
     * Kuralın adını getir
     */
    public function getName(): string;

    /**
     * Kuralın açıklamasını getir
     */
    public function getDescription(): string;
}

/**
 * PricingRuleResult
 * Fiyatlandırma kuralı sonucu
 */
class PricingRuleResult extends BusinessRuleResult
{
    private Money $originalPrice;
    private Money $adjustedPrice;
    private Money $adjustment;
    private string $adjustmentType; // 'discount', 'markup', 'tax', 'fee'
    private ?string $reason = null;

    public function __construct(
        bool $valid,
        string $ruleName,
        Money $originalPrice,
        Money $adjustedPrice,
        string $adjustmentType = 'discount',
        ?string $reason = null,
        array $errors = [],
        array $warnings = [],
        array $metadata = []
    ) {
        parent::__construct($valid, $ruleName, $errors, $warnings, $metadata);

        $this->originalPrice = $originalPrice;
        $this->adjustedPrice = $adjustedPrice;
        $this->adjustment = $adjustedPrice->subtract($originalPrice);
        $this->adjustmentType = $adjustmentType;
        $this->reason = $reason;
    }

    public static function discount(
        string $ruleName,
        Money $originalPrice,
        Money $discountedPrice,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $originalPrice,
            $discountedPrice,
            'discount',
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function markup(
        string $ruleName,
        Money $originalPrice,
        Money $markedUpPrice,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $originalPrice,
            $markedUpPrice,
            'markup',
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function tax(
        string $ruleName,
        Money $originalPrice,
        Money $priceWithTax,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $originalPrice,
            $priceWithTax,
            'tax',
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function noChange(
        string $ruleName,
        Money $price,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $price,
            $price,
            'none',
            $reason,
            [],
            [],
            $metadata
        );
    }

    public function getOriginalPrice(): Money
    {
        return $this->originalPrice;
    }

    public function getAdjustedPrice(): Money
    {
        return $this->adjustedPrice;
    }

    public function getAdjustment(): Money
    {
        return $this->adjustment;
    }

    public function getAdjustmentType(): string
    {
        return $this->adjustmentType;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function hasAdjustment(): bool
    {
        return !$this->adjustment->isZero();
    }

    public function isDiscount(): bool
    {
        return $this->adjustmentType === 'discount' && $this->adjustment->isNegative();
    }

    public function isMarkup(): bool
    {
        return $this->adjustmentType === 'markup' && $this->adjustment->isPositive();
    }

    public function isTax(): bool
    {
        return $this->adjustmentType === 'tax';
    }

    public function getDiscountAmount(): Money
    {
        return $this->isDiscount() ? $this->adjustment->abs() : Money::zero();
    }

    public function getMarkupAmount(): Money
    {
        return $this->isMarkup() ? $this->adjustment : Money::zero();
    }

    public function getDiscountPercentage(): float
    {
        if (!$this->isDiscount() || $this->originalPrice->isZero()) {
            return 0.0;
        }

        return ($this->getDiscountAmount()->getAmountInMajorUnit() / $this->originalPrice->getAmountInMajorUnit()) * 100;
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'original_price' => $this->originalPrice->toArray(),
            'adjusted_price' => $this->adjustedPrice->toArray(),
            'adjustment' => $this->adjustment->toArray(),
            'adjustment_type' => $this->adjustmentType,
            'reason' => $this->reason,
            'has_adjustment' => $this->hasAdjustment(),
            'is_discount' => $this->isDiscount(),
            'is_markup' => $this->isMarkup(),
            'is_tax' => $this->isTax(),
            'discount_percentage' => $this->getDiscountPercentage(),
        ]);
    }
}
