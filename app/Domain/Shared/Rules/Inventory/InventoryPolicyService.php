<?php

namespace App\Domain\Shared\Rules\Inventory;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRuleEngine;
use App\Domain\Products\Entities\Product;

/**
 * InventoryPolicyService
 * Stok politikalarını yöneten servis
 */
class InventoryPolicyService
{
    private BusinessRuleEngine $ruleEngine;
    private array $registeredRules = [];

    public function __construct()
    {
        $this->ruleEngine = new BusinessRuleEngine();
        $this->loadStandardRules();
    }

    /**
     * Stok durumunu kontrol et
     */
    public function checkStock(Product $product, int $requestedQuantity, array $context = []): InventoryPolicyResult
    {
        $context['quantity'] = $requestedQuantity;
        
        $results = $this->ruleEngine->executeRules($product, $context);
        
        return new InventoryPolicyResult(
            $product,
            $requestedQuantity,
            $results
        );
    }

    /**
     * Stok rezervasyonu yapılabilir mi kontrol et
     */
    public function canReserveStock(Product $product, int $quantity): bool
    {
        $result = $this->checkStock($product, $quantity);
        return $result->canFulfill();
    }

    /**
     * Reorder gerekli mi kontrol et
     */
    public function isReorderRequired(Product $product): bool
    {
        $reorderRule = $this->getRule('reorder_point');
        
        if ($reorderRule instanceof ReorderPointRule) {
            return $reorderRule->isReorderRequired($product);
        }
        
        return false;
    }

    /**
     * Backorder bilgilerini getir
     */
    public function getBackorderInfo(Product $product, int $requestedQuantity): array
    {
        $backorderRule = $this->getRule('backorder');
        
        if ($backorderRule instanceof BackorderRule) {
            return $backorderRule->getBackorderInfo($product, $requestedQuantity);
        }
        
        return [
            'backorder_allowed' => false,
            'reason' => 'Backorder rule not configured'
        ];
    }

    /**
     * Stok uyarılarını getir
     */
    public function getStockWarnings(Product $product): array
    {
        $warnings = [];
        
        // Stock validation warnings
        $stockRule = $this->getRule('stock_validation');
        if ($stockRule instanceof StockValidationRule) {
            $warning = $stockRule->getStockWarning($product);
            if ($warning) {
                $warnings[] = $warning;
            }
        }
        
        // Reorder warnings
        if ($this->isReorderRequired($product)) {
            $warnings[] = 'Product requires reordering';
        }
        
        return $warnings;
    }

    /**
     * Toplu stok kontrolü
     */
    public function checkMultipleProducts(array $items): array
    {
        $results = [];
        
        foreach ($items as $item) {
            $product = $item['product'] ?? null;
            $quantity = $item['quantity'] ?? 1;
            
            if ($product instanceof Product) {
                $results[] = $this->checkStock($product, $quantity);
            }
        }
        
        return $results;
    }

    /**
     * Stok kuralı kaydet
     */
    public function registerRule(InventoryRuleInterface $rule): self
    {
        $this->ruleEngine->addRule($rule);
        $this->registeredRules[$rule->getName()] = $rule;
        return $this;
    }

    /**
     * Stok kuralını kaldır
     */
    public function unregisterRule(string $ruleName): self
    {
        if (isset($this->registeredRules[$ruleName])) {
            $this->ruleEngine->removeRule($this->registeredRules[$ruleName]);
            unset($this->registeredRules[$ruleName]);
        }
        return $this;
    }

    /**
     * Belirli bir kuralı getir
     */
    public function getRule(string $ruleName): ?InventoryRuleInterface
    {
        return $this->registeredRules[$ruleName] ?? null;
    }

    /**
     * Tüm kuralları getir
     */
    public function getRules(): array
    {
        return $this->registeredRules;
    }

    /**
     * Servis istatistiklerini getir
     */
    public function getServiceStatistics(): array
    {
        return [
            'total_rules' => count($this->registeredRules),
            'rule_names' => array_keys($this->registeredRules),
            'engine_stats' => $this->ruleEngine->getStatistics(),
        ];
    }

    /**
     * Servis sağlık kontrolü
     */
    public function healthCheck(): array
    {
        $issues = [];
        
        if (empty($this->registeredRules)) {
            $issues[] = 'No inventory rules registered';
        }
        
        // Temel kuralların varlığını kontrol et
        $requiredRules = ['stock_validation', 'reorder_point', 'backorder'];
        foreach ($requiredRules as $ruleName) {
            if (!isset($this->registeredRules[$ruleName])) {
                $issues[] = "Required rule '{$ruleName}' is not registered";
            }
        }
        
        return [
            'healthy' => empty($issues),
            'issues' => $issues,
            'total_rules' => count($this->registeredRules),
        ];
    }

    /**
     * Standart kuralları yükle
     */
    private function loadStandardRules(): void
    {
        $this->registerRule(new StockValidationRule());
        $this->registerRule(new ReorderPointRule());
        $this->registerRule(new BackorderRule());
    }
}

/**
 * InventoryPolicyResult
 * Stok politikası sonucu
 */
class InventoryPolicyResult
{
    private Product $product;
    private int $requestedQuantity;
    private array $ruleResults;

    public function __construct(Product $product, int $requestedQuantity, array $ruleResults)
    {
        $this->product = $product;
        $this->requestedQuantity = $requestedQuantity;
        $this->ruleResults = $ruleResults;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function getRequestedQuantity(): int
    {
        return $this->requestedQuantity;
    }

    public function getRuleResults(): array
    {
        return $this->ruleResults;
    }

    public function isValid(): bool
    {
        foreach ($this->ruleResults as $result) {
            if (!$result->isValid()) {
                return false;
            }
        }
        return true;
    }

    public function canFulfill(): bool
    {
        foreach ($this->ruleResults as $result) {
            if ($result instanceof InventoryRuleResult) {
                if (!$result->canFulfill()) {
                    return false;
                }
            }
        }
        return true;
    }

    public function getErrors(): array
    {
        $errors = [];
        foreach ($this->ruleResults as $result) {
            $errors = array_merge($errors, $result->getErrors());
        }
        return $errors;
    }

    public function getWarnings(): array
    {
        $warnings = [];
        foreach ($this->ruleResults as $result) {
            $warnings = array_merge($warnings, $result->getWarnings());
        }
        return $warnings;
    }

    public function toArray(): array
    {
        return [
            'product_id' => $this->product->getId(),
            'requested_quantity' => $this->requestedQuantity,
            'valid' => $this->isValid(),
            'can_fulfill' => $this->canFulfill(),
            'errors' => $this->getErrors(),
            'warnings' => $this->getWarnings(),
            'rule_results' => array_map(fn($result) => $result->toArray(), $this->ruleResults),
        ];
    }
}
