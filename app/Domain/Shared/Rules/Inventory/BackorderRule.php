<?php

namespace App\Domain\Shared\Rules\Inventory;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Products\Entities\Product;

/**
 * BackorderRule
 * Backorder kuralı - stok yetersiz olduğunda backorder izin verme kuralları
 */
class BackorderRule implements InventoryRuleInterface
{
    private int $priority;
    private bool $globalBackorderEnabled;
    private int $maxBackorderQuantity;
    private int $maxBackorderDays;
    private array $excludedCategories;

    public function __construct(
        int $priority = 300,
        bool $globalBackorderEnabled = true,
        int $maxBackorderQuantity = 100,
        int $maxBackorderDays = 30,
        array $excludedCategories = []
    ) {
        $this->priority = $priority;
        $this->globalBackorderEnabled = $globalBackorderEnabled;
        $this->maxBackorderQuantity = $maxBackorderQuantity;
        $this->maxBackorderDays = $maxBackorderDays;
        $this->excludedCategories = $excludedCategories;
    }

    public function applyRule(EntityInterface $entity, array $context = []): InventoryRuleResult
    {
        if (!$entity instanceof Product) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                0,
                0,
                $context['quantity'] ?? 1,
                false,
                'Entity is not a product'
            );
        }

        $requestedQuantity = $context['quantity'] ?? 1;
        $currentStock = $entity->getStock()->getQuantity();
        $reservedQuantity = $entity->getStock()->getReservedQuantity();
        $availableStock = $currentStock - $reservedQuantity;

        // Stok yeterli ise backorder gerekli değil
        if ($availableStock >= $requestedQuantity) {
            return InventoryRuleResult::available(
                $this->getName(),
                $availableStock,
                $reservedQuantity,
                $requestedQuantity,
                'Stock available, backorder not needed'
            );
        }

        $shortfall = $requestedQuantity - $availableStock;
        $metadata = [
            'shortfall' => $shortfall,
            'max_backorder_quantity' => $this->maxBackorderQuantity,
            'max_backorder_days' => $this->maxBackorderDays,
            'estimated_restock_date' => $this->estimateRestockDate($entity)
        ];

        // Global backorder devre dışı
        if (!$this->globalBackorderEnabled) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                $availableStock,
                $reservedQuantity,
                $requestedQuantity,
                false,
                'Global backorder is disabled',
                $metadata
            );
        }

        // Ürün için backorder devre dışı
        if (!$entity->getStock()->isBackorderAllowed()) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                $availableStock,
                $reservedQuantity,
                $requestedQuantity,
                false,
                'Backorder not allowed for this product',
                $metadata
            );
        }

        // Kategori backorder'dan hariç tutulmuş
        if (in_array($entity->getCategoryId(), $this->excludedCategories)) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                $availableStock,
                $reservedQuantity,
                $requestedQuantity,
                false,
                'Product category excluded from backorder',
                $metadata
            );
        }

        // Backorder miktarı limiti aşıyor
        if ($shortfall > $this->maxBackorderQuantity) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                $availableStock,
                $reservedQuantity,
                $requestedQuantity,
                false,
                "Backorder quantity ({$shortfall}) exceeds maximum allowed ({$this->maxBackorderQuantity})",
                $metadata
            );
        }

        // Tahmini teslimat süresi çok uzun
        $estimatedDays = $this->estimateBackorderDays($entity);
        if ($estimatedDays > $this->maxBackorderDays) {
            return InventoryRuleResult::unavailable(
                $this->getName(),
                $availableStock,
                $reservedQuantity,
                $requestedQuantity,
                false,
                "Estimated backorder time ({$estimatedDays} days) exceeds maximum allowed ({$this->maxBackorderDays} days)",
                array_merge($metadata, ['estimated_days' => $estimatedDays])
            );
        }

        // Backorder onaylandı
        return InventoryRuleResult::backorder(
            $this->getName(),
            $availableStock,
            $reservedQuantity,
            $requestedQuantity,
            "Backorder approved for {$shortfall} units",
            array_merge($metadata, [
                'estimated_days' => $estimatedDays,
                'partial_fulfillment' => $availableStock > 0,
                'immediate_quantity' => $availableStock,
                'backorder_quantity' => $shortfall
            ])
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        if (!$entity instanceof Product) {
            return false;
        }

        // Dijital ürünler için backorder gerekli değil
        if ($entity->isDigital()) {
            return false;
        }

        // Stok takibi yapılmayan ürünler için geçerli değil
        if (!$entity->getStock()->isTrackingEnabled()) {
            return false;
        }

        return true;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'backorder';
    }

    public function getDescription(): string
    {
        return 'Manages backorder policies when stock is insufficient';
    }

    /**
     * Global backorder durumunu ayarla
     */
    public function setGlobalBackorderEnabled(bool $enabled): self
    {
        $this->globalBackorderEnabled = $enabled;
        return $this;
    }

    /**
     * Maksimum backorder miktarını ayarla
     */
    public function setMaxBackorderQuantity(int $quantity): self
    {
        $this->maxBackorderQuantity = $quantity;
        return $this;
    }

    /**
     * Maksimum backorder gün sayısını ayarla
     */
    public function setMaxBackorderDays(int $days): self
    {
        $this->maxBackorderDays = $days;
        return $this;
    }

    /**
     * Hariç tutulan kategorileri ayarla
     */
    public function setExcludedCategories(array $categories): self
    {
        $this->excludedCategories = $categories;
        return $this;
    }

    /**
     * Tahmini yeniden stok tarihini hesapla
     */
    private function estimateRestockDate(Product $product): ?string
    {
        // Bu basit bir tahmin - gerçek implementasyonda tedarikçi bilgileri kullanılabilir
        $estimatedDays = $this->estimateBackorderDays($product);
        
        if ($estimatedDays === null) {
            return null;
        }
        
        return date('Y-m-d', strtotime("+{$estimatedDays} days"));
    }

    /**
     * Tahmini backorder gün sayısını hesapla
     */
    private function estimateBackorderDays(Product $product): ?int
    {
        // Ürün için özel lead time varsa onu kullan
        $leadTime = $product->getStock()->getLeadTime();
        
        if ($leadTime !== null) {
            return $leadTime;
        }
        
        // Kategori bazında varsayılan lead time'lar
        $categoryLeadTimes = [
            1 => 7,   // Elektronik
            2 => 14,  // Giyim
            3 => 21,  // Ev & Bahçe
            // Diğer kategoriler...
        ];
        
        return $categoryLeadTimes[$product->getCategoryId()] ?? 14; // Varsayılan 14 gün
    }

    /**
     * Ürün için backorder bilgilerini getir
     */
    public function getBackorderInfo(Product $product, int $requestedQuantity): array
    {
        $availableStock = $product->getStock()->getQuantity() - $product->getStock()->getReservedQuantity();
        $shortfall = max(0, $requestedQuantity - $availableStock);
        
        return [
            'backorder_allowed' => $product->getStock()->isBackorderAllowed() && $this->globalBackorderEnabled,
            'available_stock' => $availableStock,
            'requested_quantity' => $requestedQuantity,
            'shortfall' => $shortfall,
            'can_partial_fulfill' => $availableStock > 0,
            'estimated_days' => $this->estimateBackorderDays($product),
            'estimated_restock_date' => $this->estimateRestockDate($product),
            'within_limits' => $shortfall <= $this->maxBackorderQuantity,
        ];
    }
}
