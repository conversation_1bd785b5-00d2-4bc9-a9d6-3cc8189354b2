<?php

namespace App\Domain\Shared\Rules;

use App\Domain\Shared\Contracts\EntityInterface;





/**
 * BusinessRule
 * İş kuralları için temel sınıf
 */
abstract class BusinessRule
{
    protected string $name;
    protected string $description;
    protected ?string $group = null;
    protected int $priority = 0;
    protected bool $enabled = true;
    protected array $metadata = [];

    public function __construct(
        string $name,
        string $description = '',
        ?string $group = null,
        int $priority = 0
    ) {
        $this->name = $name;
        $this->description = $description;
        $this->group = $group;
        $this->priority = $priority;
    }

    /**
     * Kuralın adını getir
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Kuralın açıklamasını getir
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * Kuralın grubunu getir
     */
    public function getGroup(): ?string
    {
        return $this->group;
    }

    /**
     * Kuralın önceliğini getir
     */
    public function getPriority(): int
    {
        return $this->priority;
    }

    /**
     * Kural aktif mi
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Kuralı aktif/pasif yap
     */
    public function setEnabled(bool $enabled): void
    {
        $this->enabled = $enabled;
    }

    /**
     * Metadata getir
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Metadata set et
     */
    public function setMetadata(array $metadata): void
    {
        $this->metadata = $metadata;
    }

    /**
     * Metadata değeri getir
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Metadata değeri set et
     */
    public function setMetadataValue(string $key, $value): void
    {
        $this->metadata[$key] = $value;
    }

    /**
     * Kural bu entity için geçerli mi
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        return $this->checkApplicability($entity, $context);
    }

    /**
     * Kuralı değerlendir
     */
    public function evaluate(EntityInterface $entity, array $context = []): BusinessRuleResult
    {
        if (!$this->isApplicable($entity, $context)) {
            return BusinessRuleResult::notApplicable($this->name);
        }

        try {
            return $this->performEvaluation($entity, $context);
        } catch (\Exception $e) {
            return BusinessRuleResult::error($this->name, $e->getMessage());
        }
    }

    /**
     * Kuralı çalıştır (side effect'ler ile)
     */
    public function execute(EntityInterface $entity, array $context = []): void
    {
        if (!$this->isApplicable($entity, $context)) {
            return;
        }

        $this->performExecution($entity, $context);
    }

    /**
     * Kuralın geçerlilik kontrolü - alt sınıflar tarafından implement edilmeli
     */
    abstract protected function checkApplicability(EntityInterface $entity, array $context = []): bool;

    /**
     * Kural değerlendirmesi - alt sınıflar tarafından implement edilmeli
     */
    abstract protected function performEvaluation(EntityInterface $entity, array $context = []): BusinessRuleResult;

    /**
     * Kural çalıştırması - alt sınıflar tarafından override edilebilir
     */
    protected function performExecution(EntityInterface $entity, array $context = []): void
    {
        // Default implementation - no side effects
    }

    /**
     * Kuralın hash'ini getir
     */
    public function getHash(): string
    {
        return md5($this->name . $this->description . serialize($this->metadata));
    }

    /**
     * Kuralın string temsilini getir
     */
    public function toString(): string
    {
        return $this->name . ($this->group ? " ({$this->group})" : '');
    }

    /**
     * Kuralın array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'group' => $this->group,
            'priority' => $this->priority,
            'enabled' => $this->enabled,
            'metadata' => $this->metadata,
            'class' => static::class,
        ];
    }

    /**
     * Magic method for string conversion
     */
    public function __toString(): string
    {
        return $this->toString();
    }
}

/**
 * BusinessRuleResult
 * İş kuralı sonucu
 */
class BusinessRuleResult
{
    private bool $valid;
    private string $ruleName;
    private array $errors = [];
    private array $warnings = [];
    private array $metadata = [];
    private bool $executed = false;
    private ?float $executionTime = null;

    public function __construct(bool $valid, string $ruleName, array $errors = [], array $warnings = [], array $metadata = [])
    {
        $this->valid = $valid;
        $this->ruleName = $ruleName;
        $this->errors = $errors;
        $this->warnings = $warnings;
        $this->metadata = $metadata;
    }

    public static function valid(string $ruleName, array $metadata = []): self
    {
        return new self(true, $ruleName, [], [], $metadata);
    }

    public static function invalid(string $ruleName, array $errors, array $warnings = [], array $metadata = []): self
    {
        return new self(false, $ruleName, $errors, $warnings, $metadata);
    }

    public static function error(string $ruleName, string $error, array $metadata = []): self
    {
        return new self(false, $ruleName, [$error], [], $metadata);
    }

    public static function notApplicable(string $ruleName, array $metadata = []): self
    {
        return new self(true, $ruleName, [], [], array_merge($metadata, ['not_applicable' => true]));
    }

    public static function notFound(string $ruleName): self
    {
        return new self(false, $ruleName, ["Rule '{$ruleName}' not found"], [], ['not_found' => true]);
    }

    public function getRuleName(): string
    {
        return $this->ruleName;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function addError(string $error): void
    {
        $this->errors[] = $error;
        $this->valid = false;
    }

    public function addWarning(string $warning): void
    {
        $this->warnings[] = $warning;
    }

    public function addMetadata(string $key, $value): void
    {
        $this->metadata[$key] = $value;
    }

    public function markAsExecuted(float $executionTime = null): void
    {
        $this->executed = true;
        $this->executionTime = $executionTime;
    }

    public function getExecutionTime(): ?float
    {
        return $this->executionTime;
    }

    public function isNotApplicable(): bool
    {
        return $this->metadata['not_applicable'] ?? false;
    }

    public function isNotFound(): bool
    {
        return $this->metadata['not_found'] ?? false;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function hasWarnings(): bool
    {
        return !empty($this->warnings);
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function setMetadata(string $key, $value): void
    {
        $this->metadata[$key] = $value;
    }

    public function isExecuted(): bool
    {
        return $this->executed;
    }

    public function toArray(): array
    {
        return [
            'valid' => $this->valid,
            'rule_name' => $this->ruleName,
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'metadata' => $this->metadata,
            'executed' => $this->executed,
            'execution_time' => $this->executionTime,
        ];
    }
}

/**
 * BusinessRuleResults
 * Birden fazla iş kuralı sonucu
 */
class BusinessRuleResults
{
    private array $results = [];

    public function addResult(string $ruleId, BusinessRuleResult $result): void
    {
        $this->results[$ruleId] = $result;
    }

    public function getResult(string $ruleId): ?BusinessRuleResult
    {
        return $this->results[$ruleId] ?? null;
    }

    public function getAllResults(): array
    {
        return $this->results;
    }

    public function isValid(): bool
    {
        foreach ($this->results as $result) {
            if (!$result->isValid()) {
                return false;
            }
        }
        return true;
    }

    public function getAllErrors(): array
    {
        $errors = [];
        foreach ($this->results as $ruleId => $result) {
            if ($result->hasErrors()) {
                $errors[$ruleId] = $result->getErrors();
            }
        }
        return $errors;
    }

    public function getAllWarnings(): array
    {
        $warnings = [];
        foreach ($this->results as $ruleId => $result) {
            if ($result->hasWarnings()) {
                $warnings[$ruleId] = $result->getWarnings();
            }
        }
        return $warnings;
    }

    public function getValidResults(): array
    {
        return array_filter($this->results, fn($result) => $result->isValid());
    }

    public function getInvalidResults(): array
    {
        return array_filter($this->results, fn($result) => !$result->isValid());
    }

    public function getExecutedResults(): array
    {
        return array_filter($this->results, fn($result) => $result->isExecuted());
    }

    public function hasErrors(): bool
    {
        foreach ($this->results as $result) {
            if ($result->hasErrors()) {
                return true;
            }
        }
        return false;
    }

    public function hasWarnings(): bool
    {
        foreach ($this->results as $result) {
            if ($result->hasWarnings()) {
                return true;
            }
        }
        return false;
    }

    public function isEmpty(): bool
    {
        return empty($this->results);
    }

    public function count(): int
    {
        return count($this->results);
    }

    public function toArray(): array
    {
        $data = [
            'valid' => $this->isValid(),
            'has_errors' => $this->hasErrors(),
            'has_warnings' => $this->hasWarnings(),
            'count' => $this->count(),
            'results' => [],
        ];

        foreach ($this->results as $ruleId => $result) {
            $data['results'][$ruleId] = $result->toArray();
        }

        return $data;
    }
}
