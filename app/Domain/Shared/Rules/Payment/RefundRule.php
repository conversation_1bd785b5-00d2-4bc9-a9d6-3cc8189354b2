<?php

namespace App\Domain\Shared\Rules\Payment;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use Carbon\Carbon;

/**
 * RefundRule
 * İade kuralı - ödeme iadesi koşullarını kontrol eder
 */
class RefundRule implements PaymentRuleInterface
{
    private int $priority;
    private int $refundPeriodDays;
    private Money $maxRefundAmount;
    private array $refundableStatuses;
    private array $nonRefundableCategories;
    private bool $partialRefundAllowed;

    public function __construct(
        int $priority = 200,
        int $refundPeriodDays = 30,
        ?Money $maxRefundAmount = null,
        array $refundableStatuses = ['delivered', 'completed'],
        array $nonRefundableCategories = [],
        bool $partialRefundAllowed = true
    ) {
        $this->priority = $priority;
        $this->refundPeriodDays = $refundPeriodDays;
        $this->maxRefundAmount = $maxRefundAmount ?? Money::fromAmount(50000, 'TRY');
        $this->refundableStatuses = $refundableStatuses;
        $this->nonRefundableCategories = $nonRefundableCategories;
        $this->partialRefundAllowed = $partialRefundAllowed;
    }

    public function applyRule(EntityInterface $entity, array $context = []): PaymentRuleResult
    {
        if (!$entity instanceof Order) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $refundAmount = $context['refund_amount'] ?? $entity->getTotalAmount();
        $refundReason = $context['reason'] ?? null;
        $isPartialRefund = $context['partial'] ?? false;

        // Sipariş durumu kontrolü
        if (!in_array($entity->getStatus()->value, $this->refundableStatuses)) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Order status '{$entity->getStatus()->value}' does not allow refund",
                [
                    'order_status' => $entity->getStatus()->value,
                    'refundable_statuses' => $this->refundableStatuses
                ]
            );
        }

        // Ödeme durumu kontrolü
        if ($entity->getPaymentStatus()->value !== 'paid') {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Order payment status '{$entity->getPaymentStatus()->value}' does not allow refund",
                ['payment_status' => $entity->getPaymentStatus()->value]
            );
        }

        // Zaman sınırı kontrolü
        $orderDate = $entity->getCreatedAt();
        $daysSinceOrder = $orderDate->diffInDays(Carbon::now());
        
        if ($daysSinceOrder > $this->refundPeriodDays) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Refund period expired. Order is {$daysSinceOrder} days old, maximum allowed is {$this->refundPeriodDays} days",
                [
                    'order_date' => $orderDate->toDateString(),
                    'days_since_order' => $daysSinceOrder,
                    'refund_period_days' => $this->refundPeriodDays
                ]
            );
        }

        // Tutar kontrolü
        if ($refundAmount->isGreaterThan($entity->getTotalAmount())) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'Refund amount cannot exceed order total',
                [
                    'refund_amount' => $refundAmount->toArray(),
                    'order_total' => $entity->getTotalAmount()->toArray()
                ]
            );
        }

        if ($refundAmount->isGreaterThan($this->maxRefundAmount)) {
            return PaymentRuleResult::denied(
                $this->getName(),
                "Refund amount exceeds maximum allowed ({$this->maxRefundAmount->toArray()['amount_major']} {$this->maxRefundAmount->getCurrency()})",
                [
                    'refund_amount' => $refundAmount->toArray(),
                    'max_refund_amount' => $this->maxRefundAmount->toArray()
                ]
            );
        }

        // Kısmi iade kontrolü
        if ($isPartialRefund && !$this->partialRefundAllowed) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'Partial refunds are not allowed',
                ['partial_refund_requested' => true]
            );
        }

        // Kategori kontrolü (sipariş öğeleri için)
        $nonRefundableItems = [];
        foreach ($entity->getItems() as $item) {
            $product = $item->getProduct();
            if (in_array($product->getCategoryId(), $this->nonRefundableCategories)) {
                $nonRefundableItems[] = [
                    'product_id' => $product->getId(),
                    'product_name' => $product->getName(),
                    'category_id' => $product->getCategoryId()
                ];
            }
        }

        if (!empty($nonRefundableItems) && !$isPartialRefund) {
            return PaymentRuleResult::denied(
                $this->getName(),
                'Order contains non-refundable items',
                [
                    'non_refundable_items' => $nonRefundableItems,
                    'suggestion' => 'Consider partial refund for refundable items only'
                ]
            );
        }

        $warnings = [];
        $metadata = [
            'refund_amount' => $refundAmount->toArray(),
            'order_total' => $entity->getTotalAmount()->toArray(),
            'days_since_order' => $daysSinceOrder,
            'remaining_refund_days' => max(0, $this->refundPeriodDays - $daysSinceOrder),
            'is_partial_refund' => $isPartialRefund,
            'refund_reason' => $refundReason
        ];

        // Uyarılar
        if (!empty($nonRefundableItems)) {
            $warnings[] = 'Some items in the order are non-refundable';
            $metadata['non_refundable_items'] = $nonRefundableItems;
        }

        if ($daysSinceOrder > ($this->refundPeriodDays * 0.8)) {
            $remainingDays = $this->refundPeriodDays - $daysSinceOrder;
            $warnings[] = "Refund period expires in {$remainingDays} days";
        }

        if ($refundAmount->isGreaterThan($this->maxRefundAmount->multiply(0.8))) {
            $warnings[] = 'High amount refund may require additional approval';
        }

        $requiredVerifications = [];
        
        // Yüksek tutar için ek doğrulama
        if ($refundAmount->isGreaterThan(Money::fromAmount(5000, $refundAmount->getCurrency()))) {
            $requiredVerifications[] = 'manager_approval';
        }

        // Geç iade için ek doğrulama
        if ($daysSinceOrder > ($this->refundPeriodDays * 0.7)) {
            $requiredVerifications[] = 'late_refund_approval';
        }

        return PaymentRuleResult::allowed(
            $this->getName(),
            $refundAmount,
            ['refund'],
            'Refund approved',
            $metadata
        )->withWarnings($warnings);
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order && ($context['action'] ?? null) === 'refund';
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'refund';
    }

    public function getDescription(): string
    {
        return 'Validates refund eligibility based on order status, time limits, and amount restrictions';
    }

    /**
     * İade süresini ayarla
     */
    public function setRefundPeriodDays(int $days): self
    {
        $this->refundPeriodDays = $days;
        return $this;
    }

    /**
     * Maksimum iade tutarını ayarla
     */
    public function setMaxRefundAmount(Money $amount): self
    {
        $this->maxRefundAmount = $amount;
        return $this;
    }

    /**
     * İade edilebilir durumları ayarla
     */
    public function setRefundableStatuses(array $statuses): self
    {
        $this->refundableStatuses = $statuses;
        return $this;
    }

    /**
     * İade edilemeyen kategorileri ayarla
     */
    public function setNonRefundableCategories(array $categories): self
    {
        $this->nonRefundableCategories = $categories;
        return $this;
    }

    /**
     * Kısmi iade izin durumunu ayarla
     */
    public function setPartialRefundAllowed(bool $allowed): self
    {
        $this->partialRefundAllowed = $allowed;
        return $this;
    }

    /**
     * Sipariş için iade bilgilerini getir
     */
    public function getRefundInfo(Order $order): array
    {
        $daysSinceOrder = $order->getCreatedAt()->diffInDays(Carbon::now());
        $remainingDays = max(0, $this->refundPeriodDays - $daysSinceOrder);
        
        return [
            'refund_eligible' => in_array($order->getStatus()->value, $this->refundableStatuses) &&
                               $order->getPaymentStatus()->value === 'paid' &&
                               $daysSinceOrder <= $this->refundPeriodDays,
            'days_since_order' => $daysSinceOrder,
            'remaining_refund_days' => $remainingDays,
            'max_refund_amount' => min($order->getTotalAmount()->getAmount(), $this->maxRefundAmount->getAmount()),
            'partial_refund_allowed' => $this->partialRefundAllowed,
            'refund_period_days' => $this->refundPeriodDays,
        ];
    }
}
