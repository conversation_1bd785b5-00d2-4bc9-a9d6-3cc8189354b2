<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;

/**
 * OrderCancellationRule
 * Sipariş iptal kuralı
 */
class OrderCancellationRule implements OrderRuleInterface
{
    private array $cancellableStatuses;
    private array $nonCancellableStatuses;
    private int $cancellationTimeLimit; // dakika cinsinden
    private Money $cancellationFeeThreshold;
    private float $cancellationFeePercentage;
    private int $priority;
    private bool $requireReason;
    private bool $requireApproval;

    public function __construct(
        array $cancellableStatuses = ['pending', 'confirmed'],
        array $nonCancellableStatuses = ['shipped', 'delivered', 'completed'],
        int $cancellationTimeLimit = 60, // 1 saat
        Money $cancellationFeeThreshold = null,
        float $cancellationFeePercentage = 0.0,
        int $priority = 300,
        bool $requireReason = true,
        bool $requireApproval = false
    ) {
        $this->cancellableStatuses = $cancellableStatuses;
        $this->nonCancellableStatuses = $nonCancellableStatuses;
        $this->cancellationTimeLimit = $cancellationTimeLimit;
        $this->cancellationFeeThreshold = $cancellationFeeThreshold ?? Money::fromAmount(500, 'TRY');
        $this->cancellationFeePercentage = $cancellationFeePercentage;
        $this->priority = $priority;
        $this->requireReason = $requireReason;
        $this->requireApproval = $requireApproval;
    }

    public function applyRule(EntityInterface $entity, array $context = []): OrderRuleResult
    {
        if (!$entity instanceof Order) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $currentStatus = $entity->getStatus();
        $errors = [];
        $warnings = [];
        $requiredActions = [];
        $metadata = [];

        // İptal edilemez durum kontrolü
        if (in_array($currentStatus, $this->nonCancellableStatuses)) {
            return OrderRuleResult::denied(
                $this->getName(),
                "Order cannot be cancelled in '{$currentStatus}' status",
                [
                    'current_status' => $currentStatus,
                    'non_cancellable_statuses' => $this->nonCancellableStatuses
                ]
            );
        }

        // İptal edilebilir durum kontrolü
        if (!in_array($currentStatus, $this->cancellableStatuses)) {
            $errors[] = "Order status '{$currentStatus}' is not in cancellable statuses";
        }

        // Zaman sınırı kontrolü
        $timeCheck = $this->checkCancellationTimeLimit($entity);
        if (!$timeCheck['allowed']) {
            if ($timeCheck['require_approval']) {
                $requiredActions[] = 'manager_approval';
                $warnings[] = $timeCheck['message'];
            } else {
                $errors[] = $timeCheck['message'];
            }
        }

        // İptal nedeni kontrolü
        if ($this->requireReason && empty($context['cancellation_reason'])) {
            $requiredActions[] = 'cancellation_reason';
            $warnings[] = 'Cancellation reason is required';
        }

        // Onay kontrolü
        if ($this->requireApproval && (!isset($context['approved']) || !$context['approved'])) {
            $requiredActions[] = 'manager_approval';
            $warnings[] = 'Manager approval required for cancellation';
        }

        // İptal ücreti hesaplama
        $cancellationFee = $this->calculateCancellationFee($entity, $context);
        if ($cancellationFee->isGreaterThan(Money::fromAmount(0, $cancellationFee->getCurrency()))) {
            $warnings[] = "Cancellation fee will be applied: {$cancellationFee->toArray()['amount_major']} {$cancellationFee->getCurrency()}";
            $metadata['cancellation_fee'] = $cancellationFee->toArray();
        }

        // Ödeme durumu kontrolü
        if ($entity->isPaid()) {
            $refundAmount = $entity->getTotalAmount()->subtract($cancellationFee);
            $warnings[] = "Refund amount: {$refundAmount->toArray()['amount_major']} {$refundAmount->getCurrency()}";
            $metadata['refund_amount'] = $refundAmount->toArray();
            $requiredActions[] = 'process_refund';
        }

        // Stok iade kontrolü
        if ($currentStatus === 'processing') {
            $requiredActions[] = 'restore_inventory';
            $warnings[] = 'Inventory will be restored';
        }

        // Sonuç değerlendirmesi
        if (!empty($errors)) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Order cancellation failed: ' . implode(', ', $errors),
                array_merge($metadata, [
                    'current_status' => $currentStatus,
                    'errors' => $errors,
                    'warnings' => $warnings
                ])
            );
        }

        if (!empty($requiredActions)) {
            return OrderRuleResult::requiresAction(
                $this->getName(),
                $requiredActions,
                'Order cancellation requires additional actions',
                array_merge($metadata, [
                    'current_status' => $currentStatus,
                    'warnings' => $warnings,
                    'cancellation_reason' => $context['cancellation_reason'] ?? null
                ])
            );
        }

        return OrderRuleResult::allowed(
            $this->getName(),
            'cancelled',
            [],
            'Order cancellation allowed',
            array_merge($metadata, [
                'current_status' => $currentStatus,
                'new_status' => 'cancelled',
                'cancellation_reason' => $context['cancellation_reason'] ?? null,
                'cancelled_at' => now()->toISOString()
            ])
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order && 
               (isset($context['action']) && $context['action'] === 'cancel');
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'order_cancellation';
    }

    public function getDescription(): string
    {
        return 'Validates order cancellation requests including time limits, fees, and business rules';
    }

    /**
     * İptal zaman sınırını kontrol et
     */
    private function checkCancellationTimeLimit(Order $order): array
    {
        $createdAt = $order->getCreatedAt();
        $minutesSinceCreation = $createdAt->diffInMinutes(now());

        if ($minutesSinceCreation <= $this->cancellationTimeLimit) {
            return [
                'allowed' => true,
                'message' => 'Within cancellation time limit'
            ];
        }

        // Zaman sınırı aşıldı, ama bazı durumlarda onay ile iptal edilebilir
        if ($order->getStatus() === 'confirmed' && $minutesSinceCreation <= ($this->cancellationTimeLimit * 2)) {
            return [
                'allowed' => false,
                'require_approval' => true,
                'message' => "Cancellation time limit exceeded ({$this->cancellationTimeLimit} minutes), manager approval required"
            ];
        }

        return [
            'allowed' => false,
            'require_approval' => false,
            'message' => "Cancellation time limit exceeded ({$this->cancellationTimeLimit} minutes)"
        ];
    }

    /**
     * İptal ücretini hesapla
     */
    private function calculateCancellationFee(Order $order, array $context): Money
    {
        $orderAmount = $order->getTotalAmount();
        
        // Eşik tutarın altındaysa ücretsiz iptal
        if ($orderAmount->isLessThan($this->cancellationFeeThreshold)) {
            return Money::fromAmount(0, $orderAmount->getCurrency());
        }

        // İptal ücreti yüzdesi 0 ise ücretsiz
        if ($this->cancellationFeePercentage <= 0) {
            return Money::fromAmount(0, $orderAmount->getCurrency());
        }

        // Durum bazlı ücret hesaplama
        $feePercentage = $this->cancellationFeePercentage;
        
        if ($order->getStatus() === 'processing') {
            $feePercentage *= 1.5; // İşleme alınan siparişler için %50 fazla ücret
        }

        $feeAmount = $orderAmount->multiply($feePercentage / 100);
        
        // Maksimum ücret sınırı (sipariş tutarının %20'si)
        $maxFee = $orderAmount->multiply(0.20);
        
        return $feeAmount->isGreaterThan($maxFee) ? $maxFee : $feeAmount;
    }

    /**
     * İptal edilebilir durumları ayarla
     */
    public function setCancellableStatuses(array $statuses): self
    {
        $this->cancellableStatuses = $statuses;
        return $this;
    }

    /**
     * İptal zaman sınırını ayarla
     */
    public function setCancellationTimeLimit(int $minutes): self
    {
        $this->cancellationTimeLimit = $minutes;
        return $this;
    }

    /**
     * İptal ücreti eşiğini ayarla
     */
    public function setCancellationFeeThreshold(Money $threshold): self
    {
        $this->cancellationFeeThreshold = $threshold;
        return $this;
    }

    /**
     * İptal ücreti yüzdesini ayarla
     */
    public function setCancellationFeePercentage(float $percentage): self
    {
        $this->cancellationFeePercentage = $percentage;
        return $this;
    }

    /**
     * Standart iptal kuralı
     */
    public static function standard(): self
    {
        return new self(
            ['pending', 'confirmed'],
            ['shipped', 'delivered', 'completed'],
            60, // 1 saat
            Money::fromAmount(500, 'TRY'),
            5.0, // %5 iptal ücreti
            300,
            true,
            false
        );
    }

    /**
     * Sıkı iptal kuralı
     */
    public static function strict(): self
    {
        return new self(
            ['pending'],
            ['confirmed', 'processing', 'shipped', 'delivered', 'completed'],
            30, // 30 dakika
            Money::fromAmount(200, 'TRY'),
            10.0, // %10 iptal ücreti
            300,
            true,
            true // Onay gerekli
        );
    }

    /**
     * Esnek iptal kuralı
     */
    public static function flexible(): self
    {
        return new self(
            ['pending', 'confirmed', 'processing'],
            ['shipped', 'delivered', 'completed'],
            120, // 2 saat
            Money::fromAmount(1000, 'TRY'),
            2.0, // %2 iptal ücreti
            300,
            false, // Neden gerekmiyor
            false
        );
    }
}
