<?php

namespace App\Domain\Shared\Rules\Order;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;

/**
 * OrderStatusTransitionRule
 * Sipariş durum geçiş kuralı
 */
class OrderStatusTransitionRule implements OrderRuleInterface
{
    private array $allowedTransitions;
    private array $restrictedTransitions;
    private int $priority;
    private bool $requireApproval;
    private array $approvalRequiredTransitions;

    public function __construct(
        array $allowedTransitions = [],
        array $restrictedTransitions = [],
        int $priority = 200,
        bool $requireApproval = false,
        array $approvalRequiredTransitions = []
    ) {
        $this->allowedTransitions = $allowedTransitions ?: $this->getDefaultAllowedTransitions();
        $this->restrictedTransitions = $restrictedTransitions;
        $this->priority = $priority;
        $this->requireApproval = $requireApproval;
        $this->approvalRequiredTransitions = $approvalRequiredTransitions ?: [
            'processing->cancelled',
            'shipped->cancelled',
            'delivered->refunded'
        ];
    }

    public function applyRule(EntityInterface $entity, array $context = []): OrderRuleResult
    {
        if (!$entity instanceof Order) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Entity is not an order',
                ['entity_type' => get_class($entity)]
            );
        }

        $currentStatus = $entity->getStatus();
        $targetStatus = $context['target_status'] ?? null;

        if (!$targetStatus) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Target status is required for transition validation',
                ['current_status' => $currentStatus]
            );
        }

        // Aynı duruma geçiş kontrolü
        if ($currentStatus === $targetStatus) {
            return OrderRuleResult::allowed(
                $this->getName(),
                $targetStatus,
                [],
                'No status change required',
                [
                    'current_status' => $currentStatus,
                    'target_status' => $targetStatus,
                    'transition' => 'none'
                ]
            );
        }

        $transition = "{$currentStatus}->{$targetStatus}";
        $errors = [];
        $warnings = [];
        $requiredActions = [];

        // Kısıtlı geçiş kontrolü
        if (in_array($transition, $this->restrictedTransitions)) {
            $errors[] = "Transition from '{$currentStatus}' to '{$targetStatus}' is restricted";
        }

        // İzin verilen geçiş kontrolü
        if (!$this->isTransitionAllowed($currentStatus, $targetStatus)) {
            $errors[] = "Transition from '{$currentStatus}' to '{$targetStatus}' is not allowed";
        }

        // Onay gerektiren geçiş kontrolü
        if (in_array($transition, $this->approvalRequiredTransitions)) {
            if (!isset($context['approved']) || !$context['approved']) {
                $requiredActions[] = 'manager_approval';
                $warnings[] = "Transition '{$transition}' requires manager approval";
            }
        }

        // İş kuralları kontrolü
        $businessRuleErrors = $this->validateBusinessRules($entity, $currentStatus, $targetStatus, $context);
        $errors = array_merge($errors, $businessRuleErrors);

        // Sonuç değerlendirmesi
        if (!empty($errors)) {
            return OrderRuleResult::denied(
                $this->getName(),
                'Status transition validation failed: ' . implode(', ', $errors),
                [
                    'current_status' => $currentStatus,
                    'target_status' => $targetStatus,
                    'transition' => $transition,
                    'errors' => $errors,
                    'warnings' => $warnings
                ]
            );
        }

        if (!empty($requiredActions)) {
            return OrderRuleResult::requiresAction(
                $this->getName(),
                $requiredActions,
                "Status transition requires additional actions",
                [
                    'current_status' => $currentStatus,
                    'target_status' => $targetStatus,
                    'transition' => $transition,
                    'warnings' => $warnings
                ]
            );
        }

        return OrderRuleResult::allowed(
            $this->getName(),
            $targetStatus,
            $this->getAllowedNextTransitions($targetStatus),
            'Status transition allowed',
            [
                'current_status' => $currentStatus,
                'target_status' => $targetStatus,
                'transition' => $transition,
                'next_allowed_transitions' => $this->getAllowedNextTransitions($targetStatus)
            ]
        );
    }

    public function isApplicable(EntityInterface $entity, array $context = []): bool
    {
        return $entity instanceof Order && isset($context['target_status']);
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getName(): string
    {
        return 'order_status_transition';
    }

    public function getDescription(): string
    {
        return 'Validates order status transitions according to business rules and workflow requirements';
    }

    /**
     * Geçişin izin verilip verilmediğini kontrol et
     */
    private function isTransitionAllowed(string $fromStatus, string $toStatus): bool
    {
        $transition = "{$fromStatus}->{$toStatus}";
        
        // Eğer izin verilen geçişler listesi boşsa, tüm geçişlere izin ver
        if (empty($this->allowedTransitions)) {
            return true;
        }

        return in_array($transition, $this->allowedTransitions);
    }

    /**
     * İş kurallarını doğrula
     */
    private function validateBusinessRules(Order $order, string $fromStatus, string $toStatus, array $context): array
    {
        $errors = [];

        // Ödeme durumu kontrolü
        if ($toStatus === 'confirmed' && !$order->isPaid()) {
            $errors[] = 'Order must be paid before confirmation';
        }

        // Stok kontrolü
        if ($toStatus === 'processing' && !$this->hasAvailableStock($order)) {
            $errors[] = 'Insufficient stock to process order';
        }

        // Kargo kontrolü
        if ($toStatus === 'shipped' && !$order->getShippingAddress()) {
            $errors[] = 'Shipping address required for shipment';
        }

        // İptal kontrolü
        if ($toStatus === 'cancelled') {
            if (in_array($fromStatus, ['shipped', 'delivered'])) {
                $errors[] = 'Cannot cancel order that has been shipped or delivered';
            }
        }

        // İade kontrolü
        if ($toStatus === 'refunded') {
            if ($fromStatus !== 'delivered') {
                $errors[] = 'Order must be delivered before refund';
            }
            
            // İade süresi kontrolü (30 gün)
            $deliveryDate = $order->getDeliveredAt();
            if ($deliveryDate && $deliveryDate->diffInDays(now()) > 30) {
                $errors[] = 'Refund period has expired (30 days)';
            }
        }

        return $errors;
    }

    /**
     * Stok durumunu kontrol et
     */
    private function hasAvailableStock(Order $order): bool
    {
        foreach ($order->getItems() as $item) {
            $product = $item->getProduct();
            if ($product->getStockQuantity() < $item->getQuantity()) {
                return false;
            }
        }
        return true;
    }

    /**
     * Belirtilen durumdan sonra izin verilen geçişleri getir
     */
    private function getAllowedNextTransitions(string $status): array
    {
        $nextTransitions = [];
        
        foreach ($this->allowedTransitions as $transition) {
            if (strpos($transition, $status . '->') === 0) {
                $nextTransitions[] = $transition;
            }
        }
        
        return $nextTransitions;
    }

    /**
     * Varsayılan izin verilen geçişleri getir
     */
    private function getDefaultAllowedTransitions(): array
    {
        return [
            'pending->confirmed',
            'pending->cancelled',
            'confirmed->processing',
            'confirmed->cancelled',
            'processing->shipped',
            'processing->cancelled',
            'shipped->delivered',
            'shipped->cancelled',
            'delivered->completed',
            'delivered->refunded',
            'cancelled->pending', // Yeniden aktifleştirme
            'refunded->completed'
        ];
    }

    /**
     * İzin verilen geçişleri ayarla
     */
    public function setAllowedTransitions(array $transitions): self
    {
        $this->allowedTransitions = $transitions;
        return $this;
    }

    /**
     * Kısıtlı geçişleri ayarla
     */
    public function setRestrictedTransitions(array $transitions): self
    {
        $this->restrictedTransitions = $transitions;
        return $this;
    }

    /**
     * Onay gerektiren geçişleri ayarla
     */
    public function setApprovalRequiredTransitions(array $transitions): self
    {
        $this->approvalRequiredTransitions = $transitions;
        return $this;
    }

    /**
     * Standart durum geçiş kuralı
     */
    public static function standard(): self
    {
        return new self();
    }

    /**
     * Sıkı durum geçiş kuralı
     */
    public static function strict(): self
    {
        return new self(
            [], // Varsayılan geçişler
            ['delivered->cancelled', 'completed->refunded'], // Kısıtlı geçişler
            200,
            true, // Onay gerekli
            ['processing->cancelled', 'shipped->cancelled', 'delivered->refunded']
        );
    }
}
