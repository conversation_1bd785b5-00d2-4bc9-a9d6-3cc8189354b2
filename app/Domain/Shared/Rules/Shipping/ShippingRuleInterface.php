<?php

namespace App\Domain\Shared\Rules\Shipping;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRuleResult;
use App\Domain\Shared\ValueObjects\Money;

/**
 * ShippingRuleInterface
 * Kargo kuralları için interface
 */
interface ShippingRuleInterface
{
    /**
     * Kargo kuralını uygula
     */
    public function applyRule(EntityInterface $entity, array $context = []): ShippingRuleResult;

    /**
     * Kural bu entity için geçerli mi
     */
    public function isApplicable(EntityInterface $entity, array $context = []): bool;

    /**
     * Kuralın önceliğini getir
     */
    public function getPriority(): int;

    /**
     * Kuralın adını getir
     */
    public function getName(): string;

    /**
     * Kuralın açıklamasını getir
     */
    public function getDescription(): string;

    /**
     * Kargo kural tipini getir
     */
    public function getRuleType(): string;
}

/**
 * ShippingRuleResult
 * Kargo kuralı sonucu
 */
class ShippingRuleResult extends BusinessRuleResult
{
    private ?Money $shippingCost = null;
    private ?int $estimatedDays = null;
    private array $availableMethods = [];
    private array $restrictions = [];
    private ?string $reason = null;

    public function __construct(
        bool $valid,
        string $ruleName,
        ?Money $shippingCost = null,
        ?int $estimatedDays = null,
        array $availableMethods = [],
        array $restrictions = [],
        ?string $reason = null,
        array $errors = [],
        array $warnings = [],
        array $metadata = []
    ) {
        parent::__construct($valid, $ruleName, $errors, $warnings, $metadata);
        
        $this->shippingCost = $shippingCost;
        $this->estimatedDays = $estimatedDays;
        $this->availableMethods = $availableMethods;
        $this->restrictions = $restrictions;
        $this->reason = $reason;
    }

    public static function allowed(
        string $ruleName,
        ?Money $shippingCost = null,
        ?int $estimatedDays = null,
        array $availableMethods = [],
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            $shippingCost,
            $estimatedDays,
            $availableMethods,
            [],
            $reason,
            [],
            [],
            $metadata
        );
    }

    public static function restricted(
        string $ruleName,
        array $restrictions,
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            false,
            $ruleName,
            null,
            null,
            [],
            $restrictions,
            $reason,
            $restrictions,
            [],
            $metadata
        );
    }

    public static function freeShipping(
        string $ruleName,
        ?int $estimatedDays = null,
        array $availableMethods = [],
        ?string $reason = null,
        array $metadata = []
    ): self {
        return new self(
            true,
            $ruleName,
            Money::zero(),
            $estimatedDays,
            $availableMethods,
            [],
            $reason ?: 'Free shipping applied',
            [],
            [],
            array_merge($metadata, ['free_shipping' => true])
        );
    }

    public function getShippingCost(): ?Money
    {
        return $this->shippingCost;
    }

    public function getEstimatedDays(): ?int
    {
        return $this->estimatedDays;
    }

    public function getAvailableMethods(): array
    {
        return $this->availableMethods;
    }

    public function getRestrictions(): array
    {
        return $this->restrictions;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function hasShippingCost(): bool
    {
        return $this->shippingCost !== null;
    }

    public function isFreeShipping(): bool
    {
        return $this->shippingCost !== null && $this->shippingCost->isZero();
    }

    public function hasRestrictions(): bool
    {
        return !empty($this->restrictions);
    }

    public function hasAvailableMethods(): bool
    {
        return !empty($this->availableMethods);
    }

    public function isShippingAllowed(): bool
    {
        return $this->isValid() && !$this->hasRestrictions();
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'shipping_cost' => $this->shippingCost?->toArray(),
            'estimated_days' => $this->estimatedDays,
            'available_methods' => $this->availableMethods,
            'restrictions' => $this->restrictions,
            'reason' => $this->reason,
            'has_shipping_cost' => $this->hasShippingCost(),
            'is_free_shipping' => $this->isFreeShipping(),
            'has_restrictions' => $this->hasRestrictions(),
            'has_available_methods' => $this->hasAvailableMethods(),
            'is_shipping_allowed' => $this->isShippingAllowed(),
        ]);
    }
}
