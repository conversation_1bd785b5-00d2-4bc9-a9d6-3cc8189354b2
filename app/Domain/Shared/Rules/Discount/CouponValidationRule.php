<?php

namespace App\Domain\Shared\Rules\Discount;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Rules\BusinessRule;
use App\Domain\Shared\Rules\BusinessRuleResult;
use App\Domain\Shared\ValueObjects\Money;
use Carbon\Carbon;

/**
 * CouponValidationRule
 * Kupon doğrulama kuralı
 */
class CouponValidationRule extends BusinessRule implements DiscountRuleInterface
{
    private array $couponData;
    private int $priority;

    public function __construct(
        array $couponData,
        int $priority = 100,
        string $name = 'coupon_validation',
        string $description = 'Coupon validation and discount rule',
        string $group = 'discount'
    ) {
        parent::__construct($name, $description, $group);
        
        $this->couponData = $this->validateCouponData($couponData);
        $this->priority = $priority;
        
        $this->setMetadataValue('coupon_data', $this->couponData);
        $this->setMetadataValue('priority', $this->priority);
    }

    /**
     * Yüzde indirimi kupon kuralı
     */
    public static function percentage(
        string $couponCode,
        float $discountPercentage,
        ?Carbon $validFrom = null,
        ?Carbon $validUntil = null,
        ?int $usageLimit = null,
        ?Money $minimumAmount = null
    ): self {
        return new self([
            'code' => $couponCode,
            'type' => 'percentage',
            'discount_percentage' => $discountPercentage,
            'valid_from' => $validFrom,
            'valid_until' => $validUntil,
            'usage_limit' => $usageLimit,
            'minimum_amount' => $minimumAmount,
            'used_count' => 0,
        ]);
    }

    /**
     * Sabit tutar indirimi kupon kuralı
     */
    public static function fixed(
        string $couponCode,
        Money $discountAmount,
        ?Carbon $validFrom = null,
        ?Carbon $validUntil = null,
        ?int $usageLimit = null,
        ?Money $minimumAmount = null
    ): self {
        return new self([
            'code' => $couponCode,
            'type' => 'fixed',
            'discount_amount' => $discountAmount,
            'valid_from' => $validFrom,
            'valid_until' => $validUntil,
            'usage_limit' => $usageLimit,
            'minimum_amount' => $minimumAmount,
            'used_count' => 0,
        ]);
    }

    public function applyDiscount(EntityInterface $entity, Money $baseAmount, array $context = []): DiscountRuleResult
    {
        $couponCode = $this->extractCouponCode($context);
        
        // Kupon kodu eşleşiyor mu
        if ($couponCode !== $this->couponData['code']) {
            return DiscountRuleResult::noDiscount(
                $this->name,
                $baseAmount,
                'Coupon code does not match'
            );
        }

        // Kupon geçerli mi
        $validationResult = $this->validateCoupon($baseAmount, $context);
        if (!$validationResult->isValid()) {
            return DiscountRuleResult::noDiscount(
                $this->name,
                $baseAmount,
                implode(', ', $validationResult->getErrors())
            );
        }

        // İndirim uygula
        if ($this->couponData['type'] === 'percentage') {
            return DiscountRuleResult::percentage(
                $this->name,
                $baseAmount,
                $this->couponData['discount_percentage'],
                $couponCode,
                "Coupon discount: {$this->couponData['discount_percentage']}%",
                [
                    'coupon_code' => $couponCode,
                    'coupon_type' => 'percentage',
                ]
            );
        } else {
            return DiscountRuleResult::fixed(
                $this->name,
                $baseAmount,
                $this->couponData['discount_amount'],
                $couponCode,
                "Coupon discount: {$this->couponData['discount_amount']->getAmountInMajorUnit()}",
                [
                    'coupon_code' => $couponCode,
                    'coupon_type' => 'fixed',
                ]
            );
        }
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function getDiscountType(): string
    {
        return $this->couponData['type'] ?? 'percentage';
    }

    protected function checkApplicability(EntityInterface $entity, array $context = []): bool
    {
        $couponCode = $this->extractCouponCode($context);
        return !empty($couponCode) && $couponCode === $this->couponData['code'];
    }

    protected function performEvaluation(EntityInterface $entity, array $context = []): BusinessRuleResult
    {
        $baseAmount = $this->extractBaseAmount($context);
        
        if (!$baseAmount) {
            return BusinessRuleResult::error(
                $this->name,
                'Base amount is required for coupon validation'
            );
        }

        $result = $this->applyDiscount($entity, $baseAmount, $context);
        
        return BusinessRuleResult::valid($this->name, [
            'discount_result' => $result->toArray(),
        ]);
    }

    /**
     * Kupon doğrulaması yap
     */
    private function validateCoupon(Money $baseAmount, array $context): BusinessRuleResult
    {
        $errors = [];
        
        // Tarih kontrolü
        $now = Carbon::now();
        
        if ($this->couponData['valid_from'] && $now->lt($this->couponData['valid_from'])) {
            $errors[] = 'Coupon is not yet valid';
        }
        
        if ($this->couponData['valid_until'] && $now->gt($this->couponData['valid_until'])) {
            $errors[] = 'Coupon has expired';
        }
        
        // Minimum tutar kontrolü
        if ($this->couponData['minimum_amount'] && $baseAmount->isLessThan($this->couponData['minimum_amount'])) {
            $errors[] = "Minimum amount required: {$this->couponData['minimum_amount']->getAmountInMajorUnit()}";
        }
        
        // Kullanım limiti kontrolü
        if ($this->couponData['usage_limit'] && $this->couponData['used_count'] >= $this->couponData['usage_limit']) {
            $errors[] = 'Coupon usage limit exceeded';
        }

        return empty($errors) 
            ? BusinessRuleResult::valid($this->name)
            : BusinessRuleResult::invalid($this->name, $errors);
    }

    /**
     * Context'ten kupon kodunu çıkar
     */
    private function extractCouponCode(array $context): ?string
    {
        return $context['coupon_code'] ?? $context['coupon'] ?? null;
    }

    /**
     * Context'ten base amount bilgisini çıkar
     */
    private function extractBaseAmount(array $context): ?Money
    {
        if (isset($context['base_amount']) && $context['base_amount'] instanceof Money) {
            return $context['base_amount'];
        }

        if (isset($context['amount']) && $context['amount'] instanceof Money) {
            return $context['amount'];
        }

        return null;
    }

    /**
     * Kupon verilerini validate et
     */
    private function validateCouponData(array $data): array
    {
        $validated = [
            'code' => $data['code'] ?? '',
            'type' => $data['type'] ?? 'percentage',
            'valid_from' => $data['valid_from'] ?? null,
            'valid_until' => $data['valid_until'] ?? null,
            'usage_limit' => $data['usage_limit'] ?? null,
            'minimum_amount' => $data['minimum_amount'] ?? null,
            'used_count' => $data['used_count'] ?? 0,
        ];

        if ($validated['type'] === 'percentage') {
            $validated['discount_percentage'] = max(0, min(100, $data['discount_percentage'] ?? 0));
        } else {
            $validated['discount_amount'] = $data['discount_amount'] ?? Money::zero();
        }

        return $validated;
    }

    /**
     * Kupon kodunu getir
     */
    public function getCouponCode(): string
    {
        return $this->couponData['code'];
    }

    /**
     * Kupon tipini getir
     */
    public function getCouponType(): string
    {
        return $this->couponData['type'];
    }

    /**
     * İndirim yüzdesini getir (yüzde tipinde ise)
     */
    public function getDiscountPercentage(): float
    {
        return $this->couponData['discount_percentage'] ?? 0.0;
    }

    /**
     * İndirim tutarını getir (sabit tipinde ise)
     */
    public function getDiscountAmount(): ?Money
    {
        return $this->couponData['discount_amount'] ?? null;
    }

    /**
     * Kupon kullanım sayısını artır
     */
    public function incrementUsageCount(): void
    {
        $this->couponData['used_count']++;
        $this->setMetadataValue('coupon_data', $this->couponData);
    }

    /**
     * Kupon geçerli mi kontrol et
     */
    public function isValid(): bool
    {
        $now = Carbon::now();
        
        if ($this->couponData['valid_from'] && $now->lt($this->couponData['valid_from'])) {
            return false;
        }
        
        if ($this->couponData['valid_until'] && $now->gt($this->couponData['valid_until'])) {
            return false;
        }
        
        if ($this->couponData['usage_limit'] && $this->couponData['used_count'] >= $this->couponData['usage_limit']) {
            return false;
        }

        return true;
    }
}
