<?php

namespace App\Domain\Shared\Entities;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Contracts\DomainEventInterface;
use App\Domain\Shared\Traits\AggregateRoot as AggregateRootTrait;
use Carbon\Carbon;

/**
 * AggregateRoot
 * Tüm aggregate root'lar için temel sınıf
 */
abstract class AggregateRoot implements EntityInterface
{
    use AggregateRootTrait;

    protected ?int $id = null;
    protected Carbon $createdAt;
    protected Carbon $updatedAt;

    /**
     * Entity'nin ID'sini getir
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Entity'nin oluşturulma tarihini getir
     */
    public function getCreatedAt(): Carbon
    {
        return $this->createdAt;
    }

    /**
     * Entity'nin güncellenme tarihini getir
     */
    public function getUpdatedAt(): Carbon
    {
        return $this->updatedAt;
    }

    /**
     * Entity'nin eşitlik kontrolü
     */
    public function equals(EntityInterface $other): bool
    {
        if (!$other instanceof static) {
            return false;
        }

        if ($this->getId() === null || $other->getId() === null) {
            return false;
        }

        return $this->getId() === $other->getId();
    }

    /**
     * Entity'nin string temsilini getir
     */
    public function toString(): string
    {
        return static::class . ':' . ($this->getId() ?? 'new');
    }

    /**
     * Entity'nin array temsilini getir
     */
    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'created_at' => $this->getCreatedAt()->toISOString(),
            'updated_at' => $this->getUpdatedAt()->toISOString(),
        ];
    }

    /**
     * Entity'nin JSON temsilini getir
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    /**
     * Entity'nin hash'ini getir
     */
    public function getHash(): string
    {
        return md5($this->toString());
    }

    /**
     * Entity'nin yaşını getir (saniye)
     */
    public function getAge(): int
    {
        return $this->createdAt->diffInSeconds(Carbon::now());
    }

    /**
     * Entity son güncellemeden bu yana geçen süreyi getir (saniye)
     */
    public function getTimeSinceLastUpdate(): int
    {
        return $this->updatedAt->diffInSeconds(Carbon::now());
    }

    /**
     * Entity yeni mi kontrol et
     */
    public function isNew(): bool
    {
        return $this->getId() === null;
    }

    /**
     * Entity'yi güncelle
     */
    protected function touch(): void
    {
        $this->updatedAt = Carbon::now();
    }

    /**
     * Domain event kaydet
     */
    protected function recordEvent(DomainEventInterface $event): void
    {
        $this->recordDomainEvent($event);
    }

    /**
     * Entity'nin debug bilgilerini getir
     */
    public function getDebugInfo(): array
    {
        return [
            'class' => static::class,
            'id' => $this->getId(),
            'is_new' => $this->isNew(),
            'age_seconds' => $this->getAge(),
            'time_since_last_update_seconds' => $this->getTimeSinceLastUpdate(),
            'recorded_events_count' => count($this->getRecordedEvents()),
            'created_at' => $this->getCreatedAt()->toISOString(),
            'updated_at' => $this->getUpdatedAt()->toISOString(),
        ];
    }

    /**
     * Magic method for string conversion
     */
    public function __toString(): string
    {
        return $this->toString();
    }
}
