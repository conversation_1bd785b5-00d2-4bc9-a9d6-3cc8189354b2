<?php

namespace App\Domain\Shared\Services;

use App\Domain\Shared\Contracts\EntityInterface;
use App\Domain\Shared\Specifications\SpecificationInterface;
use Illuminate\Support\Facades\Log;

/**
 * BusinessRuleEngine
 * İş kurallarını yöneten domain service
 */
class BusinessRuleEngine extends BaseDomainService
{
    private array $rules = [];
    private array $ruleGroups = [];
    private array $executionHistory = [];

    protected function initialize(): void
    {
        $this->name = 'business_rule_engine';
        $this->version = '1.0.0';
        $this->description = 'Business rule evaluation and execution engine';
        $this->supportedOperations = [
            'register_rule',
            'unregister_rule',
            'evaluate_rule',
            'evaluate_rules',
            'execute_rule',
            'execute_rules',
            'get_applicable_rules',
            'validate_entity',
        ];
    }

    /**
     * İş kuralı kaydet
     */
    public function registerRule(string $ruleId, BusinessRule $rule): void
    {
        $this->rules[$ruleId] = $rule;
        
        // Grup'a ekle
        $group = $rule->getGroup();
        if ($group) {
            if (!isset($this->ruleGroups[$group])) {
                $this->ruleGroups[$group] = [];
            }
            $this->ruleGroups[$group][] = $ruleId;
        }

        $this->logOperation('register_rule', [
            'rule_id' => $ruleId,
            'rule_name' => $rule->getName(),
            'group' => $group,
        ]);
    }

    /**
     * İş kuralını kaldır
     */
    public function unregisterRule(string $ruleId): bool
    {
        if (!isset($this->rules[$ruleId])) {
            return false;
        }

        $rule = $this->rules[$ruleId];
        $group = $rule->getGroup();

        unset($this->rules[$ruleId]);

        // Gruptan kaldır
        if ($group && isset($this->ruleGroups[$group])) {
            $this->ruleGroups[$group] = array_filter(
                $this->ruleGroups[$group],
                fn($id) => $id !== $ruleId
            );
        }

        $this->logOperation('unregister_rule', ['rule_id' => $ruleId]);
        return true;
    }

    /**
     * Tek kural değerlendir
     */
    public function evaluateRule(string $ruleId, EntityInterface $entity, array $context = []): BusinessRuleResult
    {
        if (!isset($this->rules[$ruleId])) {
            return BusinessRuleResult::notFound($ruleId);
        }

        $rule = $this->rules[$ruleId];
        
        try {
            $startTime = microtime(true);
            $result = $rule->evaluate($entity, $context);
            $duration = microtime(true) - $startTime;

            $this->recordExecution($ruleId, $result, $duration);
            
            $this->logOperation('evaluate_rule', [
                'rule_id' => $ruleId,
                'entity_type' => get_class($entity),
                'entity_id' => $entity->getId(),
                'result' => $result->isValid(),
                'duration_ms' => round($duration * 1000, 2),
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->logError('evaluate_rule', $e, [
                'rule_id' => $ruleId,
                'entity_type' => get_class($entity),
                'entity_id' => $entity->getId(),
            ]);

            return BusinessRuleResult::error($ruleId, $e->getMessage());
        }
    }

    /**
     * Birden fazla kural değerlendir
     */
    public function evaluateRules(array $ruleIds, EntityInterface $entity, array $context = []): BusinessRuleResults
    {
        $results = new BusinessRuleResults();

        foreach ($ruleIds as $ruleId) {
            $result = $this->evaluateRule($ruleId, $entity, $context);
            $results->addResult($ruleId, $result);
        }

        return $results;
    }

    /**
     * Grup kurallarını değerlendir
     */
    public function evaluateRuleGroup(string $group, EntityInterface $entity, array $context = []): BusinessRuleResults
    {
        $ruleIds = $this->ruleGroups[$group] ?? [];
        return $this->evaluateRules($ruleIds, $entity, $context);
    }

    /**
     * Entity için geçerli kuralları bul
     */
    public function getApplicableRules(EntityInterface $entity, array $context = []): array
    {
        $applicableRules = [];

        foreach ($this->rules as $ruleId => $rule) {
            if ($rule->isApplicable($entity, $context)) {
                $applicableRules[$ruleId] = $rule;
            }
        }

        return $applicableRules;
    }

    /**
     * Entity'yi tüm geçerli kurallara göre validate et
     */
    public function validateEntity(EntityInterface $entity, array $context = []): BusinessRuleResults
    {
        $applicableRules = $this->getApplicableRules($entity, $context);
        $ruleIds = array_keys($applicableRules);
        
        return $this->evaluateRules($ruleIds, $entity, $context);
    }

    /**
     * Kural çalıştır (side effect'ler ile)
     */
    public function executeRule(string $ruleId, EntityInterface $entity, array $context = []): BusinessRuleResult
    {
        $result = $this->evaluateRule($ruleId, $entity, $context);

        if ($result->isValid() && isset($this->rules[$ruleId])) {
            $rule = $this->rules[$ruleId];
            
            try {
                $rule->execute($entity, $context);
                $result->markAsExecuted();
                
                $this->logOperation('execute_rule', [
                    'rule_id' => $ruleId,
                    'entity_type' => get_class($entity),
                    'entity_id' => $entity->getId(),
                ]);

            } catch (\Exception $e) {
                $this->logError('execute_rule', $e, [
                    'rule_id' => $ruleId,
                    'entity_type' => get_class($entity),
                    'entity_id' => $entity->getId(),
                ]);

                $result->addError('Execution failed: ' . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * Birden fazla kural çalıştır
     */
    public function executeRules(array $ruleIds, EntityInterface $entity, array $context = []): BusinessRuleResults
    {
        $results = new BusinessRuleResults();

        foreach ($ruleIds as $ruleId) {
            $result = $this->executeRule($ruleId, $entity, $context);
            $results->addResult($ruleId, $result);
        }

        return $results;
    }

    /**
     * Tüm kuralları getir
     */
    public function getAllRules(): array
    {
        return $this->rules;
    }

    /**
     * Grup kurallarını getir
     */
    public function getRulesByGroup(string $group): array
    {
        $ruleIds = $this->ruleGroups[$group] ?? [];
        return array_intersect_key($this->rules, array_flip($ruleIds));
    }

    /**
     * Kural var mı kontrol et
     */
    public function hasRule(string $ruleId): bool
    {
        return isset($this->rules[$ruleId]);
    }

    /**
     * Grup var mı kontrol et
     */
    public function hasRuleGroup(string $group): bool
    {
        return isset($this->ruleGroups[$group]) && !empty($this->ruleGroups[$group]);
    }

    /**
     * Çalıştırma geçmişini kaydet
     */
    private function recordExecution(string $ruleId, BusinessRuleResult $result, float $duration): void
    {
        $this->executionHistory[] = [
            'rule_id' => $ruleId,
            'timestamp' => now(),
            'duration' => $duration,
            'result' => $result->isValid(),
            'errors' => $result->getErrors(),
        ];

        // Son 1000 kaydı tut
        if (count($this->executionHistory) > 1000) {
            $this->executionHistory = array_slice($this->executionHistory, -1000);
        }
    }

    /**
     * İstatistikleri getir
     */
    public function getStatistics(): array
    {
        $baseStats = parent::getStatistics();
        
        return array_merge($baseStats, [
            'total_rules' => count($this->rules),
            'total_groups' => count($this->ruleGroups),
            'execution_history_count' => count($this->executionHistory),
            'average_execution_time' => $this->getAverageExecutionTime(),
            'success_rate' => $this->getSuccessRate(),
        ]);
    }

    /**
     * Ortalama çalıştırma süresini getir
     */
    private function getAverageExecutionTime(): float
    {
        if (empty($this->executionHistory)) {
            return 0.0;
        }

        $totalDuration = array_sum(array_column($this->executionHistory, 'duration'));
        return $totalDuration / count($this->executionHistory);
    }

    /**
     * Başarı oranını getir
     */
    private function getSuccessRate(): float
    {
        if (empty($this->executionHistory)) {
            return 0.0;
        }

        $successCount = count(array_filter($this->executionHistory, fn($h) => $h['result']));
        return ($successCount / count($this->executionHistory)) * 100;
    }

    /**
     * Sağlık kontrolü
     */
    protected function performHealthCheck(): void
    {
        // Kuralların yüklendiğini kontrol et
        if (empty($this->rules)) {
            throw new \RuntimeException('No business rules loaded');
        }

        // Her kuralın geçerli olduğunu kontrol et
        foreach ($this->rules as $ruleId => $rule) {
            if (!$rule instanceof BusinessRule) {
                throw new \RuntimeException("Invalid rule type for rule: {$ruleId}");
            }
        }
    }
}
