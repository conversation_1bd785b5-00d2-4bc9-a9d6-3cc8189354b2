<?php

namespace App\Domain\Shared\Contracts;

/**
 * DomainServiceInterface
 * Tüm domain service'leri için temel interface
 */
interface DomainServiceInterface
{
    /**
     * Service'in adını getir
     */
    public function getName(): string;

    /**
     * Service'in versiyonunu getir
     */
    public function getVersion(): string;

    /**
     * Service'in açıklamasını getir
     */
    public function getDescription(): string;

    /**
     * Service'in desteklediği operasyonları getir
     */
    public function getSupportedOperations(): array;

    /**
     * Service'in sağlık durumunu kontrol et
     */
    public function healthCheck(): bool;
}
