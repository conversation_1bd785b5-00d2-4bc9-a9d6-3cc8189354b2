<?php

namespace App\Domain\Shared\Specifications;

use App\Domain\Shared\Contracts\EntityInterface;

/**
 * SpecificationInterface
 * Domain specification pattern interface
 */
interface SpecificationInterface
{
    /**
     * Specification'ı değerlendir
     */
    public function isSatisfiedBy(EntityInterface $entity): bool;

    /**
     * AND operatörü ile başka specification ile birleştir
     */
    public function and(SpecificationInterface $specification): SpecificationInterface;

    /**
     * OR operatörü ile başka specification ile birleştir
     */
    public function or(SpecificationInterface $specification): SpecificationInterface;

    /**
     * NOT operatörü ile ters çevir
     */
    public function not(): SpecificationInterface;

    /**
     * Specification'ın adını getir
     */
    public function getName(): string;

    /**
     * Specification'ın açıklamasını getir
     */
    public function getDescription(): string;

    /**
     * Specification'ın parametrelerini getir
     */
    public function getParameters(): array;

    /**
     * Specification'ın SQL query'sini getir (opsiyonel)
     */
    public function toSql(): ?string;

    /**
     * Specification'ın array temsilini getir
     */
    public function toArray(): array;
}
