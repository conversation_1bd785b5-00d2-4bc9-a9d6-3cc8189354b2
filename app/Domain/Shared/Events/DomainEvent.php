<?php

namespace App\Domain\Shared\Events;

use Carbon\Carbon;

interface DomainEvent
{
    /**
     * Event'in gerçekleştiği zaman
     */
    public function occurredOn(): Carbon;

    /**
     * Event'in adı
     */
    public function getEventName(): string;

    /**
     * Event'in verisi
     */
    public function getEventData(): array;

    /**
     * Aggregate'in ID'si
     */
    public function getAggregateId(): string;

    /**
     * Aggregate'in tipi
     */
    public function getAggregateType(): string;
}
