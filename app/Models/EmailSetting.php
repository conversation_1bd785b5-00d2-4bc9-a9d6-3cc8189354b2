<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EmailSetting extends Model
{
    protected $fillable = [
        'from_name',
        'from_email',
        'reply_to_email',
        'smtp_host',
        'smtp_port',
        'smtp_username',
        'smtp_password',
        'smtp_encryption',
        'notification_settings',
        // Mailchimp ayarları
        'mailchimp_api_key',
        'mailchimp_list_id',
        'mailchimp_enabled',
        // Mailtrap ayarları
        'mailtrap_enabled',
        'mailtrap_inbox_id',
        'mailtrap_api_token',
    ];

    protected $casts = [
        'smtp_port' => 'integer',
        'notification_settings' => 'array',
        'mailchimp_enabled' => 'boolean',
        'mailtrap_enabled' => 'boolean',
    ];

    /**
     * Get the default settings.
     */
    public static function getDefault()
    {
        return static::firstOrCreate(
            ['id' => 1],
            [
                'from_name' => config('mail.from.name'),
                'from_email' => config('mail.from.address'),
                'reply_to_email' => config('mail.from.address'),
                'smtp_host' => config('mail.mailers.smtp.host'),
                'smtp_port' => config('mail.mailers.smtp.port'),
                'smtp_username' => config('mail.mailers.smtp.username'),
                'smtp_password' => config('mail.mailers.smtp.password'),
                'smtp_encryption' => config('mail.mailers.smtp.encryption'),
                'notification_settings' => [
                    'welcome' => true,
                    'order_confirmation' => true,
                    'order_status' => true,
                    'password_reset' => true,
                ],
                'mailchimp_api_key' => env('MAILCHIMP_APIKEY', ''),
                'mailchimp_list_id' => env('MAILCHIMP_LIST_ID', ''),
                'mailchimp_enabled' => false,
                'mailtrap_enabled' => false,
                'mailtrap_inbox_id' => env('MAILTRAP_INBOX_ID', ''),
                'mailtrap_api_token' => env('MAILTRAP_API_TOKEN', ''),
            ]
        );
    }

    /**
     * Apply these settings to the Laravel config.
     */
    public function applyToConfig()
    {
        config([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => $this->smtp_host,
            'mail.mailers.smtp.port' => $this->smtp_port,
            'mail.mailers.smtp.username' => $this->smtp_username,
            'mail.mailers.smtp.password' => $this->smtp_password,
            'mail.mailers.smtp.encryption' => $this->smtp_encryption,
            'mail.from.address' => $this->from_email,
            'mail.from.name' => $this->from_name,
        ]);

        // Mailchimp ayarlarını yapılandır
        if ($this->mailchimp_enabled && $this->mailchimp_api_key && $this->mailchimp_list_id) {
            config([
                'newsletter.apiKey' => $this->mailchimp_api_key,
                'newsletter.lists.subscribers.id' => $this->mailchimp_list_id,
            ]);
        }

        // Mailtrap ayarlarını yapılandır
        if ($this->mailtrap_enabled) {
            config([
                'mail.mailers.smtp.host' => 'smtp.mailtrap.io',
                'mail.mailers.smtp.port' => 2525,
                'mail.mailers.smtp.username' => $this->smtp_username ?: $this->mailtrap_inbox_id,
                'mail.mailers.smtp.password' => $this->smtp_password ?: $this->mailtrap_api_token,
                'mail.mailers.smtp.encryption' => 'tls',
            ]);
        }

        return $this;
    }

    /**
     * Check if a notification type is enabled.
     */
    public function isNotificationEnabled($type)
    {
        $settings = $this->notification_settings ?? [];
        return $settings[$type] ?? true;
    }
}
