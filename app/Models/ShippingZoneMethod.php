<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingZoneMethod extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'zone_id',
        'method_id',
        'cost',
        'cost_per_order_percent',
        'cost_per_weight',
        'cost_per_desi',
        'min_order_amount',
        'max_order_amount',
        'min_weight',
        'max_weight',
        'min_desi',
        'max_desi',
        'is_free_shipping',
        'free_shipping_min_amount',
        'estimated_delivery_days',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'cost' => 'decimal:2',
        'cost_per_order_percent' => 'decimal:2',
        'cost_per_weight' => 'decimal:2',
        'cost_per_desi' => 'decimal:2',
        'min_order_amount' => 'decimal:2',
        'max_order_amount' => 'decimal:2',
        'min_weight' => 'decimal:2',
        'max_weight' => 'decimal:2',
        'min_desi' => 'decimal:2',
        'max_desi' => 'decimal:2',
        'free_shipping_min_amount' => 'decimal:2',
        'is_free_shipping' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Bu kargo metodu ayarının ait olduğu bölge
     */
    public function zone()
    {
        return $this->belongsTo(ShippingZone::class, 'zone_id');
    }

    /**
     * Bu ayarın ait olduğu kargo metodu
     */
    public function method()
    {
        return $this->belongsTo(ShippingMethod::class, 'method_id');
    }

    /**
     * Bu kargo metodunun desi bazlı fiyatlandırma oranları
     */
    public function rates()
    {
        return $this->hasMany(ShippingZoneMethodRate::class, 'zone_method_id');
    }

    /**
     * Aktif desi bazlı fiyatlandırma oranları
     */
    public function activeRates()
    {
        return $this->rates()->where('is_active', true)->orderBy('min_desi');
    }

    /**
     * Belirli bir sipariş için kargo ücretini hesaplar
     *
     * @param float $orderAmount Sipariş tutarı
     * @param float|null $weight Sipariş ağırlığı
     * @param float|null $desi Sipariş desi değeri
     * @return float|null Kargo ücreti (null ise bu metod uygulanamaz)
     */
    public function calculateCost($orderAmount, $weight = null, $desi = null)
    {
        // Minimum sipariş tutarı kontrolü
        if ($this->min_order_amount > 0 && $orderAmount < $this->min_order_amount) {
            return null;
        }

        // Maksimum sipariş tutarı kontrolü
        if ($this->max_order_amount > 0 && $orderAmount > $this->max_order_amount) {
            return null;
        }

        // Ağırlık kontrolü
        if ($weight !== null && $weight > 0) {
            if ($this->min_weight > 0 && $weight < $this->min_weight) {
                return null;
            }

            if ($this->max_weight > 0 && $weight > $this->max_weight) {
                return null;
            }
        }

        // Desi kontrolü
        if ($desi !== null && $desi > 0) {
            if ($this->min_desi > 0 && $desi < $this->min_desi) {
                return null;
            }

            if ($this->max_desi > 0 && $desi > $this->max_desi) {
                return null;
            }
        }

        // Ücretsiz kargo kontrolü
        if ($this->is_free_shipping) {
            if ($this->free_shipping_min_amount === null || $orderAmount >= $this->free_shipping_min_amount) {
                return 0;
            }
        }

        // Temel ücret
        $cost = $this->cost;

        // Sipariş tutarına göre ek ücret
        if ($this->cost_per_order_percent > 0) {
            $cost += ($orderAmount * $this->cost_per_order_percent / 100);
        }

        // Ağırlığa göre ek ücret
        if ($this->cost_per_weight > 0 && $weight > 0) {
            $cost += ($weight * $this->cost_per_weight);
        }

        // Desi'ye göre ek ücret
        if ($this->cost_per_desi > 0 && $desi > 0) {
            $cost += ($desi * $this->cost_per_desi);
        }

        return round($cost, 2);
    }
}
