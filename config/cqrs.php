<?php

return [
    /*
    |--------------------------------------------------------------------------
    | CQRS Configuration
    |--------------------------------------------------------------------------
    |
    | Bu dosya CQRS (Command Query Responsibility Segregation) pattern'i için
    | gerekli konfigürasyonları içerir.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Command Handlers
    |--------------------------------------------------------------------------
    |
    | Command sınıfları ve bunları işleyecek handler'ların mapping'i
    |
    */
    'command_handlers' => [
        // Products
        \App\Application\Products\Commands\CreateProductCommand::class => \App\Application\Products\Handlers\CreateProductHandler::class,
        \App\Application\Products\Commands\UpdateProductCommand::class => \App\Application\Products\Handlers\UpdateProductHandler::class,
        \App\Application\Products\Commands\DeleteProductCommand::class => \App\Application\Products\Handlers\DeleteProductHandler::class,

        // Orders
        \App\Application\Orders\Commands\CreateOrderCommand::class => \App\Application\Orders\Handlers\CreateOrderHandler::class,
        \App\Application\Orders\Commands\UpdateOrderStatusCommand::class => \App\Application\Orders\Handlers\UpdateOrderStatusHandler::class,
        \App\Application\Orders\Commands\CancelOrderCommand::class => \App\Application\Orders\Handlers\CancelOrderHandler::class,

        // Cart
        \App\Application\Cart\Commands\AddItemToCartCommand::class => \App\Application\Cart\Handlers\AddItemToCartHandler::class,
        \App\Application\Cart\Commands\RemoveItemFromCartCommand::class => \App\Application\Cart\Handlers\RemoveItemFromCartHandler::class,
        \App\Application\Cart\Commands\UpdateItemQuantityCommand::class => \App\Application\Cart\Handlers\UpdateItemQuantityHandler::class,
        \App\Application\Cart\Commands\ClearCartCommand::class => \App\Application\Cart\Handlers\ClearCartHandler::class,

        // Categories
        \App\Application\Categories\Commands\CreateCategoryCommand::class => \App\Application\Categories\Handlers\CreateCategoryHandler::class,
        \App\Application\Categories\Commands\UpdateCategoryCommand::class => \App\Application\Categories\Handlers\UpdateCategoryHandler::class,
        \App\Application\Categories\Commands\DeleteCategoryCommand::class => \App\Application\Categories\Handlers\DeleteCategoryHandler::class,

        // Users
        \App\Application\Users\Commands\CreateUserCommand::class => \App\Application\Users\Handlers\CreateUserHandler::class,
        \App\Application\Users\Commands\UpdateUserCommand::class => \App\Application\Users\Handlers\UpdateUserHandler::class,
        \App\Application\Users\Commands\DeleteUserCommand::class => \App\Application\Users\Handlers\DeleteUserHandler::class,

        // Payment
        \App\Application\Payment\Commands\ProcessPaymentCommand::class => \App\Application\Payment\Handlers\ProcessPaymentHandler::class,
        \App\Application\Payment\Commands\RefundPaymentCommand::class => \App\Application\Payment\Handlers\RefundPaymentHandler::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Query Handlers
    |--------------------------------------------------------------------------
    |
    | Query sınıfları ve bunları işleyecek handler'ların mapping'i
    |
    */
    'query_handlers' => [
        // Products
        \App\Application\Products\Queries\GetProductQuery::class => \App\Application\Products\Handlers\GetProductHandler::class,
        \App\Application\Products\Queries\GetProductsQuery::class => \App\Application\Products\Handlers\GetProductsHandler::class,

        // Orders
        \App\Application\Orders\Queries\GetOrderQuery::class => \App\Application\Orders\Handlers\GetOrderHandler::class,
        \App\Application\Orders\Queries\GetOrdersQuery::class => \App\Application\Orders\Handlers\GetOrdersHandler::class,

        // Cart
        \App\Application\Cart\Queries\GetCartQuery::class => \App\Application\Cart\Handlers\GetCartHandler::class,
        \App\Application\Cart\Queries\GetCartItemsQuery::class => \App\Application\Cart\Handlers\GetCartItemsHandler::class,

        // Categories
        \App\Application\Categories\Queries\GetCategoryQuery::class => \App\Application\Categories\Handlers\GetCategoryHandler::class,
        \App\Application\Categories\Queries\GetCategoriesQuery::class => \App\Application\Categories\Handlers\GetCategoriesHandler::class,

        // Users
        \App\Application\Users\Queries\GetUserQuery::class => \App\Application\Users\Handlers\GetUserHandler::class,
        \App\Application\Users\Queries\GetUsersQuery::class => \App\Application\Users\Handlers\GetUsersHandler::class,

        // Payment
        \App\Application\Payment\Queries\GetPaymentQuery::class => \App\Application\Payment\Handlers\GetPaymentHandler::class,
        \App\Application\Payment\Queries\GetPaymentsQuery::class => \App\Application\Payment\Handlers\GetPaymentsHandler::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Middleware Configuration
    |--------------------------------------------------------------------------
    |
    | CQRS middleware'lerinin konfigürasyonu
    |
    */
    'middlewares' => [
        'validation' => [
            'enabled' => env('CQRS_VALIDATION_ENABLED', true),
        ],
        'logging' => [
            'enabled' => env('CQRS_LOGGING_ENABLED', true),
        ],
        'performance' => [
            'enabled' => env('CQRS_PERFORMANCE_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Performance monitoring konfigürasyonu
    |
    */
    'performance' => [
        'thresholds' => [
            'command' => [
                'warning' => env('CQRS_COMMAND_WARNING_THRESHOLD', 1000), // ms
                'critical' => env('CQRS_COMMAND_CRITICAL_THRESHOLD', 5000), // ms
            ],
            'query' => [
                'warning' => env('CQRS_QUERY_WARNING_THRESHOLD', 500), // ms
                'critical' => env('CQRS_QUERY_CRITICAL_THRESHOLD', 2000), // ms
            ],
        ],
        'metrics' => [
            'enabled' => env('CQRS_METRICS_ENABLED', false),
            'driver' => env('CQRS_METRICS_DRIVER', 'log'), // log, prometheus, statsd
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Query caching konfigürasyonu
    |
    */
    'cache' => [
        'enabled' => env('CQRS_CACHE_ENABLED', true),
        'default_ttl' => env('CQRS_CACHE_DEFAULT_TTL', 3600), // saniye
        'prefix' => env('CQRS_CACHE_PREFIX', 'cqrs'),
        'tags' => [
            'enabled' => env('CQRS_CACHE_TAGS_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Event Configuration
    |--------------------------------------------------------------------------
    |
    | Domain event konfigürasyonu
    |
    */
    'events' => [
        'enabled' => env('CQRS_EVENTS_ENABLED', true),
        'async' => env('CQRS_EVENTS_ASYNC', false),
        'queue' => env('CQRS_EVENTS_QUEUE', 'default'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Read/Write database ayrımı konfigürasyonu
    |
    */
    'database' => [
        'read_write_separation' => env('CQRS_READ_WRITE_SEPARATION', false),
        'read_connection' => env('CQRS_READ_CONNECTION', 'mysql'),
        'write_connection' => env('CQRS_WRITE_CONNECTION', 'mysql'),
    ],
];
