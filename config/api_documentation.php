<?php

return [

    /*
    |--------------------------------------------------------------------------
    | API Documentation Configuration
    |--------------------------------------------------------------------------
    |
    | API documentation generation ve UI ayarları
    |
    */

    /*
    |--------------------------------------------------------------------------
    | General Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => env('API_DOCS_ENABLED', true),
    'auto_generate' => env('API_DOCS_AUTO_GENERATE', false),
    'cache_enabled' => env('API_DOCS_CACHE_ENABLED', true),
    'cache_ttl' => env('API_DOCS_CACHE_TTL', 3600), // seconds

    /*
    |--------------------------------------------------------------------------
    | Documentation Metadata
    |--------------------------------------------------------------------------
    */
    'info' => [
        'title' => env('API_DOCS_TITLE', 'ModularEcommerce API'),
        'description' => env('API_DOCS_DESCRIPTION', 'Comprehensive API for ModularEcommerce platform'),
        'version' => env('API_DOCS_VERSION', '1.0.0'),
        'terms_of_service' => env('API_DOCS_TERMS_URL', null),
        'contact' => [
            'name' => env('API_DOCS_CONTACT_NAME', 'API Support Team'),
            'email' => env('API_DOCS_CONTACT_EMAIL', '<EMAIL>'),
            'url' => env('API_DOCS_CONTACT_URL', 'https://modularecommerce.com/support'),
        ],
        'license' => [
            'name' => env('API_DOCS_LICENSE_NAME', 'MIT'),
            'url' => env('API_DOCS_LICENSE_URL', 'https://opensource.org/licenses/MIT'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Server Configuration
    |--------------------------------------------------------------------------
    */
    'servers' => [
        [
            'url' => env('API_DOCS_SERVER_URL', env('APP_URL') . '/api'),
            'description' => env('API_DOCS_SERVER_DESCRIPTION', 'Production API Server'),
        ],
        [
            'url' => env('API_DOCS_STAGING_URL', 'https://staging.modularecommerce.com/api'),
            'description' => 'Staging API Server',
        ],
        [
            'url' => env('API_DOCS_DEV_URL', 'http://localhost:8000/api'),
            'description' => 'Development API Server',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Output Formats
    |--------------------------------------------------------------------------
    */
    'formats' => [
        'openapi' => [
            'enabled' => env('API_DOCS_OPENAPI_ENABLED', true),
            'version' => env('API_DOCS_OPENAPI_VERSION', '3.0.3'),
        ],
        'swagger' => [
            'enabled' => env('API_DOCS_SWAGGER_ENABLED', true),
            'version' => env('API_DOCS_SWAGGER_VERSION', '2.0'),
        ],
        'postman' => [
            'enabled' => env('API_DOCS_POSTMAN_ENABLED', true),
            'version' => env('API_DOCS_POSTMAN_VERSION', '2.1.0'),
        ],
        'json' => [
            'enabled' => env('API_DOCS_JSON_ENABLED', true),
            'pretty_print' => env('API_DOCS_JSON_PRETTY', true),
        ],
        'yaml' => [
            'enabled' => env('API_DOCS_YAML_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | UI Configuration
    |--------------------------------------------------------------------------
    */
    'ui' => [
        'swagger_ui' => [
            'enabled' => env('API_DOCS_SWAGGER_UI_ENABLED', true),
            'theme' => env('API_DOCS_SWAGGER_UI_THEME', 'light'), // light, dark
            'layout' => env('API_DOCS_SWAGGER_UI_LAYOUT', 'sidebar'), // sidebar, standalone
            'try_it_out' => env('API_DOCS_SWAGGER_UI_TRY_IT_OUT', true),
            'show_extensions' => env('API_DOCS_SWAGGER_UI_SHOW_EXTENSIONS', false),
            'show_common_extensions' => env('API_DOCS_SWAGGER_UI_SHOW_COMMON_EXTENSIONS', true),
            'doc_expansion' => env('API_DOCS_SWAGGER_UI_DOC_EXPANSION', 'list'), // list, full, none
            'default_models_expand_depth' => env('API_DOCS_SWAGGER_UI_MODELS_EXPAND_DEPTH', 1),
            'default_model_expand_depth' => env('API_DOCS_SWAGGER_UI_MODEL_EXPAND_DEPTH', 1),
            'display_operation_id' => env('API_DOCS_SWAGGER_UI_DISPLAY_OPERATION_ID', false),
            'display_request_duration' => env('API_DOCS_SWAGGER_UI_DISPLAY_REQUEST_DURATION', true),
            'filter' => env('API_DOCS_SWAGGER_UI_FILTER', false),
            'max_displayed_tags' => env('API_DOCS_SWAGGER_UI_MAX_DISPLAYED_TAGS', -1),
        ],
        'redoc' => [
            'enabled' => env('API_DOCS_REDOC_ENABLED', true),
            'theme' => env('API_DOCS_REDOC_THEME', 'light'),
            'scroll_y_offset' => env('API_DOCS_REDOC_SCROLL_Y_OFFSET', 0),
            'hide_download_button' => env('API_DOCS_REDOC_HIDE_DOWNLOAD_BUTTON', false),
            'disable_search' => env('API_DOCS_REDOC_DISABLE_SEARCH', false),
            'expand_responses' => env('API_DOCS_REDOC_EXPAND_RESPONSES', 'all'), // all, 2xx, none
        ],
        'rapidoc' => [
            'enabled' => env('API_DOCS_RAPIDOC_ENABLED', true),
            'theme' => env('API_DOCS_RAPIDOC_THEME', 'light'),
            'layout' => env('API_DOCS_RAPIDOC_LAYOUT', 'row'), // row, column
            'render_style' => env('API_DOCS_RAPIDOC_RENDER_STYLE', 'read'), // read, view, focused
            'show_header' => env('API_DOCS_RAPIDOC_SHOW_HEADER', true),
            'allow_try' => env('API_DOCS_RAPIDOC_ALLOW_TRY', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Generation Settings
    |--------------------------------------------------------------------------
    */
    'generation' => [
        'include_deprecated' => env('API_DOCS_INCLUDE_DEPRECATED', true),
        'include_internal' => env('API_DOCS_INCLUDE_INTERNAL', false),
        'include_examples' => env('API_DOCS_INCLUDE_EXAMPLES', true),
        'include_request_examples' => env('API_DOCS_INCLUDE_REQUEST_EXAMPLES', true),
        'include_response_examples' => env('API_DOCS_INCLUDE_RESPONSE_EXAMPLES', true),
        'auto_detect_schemas' => env('API_DOCS_AUTO_DETECT_SCHEMAS', true),
        'auto_detect_parameters' => env('API_DOCS_AUTO_DETECT_PARAMETERS', true),
        'auto_detect_responses' => env('API_DOCS_AUTO_DETECT_RESPONSES', true),
        'scan_controllers' => env('API_DOCS_SCAN_CONTROLLERS', true),
        'scan_models' => env('API_DOCS_SCAN_MODELS', true),
        'scan_requests' => env('API_DOCS_SCAN_REQUESTS', true),
        'scan_resources' => env('API_DOCS_SCAN_RESOURCES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Versioning Settings
    |--------------------------------------------------------------------------
    */
    'versioning' => [
        'per_version' => env('API_DOCS_PER_VERSION', true),
        'include_deprecated_versions' => env('API_DOCS_INCLUDE_DEPRECATED_VERSIONS', true),
        'version_comparison' => env('API_DOCS_VERSION_COMPARISON', true),
        'migration_guides' => env('API_DOCS_MIGRATION_GUIDES', true),
        'changelog' => env('API_DOCS_CHANGELOG', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'schemes' => [
            'bearer' => [
                'type' => 'http',
                'scheme' => 'bearer',
                'bearer_format' => 'JWT',
                'description' => 'JWT Bearer token authentication',
            ],
            'api_key' => [
                'type' => 'apiKey',
                'in' => 'header',
                'name' => 'X-API-Key',
                'description' => 'API Key authentication',
            ],
            'oauth2' => [
                'type' => 'oauth2',
                'flows' => [
                    'authorization_code' => [
                        'authorization_url' => env('OAUTH2_AUTH_URL', '/oauth/authorize'),
                        'token_url' => env('OAUTH2_TOKEN_URL', '/oauth/token'),
                        'scopes' => [
                            'read' => 'Read access',
                            'write' => 'Write access',
                            'admin' => 'Admin access',
                        ],
                    ],
                ],
            ],
        ],
        'global_security' => [
            // Default security requirements
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tags Configuration
    |--------------------------------------------------------------------------
    */
    'tags' => [
        'auto_generate' => env('API_DOCS_AUTO_GENERATE_TAGS', true),
        'predefined' => [
            'Authentication' => [
                'name' => 'Authentication',
                'description' => 'User authentication and authorization endpoints',
            ],
            'Users' => [
                'name' => 'Users',
                'description' => 'User management endpoints',
            ],
            'Products' => [
                'name' => 'Products',
                'description' => 'Product catalog management',
            ],
            'Categories' => [
                'name' => 'Categories',
                'description' => 'Product category management',
            ],
            'Orders' => [
                'name' => 'Orders',
                'description' => 'Order processing and management',
            ],
            'Cart' => [
                'name' => 'Cart',
                'description' => 'Shopping cart operations',
            ],
            'Payment' => [
                'name' => 'Payment',
                'description' => 'Payment processing endpoints',
            ],
            'Shipping' => [
                'name' => 'Shipping',
                'description' => 'Shipping and delivery management',
            ],
            'Inventory' => [
                'name' => 'Inventory',
                'description' => 'Inventory management',
            ],
            'Reports' => [
                'name' => 'Reports',
                'description' => 'Analytics and reporting',
            ],
            'Admin' => [
                'name' => 'Admin',
                'description' => 'Administrative functions',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Output Paths
    |--------------------------------------------------------------------------
    */
    'paths' => [
        'output' => env('API_DOCS_OUTPUT_PATH', storage_path('api-docs')),
        'public' => env('API_DOCS_PUBLIC_PATH', public_path('docs')),
        'views' => env('API_DOCS_VIEWS_PATH', resource_path('views/api/docs')),
        'assets' => env('API_DOCS_ASSETS_PATH', public_path('docs/assets')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Route Settings
    |--------------------------------------------------------------------------
    */
    'routes' => [
        'enabled' => env('API_DOCS_ROUTES_ENABLED', true),
        'prefix' => env('API_DOCS_ROUTE_PREFIX', 'docs'),
        'middleware' => env('API_DOCS_MIDDLEWARE', 'web'),
        'domain' => env('API_DOCS_DOMAIN', null),
        'name_prefix' => env('API_DOCS_NAME_PREFIX', 'api.docs.'),
    ],

    /*
    |--------------------------------------------------------------------------
    | External Documentation
    |--------------------------------------------------------------------------
    */
    'external_docs' => [
        'description' => env('API_DOCS_EXTERNAL_DESCRIPTION', 'Additional Documentation'),
        'url' => env('API_DOCS_EXTERNAL_URL', 'https://modularecommerce.com/docs'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Settings
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'enabled' => env('API_DOCS_VALIDATION_ENABLED', true),
        'strict_mode' => env('API_DOCS_VALIDATION_STRICT', false),
        'validate_examples' => env('API_DOCS_VALIDATE_EXAMPLES', true),
        'validate_schemas' => env('API_DOCS_VALIDATE_SCHEMAS', true),
        'validate_responses' => env('API_DOCS_VALIDATE_RESPONSES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'lazy_loading' => env('API_DOCS_LAZY_LOADING', true),
        'compression' => env('API_DOCS_COMPRESSION', true),
        'minification' => env('API_DOCS_MINIFICATION', true),
        'cdn_assets' => env('API_DOCS_CDN_ASSETS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Settings
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_mode' => env('API_DOCS_DEBUG', false),
        'show_errors' => env('API_DOCS_SHOW_ERRORS', false),
        'log_generation' => env('API_DOCS_LOG_GENERATION', true),
        'profiling' => env('API_DOCS_PROFILING', false),
    ],

];
