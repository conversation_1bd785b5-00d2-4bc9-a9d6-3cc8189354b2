<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Testing Configuration
    |--------------------------------------------------------------------------
    |
    | Test environment için configuration ayarları
    |
    */

    /*
    |--------------------------------------------------------------------------
    | General Testing Settings
    |--------------------------------------------------------------------------
    */
    'parallel' => env('TESTING_PARALLEL', false),
    'coverage_enabled' => env('TESTING_COVERAGE_ENABLED', true),
    'mock_external_services' => env('TESTING_MOCK_EXTERNAL_SERVICES', true),
    'cleanup_after_test' => env('TESTING_CLEANUP_AFTER_TEST', true),
    'strict_mode' => env('TESTING_STRICT_MODE', true),

    /*
    |--------------------------------------------------------------------------
    | Database Testing Settings
    |--------------------------------------------------------------------------
    */
    'database' => [
        'default_connection' => env('TESTING_DB_CONNECTION', 'sqlite'),
        'memory_database' => env('TESTING_DB_MEMORY', true),
        'seed_test_data' => env('TESTING_SEED_DATA', true),
        'cleanup_after_test' => env('TESTING_DB_CLEANUP', true),
        'transaction_tests' => env('TESTING_DB_TRANSACTIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | API Testing Settings
    |--------------------------------------------------------------------------
    */
    'api' => [
        'base_url' => env('TESTING_API_BASE_URL', '/api/v1'),
        'rate_limiting_disabled' => env('TESTING_API_RATE_LIMITING_DISABLED', true),
        'security_headers_disabled' => env('TESTING_API_SECURITY_DISABLED', true),
        'validation_cache_disabled' => env('TESTING_API_VALIDATION_CACHE_DISABLED', true),
        'default_headers' => [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
        'timeout' => env('TESTING_API_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Testing Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'enabled' => env('TESTING_PERFORMANCE_ENABLED', true),
        'thresholds' => [
            'max_execution_time' => env('TESTING_MAX_EXECUTION_TIME', 1.0), // seconds
            'max_memory_usage' => env('TESTING_MAX_MEMORY_USAGE', 50), // MB
            'max_database_queries' => env('TESTING_MAX_DB_QUERIES', 10),
            'max_response_time' => env('TESTING_MAX_RESPONSE_TIME', 500), // milliseconds
        ],
        'monitoring' => [
            'query_logging' => env('TESTING_QUERY_LOGGING', true),
            'memory_tracking' => env('TESTING_MEMORY_TRACKING', true),
            'execution_time_tracking' => env('TESTING_EXECUTION_TIME_TRACKING', true),
        ],
        'reports' => [
            'enabled' => env('TESTING_PERFORMANCE_REPORTS', true),
            'output_path' => env('TESTING_PERFORMANCE_OUTPUT_PATH', 'tests/results/performance'),
            'format' => env('TESTING_PERFORMANCE_FORMAT', 'json'), // json, xml, html
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Testing Settings
    |--------------------------------------------------------------------------
    */
    'integration' => [
        'external_services' => [
            'payment_gateway' => [
                'enabled' => env('TESTING_PAYMENT_GATEWAY_ENABLED', false),
                'test_mode' => env('TESTING_PAYMENT_GATEWAY_TEST_MODE', true),
                'mock_responses' => env('TESTING_PAYMENT_GATEWAY_MOCK', true),
            ],
            'shipping_service' => [
                'enabled' => env('TESTING_SHIPPING_SERVICE_ENABLED', false),
                'test_mode' => env('TESTING_SHIPPING_SERVICE_TEST_MODE', true),
                'mock_responses' => env('TESTING_SHIPPING_SERVICE_MOCK', true),
            ],
            'email_service' => [
                'enabled' => env('TESTING_EMAIL_SERVICE_ENABLED', false),
                'test_mode' => env('TESTING_EMAIL_SERVICE_TEST_MODE', true),
                'mock_responses' => env('TESTING_EMAIL_SERVICE_MOCK', true),
            ],
        ],
        'queue_testing' => [
            'enabled' => env('TESTING_QUEUE_ENABLED', true),
            'connection' => env('TESTING_QUEUE_CONNECTION', 'sync'),
            'process_jobs' => env('TESTING_QUEUE_PROCESS_JOBS', false),
        ],
        'event_testing' => [
            'enabled' => env('TESTING_EVENT_ENABLED', true),
            'fake_events' => env('TESTING_EVENT_FAKE', false),
            'track_listeners' => env('TESTING_EVENT_TRACK_LISTENERS', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Browser Testing Settings
    |--------------------------------------------------------------------------
    */
    'browser' => [
        'enabled' => env('TESTING_BROWSER_ENABLED', false),
        'driver' => env('TESTING_BROWSER_DRIVER', 'chrome'),
        'headless' => env('TESTING_BROWSER_HEADLESS', true),
        'window_size' => [
            'width' => env('TESTING_BROWSER_WIDTH', 1920),
            'height' => env('TESTING_BROWSER_HEIGHT', 1080),
        ],
        'screenshots' => [
            'enabled' => env('TESTING_BROWSER_SCREENSHOTS', true),
            'on_failure' => env('TESTING_BROWSER_SCREENSHOTS_ON_FAILURE', true),
            'path' => env('TESTING_BROWSER_SCREENSHOTS_PATH', 'tests/Browser/screenshots'),
        ],
        'timeout' => env('TESTING_BROWSER_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Test Data Settings
    |--------------------------------------------------------------------------
    */
    'data' => [
        'factories' => [
            'enabled' => env('TESTING_FACTORIES_ENABLED', true),
            'cache_enabled' => env('TESTING_FACTORIES_CACHE', false),
            'cleanup_after_test' => env('TESTING_FACTORIES_CLEANUP', true),
        ],
        'builders' => [
            'enabled' => env('TESTING_BUILDERS_ENABLED', true),
            'validation_enabled' => env('TESTING_BUILDERS_VALIDATION', true),
        ],
        'seeders' => [
            'enabled' => env('TESTING_SEEDERS_ENABLED', true),
            'test_seeder_class' => env('TESTING_SEEDER_CLASS', 'TestSeeder'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Mocking Settings
    |--------------------------------------------------------------------------
    */
    'mocking' => [
        'http_requests' => [
            'enabled' => env('TESTING_MOCK_HTTP', true),
            'fake_responses' => env('TESTING_MOCK_HTTP_FAKE', true),
        ],
        'file_system' => [
            'enabled' => env('TESTING_MOCK_FILESYSTEM', true),
            'fake_disks' => ['public', 's3'],
        ],
        'mail' => [
            'enabled' => env('TESTING_MOCK_MAIL', true),
            'fake_mail' => env('TESTING_MOCK_MAIL_FAKE', true),
        ],
        'notifications' => [
            'enabled' => env('TESTING_MOCK_NOTIFICATIONS', true),
            'fake_notifications' => env('TESTING_MOCK_NOTIFICATIONS_FAKE', true),
        ],
        'cache' => [
            'enabled' => env('TESTING_MOCK_CACHE', false),
            'driver' => env('TESTING_CACHE_DRIVER', 'array'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('TESTING_LOGGING_ENABLED', true),
        'level' => env('TESTING_LOG_LEVEL', 'debug'),
        'channels' => [
            'test' => [
                'driver' => 'single',
                'path' => storage_path('logs/testing.log'),
                'level' => 'debug',
            ],
            'performance' => [
                'driver' => 'single',
                'path' => storage_path('logs/performance.log'),
                'level' => 'info',
            ],
        ],
        'query_logging' => env('TESTING_QUERY_LOGGING', true),
        'error_logging' => env('TESTING_ERROR_LOGGING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Coverage Settings
    |--------------------------------------------------------------------------
    */
    'coverage' => [
        'enabled' => env('TESTING_COVERAGE_ENABLED', true),
        'driver' => env('TESTING_COVERAGE_DRIVER', 'xdebug'), // xdebug, pcov
        'output_formats' => [
            'html' => env('TESTING_COVERAGE_HTML', true),
            'clover' => env('TESTING_COVERAGE_CLOVER', true),
            'text' => env('TESTING_COVERAGE_TEXT', false),
        ],
        'output_paths' => [
            'html' => 'tests/coverage/html',
            'clover' => 'tests/coverage/clover.xml',
            'text' => 'tests/coverage/coverage.txt',
        ],
        'minimum_coverage' => env('TESTING_MINIMUM_COVERAGE', 80),
        'exclude_directories' => [
            'app/Console',
            'app/Exceptions',
            'app/Http/Middleware',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        'enabled' => env('TESTING_CLEANUP_ENABLED', true),
        'temp_files' => env('TESTING_CLEANUP_TEMP_FILES', true),
        'uploaded_files' => env('TESTING_CLEANUP_UPLOADED_FILES', true),
        'cache_files' => env('TESTING_CLEANUP_CACHE_FILES', true),
        'log_files' => env('TESTING_CLEANUP_LOG_FILES', false),
        'database_data' => env('TESTING_CLEANUP_DATABASE_DATA', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Parallel Testing Settings
    |--------------------------------------------------------------------------
    */
    'parallel_testing' => [
        'enabled' => env('TESTING_PARALLEL_ENABLED', false),
        'processes' => env('TESTING_PARALLEL_PROCESSES', 4),
        'database_template' => env('TESTING_PARALLEL_DB_TEMPLATE', 'testing_template'),
        'recreate_databases' => env('TESTING_PARALLEL_RECREATE_DB', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Test Settings
    |--------------------------------------------------------------------------
    */
    'custom' => [
        'clean_architecture_compliance' => env('TESTING_CLEAN_ARCHITECTURE_COMPLIANCE', true),
        'domain_event_testing' => env('TESTING_DOMAIN_EVENT_TESTING', true),
        'value_object_testing' => env('TESTING_VALUE_OBJECT_TESTING', true),
        'use_case_testing' => env('TESTING_USE_CASE_TESTING', true),
    ],

];
