<?php

require_once '/var/www/vendor/autoload.php';

$app = require_once '/var/www/bootstrap/app.php';

echo "Testing Laravel Redis connection...\n";

try {
    // Test Redis configuration
    $config = $app->make('config');
    echo "Redis Host from config: " . $config->get('database.redis.default.host') . "\n";
    echo "Redis Port from config: " . $config->get('database.redis.default.port') . "\n";
    echo "Redis Client from config: " . $config->get('database.redis.client') . "\n";
    
    // Test Redis connection through Laravel
    $redis = $app->make('redis');
    $result = $redis->ping();
    echo "Laravel Redis connection successful: " . $result . "\n";
    
} catch (Exception $e) {
    echo "Laravel Redis connection failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "Test completed.\n";
