import React, { useState } from "react";
import { Link, usePage } from "@inertiajs/react";
import { Toaster } from "react-hot-toast";

export default function AdminLayout({ children, title, user }) {
    const { auth } = usePage().props;
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const currentUser = user || auth?.user;
    return (
        <div className="min-h-screen bg-gray-100">
            <Toaster position="bottom-right" />

            {/* Header */}
            <header className="bg-white shadow">
                <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center">
                        <h1 className="text-xl font-bold text-gray-900">
                            Modüler E-Ticaret Yönetim Paneli
                        </h1>
                        <div className="flex items-center space-x-4">
                            <Link
                                href="/"
                                className="text-blue-600 hover:text-blue-800"
                            >
                                Ana Say<PERSON>
                            </Link>

                            {currentUser ? (
                                <div className="relative">
                                    <button
                                        onClick={() =>
                                            setDropdownOpen(!dropdownOpen)
                                        }
                                        className="flex items-center text-gray-700 hover:text-gray-900 focus:outline-none"
                                    >
                                        <span className="mr-1">
                                            {currentUser.name}
                                        </span>
                                        <svg
                                            className="w-4 h-4"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth="2"
                                                d="M19 9l-7 7-7-7"
                                            ></path>
                                        </svg>
                                    </button>

                                    {dropdownOpen && (
                                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                            <Link
                                                href="/profile"
                                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() =>
                                                    setDropdownOpen(false)
                                                }
                                            >
                                                Profil
                                            </Link>
                                            <Link
                                                href="/logout"
                                                method="post"
                                                as="button"
                                                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                onClick={() =>
                                                    setDropdownOpen(false)
                                                }
                                            >
                                                Çıkış Yap
                                            </Link>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex space-x-2">
                                    <Link
                                        href="/login"
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        Giriş Yap
                                    </Link>
                                    <span className="text-gray-400">|</span>
                                    <Link
                                        href="/register"
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        Kayıt Ol
                                    </Link>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </header>

            {/* Sidebar and Main Content */}
            <div className="flex">
                {/* Sidebar */}
                <aside className="w-64 bg-white shadow-md min-h-screen">
                    <nav className="p-4">
                        <ul className="space-y-2">
                            <li>
                                <Link
                                    href="/admin/dashboard"
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Dashboard
                                </Link>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">Ürünler</span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href="/admin/products"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Ürün Listesi
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/admin/products/create"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Yeni Ürün
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/admin/products/bulk-edit"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Toplu İşlemler
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/admin/attributes"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Ürün Özellikleri
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">
                                        Kategoriler
                                    </span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href="/admin/categories"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Kategori Listesi
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/admin/categories/create"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Yeni Kategori
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/admin/categories/tree"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Kategori Ağacı
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/admin/categories/order"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Kategori Sıralama
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">
                                        Siparişler
                                    </span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href="/admin/orders"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Sipariş Listesi
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">Kargo</span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href="/admin/shipping-companies"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Kargo Şirketleri
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">Ödeme</span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href="/admin/bank-accounts"
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Banka Hesapları
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li>
                                <Link
                                    href="/admin/users"
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Kullanıcılar
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/admin/roles"
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Roller ve İzinler
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/admin/coupons"
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Kuponlar
                                </Link>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">
                                        Pazarlama
                                    </span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href={route(
                                                    "admin.search-analytics.index"
                                                )}
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                Arama Analitiği
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li className="pt-4 mt-4 border-t border-gray-200">
                                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                                    Sistem
                                </h3>
                            </li>
                            <li>
                                <Link
                                    href="/admin/modules"
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Modül Yönetimi
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={route("admin.cache.index")}
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Cache Yönetimi
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href={route("admin.queue-monitor.index")}
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Queue Monitörü
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="/admin/locations"
                                    className="block p-2 rounded hover:bg-gray-200"
                                >
                                    Konum Yönetimi
                                </Link>
                            </li>
                            <li className="mb-1">
                                <div className="block p-2 rounded hover:bg-gray-200">
                                    <span className="font-medium">
                                        E-posta Yönetimi
                                    </span>
                                    <ul className="pl-4 mt-1 space-y-1">
                                        <li>
                                            <Link
                                                href={route(
                                                    "admin.email-templates.index"
                                                )}
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                E-posta Şablonları
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href={route(
                                                    "admin.email-settings.index"
                                                )}
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                E-posta Ayarları
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href={route(
                                                    "admin.email-logs.index"
                                                )}
                                                className="block p-1 rounded hover:bg-gray-100 text-sm"
                                            >
                                                E-posta Logları
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </aside>

                {/* Main Content */}
                <main className="flex-1 p-6">
                    {title && (
                        <h2 className="text-2xl font-bold mb-6">{title}</h2>
                    )}
                    {children}
                </main>
            </div>
        </div>
    );
}
