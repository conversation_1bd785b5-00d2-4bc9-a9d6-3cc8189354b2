import { useEffect, useState, useCallback, useRef } from 'react';
import realTimeClient from '../Services/RealTimeClient';
import { toast } from 'react-hot-toast';

/**
 * Real-time connection hook
 */
export function useRealTimeConnection() {
    const [isConnected, setIsConnected] = useState(false);
    const [connectionState, setConnectionState] = useState('disconnected');
    const [error, setError] = useState(null);

    useEffect(() => {
        const handleConnected = () => {
            setIsConnected(true);
            setConnectionState('connected');
            setError(null);
        };

        const handleDisconnected = () => {
            setIsConnected(false);
            setConnectionState('disconnected');
        };

        const handleError = (event) => {
            setError(event.detail);
            setConnectionState('error');
        };

        const handleStateChange = (event) => {
            setConnectionState(event.detail.currentState);
        };

        // Event listener'ları ekle
        window.addEventListener('realtime:connected', handleConnected);
        window.addEventListener('realtime:disconnected', handleDisconnected);
        window.addEventListener('realtime:error', handleError);
        window.addEventListener('realtime:state-change', handleStateChange);

        // Initial state
        setIsConnected(realTimeClient.isConnectionActive());

        return () => {
            window.removeEventListener('realtime:connected', handleConnected);
            window.removeEventListener('realtime:disconnected', handleDisconnected);
            window.removeEventListener('realtime:error', handleError);
            window.removeEventListener('realtime:state-change', handleStateChange);
        };
    }, []);

    const reconnect = useCallback(() => {
        realTimeClient.reconnect();
    }, []);

    const disconnect = useCallback(() => {
        realTimeClient.disconnect();
    }, []);

    return {
        isConnected,
        connectionState,
        error,
        reconnect,
        disconnect,
    };
}

/**
 * Product stock updates hook
 */
export function useProductStock(productId, options = {}) {
    const [stockData, setStockData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const channelRef = useRef(null);

    const {
        showToast = true,
        onStockUpdate = null,
        onLowStock = null,
        onOutOfStock = null,
    } = options;

    useEffect(() => {
        if (!productId) return;

        setIsLoading(true);

        const handleStockUpdate = (data) => {
            setStockData(data);
            setIsLoading(false);

            // Toast notification
            if (showToast) {
                if (data.is_out_of_stock) {
                    toast.error(`Ürün stokta kalmadı!`);
                } else if (data.is_low_stock) {
                    toast.warning(`Ürün stoku azaldı! Kalan: ${data.new_stock}`);
                } else if (data.stock_change > 0) {
                    toast.success(`Ürün stoku güncellendi: ${data.new_stock}`);
                }
            }

            // Custom callback'ler
            if (onStockUpdate) {
                onStockUpdate(data);
            }

            if (data.is_low_stock && onLowStock) {
                onLowStock(data);
            }

            if (data.is_out_of_stock && onOutOfStock) {
                onOutOfStock(data);
            }
        };

        // Channel'a subscribe ol
        channelRef.current = realTimeClient.listenToProductStock(productId, handleStockUpdate);

        return () => {
            if (channelRef.current) {
                realTimeClient.unsubscribe(`product.${productId}.stock`);
            }
        };
    }, [productId, showToast, onStockUpdate, onLowStock, onOutOfStock]);

    return {
        stockData,
        isLoading,
        isInStock: stockData?.is_in_stock ?? true,
        isLowStock: stockData?.is_low_stock ?? false,
        isOutOfStock: stockData?.is_out_of_stock ?? false,
        currentStock: stockData?.new_stock ?? 0,
    };
}

/**
 * Product price updates hook
 */
export function useProductPrice(productId, options = {}) {
    const [priceData, setPriceData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const channelRef = useRef(null);

    const {
        showToast = true,
        onPriceChange = null,
        onPriceIncrease = null,
        onPriceDecrease = null,
    } = options;

    useEffect(() => {
        if (!productId) return;

        setIsLoading(true);

        const handlePriceChange = (data) => {
            setPriceData(data);
            setIsLoading(false);

            // Toast notification
            if (showToast) {
                if (data.is_price_decrease) {
                    toast.success(`Fiyat düştü! Yeni fiyat: ${data.formatted_prices.new_price_formatted}`);
                } else if (data.is_price_increase) {
                    toast.info(`Fiyat güncellendi: ${data.formatted_prices.new_price_formatted}`);
                }
            }

            // Custom callback'ler
            if (onPriceChange) {
                onPriceChange(data);
            }

            if (data.is_price_increase && onPriceIncrease) {
                onPriceIncrease(data);
            }

            if (data.is_price_decrease && onPriceDecrease) {
                onPriceDecrease(data);
            }
        };

        // Channel'a subscribe ol
        channelRef.current = realTimeClient.listenToProductPrice(productId, handlePriceChange);

        return () => {
            if (channelRef.current) {
                realTimeClient.unsubscribe(`product.${productId}.price`);
            }
        };
    }, [productId, showToast, onPriceChange, onPriceIncrease, onPriceDecrease]);

    return {
        priceData,
        isLoading,
        currentPrice: priceData?.new_price ?? 0,
        oldPrice: priceData?.old_price ?? 0,
        priceChange: priceData?.price_change ?? 0,
        priceChangePercentage: priceData?.price_change_percentage ?? 0,
        isPriceIncrease: priceData?.is_price_increase ?? false,
        isPriceDecrease: priceData?.is_price_decrease ?? false,
        formattedPrice: priceData?.formatted_prices?.new_price_formatted ?? '',
    };
}

/**
 * Order status updates hook
 */
export function useOrderStatus(orderId, options = {}) {
    const [orderData, setOrderData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const channelRef = useRef(null);

    const {
        showToast = true,
        onStatusChange = null,
        onDelivered = null,
        onCancelled = null,
    } = options;

    useEffect(() => {
        if (!orderId) return;

        setIsLoading(true);

        const handleStatusChange = (data) => {
            setOrderData(data);
            setIsLoading(false);

            // Toast notification
            if (showToast) {
                const message = data.user_message?.message || `Sipariş durumu güncellendi: ${data.status_info?.display_name}`;
                
                if (data.new_status === 'delivered') {
                    toast.success(message);
                } else if (data.new_status === 'cancelled') {
                    toast.error(message);
                } else {
                    toast.info(message);
                }
            }

            // Custom callback'ler
            if (onStatusChange) {
                onStatusChange(data);
            }

            if (data.new_status === 'delivered' && onDelivered) {
                onDelivered(data);
            }

            if (data.new_status === 'cancelled' && onCancelled) {
                onCancelled(data);
            }
        };

        // Channel'a subscribe ol
        channelRef.current = realTimeClient.listenToOrderStatus(orderId, handleStatusChange);

        return () => {
            if (channelRef.current) {
                realTimeClient.unsubscribe(`order.${orderId}`);
            }
        };
    }, [orderId, showToast, onStatusChange, onDelivered, onCancelled]);

    return {
        orderData,
        isLoading,
        currentStatus: orderData?.new_status ?? '',
        statusInfo: orderData?.status_info ?? {},
        timeline: orderData?.timeline ?? {},
        userMessage: orderData?.user_message ?? {},
        isFinalStatus: orderData?.is_final_status ?? false,
        requiresAction: orderData?.requires_action ?? false,
    };
}

/**
 * User notifications hook
 */
export function useUserNotifications(userId, options = {}) {
    const [notifications, setNotifications] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const channelRef = useRef(null);

    const {
        showToast = true,
        maxNotifications = 50,
        onNewNotification = null,
    } = options;

    useEffect(() => {
        if (!userId) return;

        const handleNotification = (data) => {
            setNotifications(prev => {
                const updated = [data, ...prev].slice(0, maxNotifications);
                return updated;
            });

            setUnreadCount(prev => prev + 1);

            // Toast notification
            if (showToast) {
                toast.info(data.message || 'Yeni bildirim');
            }

            // Custom callback
            if (onNewNotification) {
                onNewNotification(data);
            }
        };

        // Channel'a subscribe ol
        channelRef.current = realTimeClient.listenToUserNotifications(userId, handleNotification);

        return () => {
            if (channelRef.current) {
                realTimeClient.unsubscribe(`user.${userId}`);
            }
        };
    }, [userId, showToast, maxNotifications, onNewNotification]);

    const markAsRead = useCallback((notificationId) => {
        setNotifications(prev => 
            prev.map(notification => 
                notification.id === notificationId 
                    ? { ...notification, read: true }
                    : notification
            )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
    }, []);

    const markAllAsRead = useCallback(() => {
        setNotifications(prev => 
            prev.map(notification => ({ ...notification, read: true }))
        );
        setUnreadCount(0);
    }, []);

    const clearNotifications = useCallback(() => {
        setNotifications([]);
        setUnreadCount(0);
    }, []);

    return {
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        clearNotifications,
    };
}

/**
 * Admin alerts hook
 */
export function useAdminAlerts(options = {}) {
    const [alerts, setAlerts] = useState([]);
    const [criticalCount, setCriticalCount] = useState(0);
    const channelRef = useRef(null);

    const {
        showToast = true,
        maxAlerts = 100,
        onCriticalAlert = null,
    } = options;

    useEffect(() => {
        const handleAlert = (data) => {
            setAlerts(prev => {
                const updated = [data, ...prev].slice(0, maxAlerts);
                return updated;
            });

            if (data.priority >= 8) {
                setCriticalCount(prev => prev + 1);
            }

            // Toast notification
            if (showToast) {
                if (data.priority >= 8) {
                    toast.error(data.message || 'Kritik uyarı!');
                } else {
                    toast.warning(data.message || 'Yeni uyarı');
                }
            }

            // Custom callback
            if (data.priority >= 8 && onCriticalAlert) {
                onCriticalAlert(data);
            }
        };

        // Channel'a subscribe ol
        channelRef.current = realTimeClient.listenToAdminAlerts(handleAlert);

        return () => {
            if (channelRef.current) {
                realTimeClient.unsubscribe('admin');
            }
        };
    }, [showToast, maxAlerts, onCriticalAlert]);

    const dismissAlert = useCallback((alertId) => {
        setAlerts(prev => prev.filter(alert => alert.id !== alertId));
    }, []);

    const clearAlerts = useCallback(() => {
        setAlerts([]);
        setCriticalCount(0);
    }, []);

    return {
        alerts,
        criticalCount,
        dismissAlert,
        clearAlerts,
    };
}
