import React, { useState, useEffect } from "react";
import { Head, Link, router } from "@inertiajs/react";
import { toast } from "react-hot-toast";
import MainLayout from "@/Layouts/MainLayout";
import { formatPrice } from "@/Utils/cartUtils";
import PaymentModal from "@/Components/PaymentModal";
import LocationFields from "@/Components/Location/LocationFields";
import AddressSelector from "@/Components/Location/AddressSelector";
import ShippingOptions from "@/Components/Checkout/ShippingOptions";
import { calculateCartDesi } from "@/cart-desi-calculator";

export default function Checkout({
    cart,
    bankAccounts,
    user,
    addresses = [],
    turkeyId,
}) {
    const [paymentMethod, setPaymentMethod] = useState("credit_card");
    const [selectedBankAccount, setSelectedBankAccount] = useState(
        bankAccounts && bankAccounts.length > 0 ? bankAccounts[0].id : ""
    );
    const [loading, setLoading] = useState(false);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [paymentHtmlContent, setPaymentHtmlContent] = useState("");
    const [paymentRedirectUrl, setPaymentRedirectUrl] = useState("");

    // Ödeme kesintisi durumu için state'ler
    const [paymentInterrupted, setPaymentInterrupted] = useState(false);
    const [currentOrderNumber, setCurrentOrderNumber] = useState(null);

    // Form verileri
    const [formData, setFormData] = useState({
        billing_name: user ? user.name : "",
        billing_email: user ? user.email : "",
        billing_phone: "",
        billing_address: "",
        billing_city: "",
        billing_state: "",
        billing_zipcode: "",
        billing_country: "Türkiye",
        billing_country_id: turkeyId || "",
        billing_state_id: "",
        billing_city_id: "",
        shipping_name: user ? user.name : "",
        shipping_email: user ? user.email : "",
        shipping_phone: "",
        shipping_address: "",
        shipping_city: "",
        shipping_state: "",
        shipping_zipcode: "",
        shipping_country: "Türkiye",
        shipping_country_id: turkeyId || "",
        shipping_state_id: "",
        shipping_city_id: "",
        notes: "",
        card_holder_name: "",
        card_number: "",
        expiry_month: "",
        expiry_year: "",
        cvc: "",
        use_3d_secure: "true", // String olarak "true" değeri
    });

    // Adres seçimi için state'ler
    const [selectedBillingAddress, setSelectedBillingAddress] = useState("");
    const [selectedShippingAddress, setSelectedShippingAddress] = useState("");
    const [useAddressSelection, setUseAddressSelection] = useState(
        user && addresses.length > 0
    );

    // Aynı adres checkbox'ı
    const [sameAddress, setSameAddress] = useState(true);

    // Kargo seçenekleri için state'ler
    const [selectedShippingMethod, setSelectedShippingMethod] = useState(null);
    const [shippingCost, setShippingCost] = useState(0);

    // Sepet desi değerini hesapla
    useEffect(() => {
        if (cart && cart.items && cart.items.length > 0) {
            calculateCartDesi(cart.items);
        }
    }, [cart]);

    // Form verilerini güncelle
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({
            ...prevData,
            [name]: value,
        }));
    };

    // Aynı adres checkbox'ı değiştiğinde
    useEffect(() => {
        if (sameAddress) {
            // Fatura adresindeki il ve ilçe isimlerini bul
            let billingStateName = formData.billing_state;
            let billingCityName = formData.billing_city;

            // Eğer isimler yoksa ve ID'ler varsa, select elementlerinden isimleri bul
            if (!billingStateName && formData.billing_state_id) {
                const stateElement = document.querySelector(
                    `#billing_state_select option[value="${formData.billing_state_id}"]`
                );
                if (stateElement) {
                    billingStateName = stateElement.textContent;
                }
            }

            if (!billingCityName && formData.billing_city_id) {
                const cityElement = document.querySelector(
                    `#billing_city_select option[value="${formData.billing_city_id}"]`
                );
                if (cityElement) {
                    billingCityName = cityElement.textContent;
                }
            }

            setFormData((prevData) => ({
                ...prevData,
                shipping_name: prevData.billing_name,
                shipping_email: prevData.billing_email,
                shipping_phone: prevData.billing_phone,
                shipping_address: prevData.billing_address,
                shipping_city: billingCityName || prevData.billing_city,
                shipping_state: billingStateName || prevData.billing_state,
                shipping_zipcode: prevData.billing_zipcode,
                shipping_country: prevData.billing_country,
                shipping_country_id: prevData.billing_country_id,
                shipping_state_id: prevData.billing_state_id,
                shipping_city_id: prevData.billing_city_id,
            }));

            // Aynı adres seçildiğinde, teslimat adresi seçimini de fatura adresi ile aynı yap
            if (useAddressSelection) {
                setSelectedShippingAddress(selectedBillingAddress);
            }
        }
    }, [
        sameAddress,
        formData.billing_name,
        formData.billing_email,
        formData.billing_phone,
        formData.billing_address,
        formData.billing_city,
        formData.billing_state,
        formData.billing_zipcode,
        formData.billing_country,
        formData.billing_country_id,
        formData.billing_state_id,
        formData.billing_city_id,
        selectedBillingAddress,
    ]);

    // Seçilen fatura adresi değiştiğinde
    useEffect(() => {
        if (selectedBillingAddress && useAddressSelection) {
            const address = addresses.find(
                (a) => a.id.toString() === selectedBillingAddress.toString()
            );
            if (address) {
                setFormData((prev) => ({
                    ...prev,
                    billing_name: address.name,
                    billing_phone: address.phone,
                    billing_address: address.address,
                    billing_city: address.city,
                    billing_state: address.state,
                    billing_zipcode: address.zip,
                    billing_country_id: address.country_id || turkeyId || "",
                    billing_state_id: address.state_id || "",
                    billing_city_id: address.city_id || "",
                }));

                // Aynı adres seçiliyse, teslimat adresini de güncelle
                if (sameAddress) {
                    setSelectedShippingAddress(selectedBillingAddress);
                }
            }
        }
    }, [selectedBillingAddress, useAddressSelection]);

    // Seçilen teslimat adresi değiştiğinde
    useEffect(() => {
        if (selectedShippingAddress && useAddressSelection && !sameAddress) {
            const address = addresses.find(
                (a) => a.id.toString() === selectedShippingAddress.toString()
            );
            if (address) {
                setFormData((prev) => ({
                    ...prev,
                    shipping_name: address.name,
                    shipping_phone: address.phone,
                    shipping_address: address.address,
                    shipping_city: address.city,
                    shipping_state: address.state,
                    shipping_zipcode: address.zip,
                    shipping_country_id: address.country_id || turkeyId || "",
                    shipping_state_id: address.state_id || "",
                    shipping_city_id: address.city_id || "",
                }));
            }
        }
    }, [selectedShippingAddress, useAddressSelection, sameAddress]);

    // Ödeme işlemi tamamlandığında
    const handlePaymentComplete = (orderNumber) => {
        if (paymentRedirectUrl) {
            window.location.href = paymentRedirectUrl;
        } else if (orderNumber) {
            // Sipariş numarası varsa başarılı sayfasına yönlendir
            window.location.href = `/checkout/success?order_number=${orderNumber}`;
        } else {
            setShowPaymentModal(false);
            toast.success("Ödeme işlemi tamamlandı!");
        }
    };

    // Popup kapatıldığında
    const handlePaymentModalClose = () => {
        console.log("Ödeme popup'ı kapatıldı");
        setShowPaymentModal(false);

        // Eğer ödeme tamamlanmadıysa ve sipariş numarası varsa
        if (currentOrderNumber) {
            console.log(
                "Ödeme kesintiye uğradı, sipariş numarası:",
                currentOrderNumber
            );

            // Sipariş durumunu kontrol et
            checkOrderStatus(currentOrderNumber);
        }
    };

    // Sipariş durumunu kontrol et
    const checkOrderStatus = (orderNumber) => {
        console.log("Sipariş durumu kontrol ediliyor:", orderNumber);

        fetch(`/api/check-order-status?order_number=${orderNumber}`)
            .then((response) => response.json())
            .then((data) => {
                console.log("Sipariş durumu yanıtı:", data);

                if (data.success) {
                    if (
                        data.status === "pending" ||
                        data.status === "payment_pending"
                    ) {
                        // Ödeme hala bekliyor, kullanıcıya bilgi ver
                        console.log(
                            "Ödeme bekliyor, kullanıcıya bilgi verilecek"
                        );
                        setPaymentInterrupted(true);
                    } else {
                        // Ödeme tamamlanmış veya iptal edilmiş
                        console.log("Ödeme tamamlanmış veya iptal edilmiş");
                        setPaymentInterrupted(false);
                        setCurrentOrderNumber(null);

                        if (
                            data.status === "processing" ||
                            data.status === "completed"
                        ) {
                            // Ödeme başarılı
                            toast.success("Ödeme işlemi başarıyla tamamlandı!");
                            // Başarılı sayfasına yönlendir
                            window.location.href = `/checkout/success?order_number=${orderNumber}`;
                        }
                    }
                } else {
                    console.error(
                        "Sipariş durumu kontrol edilirken hata:",
                        data.message
                    );
                    toast.error(
                        data.message ||
                            "Sipariş durumu kontrol edilirken bir hata oluştu."
                    );
                }
            })
            .catch((error) => {
                console.error("Sipariş durumu kontrol edilirken hata:", error);
                toast.error(
                    "Sipariş durumu kontrol edilirken bir hata oluştu."
                );
            });
    };

    // Ödemeye devam et
    const continuePayment = () => {
        console.log("Ödemeye devam ediliyor:", currentOrderNumber);
        setPaymentInterrupted(false);

        // Mevcut sipariş için ödeme sayfasını tekrar aç
        fetch(`/api/resume-payment?order_number=${currentOrderNumber}`)
            .then((response) => response.json())
            .then((data) => {
                console.log("Ödemeye devam yanıtı:", data);

                if (data.success) {
                    if (data.html_content) {
                        // HTML içeriğini ayarla
                        setPaymentHtmlContent(data.html_content);
                        // Modalı göster
                        setShowPaymentModal(true);
                    } else if (data.redirect_url) {
                        // Yönlendirme URL'si varsa
                        window.location.href = data.redirect_url;
                    }
                } else {
                    console.error(
                        "Ödemeye devam edilirken hata:",
                        data.message
                    );
                    toast.error(
                        data.message ||
                            "Ödemeye devam edilirken bir hata oluştu."
                    );
                }
            })
            .catch((error) => {
                console.error("Ödemeye devam edilirken hata:", error);
                toast.error("Ödemeye devam edilirken bir hata oluştu.");
            });
    };

    // Siparişi iptal et
    const cancelOrder = () => {
        console.log("Sipariş iptal ediliyor:", currentOrderNumber);

        fetch(`/api/cancel-order?order_number=${currentOrderNumber}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => response.json())
            .then((data) => {
                console.log("Sipariş iptal yanıtı:", data);

                if (data.success) {
                    setPaymentInterrupted(false);
                    setCurrentOrderNumber(null);
                    // Kullanıcıya bilgi ver
                    toast.success(data.message || "Siparişiniz iptal edildi");
                } else {
                    console.error(
                        "Sipariş iptal edilirken hata:",
                        data.message
                    );
                    toast.error(
                        data.message ||
                            "Sipariş iptal edilirken bir hata oluştu."
                    );
                }
            })
            .catch((error) => {
                console.error("Sipariş iptal edilirken hata:", error);
                toast.error("Sipariş iptal edilirken bir hata oluştu.");
            });
    };

    // Kargo metodu seçildiğinde
    const handleShippingMethodSelected = (method) => {
        console.log("Seçilen kargo metodu:", method);
        setSelectedShippingMethod(method);
        setShippingCost(method.cost);
    };

    // Ödeme formunu gönder
    const handleSubmit = (e) => {
        e.preventDefault();

        // Kredi kartı bilgilerini doğrula
        if (paymentMethod === "credit_card") {
            // Kart numarası kontrolü
            if (formData.card_number.length !== 16) {
                toast.error("Kart numarası 16 haneli olmalıdır.");
                return;
            }

            // Son kullanma ayı kontrolü
            if (
                formData.expiry_month.length !== 2 ||
                parseInt(formData.expiry_month) < 1 ||
                parseInt(formData.expiry_month) > 12
            ) {
                toast.error("Geçerli bir son kullanma ayı giriniz (01-12).");
                return;
            }

            // Son kullanma yılı kontrolü
            const currentYear = parseInt(
                new Date().getFullYear().toString().substr(-2)
            );
            if (
                formData.expiry_year.length !== 2 ||
                parseInt(formData.expiry_year) < currentYear
            ) {
                toast.error("Geçerli bir son kullanma yılı giriniz.");
                return;
            }

            // CVC kontrolü
            if (formData.cvc.length !== 3) {
                toast.error("CVC kodu 3 haneli olmalıdır.");
                return;
            }
        }

        // Banka transferi için banka hesabı seçilmiş mi kontrolü
        if (paymentMethod === "bank_transfer" && !selectedBankAccount) {
            toast.error("Lütfen bir banka hesabı seçiniz.");
            return;
        }

        setLoading(true);

        try {
            console.log("Ödeme işlemi başlatılıyor...");

            // Form verilerini hazırla
            let paymentData = { ...formData };

            // İl ve ilçe ID'lerinden isimlerini bul ve ekle
            if (paymentData.billing_state_id) {
                // Fatura ili için state adını bul
                const billingState = document.querySelector(
                    `#billing_state_select option[value="${paymentData.billing_state_id}"]`
                );
                if (billingState) {
                    paymentData.billing_state = billingState.textContent;
                }
            }

            if (paymentData.billing_city_id) {
                // Fatura ilçesi için city adını bul
                const billingCity = document.querySelector(
                    `#billing_city_select option[value="${paymentData.billing_city_id}"]`
                );
                if (billingCity) {
                    paymentData.billing_city = billingCity.textContent;
                }
            }

            // Aynı adres seçeneği işaretliyse, fatura adresi bilgilerini teslimat adresine kopyala
            if (sameAddress) {
                paymentData.shipping_state = paymentData.billing_state;
                paymentData.shipping_city = paymentData.billing_city;
            } else {
                // Farklı adres seçilmişse, teslimat adresi için il ve ilçe isimlerini bul
                if (paymentData.shipping_state_id) {
                    // Teslimat ili için state adını bul
                    const shippingState = document.querySelector(
                        `#shipping_state_select option[value="${paymentData.shipping_state_id}"]`
                    );
                    if (shippingState) {
                        paymentData.shipping_state = shippingState.textContent;
                    }
                }

                if (paymentData.shipping_city_id) {
                    // Teslimat ilçesi için city adını bul
                    const shippingCity = document.querySelector(
                        `#shipping_city_select option[value="${paymentData.shipping_city_id}"]`
                    );
                    if (shippingCity) {
                        paymentData.shipping_city = shippingCity.textContent;
                    }
                }
            }

            // Eğer hala teslimat ili ve ilçesi yoksa, hata oluşmasını önlemek için boş değer atama
            if (!paymentData.shipping_state) {
                console.warn(
                    "Teslimat ili bulunamadı, varsayılan değer atanıyor"
                );
                paymentData.shipping_state =
                    paymentData.billing_state || "Belirtilmemiş";
            }

            if (!paymentData.shipping_city) {
                console.warn(
                    "Teslimat ilçesi bulunamadı, varsayılan değer atanıyor"
                );
                paymentData.shipping_city =
                    paymentData.billing_city || "Belirtilmemiş";
            }

            console.log("İl ve ilçe isimleri eklendi:", paymentData);

            // Banka transferi için banka hesabı ID'sini ekle
            if (paymentMethod === "bank_transfer") {
                paymentData.bank_account_id = selectedBankAccount;
            }

            // Kargo metodu bilgilerini ekle
            if (selectedShippingMethod) {
                paymentData.shipping_method_id = selectedShippingMethod.id;
                paymentData.shipping_cost = selectedShippingMethod.cost;
            } else {
                toast.error("Lütfen bir kargo metodu seçiniz.");
                setLoading(false);
                return;
            }

            // Ödeme yöntemine göre endpoint belirle
            let endpoint = "";
            switch (paymentMethod) {
                case "bank_transfer":
                    endpoint = "/payment/bank-transfer";
                    break;
                case "credit_card":
                    endpoint = "/payment/iyzico";
                    break;
                case "paywithiyzico":
                    endpoint = "/payment/paywithiyzico";
                    break;
                default:
                    throw new Error("Geçersiz ödeme yöntemi");
            }

            console.log("Ödeme endpoint:", endpoint);
            console.log("Ödeme verileri:", paymentData);

            // CSRF token al
            const csrfToken = document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content");

            console.log("CSRF Token:", csrfToken);

            // Form verilerini hazırla
            const formDataObj = new FormData();
            formDataObj.append("_token", csrfToken);

            // Diğer form verilerini ekle
            Object.keys(paymentData).forEach((key) => {
                // use_3d_secure değerini özel olarak işle
                if (key === "use_3d_secure") {
                    // Boolean değeri string'e dönüştür
                    const value = paymentData[key] ? "true" : "false";
                    console.log(
                        `use_3d_secure değeri: ${value} (${typeof value})`
                    );
                    formDataObj.append(key, value);
                } else {
                    formDataObj.append(key, paymentData[key]);
                }
            });

            // Banka transferi için banka hesabı ID'sini ekle
            if (paymentMethod === "bank_transfer") {
                formDataObj.append("bank_account_id", selectedBankAccount);
            }

            console.log("Form verileri hazırlandı");

            // Fetch API ile ödeme isteği gönder
            fetch(endpoint, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": csrfToken,
                    Accept: "application/json",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formDataObj,
                credentials: "same-origin",
            })
                .then((response) => {
                    console.log("Ödeme yanıtı alındı:", response);
                    if (!response.ok) {
                        console.error(
                            "Ödeme yanıtı başarısız:",
                            response.status,
                            response.statusText
                        );
                        throw new Error(
                            `Ödeme isteği başarısız: ${response.status} ${response.statusText}`
                        );
                    }
                    return response.json();
                })
                .then((data) => {
                    console.log("Ödeme yanıtı JSON:", data);
                    setLoading(false);

                    if (data.success) {
                        console.log("Ödeme isteği başarılı:", data);

                        if (data.html_content) {
                            // 3D Secure için HTML içeriği varsa modalda göster
                            console.log(
                                "HTML içeriği alındı, modal gösteriliyor"
                            );

                            // HTML içeriğini ayarla
                            setPaymentHtmlContent(data.html_content);

                            // Popup olarak açılması gerektiğini kontrol et
                            if (data.popup === true) {
                                console.log("Popup olarak açılacak");
                                // Modalı göster
                                setShowPaymentModal(true);
                            } else {
                                console.log("Direkt yönlendirme yapılacak");
                                // HTML içeriğini yeni bir sayfada aç
                                const newWindow = window.open("", "_blank");
                                newWindow.document.write(data.html_content);
                                newWindow.document.close();
                            }

                            // Sipariş numarasını sakla
                            console.log(
                                "Sipariş numarası saklandı:",
                                data.order_number
                            );

                            // Sipariş numarasını state'e kaydet
                            setCurrentOrderNumber(data.order_number);
                        } else if (data.redirect_url) {
                            // Yönlendirme URL'si varsa
                            console.log(
                                "Yönlendirme URL'si alındı:",
                                data.redirect_url
                            );

                            if (paymentMethod === "paywithiyzico") {
                                // PayWithIyzico için HTML içeriği oluştur
                                console.log(
                                    "PayWithIyzico için HTML içeriği oluşturuluyor"
                                );

                                // iFrame içinde göstermek için HTML oluştur
                                const iframeHtml = `
                                    <iframe
                                        src="${data.redirect_url}"
                                        style="width: 100%; height: 600px; border: none;"
                                        id="iyzico-checkout-iframe"
                                        onload="window.scrollTo(0, 0);"
                                    ></iframe>
                                `;

                                // HTML içeriğini ayarla ve modalı göster
                                setPaymentHtmlContent(iframeHtml);
                                setShowPaymentModal(true);

                                // Sipariş numarasını sakla
                                console.log(
                                    "Sipariş numarası saklandı:",
                                    data.order_number
                                );

                                // Sipariş numarasını state'e kaydet
                                setCurrentOrderNumber(data.order_number);
                            } else {
                                // Diğer yönlendirmeler için doğrudan yönlendir
                                console.log(
                                    "Doğrudan yönlendiriliyor:",
                                    data.redirect_url
                                );
                                setPaymentRedirectUrl(data.redirect_url);
                                window.location.href = data.redirect_url;
                            }
                        } else {
                            console.warn(
                                "Ödeme yanıtında HTML içeriği veya yönlendirme URL'si yok"
                            );
                            toast.success("Ödeme işlemi başarıyla tamamlandı.");
                        }
                    } else {
                        console.error("Ödeme yanıtı başarısız:", data.message);
                        toast.error(
                            data.message ||
                                "Ödeme işlemi sırasında bir hata oluştu."
                        );
                    }
                })
                .catch((error) => {
                    console.error("Ödeme isteği hatası:", error);
                    setLoading(false);
                    toast.error(
                        "Ödeme işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin."
                    );
                });
        } catch (error) {
            console.error("Ödeme işlemi başlatılırken hata oluştu:", error);
            setLoading(false);
            toast.error(
                "Ödeme işlemi başlatılırken bir hata oluştu. Lütfen tekrar deneyin."
            );
        }
    };

    return (
        <MainLayout>
            <Head title="Ödeme" />

            <div className="container mx-auto px-4 py-8">
                <h1 className="text-2xl font-bold mb-6">Ödeme</h1>

                {/* Ödeme Kesintisi Bildirimi */}
                {paymentInterrupted && (
                    <div className="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <div className="flex items-center justify-center mb-4 text-yellow-500">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-12 w-12"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                    />
                                </svg>
                            </div>
                            <h3 className="text-xl font-bold mb-3 text-center">
                                Ödeme İşlemi Yarıda Kaldı
                            </h3>
                            <p className="mb-5 text-gray-600 text-center">
                                Ödeme işleminiz henüz tamamlanmadı. Ödemeye
                                devam etmek veya siparişi iptal etmek için
                                aşağıdaki seçeneklerden birini seçebilirsiniz.
                            </p>
                            <div className="flex flex-col space-y-2">
                                <button
                                    onClick={continuePayment}
                                    className="w-full px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200 font-medium"
                                >
                                    Ödemeye Devam Et
                                </button>
                                <button
                                    onClick={cancelOrder}
                                    className="w-full px-4 py-3 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors duration-200 font-medium"
                                >
                                    Siparişi İptal Et
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                <div className="flex flex-col lg:flex-row gap-8">
                    {/* Sol Taraf - Form */}
                    <div className="lg:w-2/3">
                        {/* Ödeme Modalı */}
                        <PaymentModal
                            isOpen={showPaymentModal}
                            onClose={handlePaymentModalClose}
                            htmlContent={paymentHtmlContent}
                            onPaymentComplete={handlePaymentComplete}
                        />
                        <form onSubmit={handleSubmit}>
                            {/* Fatura Bilgileri */}
                            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Fatura Bilgileri
                                </h2>

                                {/* Kullanıcı giriş yapmışsa adres seçimi göster */}
                                {user && addresses.length > 0 && (
                                    <div className="mb-6">
                                        <label className="flex items-center mb-3">
                                            <input
                                                type="checkbox"
                                                checked={useAddressSelection}
                                                onChange={() =>
                                                    setUseAddressSelection(
                                                        !useAddressSelection
                                                    )
                                                }
                                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            />
                                            <span className="ml-2 text-sm text-gray-600">
                                                Kayıtlı adreslerimden seç
                                            </span>
                                        </label>

                                        {useAddressSelection && (
                                            <AddressSelector
                                                addresses={addresses}
                                                value={selectedBillingAddress}
                                                onChange={
                                                    setSelectedBillingAddress
                                                }
                                                className="mb-4"
                                                required
                                            />
                                        )}
                                    </div>
                                )}

                                {/* Manuel adres girişi */}
                                {!useAddressSelection && (
                                    <div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                            <div>
                                                <label className="block text-gray-700 mb-1">
                                                    Ad Soyad{" "}
                                                    <span className="text-red-500">
                                                        *
                                                    </span>
                                                </label>
                                                <input
                                                    type="text"
                                                    name="billing_name"
                                                    value={
                                                        formData.billing_name
                                                    }
                                                    onChange={handleChange}
                                                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-gray-700 mb-1">
                                                    E-posta{" "}
                                                    <span className="text-red-500">
                                                        *
                                                    </span>
                                                </label>
                                                <input
                                                    type="email"
                                                    name="billing_email"
                                                    value={
                                                        formData.billing_email
                                                    }
                                                    onChange={handleChange}
                                                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-gray-700 mb-1">
                                                    Telefon{" "}
                                                    <span className="text-red-500">
                                                        *
                                                    </span>
                                                </label>
                                                <input
                                                    type="tel"
                                                    name="billing_phone"
                                                    value={
                                                        formData.billing_phone
                                                    }
                                                    onChange={handleChange}
                                                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                    required
                                                />
                                            </div>

                                            <div>
                                                <label className="block text-gray-700 mb-1">
                                                    Posta Kodu
                                                </label>
                                                <input
                                                    type="text"
                                                    name="billing_zipcode"
                                                    value={
                                                        formData.billing_zipcode
                                                    }
                                                    onChange={handleChange}
                                                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                />
                                            </div>
                                        </div>

                                        {/* Konum seçimi */}
                                        <LocationFields
                                            countryId={
                                                formData.billing_country_id
                                            }
                                            stateId={formData.billing_state_id}
                                            cityId={formData.billing_city_id}
                                            onCountryChange={(value) =>
                                                setFormData({
                                                    ...formData,
                                                    billing_country_id: value,
                                                    billing_state_id: "",
                                                    billing_city_id: "",
                                                })
                                            }
                                            onStateChange={(value) =>
                                                setFormData({
                                                    ...formData,
                                                    billing_state_id: value,
                                                    billing_city_id: "",
                                                })
                                            }
                                            onCityChange={(value) =>
                                                setFormData({
                                                    ...formData,
                                                    billing_city_id: value,
                                                })
                                            }
                                            required={true}
                                            prefix="billing_"
                                        />

                                        <div className="mt-4">
                                            <label className="block text-gray-700 mb-1">
                                                Adres{" "}
                                                <span className="text-red-500">
                                                    *
                                                </span>
                                            </label>
                                            <textarea
                                                name="billing_address"
                                                value={formData.billing_address}
                                                onChange={handleChange}
                                                className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                rows="3"
                                                required
                                            ></textarea>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Teslimat Bilgileri */}
                            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                                <div className="flex justify-between items-center mb-4">
                                    <h2 className="text-lg font-bold">
                                        Teslimat Bilgileri
                                    </h2>

                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={sameAddress}
                                            onChange={() =>
                                                setSameAddress(!sameAddress)
                                            }
                                            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                        <span className="ml-2 text-sm text-gray-600">
                                            Fatura adresi ile aynı
                                        </span>
                                    </label>
                                </div>

                                {!sameAddress && (
                                    <>
                                        {/* Kullanıcı giriş yapmışsa adres seçimi göster */}
                                        {user &&
                                            addresses.length > 0 &&
                                            useAddressSelection && (
                                                <div className="mb-6">
                                                    <AddressSelector
                                                        addresses={addresses}
                                                        value={
                                                            selectedShippingAddress
                                                        }
                                                        onChange={
                                                            setSelectedShippingAddress
                                                        }
                                                        className="mb-4"
                                                        required
                                                    />
                                                </div>
                                            )}

                                        {/* Manuel adres girişi */}
                                        {!useAddressSelection && (
                                            <div>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                    <div>
                                                        <label className="block text-gray-700 mb-1">
                                                            Ad Soyad{" "}
                                                            <span className="text-red-500">
                                                                *
                                                            </span>
                                                        </label>
                                                        <input
                                                            type="text"
                                                            name="shipping_name"
                                                            value={
                                                                formData.shipping_name
                                                            }
                                                            onChange={
                                                                handleChange
                                                            }
                                                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                            required
                                                        />
                                                    </div>

                                                    <div>
                                                        <label className="block text-gray-700 mb-1">
                                                            E-posta{" "}
                                                            <span className="text-red-500">
                                                                *
                                                            </span>
                                                        </label>
                                                        <input
                                                            type="email"
                                                            name="shipping_email"
                                                            value={
                                                                formData.shipping_email
                                                            }
                                                            onChange={
                                                                handleChange
                                                            }
                                                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                            required
                                                        />
                                                    </div>

                                                    <div>
                                                        <label className="block text-gray-700 mb-1">
                                                            Telefon{" "}
                                                            <span className="text-red-500">
                                                                *
                                                            </span>
                                                        </label>
                                                        <input
                                                            type="tel"
                                                            name="shipping_phone"
                                                            value={
                                                                formData.shipping_phone
                                                            }
                                                            onChange={
                                                                handleChange
                                                            }
                                                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                            required
                                                        />
                                                    </div>

                                                    <div>
                                                        <label className="block text-gray-700 mb-1">
                                                            Posta Kodu
                                                        </label>
                                                        <input
                                                            type="text"
                                                            name="shipping_zipcode"
                                                            value={
                                                                formData.shipping_zipcode
                                                            }
                                                            onChange={
                                                                handleChange
                                                            }
                                                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                        />
                                                    </div>
                                                </div>

                                                {/* Konum seçimi */}
                                                <LocationFields
                                                    countryId={
                                                        formData.shipping_country_id
                                                    }
                                                    stateId={
                                                        formData.shipping_state_id
                                                    }
                                                    cityId={
                                                        formData.shipping_city_id
                                                    }
                                                    onCountryChange={(value) =>
                                                        setFormData({
                                                            ...formData,
                                                            shipping_country_id:
                                                                value,
                                                            shipping_state_id:
                                                                "",
                                                            shipping_city_id:
                                                                "",
                                                        })
                                                    }
                                                    onStateChange={(value) =>
                                                        setFormData({
                                                            ...formData,
                                                            shipping_state_id:
                                                                value,
                                                            shipping_city_id:
                                                                "",
                                                        })
                                                    }
                                                    onCityChange={(value) =>
                                                        setFormData({
                                                            ...formData,
                                                            shipping_city_id:
                                                                value,
                                                        })
                                                    }
                                                    required={true}
                                                    prefix="shipping_"
                                                />

                                                <div className="mt-4">
                                                    <label className="block text-gray-700 mb-1">
                                                        Adres{" "}
                                                        <span className="text-red-500">
                                                            *
                                                        </span>
                                                    </label>
                                                    <textarea
                                                        name="shipping_address"
                                                        value={
                                                            formData.shipping_address
                                                        }
                                                        onChange={handleChange}
                                                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                        rows="3"
                                                        required
                                                    ></textarea>
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>

                            {/* Kargo Seçenekleri */}
                            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Kargo Seçenekleri
                                </h2>

                                <ShippingOptions
                                    address={{
                                        country_id:
                                            formData.shipping_country_id,
                                        state_id: formData.shipping_state_id,
                                        city_id: formData.shipping_city_id,
                                        postal_code: formData.shipping_zipcode,
                                    }}
                                    cartTotal={cart.total}
                                    onShippingMethodSelected={
                                        handleShippingMethodSelected
                                    }
                                    selectedShippingMethod={
                                        selectedShippingMethod
                                    }
                                />
                            </div>

                            {/* Sipariş Notu */}
                            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Sipariş Notu
                                </h2>

                                <div>
                                    <textarea
                                        name="notes"
                                        value={formData.notes}
                                        onChange={handleChange}
                                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                        rows="3"
                                        placeholder="Siparişinizle ilgili eklemek istediğiniz notlar..."
                                    ></textarea>
                                </div>
                            </div>

                            {/* Ödeme Yöntemi */}
                            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Ödeme Yöntemi
                                </h2>

                                <div className="space-y-4">
                                    <label className="flex items-center">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="credit_card"
                                            checked={
                                                paymentMethod === "credit_card"
                                            }
                                            onChange={() =>
                                                setPaymentMethod("credit_card")
                                            }
                                            className="text-blue-600 focus:ring-blue-500"
                                        />
                                        <span className="ml-2">
                                            Kredi Kartı (3D Secure)
                                        </span>
                                    </label>

                                    <label className="flex items-center">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="paywithiyzico"
                                            checked={
                                                paymentMethod ===
                                                "paywithiyzico"
                                            }
                                            onChange={() =>
                                                setPaymentMethod(
                                                    "paywithiyzico"
                                                )
                                            }
                                            className="text-blue-600 focus:ring-blue-500"
                                        />
                                        <span className="ml-2">
                                            iyzico ile Öde
                                        </span>
                                    </label>

                                    <label className="flex items-center">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="bank_transfer"
                                            checked={
                                                paymentMethod ===
                                                "bank_transfer"
                                            }
                                            onChange={() =>
                                                setPaymentMethod(
                                                    "bank_transfer"
                                                )
                                            }
                                            className="text-blue-600 focus:ring-blue-500"
                                        />
                                        <span className="ml-2">
                                            Havale / EFT
                                        </span>
                                    </label>
                                </div>

                                {/* Kredi Kartı Formu */}
                                {paymentMethod === "credit_card" && (
                                    <div className="mt-4 border-t pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="md:col-span-2 mb-2">
                                                <label className="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        name="use_3d_secure"
                                                        checked={
                                                            formData.use_3d_secure ===
                                                            "true"
                                                        }
                                                        onChange={(e) => {
                                                            const value = e
                                                                .target.checked
                                                                ? "true"
                                                                : "false";
                                                            console.log(
                                                                `3D Secure checkbox değişti: ${value}`
                                                            );
                                                            setFormData({
                                                                ...formData,
                                                                use_3d_secure:
                                                                    value,
                                                            });
                                                        }}
                                                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                    />
                                                    <span className="ml-2 text-sm text-gray-600">
                                                        3D Secure ile güvenli
                                                        ödeme
                                                    </span>
                                                </label>
                                                <div className="text-xs text-gray-500 mt-1">
                                                    3D Secure, kredi kartı
                                                    ödemelerinizi daha güvenli
                                                    hale getiren ek bir
                                                    doğrulama adımıdır.
                                                </div>
                                                <div className="text-xs text-blue-600 mt-1 font-medium">
                                                    Not: 3D Secure kullanımı,
                                                    bankanızın güvenlik
                                                    politikalarına göre zorunlu
                                                    olabilir.
                                                </div>
                                            </div>

                                            <div className="md:col-span-2">
                                                <label className="block text-gray-700 mb-1">
                                                    Kart Üzerindeki İsim
                                                </label>
                                                <input
                                                    type="text"
                                                    name="card_holder_name"
                                                    value={
                                                        formData.card_holder_name
                                                    }
                                                    onChange={handleChange}
                                                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                                                    required={
                                                        paymentMethod ===
                                                        "credit_card"
                                                    }
                                                />
                                            </div>

                                            <div className="md:col-span-2">
                                                <label className="block text-gray-700 mb-1">
                                                    Kart Numarası
                                                </label>
                                                <input
                                                    type="text"
                                                    name="card_number"
                                                    value={formData.card_number}
                                                    onChange={(e) => {
                                                        // Sadece rakam girişine izin ver
                                                        const value =
                                                            e.target.value.replace(
                                                                /\D/g,
                                                                ""
                                                            );
                                                        if (
                                                            value.length <= 16
                                                        ) {
                                                            setFormData({
                                                                ...formData,
                                                                card_number:
                                                                    value,
                                                            });
                                                        }
                                                    }}
                                                    className={`w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${
                                                        formData.card_number &&
                                                        formData.card_number
                                                            .length !== 16
                                                            ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                                                            : ""
                                                    }`}
                                                    placeholder="1234 5678 9012 3456"
                                                    maxLength="16"
                                                    required={
                                                        paymentMethod ===
                                                        "credit_card"
                                                    }
                                                />
                                                {formData.card_number &&
                                                    formData.card_number
                                                        .length !== 16 && (
                                                        <p className="mt-1 text-sm text-red-600">
                                                            Kart numarası 16
                                                            haneli olmalıdır
                                                        </p>
                                                    )}
                                            </div>

                                            <div>
                                                <label className="block text-gray-700 mb-1">
                                                    Son Kullanma Tarihi
                                                </label>
                                                <div className="flex space-x-2">
                                                    <input
                                                        type="text"
                                                        name="expiry_month"
                                                        value={
                                                            formData.expiry_month
                                                        }
                                                        onChange={(e) => {
                                                            // Sadece rakam girişine izin ver
                                                            const value =
                                                                e.target.value.replace(
                                                                    /\D/g,
                                                                    ""
                                                                );
                                                            if (
                                                                value.length <=
                                                                2
                                                            ) {
                                                                // Ay değeri 1-12 arasında olmalı
                                                                if (
                                                                    value ===
                                                                        "" ||
                                                                    (parseInt(
                                                                        value
                                                                    ) >= 1 &&
                                                                        parseInt(
                                                                            value
                                                                        ) <=
                                                                            12) ||
                                                                    (value.length ===
                                                                        1 &&
                                                                        parseInt(
                                                                            value
                                                                        ) >= 1)
                                                                ) {
                                                                    setFormData(
                                                                        {
                                                                            ...formData,
                                                                            expiry_month:
                                                                                value,
                                                                        }
                                                                    );
                                                                }
                                                            }
                                                        }}
                                                        className={`w-1/2 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${
                                                            formData.expiry_month &&
                                                            (formData
                                                                .expiry_month
                                                                .length !== 2 ||
                                                                parseInt(
                                                                    formData.expiry_month
                                                                ) < 1 ||
                                                                parseInt(
                                                                    formData.expiry_month
                                                                ) > 12)
                                                                ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                                                                : ""
                                                        }`}
                                                        placeholder="AA"
                                                        maxLength="2"
                                                        required={
                                                            paymentMethod ===
                                                            "credit_card"
                                                        }
                                                    />
                                                    <span className="flex items-center">
                                                        /
                                                    </span>
                                                    <input
                                                        type="text"
                                                        name="expiry_year"
                                                        value={
                                                            formData.expiry_year
                                                        }
                                                        onChange={(e) => {
                                                            // Sadece rakam girişine izin ver
                                                            const value =
                                                                e.target.value.replace(
                                                                    /\D/g,
                                                                    ""
                                                                );
                                                            if (
                                                                value.length <=
                                                                2
                                                            ) {
                                                                setFormData({
                                                                    ...formData,
                                                                    expiry_year:
                                                                        value,
                                                                });
                                                            }
                                                        }}
                                                        className={`w-1/2 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${
                                                            formData.expiry_year &&
                                                            (formData
                                                                .expiry_year
                                                                .length !== 2 ||
                                                                parseInt(
                                                                    formData.expiry_year
                                                                ) <
                                                                    parseInt(
                                                                        new Date()
                                                                            .getFullYear()
                                                                            .toString()
                                                                            .substr(
                                                                                -2
                                                                            )
                                                                    ))
                                                                ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                                                                : ""
                                                        }`}
                                                        placeholder="YY"
                                                        maxLength="2"
                                                        required={
                                                            paymentMethod ===
                                                            "credit_card"
                                                        }
                                                    />
                                                    {formData.expiry_month &&
                                                        formData.expiry_month
                                                            .length === 2 &&
                                                        (parseInt(
                                                            formData.expiry_month
                                                        ) < 1 ||
                                                            parseInt(
                                                                formData.expiry_month
                                                            ) > 12) && (
                                                            <p className="mt-1 text-sm text-red-600 col-span-2">
                                                                Ay değeri 1-12
                                                                arasında
                                                                olmalıdır
                                                            </p>
                                                        )}
                                                    {formData.expiry_year &&
                                                        formData.expiry_year
                                                            .length === 2 &&
                                                        parseInt(
                                                            formData.expiry_year
                                                        ) <
                                                            parseInt(
                                                                new Date()
                                                                    .getFullYear()
                                                                    .toString()
                                                                    .substr(-2)
                                                            ) && (
                                                            <p className="mt-1 text-sm text-red-600 col-span-2">
                                                                Geçersiz son
                                                                kullanma yılı
                                                            </p>
                                                        )}
                                                </div>
                                            </div>

                                            <div>
                                                <label className="block text-gray-700 mb-1">
                                                    Güvenlik Kodu (CVC)
                                                </label>
                                                <input
                                                    type="text"
                                                    name="cvc"
                                                    value={formData.cvc}
                                                    onChange={(e) => {
                                                        // Sadece rakam girişine izin ver
                                                        const value =
                                                            e.target.value.replace(
                                                                /\D/g,
                                                                ""
                                                            );
                                                        if (value.length <= 3) {
                                                            setFormData({
                                                                ...formData,
                                                                cvc: value,
                                                            });
                                                        }
                                                    }}
                                                    className={`w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${
                                                        formData.cvc &&
                                                        formData.cvc.length !==
                                                            3
                                                            ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                                                            : ""
                                                    }`}
                                                    placeholder="123"
                                                    maxLength="3"
                                                    required={
                                                        paymentMethod ===
                                                        "credit_card"
                                                    }
                                                />
                                                {formData.cvc &&
                                                    formData.cvc.length !==
                                                        3 && (
                                                        <p className="mt-1 text-sm text-red-600">
                                                            CVC kodu 3 haneli
                                                            olmalıdır
                                                        </p>
                                                    )}
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Havale / EFT Formu */}
                                {paymentMethod === "bank_transfer" && (
                                    <div className="mt-4 border-t pt-4">
                                        <p className="mb-2 text-gray-700">
                                            Lütfen ödeme yapmak istediğiniz
                                            banka hesabını seçin:
                                        </p>

                                        <div className="space-y-4">
                                            {bankAccounts.map((account) => (
                                                <label
                                                    key={account.id}
                                                    className="flex items-start"
                                                >
                                                    <input
                                                        type="radio"
                                                        name="bank_account_id"
                                                        value={account.id}
                                                        checked={
                                                            selectedBankAccount ===
                                                            account.id
                                                        }
                                                        onChange={() =>
                                                            setSelectedBankAccount(
                                                                account.id
                                                            )
                                                        }
                                                        className="mt-1 text-blue-600 focus:ring-blue-500"
                                                    />
                                                    <div className="ml-2">
                                                        <div className="font-medium">
                                                            {account.bank_name}
                                                        </div>
                                                        <div className="text-sm text-gray-600">
                                                            {
                                                                account.account_name
                                                            }
                                                        </div>
                                                        <div className="text-sm text-gray-600">
                                                            IBAN: {account.iban}
                                                        </div>
                                                        {account.description && (
                                                            <div className="text-sm text-gray-500 mt-1">
                                                                {
                                                                    account.description
                                                                }
                                                            </div>
                                                        )}
                                                    </div>
                                                </label>
                                            ))}
                                        </div>

                                        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded p-3 text-sm text-yellow-800">
                                            <p>
                                                <strong>Not:</strong> Havale/EFT
                                                ile ödeme seçeneğinde,
                                                siparişiniz ödemeniz banka
                                                hesabımıza ulaştıktan sonra
                                                işleme alınacaktır. Ödeme
                                                açıklamasına sipariş numaranızı
                                                yazmayı unutmayın.
                                            </p>
                                        </div>

                                        <div className="mt-4 bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-800">
                                            <p>
                                                <strong>Bilgi:</strong>{" "}
                                                Havale/EFT ödemesi yaptıktan
                                                sonra, müşteri panelinizden
                                                sipariş detaylarını
                                                görüntüleyebilir ve ödeme
                                                durumunu takip edebilirsiniz.
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="flex justify-between items-center">
                                <Link
                                    href="/cart"
                                    className="bg-gray-200 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-300"
                                >
                                    Sepete Dön
                                </Link>

                                <button
                                    type="submit"
                                    className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 disabled:opacity-50"
                                    disabled={loading}
                                >
                                    {loading
                                        ? "İşleniyor..."
                                        : "Siparişi Tamamla"}
                                </button>
                            </div>
                        </form>
                    </div>

                    {/* Sağ Taraf - Sipariş Özeti */}
                    <div className="lg:w-1/3">
                        <div className="bg-white rounded-lg shadow-md p-6 sticky top-6">
                            <h2 className="text-lg font-bold mb-4">
                                Sipariş Özeti
                            </h2>

                            <div className="border-b pb-4 mb-4">
                                {cart.items.map((item) => (
                                    <div
                                        key={item.id}
                                        className="flex justify-between items-start mb-2"
                                    >
                                        <div className="flex items-start">
                                            <div className="w-10 h-10 flex-shrink-0 mr-2 bg-gray-100 rounded overflow-hidden">
                                                <img
                                                    src={
                                                        item.product.image ||
                                                        "/images/placeholder.png"
                                                    }
                                                    alt={item.product.name}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                            <div>
                                                <div className="font-medium">
                                                    {item.product.name}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {item.quantity} x{" "}
                                                    {formatPrice(item.price)}
                                                </div>
                                                {item.options &&
                                                    Object.keys(item.options)
                                                        .length > 0 && (
                                                        <div className="text-xs text-gray-500">
                                                            {Object.entries(
                                                                item.options
                                                            ).map(
                                                                ([
                                                                    key,
                                                                    value,
                                                                ]) => (
                                                                    <span
                                                                        key={
                                                                            key
                                                                        }
                                                                    >
                                                                        {key}:{" "}
                                                                        {value}
                                                                    </span>
                                                                )
                                                            )}
                                                        </div>
                                                    )}
                                            </div>
                                        </div>
                                        <div className="font-medium">
                                            {formatPrice(
                                                item.price * item.quantity
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span>Ara Toplam</span>
                                    <span>{formatPrice(cart.subtotal)}</span>
                                </div>

                                {cart.discount_amount > 0 && (
                                    <div className="flex justify-between text-green-600">
                                        <span>
                                            İndirim{" "}
                                            {cart.coupon_code &&
                                                `(${cart.coupon_code})`}
                                        </span>
                                        <span>
                                            -{formatPrice(cart.discount_amount)}
                                        </span>
                                    </div>
                                )}

                                <div className="flex justify-between">
                                    <span>Kargo</span>
                                    <span>
                                        {selectedShippingMethod
                                            ? selectedShippingMethod.is_free
                                                ? "Ücretsiz"
                                                : formatPrice(
                                                      selectedShippingMethod.cost
                                                  )
                                            : "Seçilmedi"}
                                    </span>
                                </div>

                                <div className="flex justify-between font-bold text-lg pt-2 border-t">
                                    <span>Toplam</span>
                                    <span>
                                        {formatPrice(
                                            selectedShippingMethod &&
                                                !selectedShippingMethod.is_free
                                                ? cart.total +
                                                      selectedShippingMethod.cost
                                                : cart.total
                                        )}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
