import React, { useState } from "react";
import { Head, Link, usePage } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";
import { formatPrice } from "@/Utils/cartUtils";
import { toast } from "react-toastify";

export default function Success({ order }) {
    const { auth } = usePage().props;
    const [copySuccess, setCopySuccess] = useState(false);
    const [trackOrderEmail, setTrackOrderEmail] = useState("");
    const [showTrackForm, setShowTrackForm] = useState(false);

    // Sipariş numarasını kopyala
    const copyOrderNumber = () => {
        navigator.clipboard
            .writeText(order.order_number)
            .then(() => {
                setCopySuccess(true);
                toast.success("Sipariş numarası kopyalandı!");
                setTimeout(() => setCopySuccess(false), 2000);
            })
            .catch((err) => {
                console.error("Kopyala<PERSON> başarısız oldu: ", err);
                toast.error(
                    "Kopyalama başarısız oldu. Lütfen manuel olarak kopyalayın."
                );
            });
    };

    // IBAN kopyala
    const copyIban = () => {
        if (order.bank_account && order.bank_account.iban) {
            navigator.clipboard
                .writeText(order.bank_account.iban)
                .then(() => {
                    toast.success("IBAN kopyalandı!");
                })
                .catch((err) => {
                    console.error("IBAN kopyalama başarısız oldu: ", err);
                    toast.error(
                        "Kopyalama başarısız oldu. Lütfen manuel olarak kopyalayın."
                    );
                });
        }
    };

    // Sipariş takip formunu gönder
    const handleTrackOrder = (e) => {
        e.preventDefault();
        if (!trackOrderEmail) {
            toast.error("Lütfen e-posta adresinizi girin");
            return;
        }

        // E-posta formatını kontrol et
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(trackOrderEmail)) {
            toast.error("Lütfen geçerli bir e-posta adresi girin");
            return;
        }

        // Sipariş takip sayfasına yönlendir
        window.location.href = `/orders/track?order_number=${
            order.order_number
        }&email=${encodeURIComponent(trackOrderEmail)}`;
    };
    return (
        <MainLayout>
            <Head title="Sipariş Tamamlandı" />

            <div className="container mx-auto px-4 py-8">
                <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-8">
                    <div className="text-center mb-8">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-8 w-8 text-green-600"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                />
                            </svg>
                        </div>

                        <h1 className="text-2xl font-bold text-gray-800 mb-2">
                            Siparişiniz Alındı!
                        </h1>
                        <p className="text-gray-600">
                            Siparişiniz başarıyla oluşturuldu. Sipariş
                            detaylarınız aşağıda yer almaktadır.
                        </p>
                    </div>

                    <div className="border-t border-b py-4 mb-6">
                        <div className="flex justify-between mb-2">
                            <span className="font-medium">
                                Sipariş Numarası:
                            </span>
                            <div className="flex items-center">
                                <span>{order.order_number}</span>
                                <button
                                    onClick={copyOrderNumber}
                                    className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                                    title="Sipariş numarasını kopyala"
                                >
                                    {copySuccess ? (
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-5 w-5 text-green-600"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    ) : (
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-5 w-5"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                        </svg>
                                    )}
                                </button>
                            </div>
                        </div>

                        <div className="flex justify-between mb-2">
                            <span className="font-medium">Sipariş Tarihi:</span>
                            <span>
                                {new Date(order.created_at).toLocaleDateString(
                                    "tr-TR"
                                )}
                            </span>
                        </div>

                        <div className="flex justify-between mb-2">
                            <span className="font-medium">Ödeme Yöntemi:</span>
                            <span>
                                {order.payment_method === "credit_card"
                                    ? "Kredi Kartı"
                                    : order.payment_method === "bank_transfer"
                                    ? "Havale/EFT"
                                    : order.payment_method}
                            </span>
                        </div>

                        <div className="flex justify-between mb-2">
                            <span className="font-medium">Sipariş Durumu:</span>
                            <span
                                className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    order.status === "pending"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : order.status === "processing"
                                        ? "bg-blue-100 text-blue-800"
                                        : order.status === "shipped"
                                        ? "bg-purple-100 text-purple-800"
                                        : order.status === "delivered"
                                        ? "bg-green-100 text-green-800"
                                        : order.status === "cancelled"
                                        ? "bg-red-100 text-red-800"
                                        : "bg-gray-100 text-gray-800"
                                }`}
                            >
                                {order.status === "pending"
                                    ? "Beklemede"
                                    : order.status === "processing"
                                    ? "İşleniyor"
                                    : order.status === "shipped"
                                    ? "Kargoya Verildi"
                                    : order.status === "delivered"
                                    ? "Teslim Edildi"
                                    : order.status === "cancelled"
                                    ? "İptal Edildi"
                                    : order.status}
                            </span>
                        </div>

                        <div className="flex justify-between mb-2">
                            <span className="font-medium">Ödeme Durumu:</span>
                            <span
                                className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    order.payment_status === "pending"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : order.payment_status === "paid"
                                        ? "bg-green-100 text-green-800"
                                        : order.payment_status === "failed"
                                        ? "bg-red-100 text-red-800"
                                        : order.payment_status === "refunded"
                                        ? "bg-purple-100 text-purple-800"
                                        : "bg-gray-100 text-gray-800"
                                }`}
                            >
                                {order.payment_status === "pending"
                                    ? "Beklemede"
                                    : order.payment_status === "paid"
                                    ? "Ödendi"
                                    : order.payment_status === "failed"
                                    ? "Başarısız"
                                    : order.payment_status === "refunded"
                                    ? "İade Edildi"
                                    : order.payment_status}
                            </span>
                        </div>

                        <div className="flex justify-between">
                            <span className="font-medium">Toplam Tutar:</span>
                            <span className="font-bold">
                                {formatPrice(order.total_amount)}
                            </span>
                        </div>
                    </div>

                    {order.payment_method === "bank_transfer" &&
                        order.payment_status === "pending" && (
                            <div className="bg-yellow-50 border-2 border-yellow-300 rounded-lg p-6 mb-6">
                                <div className="flex items-center mb-3">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-6 w-6 text-yellow-600 mr-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                    </svg>
                                    <h3 className="font-bold text-yellow-800 text-lg">
                                        Ödeme Bilgileri
                                    </h3>
                                </div>
                                <div className="bg-white border border-yellow-200 rounded p-4 mb-4">
                                    <p className="text-yellow-800 font-medium mb-2">
                                        Siparişinizi tamamlamak için aşağıdaki
                                        banka hesabına ödeme yapmanız
                                        gerekmektedir.
                                    </p>
                                    <p className="text-red-600 font-bold mb-2">
                                        Ödeme açıklamasına sipariş numaranızı (
                                        {order.order_number}) yazmayı unutmayın!
                                    </p>

                                    {order.bank_account && (
                                        <div className="text-sm bg-gray-50 p-4 rounded border border-gray-200 mt-3">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                <div className="p-2 bg-white rounded border border-gray-100">
                                                    <div className="font-bold text-gray-700 mb-1">
                                                        Banka
                                                    </div>
                                                    <div className="text-gray-800">
                                                        {
                                                            order.bank_account
                                                                .bank_name
                                                        }
                                                    </div>
                                                </div>

                                                <div className="p-2 bg-white rounded border border-gray-100">
                                                    <div className="font-bold text-gray-700 mb-1">
                                                        Hesap Sahibi
                                                    </div>
                                                    <div className="text-gray-800">
                                                        {
                                                            order.bank_account
                                                                .account_name
                                                        }
                                                    </div>
                                                </div>

                                                <div className="p-2 bg-white rounded border border-gray-100 md:col-span-2">
                                                    <div className="font-bold text-gray-700 mb-1">
                                                        IBAN
                                                    </div>
                                                    <div className="flex items-center">
                                                        <span className="text-gray-800 font-mono mr-2">
                                                            {
                                                                order
                                                                    .bank_account
                                                                    .iban
                                                            }
                                                        </span>
                                                        <button
                                                            onClick={copyIban}
                                                            className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded flex items-center text-xs"
                                                            title="IBAN'ı kopyala"
                                                        >
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                className="h-4 w-4 mr-1"
                                                                viewBox="0 0 20 20"
                                                                fill="currentColor"
                                                            >
                                                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                                            </svg>
                                                            Kopyala
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            {order.bank_account.description && (
                                                <div className="mt-3 p-2 bg-yellow-50 text-yellow-700 rounded border border-yellow-200">
                                                    {
                                                        order.bank_account
                                                            .description
                                                    }
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                                <div className="mt-4 bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-800">
                                    <p className="flex items-center">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-5 w-5 mr-2 text-blue-600"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                            />
                                        </svg>
                                        Ödemeniz banka hesabımıza ulaştıktan
                                        sonra (genellikle 1-2 iş günü içinde)
                                        siparişiniz işleme alınacaktır.
                                    </p>
                                </div>
                            </div>
                        )}

                    <div className="text-center">
                        {auth.user ? (
                            // Giriş yapmış kullanıcı için içerik
                            <>
                                <p className="text-gray-600 mb-6">
                                    Siparişinizle ilgili güncellemeler e-posta
                                    adresinize gönderilecektir. Siparişlerinizi
                                    hesabınızdan da takip edebilirsiniz.
                                </p>

                                <div className="flex flex-col sm:flex-row justify-center gap-4">
                                    <Link
                                        href={route(
                                            "customer.orders.show",
                                            order.order_number
                                        )}
                                        className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
                                    >
                                        Sipariş Detayları
                                    </Link>

                                    <Link
                                        href="/"
                                        className="bg-gray-200 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-300"
                                    >
                                        Alışverişe Devam Et
                                    </Link>
                                </div>
                            </>
                        ) : (
                            // Misafir kullanıcı için içerik
                            <>
                                <p className="text-gray-600 mb-6">
                                    Siparişinizle ilgili güncellemeler e-posta
                                    adresinize gönderilecektir. Siparişinizi
                                    takip etmek için aşağıdaki seçenekleri
                                    kullanabilirsiniz.
                                </p>

                                {showTrackForm ? (
                                    <div className="max-w-md mx-auto mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                                        <h3 className="font-bold text-gray-700 mb-3">
                                            Siparişinizi Takip Edin
                                        </h3>
                                        <form
                                            onSubmit={handleTrackOrder}
                                            className="flex flex-col gap-3"
                                        >
                                            <div className="flex flex-col">
                                                <label
                                                    htmlFor="track-email"
                                                    className="text-sm text-gray-600 mb-1 text-left"
                                                >
                                                    E-posta Adresiniz
                                                </label>
                                                <input
                                                    type="email"
                                                    id="track-email"
                                                    value={trackOrderEmail}
                                                    onChange={(e) =>
                                                        setTrackOrderEmail(
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="E-posta adresinizi girin"
                                                    className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                    required
                                                />
                                            </div>
                                            <div className="flex flex-col sm:flex-row gap-2">
                                                <button
                                                    type="submit"
                                                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex-1"
                                                >
                                                    Siparişi Takip Et
                                                </button>
                                                <button
                                                    type="button"
                                                    onClick={() =>
                                                        setShowTrackForm(false)
                                                    }
                                                    className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300"
                                                >
                                                    İptal
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                ) : (
                                    <div className="flex flex-col sm:flex-row justify-center gap-4 mb-6">
                                        <button
                                            onClick={() =>
                                                setShowTrackForm(true)
                                            }
                                            className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
                                        >
                                            Siparişimi Takip Et
                                        </button>

                                        <Link
                                            href="/login"
                                            className="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700"
                                        >
                                            Giriş Yap
                                        </Link>
                                    </div>
                                )}

                                <div className="flex justify-center">
                                    <Link
                                        href="/"
                                        className="bg-gray-200 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-300"
                                    >
                                        Alışverişe Devam Et
                                    </Link>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
