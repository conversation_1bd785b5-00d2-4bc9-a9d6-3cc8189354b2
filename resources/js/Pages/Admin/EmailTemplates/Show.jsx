import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import InputLabel from '@/Components/InputLabel';
import TextInput from '@/Components/TextInput';
import InputError from '@/Components/InputError';

export default function Show({ template }) {
    const [showTestEmailForm, setShowTestEmailForm] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm({
        email: '',
    });

    const handleTestSubmit = (e) => {
        e.preventDefault();
        post(route('admin.email-templates.send-test', template.id), {
            onSuccess: () => {
                setShowTestEmailForm(false);
                reset();
            }
        });
    };

    return (
        <AdminLayout title={`E-posta Şablonu: ${template.name}`}>
            <Head title={`E-posta Şablonu: ${template.name}`} />

            <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-semibold text-gray-800">E-posta Şablonu Detayları</h2>
                        <div>
                            <SecondaryButton
                                onClick={() => setShowTestEmailForm(!showTestEmailForm)}
                                className="mr-2"
                            >
                                Test E-postası Gönder
                            </SecondaryButton>
                            <Link
                                href={route('admin.email-templates.edit', template.id)}
                                className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mr-2"
                            >
                                Düzenle
                            </Link>
                            <Link
                                href={route('admin.email-templates.index')}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                            >
                                Geri Dön
                            </Link>
                        </div>
                    </div>

                    {showTestEmailForm && (
                        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                            <h3 className="text-lg font-medium mb-2">Test E-postası Gönder</h3>
                            <form onSubmit={handleTestSubmit} className="flex items-end">
                                <div className="flex-1 mr-2">
                                    <InputLabel htmlFor="email" value="E-posta Adresi" />
                                    <TextInput
                                        id="email"
                                        type="email"
                                        name="email"
                                        value={data.email}
                                        className="mt-1 block w-full"
                                        onChange={(e) => setData('email', e.target.value)}
                                        required
                                    />
                                    <InputError message={errors.email} className="mt-2" />
                                </div>
                                <PrimaryButton
                                    className="ml-4"
                                    disabled={processing}
                                >
                                    {processing ? 'Gönderiliyor...' : 'Gönder'}
                                </PrimaryButton>
                            </form>
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h3 className="text-lg font-medium mb-2">Temel Bilgiler</h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500">Şablon Adı</div>
                                    <div className="mt-1 text-sm text-gray-900">{template.name}</div>
                                </div>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500">Şablon Türü</div>
                                    <div className="mt-1 text-sm text-gray-900">{template.type}</div>
                                </div>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500">Konu</div>
                                    <div className="mt-1 text-sm text-gray-900">{template.subject}</div>
                                </div>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500">Durum</div>
                                    <div className="mt-1">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${template.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                            {template.is_active ? 'Aktif' : 'Pasif'}
                                        </span>
                                    </div>
                                </div>
                                {template.description && (
                                    <div className="mb-4">
                                        <div className="text-sm font-medium text-gray-500">Açıklama</div>
                                        <div className="mt-1 text-sm text-gray-900">{template.description}</div>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div>
                            <h3 className="text-lg font-medium mb-2">Kullanılabilir Değişkenler</h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <ul className="list-disc list-inside">
                                    {template.variables && template.variables.map((variable, index) => (
                                        <li key={index} className="text-sm text-gray-700">
                                            <code className="bg-gray-200 px-1 py-0.5 rounded">{'{{' + variable + '}}'}</code>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div className="mb-6">
                        <h3 className="text-lg font-medium mb-2">HTML İçerik</h3>
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <div className="border border-gray-300 rounded-lg p-4 bg-white">
                                <div dangerouslySetInnerHTML={{ __html: template.body_html }} />
                            </div>
                        </div>
                    </div>

                    {template.body_text && (
                        <div className="mb-6">
                            <h3 className="text-lg font-medium mb-2">Düz Metin İçerik</h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono bg-white p-4 border border-gray-300 rounded-lg">
                                    {template.body_text}
                                </pre>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
}
