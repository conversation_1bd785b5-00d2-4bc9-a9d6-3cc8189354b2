import React, { useEffect } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { toast } from "react-hot-toast";

export default function Index({ cacheStats, auth, flash }) {
    const clearForm = useForm({
        type: "all",
    });

    const warmForm = useForm({
        type: "all",
    });

    // Flash mesajı varsa göster
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.error) {
            toast.error(flash.error);
        }
    }, [flash.success, flash.error]);

    return (
        <AdminLayout user={auth.user} title="Cache Yönetimi">
            <Head title="Cache Yönetimi" />

            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div className="p-6 bg-white border-b border-gray-200">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium"><PERSON><PERSON></h3>
                    </div>

                    {/* <PERSON>ache İstatistikleri */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div className="bg-blue-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-blue-700 mb-2">
                                Cache Sürücüsü
                            </h4>
                            <p className="text-xl font-bold text-gray-800">
                                {cacheStats.driver}
                            </p>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-green-700 mb-2">
                                Cache Öneki
                            </h4>
                            <p className="text-xl font-bold text-gray-800">
                                {cacheStats.prefix}
                            </p>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-purple-700 mb-2">
                                Toplam Ürün Sayısı
                            </h4>
                            <p className="text-xl font-bold text-gray-800">
                                {cacheStats.product_count}
                            </p>
                        </div>

                        <div className="bg-amber-50 p-4 rounded-lg shadow">
                            <h4 className="font-semibold text-amber-700 mb-2">
                                Toplam Kategori Sayısı
                            </h4>
                            <p className="text-xl font-bold text-gray-800">
                                {cacheStats.category_count}
                            </p>
                        </div>
                    </div>

                    {/* Toplu İşlemler */}
                    <div className="mb-8">
                        <h3 className="text-lg font-medium mb-4">
                            Toplu Cache İşlemleri
                        </h3>
                        <div className="flex flex-wrap gap-4 mb-6">
                            <form
                                onSubmit={(e) => {
                                    e.preventDefault();
                                    clearForm.data.type = "all";
                                    clearForm.post(route("admin.cache.clear"));
                                }}
                            >
                                <button
                                    type="submit"
                                    className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded"
                                    disabled={clearForm.processing}
                                >
                                    {clearForm.processing &&
                                    clearForm.data.type === "all"
                                        ? "İşleniyor..."
                                        : "Tüm Cache'leri Temizle"}
                                </button>
                            </form>

                            <form
                                onSubmit={(e) => {
                                    e.preventDefault();
                                    warmForm.data.type = "all";
                                    warmForm.post(route("admin.cache.warm"));
                                }}
                            >
                                <button
                                    type="submit"
                                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
                                    disabled={warmForm.processing}
                                >
                                    {warmForm.processing &&
                                    warmForm.data.type === "all"
                                        ? "İşleniyor..."
                                        : "Tüm Cache'leri Isıt"}
                                </button>
                            </form>
                        </div>
                    </div>

                    {/* Cache Türleri ve İşlemler */}
                    <div className="mb-8">
                        <h3 className="text-lg font-medium mb-4">
                            Tek Tek Cache İşlemleri
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                            {cacheStats.cache_types &&
                                Object.entries(cacheStats.cache_types).map(
                                    ([type, info]) => {
                                        // Tailwind sınıflarını dinamik olarak oluşturmak için
                                        const bgColorClass = `bg-${info.color}-50`;
                                        const textColorClass = `text-${info.color}-700`;
                                        const buttonColorClass = `bg-${info.color}-500 hover:bg-${info.color}-600`;

                                        return (
                                            <div
                                                key={type}
                                                className={`${bgColorClass} p-4 rounded-lg shadow`}
                                            >
                                                <h4
                                                    className={`font-semibold ${textColorClass} mb-2`}
                                                >
                                                    {info.name}
                                                </h4>
                                                <p className="text-gray-600 text-sm mb-3">
                                                    {info.description}
                                                </p>
                                                <form
                                                    onSubmit={(e) => {
                                                        e.preventDefault();
                                                        clearForm.data.type =
                                                            type;
                                                        clearForm.post(
                                                            route(
                                                                "admin.cache.clear"
                                                            )
                                                        );
                                                    }}
                                                >
                                                    <button
                                                        type="submit"
                                                        className={`${buttonColorClass} text-white py-1 px-3 rounded text-sm`}
                                                        disabled={
                                                            clearForm.processing
                                                        }
                                                    >
                                                        {clearForm.processing &&
                                                        clearForm.data.type ===
                                                            type
                                                            ? "İşleniyor..."
                                                            : "Temizle"}
                                                    </button>
                                                </form>
                                            </div>
                                        );
                                    }
                                )}
                        </div>

                        <h3 className="text-lg font-medium mb-4 mt-8">
                            Tek Tek Cache Isıtma İşlemleri
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {cacheStats.warmable_types &&
                                Object.entries(cacheStats.warmable_types).map(
                                    ([type, info]) => {
                                        // Tailwind sınıflarını dinamik olarak oluşturmak için
                                        const bgColorClass = `bg-${info.color}-50`;
                                        const textColorClass = `text-${info.color}-700`;
                                        const buttonColorClass = `bg-${info.color}-500 hover:bg-${info.color}-600`;

                                        return (
                                            <div
                                                key={type}
                                                className={`${bgColorClass} p-4 rounded-lg shadow`}
                                            >
                                                <h4
                                                    className={`font-semibold ${textColorClass} mb-2`}
                                                >
                                                    {info.name}
                                                </h4>
                                                <p className="text-gray-600 text-sm mb-3">
                                                    {info.description}
                                                </p>
                                                <form
                                                    onSubmit={(e) => {
                                                        e.preventDefault();
                                                        warmForm.data.type =
                                                            type;
                                                        warmForm.post(
                                                            route(
                                                                "admin.cache.warm"
                                                            )
                                                        );
                                                    }}
                                                >
                                                    <button
                                                        type="submit"
                                                        className={`${buttonColorClass} text-white py-1 px-3 rounded text-sm`}
                                                        disabled={
                                                            warmForm.processing
                                                        }
                                                    >
                                                        {warmForm.processing &&
                                                        warmForm.data.type ===
                                                            type
                                                            ? "İşleniyor..."
                                                            : "Isıt"}
                                                    </button>
                                                </form>
                                            </div>
                                        );
                                    }
                                )}
                        </div>
                    </div>

                    {/* Cache TTL Ayarları */}
                    <div>
                        <h3 className="text-lg font-medium mb-4">
                            Cache TTL Ayarları
                        </h3>
                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white">
                                <thead className="bg-gray-100">
                                    <tr>
                                        <th className="py-2 px-4 border-b text-left">
                                            Tür
                                        </th>
                                        <th className="py-2 px-4 border-b text-left">
                                            Süre (saniye)
                                        </th>
                                        <th className="py-2 px-4 border-b text-left">
                                            Açıklama
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {cacheStats.ttl_settings &&
                                        Object.entries(
                                            cacheStats.ttl_settings
                                        ).map(([category, settings]) => (
                                            <React.Fragment key={category}>
                                                {Object.entries(settings).map(
                                                    ([key, value]) => (
                                                        <tr
                                                            key={`${category}_${key}`}
                                                            className="hover:bg-gray-50"
                                                        >
                                                            <td className="py-2 px-4 border-b">
                                                                {category}.{key}
                                                            </td>
                                                            <td className="py-2 px-4 border-b">
                                                                {value}
                                                            </td>
                                                            <td className="py-2 px-4 border-b">
                                                                {value === 3600
                                                                    ? "1 saat"
                                                                    : value ===
                                                                      7200
                                                                    ? "2 saat"
                                                                    : value ===
                                                                      1800
                                                                    ? "30 dakika"
                                                                    : value ===
                                                                      86400
                                                                    ? "24 saat"
                                                                    : value ===
                                                                      900
                                                                    ? "15 dakika"
                                                                    : `${Math.round(
                                                                          value /
                                                                              60
                                                                      )} dakika`}
                                                            </td>
                                                        </tr>
                                                    )
                                                )}
                                            </React.Fragment>
                                        ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
