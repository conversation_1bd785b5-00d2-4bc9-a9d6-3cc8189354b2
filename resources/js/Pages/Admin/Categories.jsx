import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import { toast } from "react-hot-toast";
import AdminLayout from "@/Layouts/AdminLayout";
import Pagination from "@/Components/Pagination";

export default function Categories({ categories, filters }) {
    const [searchTerm, setSearchTerm] = useState(filters.search || "");

    function handleSearch(e) {
        e.preventDefault();
        router.get(
            "/admin/categories",
            { search: searchTerm, per_page: filters.per_page },
            { preserveState: true }
        );
    }

    function handlePerPageChange(e) {
        router.get(
            "/admin/categories",
            { search: searchTerm, per_page: e.target.value },
            { preserveState: true }
        );
    }
    return (
        <AdminLayout title="Kategoriler">
            <Head title="Kategoriler" />

            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div className="p-6 bg-white border-b border-gray-200">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium">
                            Kate<PERSON><PERSON>
                        </h3>
                        <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Yeni Kategori Ekle
                        </button>
                    </div>

                    {/* Arama ve Filtreleme */}
                    <div className="mb-6">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <div className="flex-1">
                                <input
                                    type="text"
                                    placeholder="Kategori ara..."
                                    className="w-full px-4 py-2 border rounded-lg"
                                    value={searchTerm}
                                    onChange={(e) =>
                                        setSearchTerm(e.target.value)
                                    }
                                />
                            </div>
                            <button
                                type="submit"
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg"
                            >
                                Ara
                            </button>
                            <select
                                className="px-4 py-2 border rounded-lg"
                                value={filters.per_page}
                                onChange={handlePerPageChange}
                            >
                                <option value="5">5 / sayfa</option>
                                <option value="10">10 / sayfa</option>
                                <option value="25">25 / sayfa</option>
                                <option value="50">50 / sayfa</option>
                            </select>
                        </form>
                    </div>

                    {/* Kategori Tablosu */}
                    <div className="overflow-x-auto">
                        <table className="min-w-full bg-white">
                            <thead className="bg-gray-100">
                                <tr>
                                    <th className="py-2 px-4 border-b text-left">
                                        ID
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Kategori Adı
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Üst Kategori
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        Durum
                                    </th>
                                    <th className="py-2 px-4 border-b text-left">
                                        İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {categories.data.map((category) => (
                                    <tr key={category.id}>
                                        <td className="py-2 px-4 border-b">
                                            {category.id}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            {category.name}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            {category.parent
                                                ? category.parent.name
                                                : "-"}
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            <span
                                                className={`px-2 py-1 rounded text-xs ${
                                                    category.status
                                                        ? "bg-green-100 text-green-800"
                                                        : "bg-red-100 text-red-800"
                                                }`}
                                            >
                                                {category.status
                                                    ? "Aktif"
                                                    : "Pasif"}
                                            </span>
                                        </td>
                                        <td className="py-2 px-4 border-b">
                                            <Link
                                                href={route(
                                                    "admin.categories.edit",
                                                    category.id
                                                )}
                                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                                            >
                                                Düzenle
                                            </Link>
                                            <Link
                                                href={route(
                                                    "admin.categories.attributes.edit",
                                                    category.id
                                                )}
                                                className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                                            >
                                                Özellikler
                                            </Link>
                                            <button className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded">
                                                Sil
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Sayfalama */}
                    <div className="mt-6">
                        <Pagination links={categories.links} />
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
