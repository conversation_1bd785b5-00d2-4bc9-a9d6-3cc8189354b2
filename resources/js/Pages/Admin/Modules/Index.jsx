import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { toast } from "react-hot-toast";

export default function Index({ modules }) {
    const [expandedModule, setExpandedModule] = useState(null);
    
    const { data, setData, post, processing, errors } = useForm({
        modules: modules.reduce((acc, module) => {
            acc[module.name] = module.enabled;
            return acc;
        }, {})
    });
    
    const toggleModule = (moduleName) => {
        // Modülün mevcut durumunu al
        const currentStatus = data.modules[moduleName];
        
        // Modülü devre dışı bırakıyorsak, bağımlı modülleri kontrol et
        if (currentStatus) {
            const dependentModules = modules.find(m => m.name === moduleName)?.dependents || [];
            
            if (dependentModules.length > 0) {
                const enabledDependents = dependentModules.filter(dep => data.modules[dep]);
                
                if (enabledDependents.length > 0) {
                    toast.error(`Bu modül devre dışı bırakılamaz çünkü şu modüller tarafından kullanılıyor: ${enabledDependents.join(', ')}`);
                    return;
                }
            }
        }
        
        // Modülü etkinleştiriyorsak, bağımlılıklarını da etkinleştir
        if (!currentStatus) {
            const dependencies = modules.find(m => m.name === moduleName)?.dependencies || [];
            
            const updatedModules = { ...data.modules };
            updatedModules[moduleName] = !currentStatus;
            
            // Bağımlılıkları da etkinleştir
            dependencies.forEach(dep => {
                updatedModules[dep] = true;
            });
            
            setData('modules', updatedModules);
            return;
        }
        
        // Normal durum değişikliği
        setData('modules', {
            ...data.modules,
            [moduleName]: !currentStatus
        });
    };
    
    const handleSubmit = (e) => {
        e.preventDefault();
        
        post(route('admin.modules.update'), {
            onSuccess: () => {
                toast.success('Modül ayarları başarıyla güncellendi');
            }
        });
    };
    
    const toggleDetails = (moduleName) => {
        if (expandedModule === moduleName) {
            setExpandedModule(null);
        } else {
            setExpandedModule(moduleName);
        }
    };
    
    return (
        <AdminLayout>
            <Head title="Modül Yönetimi" />
            
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    Modül Yönetimi
                                </h2>
                            </div>
                            
                            <div className="mb-6">
                                <p className="text-gray-600">
                                    Modülleri etkinleştirip devre dışı bırakarak sistemin hangi özelliklerinin aktif olacağını belirleyebilirsiniz. 
                                    Bazı modüller diğer modüllere bağımlı olabilir, bu durumda bağımlı olunan modüller otomatik olarak etkinleştirilir.
                                </p>
                            </div>
                            
                            <form onSubmit={handleSubmit}>
                                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg mb-6">
                                    <table className="min-w-full divide-y divide-gray-300">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                                                    Modül Adı
                                                </th>
                                                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                    Açıklama
                                                </th>
                                                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                    Durum
                                                </th>
                                                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                    Detaylar
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200 bg-white">
                                            {modules.map((module) => (
                                                <React.Fragment key={module.name}>
                                                    <tr>
                                                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                            {module.name}
                                                        </td>
                                                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                            {module.description}
                                                        </td>
                                                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                            <div className="flex items-center">
                                                                <button
                                                                    type="button"
                                                                    className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                                                                        data.modules[module.name] ? 'bg-indigo-600' : 'bg-gray-200'
                                                                    }`}
                                                                    onClick={() => toggleModule(module.name)}
                                                                >
                                                                    <span
                                                                        aria-hidden="true"
                                                                        className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                                                                            data.modules[module.name] ? 'translate-x-5' : 'translate-x-0'
                                                                        }`}
                                                                    />
                                                                </button>
                                                                <span className="ml-3 text-sm">
                                                                    {data.modules[module.name] ? 'Etkin' : 'Devre Dışı'}
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                            <button
                                                                type="button"
                                                                className="text-indigo-600 hover:text-indigo-900"
                                                                onClick={() => toggleDetails(module.name)}
                                                            >
                                                                {expandedModule === module.name ? 'Gizle' : 'Göster'}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    
                                                    {expandedModule === module.name && (
                                                        <tr>
                                                            <td colSpan="4" className="px-6 py-4 bg-gray-50">
                                                                <div className="text-sm">
                                                                    <div className="mb-3">
                                                                        <h4 className="font-medium text-gray-900">Bağımlılıklar</h4>
                                                                        {module.dependencies.length > 0 ? (
                                                                            <ul className="mt-1 list-disc list-inside text-gray-600">
                                                                                {module.dependencies.map(dep => (
                                                                                    <li key={dep}>
                                                                                        {dep} {data.modules[dep] ? '(Etkin)' : '(Devre Dışı)'}
                                                                                    </li>
                                                                                ))}
                                                                            </ul>
                                                                        ) : (
                                                                            <p className="mt-1 text-gray-600">Bu modülün bağımlılığı bulunmamaktadır.</p>
                                                                        )}
                                                                    </div>
                                                                    
                                                                    <div>
                                                                        <h4 className="font-medium text-gray-900">Bağımlı Modüller</h4>
                                                                        {module.dependents.length > 0 ? (
                                                                            <ul className="mt-1 list-disc list-inside text-gray-600">
                                                                                {module.dependents.map(dep => (
                                                                                    <li key={dep}>
                                                                                        {dep} {data.modules[dep] ? '(Etkin)' : '(Devre Dışı)'}
                                                                                    </li>
                                                                                ))}
                                                                            </ul>
                                                                        ) : (
                                                                            <p className="mt-1 text-gray-600">Bu modüle bağımlı modül bulunmamaktadır.</p>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    )}
                                                </React.Fragment>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div className="flex justify-end">
                                    <button
                                        type="submit"
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        disabled={processing}
                                    >
                                        {processing ? 'Kaydediliyor...' : 'Değişiklikleri Kaydet'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
