import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AdminLayout from '../../../Layouts/AdminLayout.jsx';
import ProductList from '../../../Components/Modern/ProductList.jsx';
import { useProducts, useMutation } from '../../../Hooks/useApiData.js';
import { useForm, validators, createValidationSchema } from '../../../Hooks/useForm.js';
import { useApi } from '../../../Contexts/ApiContext.jsx';
import apiAdapter from '../../../Services/ApiAdapter.js';
import ErrorBoundary from '../../../Components/ui/ErrorBoundary.jsx';
import LoadingSpinner, { LoadingButton } from '../../../Components/ui/LoadingSpinner.jsx';
import Modal from '../../../Components/Modal.jsx';

/**
 * Modern Products Management Page
 * Phase 4: Frontend Integration Example
 * 
 * Bu sayfa Phase 4'te implementasyonu tamamlanan tüm özellikleri gösterir
 */

// Form validation schema
const productValidationSchema = createValidationSchema({
    name: [validators.required('Ürün adı zorunludur'), validators.minLength(3, 'En az 3 karakter olmalıdır')],
    sku: [validators.required('SKU zorunludur')],
    price: [validators.required('Fiyat zorunludur'), validators.min(0, 'Fiyat 0\'dan büyük olmalıdır')],
    description: [validators.maxLength(1000, 'Açıklama en fazla 1000 karakter olabilir')]
});

function ModernProductsPage() {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [filters, setFilters] = useState({});
    
    // API Context
    const { config, updateConfig } = useApi();
    
    // Create product mutation
    const { 
        mutate: createProduct, 
        loading: creating, 
        error: createError 
    } = useMutation(apiAdapter.createProduct);
    
    // Form for new product
    const {
        values,
        errors,
        touched,
        isSubmitting,
        handleChange,
        handleBlur,
        handleSubmit,
        resetForm,
        getFieldProps,
        getFieldMeta
    } = useForm(
        {
            name: '',
            sku: '',
            price: '',
            description: '',
            category_id: '',
            status: true
        },
        {
            validate: productValidationSchema,
            onSubmit: async (data) => {
                try {
                    await createProduct({
                        ...data,
                        price: parseFloat(data.price),
                        category_id: data.category_id ? parseInt(data.category_id) : null
                    });
                    
                    setShowCreateModal(false);
                    resetForm();
                    
                    // Show success message
                    window.toast?.success('Ürün başarıyla oluşturuldu!');
                } catch (error) {
                    console.error('Product creation failed:', error);
                    window.toast?.error('Ürün oluşturulurken hata oluştu!');
                }
            }
        }
    );
    
    // Handle API configuration changes
    const handleConfigChange = (key, value) => {
        updateConfig({ [key]: value });
        window.toast?.info(`${key} ${value ? 'aktifleştirildi' : 'devre dışı bırakıldı'}`);
    };
    
    return (
        <AdminLayout title="Modern Ürün Yönetimi">
            <Head title="Modern Ürün Yönetimi" />
            
            <ErrorBoundary>
                <div className="space-y-6">
                    {/* Header */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                                    Modern Ürün Yönetimi
                                </h1>
                                <p className="text-gray-600">
                                    Phase 4: Frontend Integration ile modernize edilmiş ürün yönetimi
                                </p>
                            </div>
                            
                            <div className="flex items-center space-x-4">
                                {/* API Configuration */}
                                <div className="flex items-center space-x-3 text-sm">
                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={config.useCQRS}
                                            onChange={(e) => handleConfigChange('useCQRS', e.target.checked)}
                                            className="mr-2"
                                        />
                                        CQRS Kullan
                                    </label>
                                    
                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={config.useNewAPI}
                                            onChange={(e) => handleConfigChange('useNewAPI', e.target.checked)}
                                            className="mr-2"
                                        />
                                        Yeni API
                                    </label>
                                    
                                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                        {config.version}
                                    </span>
                                </div>
                                
                                <button
                                    onClick={() => setShowCreateModal(true)}
                                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Yeni Ürün Ekle
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    {/* Filters */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-semibold mb-4">Filtreler</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Kategori
                                </label>
                                <select
                                    value={filters.category_id || ''}
                                    onChange={(e) => setFilters(prev => ({ 
                                        ...prev, 
                                        category_id: e.target.value || undefined 
                                    }))}
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Tüm Kategoriler</option>
                                    <option value="1">Elektronik</option>
                                    <option value="2">Giyim</option>
                                    <option value="3">Ev & Yaşam</option>
                                </select>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Durum
                                </label>
                                <select
                                    value={filters.status || ''}
                                    onChange={(e) => setFilters(prev => ({ 
                                        ...prev, 
                                        status: e.target.value || undefined 
                                    }))}
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Tüm Durumlar</option>
                                    <option value="true">Aktif</option>
                                    <option value="false">Pasif</option>
                                </select>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Min Fiyat
                                </label>
                                <input
                                    type="number"
                                    value={filters.min_price || ''}
                                    onChange={(e) => setFilters(prev => ({ 
                                        ...prev, 
                                        min_price: e.target.value || undefined 
                                    }))}
                                    placeholder="0"
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Max Fiyat
                                </label>
                                <input
                                    type="number"
                                    value={filters.max_price || ''}
                                    onChange={(e) => setFilters(prev => ({ 
                                        ...prev, 
                                        max_price: e.target.value || undefined 
                                    }))}
                                    placeholder="1000"
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>
                        
                        <div className="mt-4 flex space-x-3">
                            <button
                                onClick={() => setFilters({})}
                                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                            >
                                Filtreleri Temizle
                            </button>
                        </div>
                    </div>
                    
                    {/* Product List */}
                    <ProductList
                        initialFilters={filters}
                        showFilters={false}
                        showPagination={true}
                        showSorting={true}
                        pageSize={12}
                        layout="grid"
                        className="bg-white rounded-lg shadow"
                    />
                </div>
                
                {/* Create Product Modal */}
                <Modal
                    show={showCreateModal}
                    onClose={() => {
                        setShowCreateModal(false);
                        resetForm();
                    }}
                    maxWidth="2xl"
                >
                    <div className="p-6">
                        <h2 className="text-xl font-semibold mb-6">Yeni Ürün Ekle</h2>
                        
                        {createError && (
                            <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
                                <p className="text-red-600 text-sm">{createError}</p>
                            </div>
                        )}
                        
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Ürün Adı *
                                    </label>
                                    <input
                                        {...getFieldProps('name')}
                                        type="text"
                                        className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 ${
                                            getFieldMeta('name').invalid 
                                                ? 'border-red-300 focus:border-red-500' 
                                                : 'border-gray-300 focus:border-blue-500'
                                        }`}
                                    />
                                    {getFieldMeta('name').invalid && (
                                        <p className="text-red-600 text-sm mt-1">{errors.name}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        SKU *
                                    </label>
                                    <input
                                        {...getFieldProps('sku')}
                                        type="text"
                                        className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 ${
                                            getFieldMeta('sku').invalid 
                                                ? 'border-red-300 focus:border-red-500' 
                                                : 'border-gray-300 focus:border-blue-500'
                                        }`}
                                    />
                                    {getFieldMeta('sku').invalid && (
                                        <p className="text-red-600 text-sm mt-1">{errors.sku}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Fiyat *
                                    </label>
                                    <input
                                        {...getFieldProps('price')}
                                        type="number"
                                        step="0.01"
                                        className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 ${
                                            getFieldMeta('price').invalid 
                                                ? 'border-red-300 focus:border-red-500' 
                                                : 'border-gray-300 focus:border-blue-500'
                                        }`}
                                    />
                                    {getFieldMeta('price').invalid && (
                                        <p className="text-red-600 text-sm mt-1">{errors.price}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Kategori
                                    </label>
                                    <select
                                        {...getFieldProps('category_id')}
                                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Kategori Seçin</option>
                                        <option value="1">Elektronik</option>
                                        <option value="2">Giyim</option>
                                        <option value="3">Ev & Yaşam</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Açıklama
                                </label>
                                <textarea
                                    {...getFieldProps('description')}
                                    rows={4}
                                    className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 ${
                                        getFieldMeta('description').invalid 
                                            ? 'border-red-300 focus:border-red-500' 
                                            : 'border-gray-300 focus:border-blue-500'
                                    }`}
                                />
                                {getFieldMeta('description').invalid && (
                                    <p className="text-red-600 text-sm mt-1">{errors.description}</p>
                                )}
                            </div>
                            
                            <div className="flex items-center">
                                <input
                                    {...getFieldProps('status')}
                                    type="checkbox"
                                    className="mr-2"
                                />
                                <label className="text-sm font-medium text-gray-700">
                                    Aktif
                                </label>
                            </div>
                            
                            <div className="flex justify-end space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={() => {
                                        setShowCreateModal(false);
                                        resetForm();
                                    }}
                                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                                >
                                    İptal
                                </button>
                                
                                <LoadingButton
                                    type="submit"
                                    isLoading={isSubmitting || creating}
                                    loadingText="Kaydediliyor..."
                                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Kaydet
                                </LoadingButton>
                            </div>
                        </form>
                    </div>
                </Modal>
            </ErrorBoundary>
        </AdminLayout>
    );
}

export default ModernProductsPage;
