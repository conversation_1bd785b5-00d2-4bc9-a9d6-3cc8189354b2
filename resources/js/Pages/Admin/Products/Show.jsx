import React from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Show({ product }) {
    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Stok durumu formatla
    const formatStockStatus = (stock) => {
        if (stock > 10) {
            return <span className="text-green-600">Stokta</span>;
        } else if (stock > 0) {
            return <span className="text-yellow-600">Sınırlı Stok</span>;
        } else {
            return <span className="text-red-600">Stok Dışı</span>;
        }
    };

    return (
        <AdminLayout title={`Ürün: ${product.name}`}>
            <Head title={`Ürün: ${product.name}`} />

            <div className="mb-6 flex justify-between items-center">
                <div>
                    <Link
                        href={route("admin.products.index")}
                        className="text-blue-600 hover:text-blue-800"
                    >
                        &larr; Ürünlere Dön
                    </Link>
                    <h2 className="text-xl font-semibold mt-2">
                        Ürün Detayı: {product.name}
                    </h2>
                </div>
                <div className="flex space-x-2">
                    <Link
                        href={route("admin.products.edit", product.id)}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                    >
                        Düzenle
                    </Link>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="md:flex">
                    {/* Ürün Görseli */}
                    <div className="md:w-1/3 p-6">
                        <img
                            src={product.image || "/images/placeholder.png"}
                            alt={product.name}
                            className="w-full h-auto object-cover rounded-lg"
                        />
                    </div>

                    {/* Ürün Bilgileri */}
                    <div className="md:w-2/3 p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="text-lg font-semibold mb-4">
                                    Temel Bilgiler
                                </h3>
                                <div className="space-y-3">
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            ID:
                                        </span>{" "}
                                        {product.id}
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Ad:
                                        </span>{" "}
                                        {product.name}
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Slug:
                                        </span>{" "}
                                        {product.slug}
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Fiyat:
                                        </span>{" "}
                                        {formatPrice(product.price)}
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Stok:
                                        </span>{" "}
                                        {product.stock} ({formatStockStatus(product.stock)})
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Durum:
                                        </span>{" "}
                                        {product.status ? (
                                            <span className="text-green-600">
                                                Aktif
                                            </span>
                                        ) : (
                                            <span className="text-red-600">
                                                Pasif
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold mb-4">
                                    Kategori Bilgileri
                                </h3>
                                <div className="space-y-3">
                                    {product.category ? (
                                        <>
                                            <div>
                                                <span className="text-gray-600 font-medium">
                                                    Kategori:
                                                </span>{" "}
                                                {product.category.name}
                                            </div>
                                            <div>
                                                <span className="text-gray-600 font-medium">
                                                    Kategori ID:
                                                </span>{" "}
                                                {product.category.id}
                                            </div>
                                        </>
                                    ) : (
                                        <div className="text-gray-500">
                                            Kategori atanmamış
                                        </div>
                                    )}
                                </div>

                                <h3 className="text-lg font-semibold mb-4 mt-8">
                                    Tarih Bilgileri
                                </h3>
                                <div className="space-y-3">
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Oluşturulma:
                                        </span>{" "}
                                        {new Date(
                                            product.created_at
                                        ).toLocaleString("tr-TR")}
                                    </div>
                                    <div>
                                        <span className="text-gray-600 font-medium">
                                            Güncelleme:
                                        </span>{" "}
                                        {new Date(
                                            product.updated_at
                                        ).toLocaleString("tr-TR")}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="mt-8">
                            <h3 className="text-lg font-semibold mb-4">
                                Açıklama
                            </h3>
                            <div className="prose max-w-none">
                                {product.description ? (
                                    <p>{product.description}</p>
                                ) : (
                                    <p className="text-gray-500">
                                        Açıklama bulunmuyor
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
