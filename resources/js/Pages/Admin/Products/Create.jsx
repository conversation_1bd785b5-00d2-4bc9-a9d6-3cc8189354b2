import React, { useState, useEffect, useRef } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputError from "@/Components/InputError";
import CategoryTree from "@/Components/CategoryTree";
import CategoryService from "@/Services/CategoryService";

export default function Create({ categories: initialCategories }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: "",
        description: "",
        price: "",
        stock: "",
        category_id: "",
        status: true,
        weight: "",
        desi: "1", // Default değer 1
        is_featured: false,
        is_on_sale: false,
        sale_price: "",
        sale_starts_at: "",
        sale_ends_at: "",
    });

    // Yönlendirme türü için state
    const [redirectType, setRedirectType] = useState("index"); // 'index' veya 'create'
    const [categoryTree, setCategoryTree] = useState([]);
    const [showCategoryTree, setShowCategoryTree] = useState(false);
    const [isLoadingCategories, setIsLoadingCategories] = useState(false);
    const [categoryPath, setCategoryPath] = useState([]);
    const categoryDropdownRef = useRef(null);

    // Dışarıda bir yere tıklandığında kategori ağacını kapat
    useEffect(() => {
        function handleClickOutside(event) {
            if (
                categoryDropdownRef.current &&
                !categoryDropdownRef.current.contains(event.target)
            ) {
                setShowCategoryTree(false);
            }
        }

        // Event listener ekle
        document.addEventListener("mousedown", handleClickOutside);

        // Cleanup
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [categoryDropdownRef]);

    // Kategori ağacını yükle
    useEffect(() => {
        const loadCategoryTree = async () => {
            try {
                setIsLoadingCategories(true);
                const treeData = await CategoryService.getCategoryTree();
                setCategoryTree(treeData);
            } catch (error) {
                console.error("Kategori ağacı yüklenirken hata oluştu:", error);
                // Hata durumunda düz listeyi kullan
                setCategoryTree([]);
            } finally {
                setIsLoadingCategories(false);
            }
        };

        loadCategoryTree();
    }, []);

    // Kategori seçimi
    const handleCategorySelect = (categoryId) => {
        setData("category_id", categoryId);
        setShowCategoryTree(false);
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Form verilerine redirect_type ekle
        const formData = {
            ...data,
            redirect_type: redirectType,
        };

        post(route("admin.products.store"), formData, {
            onSuccess: () => {
                if (redirectType === "create") {
                    // Formu temizle ve aynı sayfada kal
                    reset();
                    window.scrollTo(0, 0);
                }
                // 'index' durumunda controller zaten ürünler sayfasına yönlendirecek
            },
        });
    };

    return (
        <AdminLayout title="Yeni Ürün Ekle">
            <Head title="Yeni Ürün Ekle" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-lg font-medium text-gray-900">
                                    Yeni Ürün Ekle
                                </h3>
                                <a
                                    href={route("admin.products.index")}
                                    className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded"
                                >
                                    Geri Dön
                                </a>
                            </div>

                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    {/* Ürün Adı */}
                                    <div>
                                        <label
                                            htmlFor="name"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Ürün Adı
                                        </label>
                                        <input
                                            type="text"
                                            id="name"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.name}
                                            onChange={(e) =>
                                                setData("name", e.target.value)
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.name}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Kategori */}
                                    <div>
                                        <label
                                            htmlFor="category_id"
                                            className="block text-sm font-medium text-gray-700 mb-1"
                                        >
                                            Kategori
                                        </label>

                                        <div
                                            className="relative"
                                            ref={categoryDropdownRef}
                                        >
                                            {/* Seçili kategori gösterimi */}
                                            <div
                                                className="flex items-center justify-between w-full px-3 py-2 border rounded-md cursor-pointer hover:border-gray-400"
                                                onClick={() =>
                                                    setShowCategoryTree(
                                                        !showCategoryTree
                                                    )
                                                }
                                            >
                                                <span
                                                    className={
                                                        data.category_id
                                                            ? "text-gray-900"
                                                            : "text-gray-500"
                                                    }
                                                >
                                                    {data.category_id
                                                        ? categoryPath.length >
                                                          0
                                                            ? categoryPath
                                                                  .map(
                                                                      (cat) =>
                                                                          cat.name
                                                                  )
                                                                  .join(" > ")
                                                            : initialCategories.find(
                                                                  (c) =>
                                                                      c.id ===
                                                                      parseInt(
                                                                          data.category_id
                                                                      )
                                                              )?.name ||
                                                              "Kategori Seçin"
                                                        : "Kategori Seçin"}
                                                </span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className="h-5 w-5 text-gray-400"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                            </div>

                                            {/* Kategori ağacı */}
                                            {showCategoryTree && (
                                                <div className="absolute z-10 w-full mt-1">
                                                    {isLoadingCategories ? (
                                                        <div className="bg-white border rounded-md p-4 text-center">
                                                            <svg
                                                                className="animate-spin h-5 w-5 text-blue-500 mx-auto"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                fill="none"
                                                                viewBox="0 0 24 24"
                                                            >
                                                                <circle
                                                                    className="opacity-25"
                                                                    cx="12"
                                                                    cy="12"
                                                                    r="10"
                                                                    stroke="currentColor"
                                                                    strokeWidth="4"
                                                                ></circle>
                                                                <path
                                                                    className="opacity-75"
                                                                    fill="currentColor"
                                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                                ></path>
                                                            </svg>
                                                            <p className="mt-2 text-sm text-gray-600">
                                                                Kategoriler
                                                                yükleniyor...
                                                            </p>
                                                        </div>
                                                    ) : (
                                                        <CategoryTree
                                                            categories={
                                                                categoryTree
                                                            }
                                                            selectedCategoryId={
                                                                data.category_id
                                                                    ? parseInt(
                                                                          data.category_id
                                                                      )
                                                                    : null
                                                            }
                                                            onSelect={
                                                                handleCategorySelect
                                                            }
                                                            onCategoryPathChange={
                                                                setCategoryPath
                                                            }
                                                            showCheckbox={true}
                                                        />
                                                    )}
                                                </div>
                                            )}
                                        </div>

                                        <InputError
                                            message={errors.category_id}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Fiyat */}
                                    <div>
                                        <label
                                            htmlFor="price"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Fiyat (TL)
                                        </label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            id="price"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.price}
                                            onChange={(e) =>
                                                setData("price", e.target.value)
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.price}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Stok */}
                                    <div>
                                        <label
                                            htmlFor="stock"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Stok Miktarı
                                        </label>
                                        <input
                                            type="number"
                                            id="stock"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.stock}
                                            onChange={(e) =>
                                                setData("stock", e.target.value)
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.stock}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Durum */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                            Durum
                                        </label>
                                        <div className="mt-2">
                                            <label className="inline-flex items-center">
                                                <input
                                                    type="radio"
                                                    className="form-radio"
                                                    name="status"
                                                    value="1"
                                                    checked={
                                                        data.status === true
                                                    }
                                                    onChange={() =>
                                                        setData("status", true)
                                                    }
                                                />
                                                <span className="ml-2">
                                                    Aktif
                                                </span>
                                            </label>
                                            <label className="inline-flex items-center ml-6">
                                                <input
                                                    type="radio"
                                                    className="form-radio"
                                                    name="status"
                                                    value="0"
                                                    checked={
                                                        data.status === false
                                                    }
                                                    onChange={() =>
                                                        setData("status", false)
                                                    }
                                                />
                                                <span className="ml-2">
                                                    Pasif
                                                </span>
                                            </label>
                                        </div>
                                        <InputError
                                            message={errors.status}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Ağırlık */}
                                    <div>
                                        <label
                                            htmlFor="weight"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Ağırlık (kg)
                                        </label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            id="weight"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.weight}
                                            onChange={(e) =>
                                                setData(
                                                    "weight",
                                                    e.target.value
                                                )
                                            }
                                        />
                                        <InputError
                                            message={errors.weight}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Desi */}
                                    <div>
                                        <label
                                            htmlFor="desi"
                                            className="block text-sm font-medium text-gray-700"
                                        >
                                            Desi
                                        </label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            id="desi"
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            value={data.desi}
                                            onChange={(e) =>
                                                setData("desi", e.target.value)
                                            }
                                        />
                                        <p className="text-xs text-gray-500 mt-1">
                                            Desi = (En x Boy x Yükseklik) / 3000
                                            (cm cinsinden)
                                        </p>
                                        <InputError
                                            message={errors.desi}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* Öne Çıkan Ürün */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                            Öne Çıkan Ürün
                                        </label>
                                        <div className="mt-2">
                                            <label className="inline-flex items-center">
                                                <input
                                                    type="checkbox"
                                                    className="form-checkbox"
                                                    checked={data.is_featured}
                                                    onChange={(e) =>
                                                        setData(
                                                            "is_featured",
                                                            e.target.checked
                                                        )
                                                    }
                                                />
                                                <span className="ml-2">
                                                    Bu ürünü öne çıkar
                                                </span>
                                            </label>
                                        </div>
                                        <InputError
                                            message={errors.is_featured}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* İndirimde */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">
                                            İndirimde
                                        </label>
                                        <div className="mt-2">
                                            <label className="inline-flex items-center">
                                                <input
                                                    type="checkbox"
                                                    className="form-checkbox"
                                                    checked={data.is_on_sale}
                                                    onChange={(e) =>
                                                        setData(
                                                            "is_on_sale",
                                                            e.target.checked
                                                        )
                                                    }
                                                />
                                                <span className="ml-2">
                                                    Bu ürün indirimde
                                                </span>
                                            </label>
                                        </div>
                                        <InputError
                                            message={errors.is_on_sale}
                                            className="mt-2"
                                        />
                                    </div>

                                    {/* İndirimli Fiyat */}
                                    {data.is_on_sale && (
                                        <div>
                                            <label
                                                htmlFor="sale_price"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                İndirimli Fiyat (TL)
                                            </label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                id="sale_price"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                value={data.sale_price}
                                                onChange={(e) =>
                                                    setData(
                                                        "sale_price",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <InputError
                                                message={errors.sale_price}
                                                className="mt-2"
                                            />
                                        </div>
                                    )}

                                    {/* İndirim Başlangıç Tarihi */}
                                    {data.is_on_sale && (
                                        <div>
                                            <label
                                                htmlFor="sale_starts_at"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                İndirim Başlangıç Tarihi
                                            </label>
                                            <input
                                                type="datetime-local"
                                                id="sale_starts_at"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                value={data.sale_starts_at}
                                                onChange={(e) =>
                                                    setData(
                                                        "sale_starts_at",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <InputError
                                                message={errors.sale_starts_at}
                                                className="mt-2"
                                            />
                                        </div>
                                    )}

                                    {/* İndirim Bitiş Tarihi */}
                                    {data.is_on_sale && (
                                        <div>
                                            <label
                                                htmlFor="sale_ends_at"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                İndirim Bitiş Tarihi
                                            </label>
                                            <input
                                                type="datetime-local"
                                                id="sale_ends_at"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                value={data.sale_ends_at}
                                                onChange={(e) =>
                                                    setData(
                                                        "sale_ends_at",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <InputError
                                                message={errors.sale_ends_at}
                                                className="mt-2"
                                            />
                                        </div>
                                    )}
                                </div>

                                {/* Açıklama */}
                                <div className="mb-6">
                                    <label
                                        htmlFor="description"
                                        className="block text-sm font-medium text-gray-700"
                                    >
                                        Ürün Açıklaması
                                    </label>
                                    <textarea
                                        id="description"
                                        rows="4"
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        value={data.description}
                                        onChange={(e) =>
                                            setData(
                                                "description",
                                                e.target.value
                                            )
                                        }
                                    ></textarea>
                                    <InputError
                                        message={errors.description}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="flex items-center justify-end">
                                    <a
                                        href={route("admin.products.index")}
                                        className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                    >
                                        İptal
                                    </a>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setRedirectType("create");
                                            handleSubmit(new Event("submit"));
                                        }}
                                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2"
                                        disabled={processing}
                                    >
                                        {processing
                                            ? "Kaydediliyor..."
                                            : "Kaydet ve Yeni Ekle"}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setRedirectType("index");
                                            handleSubmit(new Event("submit"));
                                        }}
                                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                        disabled={processing}
                                    >
                                        {processing
                                            ? "Kaydediliyor..."
                                            : "Kaydet"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
