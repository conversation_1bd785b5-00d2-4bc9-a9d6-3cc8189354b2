import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Create({ types }) {
    const { data, setData, post, processing, errors } = useForm({
        name: "",
        code: "",
        type: "select",
        is_required: false,
        is_filterable: false,
        is_variant: false,
        position: 0,
        values: [],
    });

    const [newValue, setNewValue] = useState("");

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("admin.attributes.store"));
    };

    const addValue = () => {
        if (!newValue.trim()) return;

        setData("values", [
            ...data.values,
            {
                value: newValue,
                label: newValue,
                position: data.values.length,
            },
        ]);
        setNewValue("");
    };

    const removeValue = (index) => {
        const updatedValues = [...data.values];
        updatedValues.splice(index, 1);

        // Update positions
        const reorderedValues = updatedValues.map((value, idx) => ({
            ...value,
            position: idx,
        }));

        setData("values", reorderedValues);
    };

    const needsValues = ["select", "multiple"].includes(data.type);

    return (
        <AdminLayout title="Yeni Özellik Ekle">
            <Head title="Yeni Özellik Ekle" />

            <div className="mb-6">
                <Link
                    href={route("admin.attributes.index")}
                    className="text-blue-600 hover:text-blue-800"
                >
                    &larr; Özelliklere Dön
                </Link>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
                <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Özellik Adı
                            </label>
                            <input
                                type="text"
                                id="name"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.name}
                                onChange={(e) =>
                                    setData("name", e.target.value)
                                }
                                required
                            />
                            {errors.name && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="code"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Kod (Boş bırakılırsa otomatik oluşturulur)
                            </label>
                            <input
                                type="text"
                                id="code"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.code}
                                onChange={(e) =>
                                    setData("code", e.target.value)
                                }
                            />
                            {errors.code && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.code}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="type"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Tür
                            </label>
                            <select
                                id="type"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.type}
                                onChange={(e) =>
                                    setData("type", e.target.value)
                                }
                                required
                            >
                                {Object.entries(types).map(([value, label]) => (
                                    <option key={value} value={value}>
                                        {label}
                                    </option>
                                ))}
                            </select>
                            {errors.type && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.type}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="position"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Sıralama Pozisyonu
                            </label>
                            <input
                                type="number"
                                id="position"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.position}
                                onChange={(e) =>
                                    setData(
                                        "position",
                                        parseInt(e.target.value) || 0
                                    )
                                }
                                min="0"
                            />
                            {errors.position && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.position}
                                </p>
                            )}
                        </div>

                        <div className="md:col-span-2">
                            <div className="flex flex-wrap gap-4">
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_required"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.is_required}
                                        onChange={(e) =>
                                            setData(
                                                "is_required",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="is_required"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Zorunlu
                                    </label>
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_filterable"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.is_filterable}
                                        onChange={(e) =>
                                            setData(
                                                "is_filterable",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="is_filterable"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Filtrelenebilir
                                    </label>
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_variant"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.is_variant}
                                        onChange={(e) =>
                                            setData(
                                                "is_variant",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="is_variant"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Varyant Özelliği
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    {needsValues && (
                        <div className="mb-6">
                            <h3 className="text-lg font-medium mb-4">
                                Değerler
                            </h3>

                            <div className="mb-4 flex gap-2">
                                <div className="flex-1">
                                    <input
                                        type="text"
                                        placeholder="Değer"
                                        className="w-full px-4 py-2 border rounded-lg"
                                        value={newValue}
                                        onChange={(e) =>
                                            setNewValue(e.target.value)
                                        }
                                        onKeyPress={(e) =>
                                            e.key === "Enter" &&
                                            (e.preventDefault(), addValue())
                                        }
                                    />
                                </div>
                                <button
                                    type="button"
                                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                                    onClick={addValue}
                                >
                                    Ekle
                                </button>
                            </div>

                            {data.values.length > 0 && (
                                <div className="border rounded-lg overflow-hidden">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Değer
                                                </th>

                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    İşlemler
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {data.values.map((value, index) => (
                                                <tr key={index}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {value.value}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {value.label}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-center">
                                                        <div className="flex justify-center space-x-2">
                                                            <button
                                                                type="button"
                                                                className="text-gray-500 hover:text-gray-700"
                                                                onClick={() =>
                                                                    moveValue(
                                                                        index,
                                                                        "up"
                                                                    )
                                                                }
                                                                disabled={
                                                                    index === 0
                                                                }
                                                            >
                                                                <svg
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    className="h-5 w-5"
                                                                    viewBox="0 0 20 20"
                                                                    fill="currentColor"
                                                                >
                                                                    <path
                                                                        fillRule="evenodd"
                                                                        d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                                                                        clipRule="evenodd"
                                                                    />
                                                                </svg>
                                                            </button>
                                                            <button
                                                                type="button"
                                                                className="text-gray-500 hover:text-gray-700"
                                                                onClick={() =>
                                                                    moveValue(
                                                                        index,
                                                                        "down"
                                                                    )
                                                                }
                                                                disabled={
                                                                    index ===
                                                                    data.values
                                                                        .length -
                                                                        1
                                                                }
                                                            >
                                                                <svg
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    className="h-5 w-5"
                                                                    viewBox="0 0 20 20"
                                                                    fill="currentColor"
                                                                >
                                                                    <path
                                                                        fillRule="evenodd"
                                                                        d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                                                                        clipRule="evenodd"
                                                                    />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right">
                                                        <button
                                                            type="button"
                                                            className="text-red-600 hover:text-red-900"
                                                            onClick={() =>
                                                                removeValue(
                                                                    index
                                                                )
                                                            }
                                                        >
                                                            Sil
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    )}

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                            disabled={processing}
                        >
                            {processing ? "Kaydediliyor..." : "Kaydet"}
                        </button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
