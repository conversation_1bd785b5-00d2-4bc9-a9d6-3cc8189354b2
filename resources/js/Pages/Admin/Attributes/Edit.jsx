import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Edit({ attribute, types }) {
    const { data, setData, put, processing, errors } = useForm({
        name: attribute.name,
        code: attribute.code,
        type: attribute.type,
        is_required: attribute.is_required,
        is_filterable: attribute.is_filterable,
        is_variant: attribute.is_variant,
        position: attribute.position,
        values: attribute.values.map((value) => ({
            id: value.id,
            value: value.value,
            label: value.label,
            position: value.position,
            _delete: false,
        })),
    });

    const [newValue, setNewValue] = useState({ value: "", label: "" });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route("admin.attributes.update", attribute.id));
    };

    const addValue = () => {
        if (!newValue.value.trim()) return;

        setData("values", [
            ...data.values,
            {
                value: newValue.value,
                label: newValue.label || newValue.value,
                position: data.values.filter((v) => !v._delete).length,
            },
        ]);
        setNewValue({ value: "", label: "" });
    };

    const removeValue = (index) => {
        const updatedValues = [...data.values];

        // If it's an existing value, mark it for deletion
        if (updatedValues[index].id) {
            updatedValues[index] = {
                ...updatedValues[index],
                _delete: true,
            };
        } else {
            // If it's a new value, remove it from the array
            updatedValues.splice(index, 1);
        }

        // Update positions
        const visibleValues = updatedValues.filter((v) => !v._delete);
        const reorderedValues = updatedValues.map((value) => {
            if (value._delete) return value;

            const newPosition = visibleValues.findIndex(
                (v) => v.value === value.value
            );
            return {
                ...value,
                position: newPosition >= 0 ? newPosition : value.position,
            };
        });

        setData("values", reorderedValues);
    };

    const moveValue = (index, direction) => {
        // Görünür değerleri al ve pozisyona göre sırala
        const visibleValues = [...data.values.filter((v) => !v._delete)];
        visibleValues.sort((a, b) => a.position - b.position);

        // Görünür değerler içindeki indeksi bul
        const visibleIndex = visibleValues.findIndex(
            (v) => v.value === data.values[index].value
        );

        // Sınırları kontrol et
        if (
            (direction === "up" && visibleIndex === 0) ||
            (direction === "down" && visibleIndex === visibleValues.length - 1)
        ) {
            return;
        }

        // Hedef indeksi hesapla
        const newVisibleIndex =
            direction === "up" ? visibleIndex - 1 : visibleIndex + 1;

        // Orijinal dizideki hedef indeksi bul
        const targetIndex = data.values.findIndex(
            (v) =>
                v.value === visibleValues[newVisibleIndex].value && !v._delete
        );

        // Pozisyonları değiştir
        const updatedValues = [...data.values];
        const tempPosition = updatedValues[index].position;
        updatedValues[index].position = updatedValues[targetIndex].position;
        updatedValues[targetIndex].position = tempPosition;

        // Değerleri güncelle
        setData("values", updatedValues);
    };

    const needsValues = ["select", "multiple"].includes(data.type);
    // Görünür değerleri al ve pozisyona göre sırala
    const visibleValues = [...data.values.filter((v) => !v._delete)].sort(
        (a, b) => a.position - b.position
    );

    return (
        <AdminLayout title="Özellik Düzenle">
            <Head title="Özellik Düzenle" />

            <div className="mb-6">
                <Link
                    href={route("admin.attributes.index")}
                    className="text-blue-600 hover:text-blue-800"
                >
                    &larr; Özelliklere Dön
                </Link>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
                <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label
                                htmlFor="name"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Özellik Adı
                            </label>
                            <input
                                type="text"
                                id="name"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.name}
                                onChange={(e) =>
                                    setData("name", e.target.value)
                                }
                                required
                            />
                            {errors.name && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.name}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="code"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Kod
                            </label>
                            <input
                                type="text"
                                id="code"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.code}
                                onChange={(e) =>
                                    setData("code", e.target.value)
                                }
                                required
                            />
                            {errors.code && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.code}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="type"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Tür
                            </label>
                            <select
                                id="type"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.type}
                                onChange={(e) =>
                                    setData("type", e.target.value)
                                }
                                required
                            >
                                {Object.entries(types).map(([value, label]) => (
                                    <option key={value} value={value}>
                                        {label}
                                    </option>
                                ))}
                            </select>
                            {errors.type && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.type}
                                </p>
                            )}
                        </div>

                        <div>
                            <label
                                htmlFor="position"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Sıralama Pozisyonu
                            </label>
                            <input
                                type="number"
                                id="position"
                                className="w-full px-4 py-2 border rounded-lg"
                                value={data.position}
                                onChange={(e) =>
                                    setData(
                                        "position",
                                        parseInt(e.target.value) || 0
                                    )
                                }
                                min="0"
                            />
                            {errors.position && (
                                <p className="text-red-500 text-xs mt-1">
                                    {errors.position}
                                </p>
                            )}
                        </div>

                        <div className="md:col-span-2">
                            <div className="flex flex-wrap gap-4">
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_required"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.is_required}
                                        onChange={(e) =>
                                            setData(
                                                "is_required",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="is_required"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Zorunlu
                                    </label>
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_filterable"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.is_filterable}
                                        onChange={(e) =>
                                            setData(
                                                "is_filterable",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="is_filterable"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Filtrelenebilir
                                    </label>
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_variant"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={data.is_variant}
                                        onChange={(e) =>
                                            setData(
                                                "is_variant",
                                                e.target.checked
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="is_variant"
                                        className="ml-2 block text-sm text-gray-700"
                                    >
                                        Varyant Özelliği
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    {needsValues && (
                        <div className="mb-6">
                            <h3 className="text-lg font-medium mb-4">
                                Değerler
                            </h3>

                            <div className="mb-4 flex gap-2">
                                <div className="flex-1">
                                    <input
                                        type="text"
                                        placeholder="Değer"
                                        className="w-full px-4 py-2 border rounded-lg"
                                        value={newValue.value}
                                        onChange={(e) =>
                                            setNewValue({
                                                ...newValue,
                                                value: e.target.value,
                                            })
                                        }
                                    />
                                </div>
                                <div className="flex-1">
                                    <input
                                        type="text"
                                        placeholder="Etiket (Opsiyonel)"
                                        className="w-full px-4 py-2 border rounded-lg"
                                        value={newValue.label}
                                        onChange={(e) =>
                                            setNewValue({
                                                ...newValue,
                                                label: e.target.value,
                                            })
                                        }
                                    />
                                </div>
                                <button
                                    type="button"
                                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                                    onClick={addValue}
                                >
                                    Ekle
                                </button>
                            </div>

                            {visibleValues.length > 0 && (
                                <div className="border rounded-lg overflow-hidden">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Değer
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Etiket
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Sıralama
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    İşlemler
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {visibleValues.map(
                                                (value, index) => (
                                                    <tr
                                                        key={
                                                            value.id ||
                                                            `new-${index}`
                                                        }
                                                    >
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            {value.value}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            {value.label}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-center">
                                                            <div className="flex justify-center space-x-2">
                                                                <button
                                                                    type="button"
                                                                    className="text-gray-500 hover:text-gray-700"
                                                                    onClick={() =>
                                                                        moveValue(
                                                                            data.values.findIndex(
                                                                                (
                                                                                    v
                                                                                ) =>
                                                                                    v.value ===
                                                                                        value.value &&
                                                                                    !v._delete
                                                                            ),
                                                                            "up"
                                                                        )
                                                                    }
                                                                    disabled={
                                                                        index ===
                                                                        0
                                                                    }
                                                                >
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        className="h-5 w-5"
                                                                        viewBox="0 0 20 20"
                                                                        fill="currentColor"
                                                                    >
                                                                        <path
                                                                            fillRule="evenodd"
                                                                            d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                                                                            clipRule="evenodd"
                                                                        />
                                                                    </svg>
                                                                </button>
                                                                <button
                                                                    type="button"
                                                                    className="text-gray-500 hover:text-gray-700"
                                                                    onClick={() =>
                                                                        moveValue(
                                                                            data.values.findIndex(
                                                                                (
                                                                                    v
                                                                                ) =>
                                                                                    v.value ===
                                                                                        value.value &&
                                                                                    !v._delete
                                                                            ),
                                                                            "down"
                                                                        )
                                                                    }
                                                                    disabled={
                                                                        index ===
                                                                        visibleValues.length -
                                                                            1
                                                                    }
                                                                >
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        className="h-5 w-5"
                                                                        viewBox="0 0 20 20"
                                                                        fill="currentColor"
                                                                    >
                                                                        <path
                                                                            fillRule="evenodd"
                                                                            d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                                                                            clipRule="evenodd"
                                                                        />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-right">
                                                            <button
                                                                type="button"
                                                                className="text-red-600 hover:text-red-900"
                                                                onClick={() =>
                                                                    removeValue(
                                                                        data.values.findIndex(
                                                                            (
                                                                                v
                                                                            ) =>
                                                                                v.value ===
                                                                                    value.value &&
                                                                                !v._delete
                                                                        )
                                                                    )
                                                                }
                                                            >
                                                                Sil
                                                            </button>
                                                        </td>
                                                    </tr>
                                                )
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    )}

                    <div className="flex justify-end">
                        <button
                            type="submit"
                            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                            disabled={processing}
                        >
                            {processing ? "Kaydediliyor..." : "Kaydet"}
                        </button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
