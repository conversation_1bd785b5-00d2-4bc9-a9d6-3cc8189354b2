import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/Components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

export default function SearchTermDetail({ query, stats, searchTermTrends, mostViewedProducts, mostPurchasedProducts, filterAnalysis }) {
    const [timeRange, setTimeRange] = useState(stats.days.toString());
    
    // Renk paleti
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1', '#a4de6c', '#d0ed57'];
    
    // <PERSON><PERSON>h <PERSON>ığını değiştir
    const handleTimeRangeChange = (value) => {
        setTimeRange(value);
        window.location.href = `/admin/search-analytics/term/${encodeURIComponent(query)}?days=${value}`;
    };
    
    // Ürün detay sayfasına git
    const goToProductDetail = (id) => {
        window.location.href = `/admin/search-analytics/product/${id}?days=${timeRange}`;
    };
    
    // Tarih formatla
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR');
    };
    
    // Filtre analizini grafik verilerine dönüştür
    const prepareFilterData = (filterData) => {
        return Object.entries(filterData).map(([key, value]) => ({
            name: key,
            value: value
        }));
    };
    
    // Kategori filtre verileri
    const categoryFilterData = prepareFilterData(filterAnalysis.categories);
    
    // Fiyat aralığı filtre verileri
    const priceRangeFilterData = prepareFilterData(filterAnalysis.price_ranges);
    
    // Özellik filtre verileri
    const attributeFilterData = prepareFilterData(filterAnalysis.attributes);
    
    // Varyant filtre verileri
    const variantFilterData = prepareFilterData(filterAnalysis.variants);
    
    // Sıralama seçeneği filtre verileri
    const sortOptionFilterData = prepareFilterData(filterAnalysis.sort_options);
    
    return (
        <AdminLayout>
            <Head title={`Arama Terimi: ${query}`} />
            
            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-2xl font-bold">Arama Terimi: "{query}"</h1>
                        <p className="text-gray-500">Arama terimi analizi</p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">Zaman Aralığı:</span>
                        <select 
                            value={timeRange} 
                            onChange={(e) => handleTimeRangeChange(e.target.value)}
                            className="border border-gray-300 rounded px-2 py-1 text-sm"
                        >
                            <option value="7">Son 7 Gün</option>
                            <option value="30">Son 30 Gün</option>
                            <option value="90">Son 90 Gün</option>
                            <option value="365">Son 1 Yıl</option>
                        </select>
                    </div>
                </div>
                
                {/* Özet İstatistikler */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Arama Sayısı</div>
                            <div className="text-3xl font-bold mt-2">{stats.searchCount}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Toplam Sonuç</div>
                            <div className="text-3xl font-bold mt-2">{stats.totalResults}</div>
                            <div className="text-sm text-gray-500 mt-1">
                                Ortalama: {stats.avgResults} sonuç/arama
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Tıklama Oranı</div>
                            <div className="text-3xl font-bold mt-2">{stats.clickThroughRate}%</div>
                            <div className="text-sm text-gray-500 mt-1">
                                {stats.totalClicks} tıklama
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-sm font-medium text-gray-500">Dönüşüm Oranı</div>
                            <div className="text-3xl font-bold mt-2">{stats.conversionRate}%</div>
                            <div className="text-sm text-gray-500 mt-1">
                                {stats.totalConversions} dönüşüm
                            </div>
                        </CardContent>
                    </Card>
                </div>
                
                <Tabs defaultValue="overview" className="mb-6">
                    <TabsList className="mb-4">
                        <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
                        <TabsTrigger value="products">Ürünler</TabsTrigger>
                        <TabsTrigger value="filters">Filtreler</TabsTrigger>
                    </TabsList>
                    
                    {/* Genel Bakış Sekmesi */}
                    <TabsContent value="overview">
                        <div className="grid grid-cols-1 gap-6">
                            {/* Arama Trendi Grafiği */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Arama Trendi</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <LineChart
                                                data={searchTermTrends}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis 
                                                    dataKey="date" 
                                                    tickFormatter={formatDate}
                                                />
                                                <YAxis />
                                                <Tooltip 
                                                    formatter={(value) => [value, 'Arama Sayısı']}
                                                    labelFormatter={formatDate}
                                                />
                                                <Legend />
                                                <Line 
                                                    type="monotone" 
                                                    dataKey="search_count" 
                                                    name="Arama Sayısı" 
                                                    stroke="#8884d8" 
                                                    activeDot={{ r: 8 }} 
                                                />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                    
                    {/* Ürünler Sekmesi */}
                    <TabsContent value="products">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* En Çok Görüntülenen Ürünler */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>En Çok Görüntülenen Ürünler</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <BarChart
                                                data={mostViewedProducts}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis 
                                                    dataKey="name" 
                                                    type="category" 
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip 
                                                    formatter={(value) => [value, 'Görüntülenme Sayısı']}
                                                />
                                                <Legend />
                                                <Bar 
                                                    dataKey="view_count" 
                                                    name="Görüntülenme Sayısı" 
                                                    fill="#0088FE" 
                                                    onClick={(data) => goToProductDetail(data.id)}
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>
                            
                            {/* En Çok Satın Alınan Ürünler */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>En Çok Satın Alınan Ürünler</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-80">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <BarChart
                                                data={mostPurchasedProducts}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                                layout="vertical"
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis type="number" />
                                                <YAxis 
                                                    dataKey="name" 
                                                    type="category" 
                                                    width={100}
                                                    tick={{ fontSize: 12 }}
                                                />
                                                <Tooltip 
                                                    formatter={(value) => [value, 'Satın Alma Sayısı']}
                                                />
                                                <Legend />
                                                <Bar 
                                                    dataKey="purchase_count" 
                                                    name="Satın Alma Sayısı" 
                                                    fill="#00C49F" 
                                                    onClick={(data) => goToProductDetail(data.id)}
                                                    cursor="pointer"
                                                />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </CardContent>
                            </Card>
                            
                            {/* Tüm Ürünler Tablosu */}
                            <Card className="lg:col-span-2">
                                <CardHeader>
                                    <CardTitle>Tüm Ürünler</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="overflow-x-auto">
                                        <table className="w-full text-sm">
                                            <thead>
                                                <tr className="border-b">
                                                    <th className="text-left py-3 px-4">Ürün Adı</th>
                                                    <th className="text-left py-3 px-4">Stok Kodu</th>
                                                    <th className="text-right py-3 px-4">Görüntülenme</th>
                                                    <th className="text-right py-3 px-4">Satın Alma</th>
                                                    <th className="text-right py-3 px-4">Dönüşüm Oranı</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {mostViewedProducts.map((product, index) => {
                                                    const purchaseData = mostPurchasedProducts.find(p => p.id === product.id);
                                                    const purchaseCount = purchaseData ? purchaseData.purchase_count : 0;
                                                    const conversionRate = product.view_count > 0 
                                                        ? ((purchaseCount / product.view_count) * 100).toFixed(1) 
                                                        : 0;
                                                        
                                                    return (
                                                        <tr 
                                                            key={index} 
                                                            className="border-b hover:bg-gray-50 cursor-pointer"
                                                            onClick={() => goToProductDetail(product.id)}
                                                        >
                                                            <td className="py-3 px-4">{product.name}</td>
                                                            <td className="py-3 px-4">{product.stock_code}</td>
                                                            <td className="text-right py-3 px-4">{product.view_count}</td>
                                                            <td className="text-right py-3 px-4">{purchaseCount}</td>
                                                            <td className="text-right py-3 px-4">{conversionRate}%</td>
                                                        </tr>
                                                    );
                                                })}
                                            </tbody>
                                        </table>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                    
                    {/* Filtreler Sekmesi */}
                    <TabsContent value="filters">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Kategori Filtreleri */}
                            {categoryFilterData.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Kategori Filtreleri</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="h-80">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <PieChart>
                                                    <Pie
                                                        data={categoryFilterData}
                                                        cx="50%"
                                                        cy="50%"
                                                        labelLine={true}
                                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                                        outerRadius={80}
                                                        fill="#8884d8"
                                                        dataKey="value"
                                                    >
                                                        {categoryFilterData.map((entry, index) => (
                                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                                        ))}
                                                    </Pie>
                                                    <Tooltip formatter={(value, name) => [value, name]} />
                                                    <Legend />
                                                </PieChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                            
                            {/* Fiyat Aralığı Filtreleri */}
                            {priceRangeFilterData.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Fiyat Aralığı Filtreleri</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="h-80">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <PieChart>
                                                    <Pie
                                                        data={priceRangeFilterData}
                                                        cx="50%"
                                                        cy="50%"
                                                        labelLine={true}
                                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                                        outerRadius={80}
                                                        fill="#8884d8"
                                                        dataKey="value"
                                                    >
                                                        {priceRangeFilterData.map((entry, index) => (
                                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                                        ))}
                                                    </Pie>
                                                    <Tooltip formatter={(value, name) => [value, name]} />
                                                    <Legend />
                                                </PieChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                            
                            {/* Özellik Filtreleri */}
                            {attributeFilterData.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Özellik Filtreleri</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="h-80">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <BarChart
                                                    data={attributeFilterData}
                                                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                                    layout="vertical"
                                                >
                                                    <CartesianGrid strokeDasharray="3 3" />
                                                    <XAxis type="number" />
                                                    <YAxis 
                                                        dataKey="name" 
                                                        type="category" 
                                                        width={150}
                                                        tick={{ fontSize: 12 }}
                                                    />
                                                    <Tooltip 
                                                        formatter={(value) => [value, 'Kullanım Sayısı']}
                                                    />
                                                    <Legend />
                                                    <Bar 
                                                        dataKey="value" 
                                                        name="Kullanım Sayısı" 
                                                        fill="#FFBB28" 
                                                    />
                                                </BarChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                            
                            {/* Varyant Filtreleri */}
                            {variantFilterData.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Varyant Filtreleri</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="h-80">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <BarChart
                                                    data={variantFilterData}
                                                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                                    layout="vertical"
                                                >
                                                    <CartesianGrid strokeDasharray="3 3" />
                                                    <XAxis type="number" />
                                                    <YAxis 
                                                        dataKey="name" 
                                                        type="category" 
                                                        width={150}
                                                        tick={{ fontSize: 12 }}
                                                    />
                                                    <Tooltip 
                                                        formatter={(value) => [value, 'Kullanım Sayısı']}
                                                    />
                                                    <Legend />
                                                    <Bar 
                                                        dataKey="value" 
                                                        name="Kullanım Sayısı" 
                                                        fill="#FF8042" 
                                                    />
                                                </BarChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                            
                            {/* Sıralama Seçenekleri */}
                            {sortOptionFilterData.length > 0 && (
                                <Card className={`${(attributeFilterData.length === 0 || variantFilterData.length === 0) ? 'lg:col-span-2' : ''}`}>
                                    <CardHeader>
                                        <CardTitle>Sıralama Seçenekleri</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="h-80">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <PieChart>
                                                    <Pie
                                                        data={sortOptionFilterData}
                                                        cx="50%"
                                                        cy="50%"
                                                        labelLine={true}
                                                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                                        outerRadius={80}
                                                        fill="#8884d8"
                                                        dataKey="value"
                                                    >
                                                        {sortOptionFilterData.map((entry, index) => (
                                                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                                        ))}
                                                    </Pie>
                                                    <Tooltip formatter={(value, name) => [value, name]} />
                                                    <Legend />
                                                </PieChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
