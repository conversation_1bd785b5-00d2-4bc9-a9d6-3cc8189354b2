import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputError from "@/Components/InputError";
import { toast } from "react-hot-toast";

export default function Edit({ role, permissions, permissionCategories, rolePermissions }) {
    const { data, setData, put, processing, errors } = useForm({
        name: role.name,
        permissions: rolePermissions || [],
    });
    
    const [expandedCategories, setExpandedCategories] = useState(
        Object.keys(permissionCategories).reduce((acc, category) => {
            acc[category] = true; // Başlangıçta tüm kategoriler açık
            return acc;
        }, {})
    );
    
    const handleSubmit = (e) => {
        e.preventDefault();
        
        put(route("admin.roles.update", role.id), {
            onSuccess: () => {
                toast.success("Rol başarıyla güncellendi");
            }
        });
    };
    
    const handlePermissionChange = (permissionId) => {
        const updatedPermissions = [...data.permissions];
        
        if (updatedPermissions.includes(permissionId)) {
            // İzin zaten seçiliyse, kaldır
            const index = updatedPermissions.indexOf(permissionId);
            updatedPermissions.splice(index, 1);
        } else {
            // İzin seçili değilse, ekle
            updatedPermissions.push(permissionId);
        }
        
        setData("permissions", updatedPermissions);
    };
    
    const toggleCategory = (category) => {
        setExpandedCategories({
            ...expandedCategories,
            [category]: !expandedCategories[category]
        });
    };
    
    const selectAllInCategory = (category) => {
        const categoryPermissions = permissionCategories[category];
        const categoryPermissionIds = categoryPermissions.map(p => p.id);
        
        // Kategorideki tüm izinler seçili mi kontrol et
        const allSelected = categoryPermissionIds.every(id => data.permissions.includes(id));
        
        let updatedPermissions;
        
        if (allSelected) {
            // Tümü seçiliyse, bu kategoridekileri kaldır
            updatedPermissions = data.permissions.filter(id => !categoryPermissionIds.includes(id));
        } else {
            // Tümü seçili değilse, eksik olanları ekle
            updatedPermissions = [...data.permissions];
            
            categoryPermissionIds.forEach(id => {
                if (!updatedPermissions.includes(id)) {
                    updatedPermissions.push(id);
                }
            });
        }
        
        setData("permissions", updatedPermissions);
    };
    
    const getPermissionLabel = (permission) => {
        const parts = permission.name.split('.');
        if (parts.length > 1) {
            return parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
        }
        return permission.name;
    };
    
    return (
        <AdminLayout>
            <Head title={`${role.name} Rolünü Düzenle`} />
            
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    {role.name} Rolünü Düzenle
                                </h2>
                                <Link
                                    href={route("admin.roles.index")}
                                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                                >
                                    Geri Dön
                                </Link>
                            </div>
                            
                            <form onSubmit={handleSubmit}>
                                <div className="mb-6">
                                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                                        Rol Adı
                                    </label>
                                    <input
                                        id="name"
                                        type="text"
                                        className={`w-full px-4 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                                            role.name === "admin" ? "bg-gray-100" : ""
                                        }`}
                                        value={data.name}
                                        onChange={(e) => setData("name", e.target.value)}
                                        required
                                        disabled={role.name === "admin"}
                                    />
                                    {role.name === "admin" && (
                                        <p className="mt-1 text-sm text-gray-500">
                                            Admin rolünün adı değiştirilemez.
                                        </p>
                                    )}
                                    <InputError message={errors.name} className="mt-2" />
                                </div>
                                
                                <div className="mb-6">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        İzinler
                                    </label>
                                    
                                    <div className="border rounded-lg p-4 bg-gray-50">
                                        {Object.keys(permissionCategories).length > 0 ? (
                                            <div className="space-y-4">
                                                {Object.entries(permissionCategories).map(([category, categoryPermissions]) => (
                                                    <div key={category} className="border rounded-lg overflow-hidden">
                                                        <div 
                                                            className="flex justify-between items-center p-3 bg-gray-100 cursor-pointer"
                                                            onClick={() => toggleCategory(category)}
                                                        >
                                                            <h3 className="text-md font-medium text-gray-800 capitalize">
                                                                {category}
                                                            </h3>
                                                            <div className="flex items-center">
                                                                <button
                                                                    type="button"
                                                                    className={`text-sm font-medium text-blue-600 hover:text-blue-800 mr-4 ${
                                                                        role.name === "admin" ? "opacity-50 cursor-not-allowed" : ""
                                                                    }`}
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        if (role.name !== "admin") {
                                                                            selectAllInCategory(category);
                                                                        }
                                                                    }}
                                                                    disabled={role.name === "admin"}
                                                                >
                                                                    {categoryPermissions.every(p => data.permissions.includes(p.id)) 
                                                                        ? "Tümünü Kaldır" 
                                                                        : "Tümünü Seç"}
                                                                </button>
                                                                <svg 
                                                                    className={`w-5 h-5 transition-transform ${expandedCategories[category] ? 'transform rotate-180' : ''}`} 
                                                                    fill="none" 
                                                                    stroke="currentColor" 
                                                                    viewBox="0 0 24 24" 
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                >
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                        
                                                        {expandedCategories[category] && (
                                                            <div className="p-3 bg-white">
                                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                                    {categoryPermissions.map((permission) => (
                                                                        <div key={permission.id} className="flex items-center">
                                                                            <input
                                                                                id={`permission-${permission.id}`}
                                                                                type="checkbox"
                                                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                                                checked={data.permissions.includes(permission.id)}
                                                                                onChange={() => handlePermissionChange(permission.id)}
                                                                                disabled={role.name === "admin"}
                                                                            />
                                                                            <label
                                                                                htmlFor={`permission-${permission.id}`}
                                                                                className={`ml-2 block text-sm ${
                                                                                    role.name === "admin" ? "text-gray-500" : "text-gray-900"
                                                                                }`}
                                                                            >
                                                                                {getPermissionLabel(permission)}
                                                                            </label>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="text-gray-500 text-center py-4">
                                                Henüz hiç izin tanımlanmamış.
                                            </p>
                                        )}
                                    </div>
                                    {role.name === "admin" && (
                                        <p className="mt-2 text-sm text-gray-500">
                                            Admin rolünün izinleri değiştirilemez. Admin rolü tüm izinlere sahiptir.
                                        </p>
                                    )}
                                    <InputError message={errors.permissions} className="mt-2" />
                                </div>
                                
                                <div className="flex justify-end">
                                    <Link
                                        href={route("admin.roles.index")}
                                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 mr-2"
                                    >
                                        İptal
                                    </Link>
                                    <button
                                        type="submit"
                                        className={`px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 ${
                                            role.name === "admin" ? "opacity-50 cursor-not-allowed" : ""
                                        }`}
                                        disabled={processing || role.name === "admin"}
                                    >
                                        {processing ? "Güncelleniyor..." : "Güncelle"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
