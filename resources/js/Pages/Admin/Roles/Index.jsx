import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import Pagination from "@/Components/Pagination";
import { toast } from "react-hot-toast";
import { safeDelete } from "@/Utils/inertiaHelper";

export default function Index({
    roles,
    permissions,
    permissionCategories,
    filters,
    flash,
}) {
    const [search, setSearch] = useState(filters?.search || "");
    const [perPage, setPerPage] = useState(filters?.per_page || 10);
    const [expandedRole, setExpandedRole] = useState(null);
    const [newRoleName, setNewRoleName] = useState("");
    const [selectedPermissions, setSelectedPermissions] = useState([]);
    const [editingRole, setEditingRole] = useState(null);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [roleToDelete, setRoleToDelete] = useState(null);

    // Flash mesajı varsa göster
    React.useEffect(() => {
        if (flash?.success) {
            toast.success(flash.success);
        }
        if (flash?.error) {
            toast.error(flash.error);
        }
    }, [flash?.success, flash?.error]);

    const handleSearch = (e) => {
        e.preventDefault();
        router.get(
            route("admin.roles.index"),
            { search, per_page: perPage },
            { preserveState: true }
        );
    };

    const handlePerPageChange = (e) => {
        const value = e.target.value;
        setPerPage(value);
        router.get(
            route("admin.roles.index"),
            { search, per_page: value },
            { preserveState: true }
        );
    };

    const toggleRoleDetails = (roleId) => {
        if (expandedRole === roleId) {
            setExpandedRole(null);
        } else {
            setExpandedRole(roleId);
        }
    };

    const getPermissionLabel = (permission) => {
        const parts = permission.name.split(".");
        if (parts.length > 1) {
            return parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
        }
        return permission.name;
    };

    // Not: permissionCategories artık backend'den geliyor, burada oluşturmaya gerek yok

    function handlePermissionChange(permissionId) {
        if (selectedPermissions.includes(permissionId)) {
            setSelectedPermissions(
                selectedPermissions.filter((id) => id !== permissionId)
            );
        } else {
            setSelectedPermissions([...selectedPermissions, permissionId]);
        }
    }

    function handleSubmit(e) {
        e.preventDefault();

        if (editingRole) {
            // Rol güncelleme
            router.put(
                route("admin.roles.update", editingRole.id),
                {
                    name: newRoleName,
                    permissions: selectedPermissions,
                },
                {
                    onSuccess: () => {
                        setEditingRole(null);
                        setNewRoleName("");
                        setSelectedPermissions([]);
                        toast.success("Rol başarıyla güncellendi");
                    },
                    onError: (errors) => {
                        if (errors.name) {
                            toast.error(errors.name);
                        } else {
                            toast.error("Rol güncellenirken bir hata oluştu");
                        }
                    },
                }
            );
        } else {
            // Yeni rol oluşturma
            router.post(
                route("admin.roles.store"),
                {
                    name: newRoleName,
                    permissions: selectedPermissions,
                },
                {
                    onSuccess: () => {
                        setNewRoleName("");
                        setSelectedPermissions([]);
                        toast.success("Rol başarıyla oluşturuldu");
                    },
                    onError: (errors) => {
                        if (errors.name) {
                            toast.error(errors.name);
                        } else {
                            toast.error("Rol oluşturulurken bir hata oluştu");
                        }
                    },
                }
            );
        }
    }

    function startEditing(role) {
        setEditingRole(role);
        setNewRoleName(role.name);
        setSelectedPermissions(role.permissions.map((p) => p.id));
    }

    function cancelEditing() {
        setEditingRole(null);
        setNewRoleName("");
        setSelectedPermissions([]);
    }

    function confirmDelete(role) {
        setRoleToDelete(role);
        setDeleteModalOpen(true);
    }

    function handleDelete() {
        if (!roleToDelete) return;

        // router.delete yerine safeDelete kullan
        safeDelete(route("admin.roles.destroy", roleToDelete.id), {
            onSuccess: () => {
                setDeleteModalOpen(false);
                setRoleToDelete(null);
                toast.success("Rol başarıyla silindi");
            },
            onError: (errors) => {
                setDeleteModalOpen(false);
                setRoleToDelete(null);
                if (errors.error) {
                    toast.error(errors.error);
                } else {
                    toast.error("Rol silinirken bir hata oluştu");
                }
            },
        });
    }

    const handleDelete2 = (role) => {
        if (role.name === "admin") {
            toast.error("Admin rolü silinemez");
            return;
        }

        if (confirm(`${role.name} rolünü silmek istediğinize emin misiniz?`)) {
            // router.delete yerine safeDelete kullan
            safeDelete(route("admin.roles.destroy", role.id), {
                onSuccess: () => {
                    toast.success("Rol başarıyla silindi");
                },
                onError: () => {
                    toast.error("Rol silinirken bir hata oluştu");
                },
            });
        }
    };

    function selectAllPermissionsInGroup(moduleName) {
        // İlgili kategorinin izinlerini bul
        const category = permissionCategories.find(
            (cat) => cat.name === moduleName
        );
        if (!category) return;

        const modulePermissionIds = category.permissions.map((p) => p.id);
        const newSelectedPermissions = [...selectedPermissions];

        // Gruptaki tüm izinler seçili mi kontrol et
        const allSelected = modulePermissionIds.every((id) =>
            selectedPermissions.includes(id)
        );

        if (allSelected) {
            // Tümü seçiliyse, hepsini kaldır
            setSelectedPermissions(
                selectedPermissions.filter(
                    (id) => !modulePermissionIds.includes(id)
                )
            );
        } else {
            // Tümü seçili değilse, eksik olanları ekle
            modulePermissionIds.forEach((id) => {
                if (!newSelectedPermissions.includes(id)) {
                    newSelectedPermissions.push(id);
                }
            });
            setSelectedPermissions(newSelectedPermissions);
        }
    }

    return (
        <AdminLayout title="Roller ve İzinler">
            <Head title="Roller ve İzinler" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-medium">
                                {editingRole
                                    ? `Rol Düzenle: ${editingRole.name}`
                                    : "Yeni Rol Ekle"}
                            </h3>
                            {editingRole && (
                                <button
                                    onClick={cancelEditing}
                                    className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded"
                                >
                                    İptal
                                </button>
                            )}
                        </div>

                        {/* Rol Ekleme/Düzenleme Formu */}
                        <form onSubmit={handleSubmit} className="mb-8">
                            <div className="mb-4">
                                <label
                                    htmlFor="name"
                                    className="block text-sm font-medium text-gray-700 mb-1"
                                >
                                    Rol Adı
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    className="w-full px-4 py-2 border rounded-lg"
                                    value={newRoleName}
                                    onChange={(e) =>
                                        setNewRoleName(e.target.value)
                                    }
                                    required
                                />
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    İzinler
                                </label>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {permissionCategories &&
                                        permissionCategories.map((category) => (
                                            <div
                                                key={category.name}
                                                className="border rounded-lg p-4"
                                            >
                                                <div className="flex items-center mb-2">
                                                    <button
                                                        type="button"
                                                        className="text-sm font-medium text-blue-600 hover:text-blue-800"
                                                        onClick={() =>
                                                            selectAllPermissionsInGroup(
                                                                category.name
                                                            )
                                                        }
                                                    >
                                                        {category.permissions.every(
                                                            (p) =>
                                                                selectedPermissions.includes(
                                                                    p.id
                                                                )
                                                        )
                                                            ? "Tümünü Kaldır"
                                                            : "Tümünü Seç"}
                                                    </button>
                                                    <h4 className="text-md font-medium text-gray-700 ml-2 capitalize">
                                                        {category.name}
                                                    </h4>
                                                </div>

                                                <div className="space-y-2">
                                                    {category.permissions.map(
                                                        (permission) => (
                                                            <div
                                                                key={
                                                                    permission.id
                                                                }
                                                                className="flex items-center"
                                                            >
                                                                <input
                                                                    id={`permission-${permission.id}`}
                                                                    type="checkbox"
                                                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                                                    checked={selectedPermissions.includes(
                                                                        permission.id
                                                                    )}
                                                                    onChange={() =>
                                                                        handlePermissionChange(
                                                                            permission.id
                                                                        )
                                                                    }
                                                                />
                                                                <label
                                                                    htmlFor={`permission-${permission.id}`}
                                                                    className="ml-2 block text-sm text-gray-900"
                                                                >
                                                                    {
                                                                        permission.name.split(
                                                                            "."
                                                                        )[1]
                                                                    }
                                                                </label>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>

                            <div className="flex justify-end">
                                <button
                                    type="submit"
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    {editingRole ? "Güncelle" : "Ekle"}
                                </button>
                            </div>
                        </form>

                        <hr className="my-6" />

                        {/* Rol Listesi */}
                        <h3 className="text-lg font-medium mb-4">
                            Mevcut Roller
                        </h3>

                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white">
                                <thead>
                                    <tr>
                                        <th className="py-2 px-4 border-b">
                                            Rol Adı
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            İzinler
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            İşlemler
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {roles.data &&
                                        roles.data.map((role) => (
                                            <tr key={role.id}>
                                                <td className="py-2 px-4 border-b">
                                                    {role.name}
                                                </td>
                                                <td className="py-2 px-4 border-b">
                                                    <div className="flex flex-wrap gap-1">
                                                        {role.permissions.map(
                                                            (permission) => (
                                                                <span
                                                                    key={
                                                                        permission.id
                                                                    }
                                                                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                                >
                                                                    {
                                                                        permission.name
                                                                    }
                                                                </span>
                                                            )
                                                        )}

                                                        {role.permissions
                                                            .length === 0 && (
                                                            <span className="text-gray-500 italic">
                                                                İzin yok
                                                            </span>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="py-2 px-4 border-b">
                                                    <button
                                                        onClick={() =>
                                                            startEditing(role)
                                                        }
                                                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                                                    >
                                                        Düzenle
                                                    </button>
                                                    <button
                                                        onClick={() =>
                                                            confirmDelete(role)
                                                        }
                                                        className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                                        disabled={
                                                            role.name ===
                                                            "admin"
                                                        }
                                                    >
                                                        Sil
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}

                                    {(!roles.data ||
                                        roles.data.length === 0) && (
                                        <tr>
                                            <td
                                                colSpan="3"
                                                className="py-4 text-center text-gray-500"
                                            >
                                                Rol bulunamadı
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Silme Onay Modalı */}
            {deleteModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Rolü Sil
                        </h3>
                        <p className="mb-4 text-gray-600">
                            <strong>{roleToDelete?.name}</strong> rolünü silmek
                            istediğinize emin misiniz? Bu işlem geri alınamaz.
                        </p>
                        <div className="flex justify-end">
                            <button
                                type="button"
                                className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                onClick={() => setDeleteModalOpen(false)}
                            >
                                İptal
                            </button>
                            <button
                                type="button"
                                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                onClick={handleDelete}
                            >
                                Sil
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
