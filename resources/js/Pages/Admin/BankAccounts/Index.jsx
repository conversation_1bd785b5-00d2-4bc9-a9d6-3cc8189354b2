import React, { useState } from "react";
import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { PencilIcon, TrashIcon, PlusIcon, CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import DeleteConfirmationModal from "@/Components/DeleteConfirmationModal";

export default function Index({ bankAccounts }) {
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [accountToDelete, setAccountToDelete] = useState(null);

    const confirmDelete = (account) => {
        setAccountToDelete(account);
        setDeleteModalOpen(true);
    };

    const deleteAccount = () => {
        router.delete(route("admin.bank-accounts.destroy", accountToDelete.id), {
            onSuccess: () => {
                setDeleteModalOpen(false);
                setAccountToDelete(null);
            },
        });
    };

    const toggleStatus = (account) => {
        router.post(route("admin.bank-accounts.toggle-status", account.id));
    };

    return (
        <AdminLayout>
            <Head title="Banka Hesapları" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    Banka Hesapları
                                </h2>
                                <Link
                                    href={route("admin.bank-accounts.create")}
                                    className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition"
                                >
                                    <PlusIcon className="w-4 h-4 mr-2" />
                                    Yeni Banka Hesabı
                                </Link>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Banka Adı
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Hesap Adı
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                IBAN
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Durum
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                İşlemler
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {bankAccounts.length > 0 ? (
                                            bankAccounts.map((account) => (
                                                <tr key={account.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {account.bank_name}
                                                        </div>
                                                        {account.branch_name && (
                                                            <div className="text-sm text-gray-500">
                                                                Şube: {account.branch_name}
                                                                {account.branch_code && ` (${account.branch_code})`}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {account.account_name}
                                                        </div>
                                                        {account.account_number && (
                                                            <div className="text-sm text-gray-500">
                                                                Hesap No: {account.account_number}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900 font-mono">
                                                            {account.iban}
                                                        </div>
                                                        {account.swift_code && (
                                                            <div className="text-sm text-gray-500">
                                                                SWIFT: {account.swift_code}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <button
                                                            onClick={() => toggleStatus(account)}
                                                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                account.is_active
                                                                    ? "bg-green-100 text-green-800"
                                                                    : "bg-red-100 text-red-800"
                                                            }`}
                                                        >
                                                            {account.is_active ? (
                                                                <>
                                                                    <CheckIcon className="w-4 h-4 mr-1" />
                                                                    Aktif
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <XMarkIcon className="w-4 h-4 mr-1" />
                                                                    Pasif
                                                                </>
                                                            )}
                                                        </button>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <div className="flex space-x-2">
                                                            <Link
                                                                href={route(
                                                                    "admin.bank-accounts.edit",
                                                                    account.id
                                                                )}
                                                                className="text-indigo-600 hover:text-indigo-900"
                                                            >
                                                                <PencilIcon className="w-5 h-5" />
                                                            </Link>
                                                            <button
                                                                onClick={() => confirmDelete(account)}
                                                                className="text-red-600 hover:text-red-900"
                                                            >
                                                                <TrashIcon className="w-5 h-5" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td
                                                    colSpan="5"
                                                    className="px-6 py-4 text-center text-sm text-gray-500"
                                                >
                                                    Henüz banka hesabı eklenmemiş.
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <DeleteConfirmationModal
                isOpen={deleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                onConfirm={deleteAccount}
                title="Banka Hesabını Sil"
                message={`"${accountToDelete?.bank_name} - ${accountToDelete?.account_name}" hesabını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
            />
        </AdminLayout>
    );
}
