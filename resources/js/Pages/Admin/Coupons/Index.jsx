import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import Pagination from "@/Components/Pagination";
import { toast } from "react-hot-toast";

export default function Index({ coupons, filters, flash }) {
    const [searchTerm, setSearchTerm] = useState(filters.search || "");
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [couponToDelete, setCouponToDelete] = useState(null);
    
    // Flash mesajı varsa göster
    React.useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
    }, [flash.success]);

    function handleSearch(e) {
        e.preventDefault();
        router.get(
            route("admin.coupons.index"),
            { search: searchTerm, per_page: filters.per_page },
            { preserveState: true }
        );
    }

    function handlePerPageChange(e) {
        router.get(
            route("admin.coupons.index"),
            { search: searchTerm, per_page: e.target.value },
            { preserveState: true }
        );
    }
    
    function confirmDelete(coupon) {
        setCouponToDelete(coupon);
        setDeleteModalOpen(true);
    }
    
    function handleDelete() {
        if (!couponToDelete) return;
        
        router.delete(route("admin.coupons.destroy", couponToDelete.id), {
            onSuccess: () => {
                setDeleteModalOpen(false);
                setCouponToDelete(null);
                toast.success("Kupon başarıyla silindi");
            },
            onError: () => {
                toast.error("Kupon silinirken bir hata oluştu");
            },
        });
    }
    
    // Kupon durumuna göre renk sınıfı
    function getStatusClass(isActive) {
        return isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800';
    }
    
    // Kupon tipine göre metin
    function getTypeText(type) {
        return type === 'percentage' ? 'Yüzde' : 'Sabit Tutar';
    }
    
    // Kupon değerini formatla
    function formatValue(type, value) {
        return type === 'percentage' ? `%${value}` : `${value} TL`;
    }
    
    // Tarih formatla
    function formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('tr-TR');
    }

    return (
        <AdminLayout title="Kuponlar">
            <Head title="Kuponlar" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-medium">
                                Kupon Listesi
                            </h3>
                            <Link
                                href={route("admin.coupons.create")}
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Yeni Kupon Ekle
                            </Link>
                        </div>
                        
                        {/* Arama ve Filtreleme */}
                        <div className="mb-6">
                            <form onSubmit={handleSearch} className="flex gap-2">
                                <div className="flex-1">
                                    <input
                                        type="text"
                                        placeholder="Kupon ara..."
                                        className="w-full px-4 py-2 border rounded-lg"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <button
                                    type="submit"
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg"
                                >
                                    Ara
                                </button>
                                <select
                                    className="px-4 py-2 border rounded-lg"
                                    value={filters.per_page}
                                    onChange={handlePerPageChange}
                                >
                                    <option value="5">5 / sayfa</option>
                                    <option value="10">10 / sayfa</option>
                                    <option value="25">25 / sayfa</option>
                                    <option value="50">50 / sayfa</option>
                                </select>
                            </form>
                        </div>

                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white">
                                <thead>
                                    <tr>
                                        <th className="py-2 px-4 border-b">
                                            Kod
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Açıklama
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Tip
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Değer
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Durum
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Geçerlilik
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            İşlemler
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {coupons.data.map((coupon) => (
                                        <tr key={coupon.id}>
                                            <td className="py-2 px-4 border-b font-medium">
                                                {coupon.code}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                {coupon.description || '-'}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                {getTypeText(coupon.type)}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                {formatValue(coupon.type, coupon.value)}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(coupon.is_active)}`}>
                                                    {coupon.is_active ? 'Aktif' : 'Pasif'}
                                                </span>
                                            </td>
                                            <td className="py-2 px-4 border-b text-sm">
                                                <div>Başlangıç: {formatDate(coupon.starts_at)}</div>
                                                <div>Bitiş: {formatDate(coupon.expires_at)}</div>
                                                <div>Kullanım: {coupon.used_count} / {coupon.max_uses || '∞'}</div>
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                <Link
                                                    href={route("admin.coupons.edit", coupon.id)}
                                                    className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                                                >
                                                    Düzenle
                                                </Link>
                                                <button 
                                                    onClick={() => confirmDelete(coupon)}
                                                    className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                                >
                                                    Sil
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                    
                                    {coupons.data.length === 0 && (
                                        <tr>
                                            <td colSpan="7" className="py-4 text-center text-gray-500">
                                                Kupon bulunamadı
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                        
                        {/* Sayfalama */}
                        <div className="mt-6">
                            <Pagination links={coupons.links} />
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Silme Onay Modalı */}
            {deleteModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Kuponu Sil
                        </h3>
                        <p className="mb-4 text-gray-600">
                            <strong>{couponToDelete?.code}</strong> kodlu kuponu silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
                        </p>
                        <div className="flex justify-end">
                            <button
                                type="button"
                                className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                onClick={() => setDeleteModalOpen(false)}
                            >
                                İptal
                            </button>
                            <button
                                type="button"
                                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                onClick={handleDelete}
                            >
                                Sil
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
