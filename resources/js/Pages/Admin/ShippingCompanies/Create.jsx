import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import Checkbox from "@/Components/Checkbox";
import TextArea from "@/Components/TextArea";

export default function Create() {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: "",
        code: "",
        description: "",
        logo: null,
        is_active: true,
    });

    const [logoPreview, setLogoPreview] = useState(null);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("admin.shipping-companies.store"), {
            onSuccess: () => reset(),
        });
    };

    const handleLogoChange = (e) => {
        const file = e.target.files[0];
        setData("logo", file);

        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                setLogoPreview(e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            setLogoPreview(null);
        }
    };

    return (
        <AdminLayout>
            <Head title="Yeni Kargo Şirketi" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-2xl font-semibold text-gray-900">
                                    Yeni Kargo Şirketi
                                </h2>
                                <Link
                                    href={route("admin.shipping-companies.index")}
                                    className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:ring ring-gray-300 disabled:opacity-25 transition"
                                >
                                    Geri Dön
                                </Link>
                            </div>

                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <InputLabel htmlFor="name" value="Şirket Adı" />
                                        <TextInput
                                            id="name"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.name}
                                            onChange={(e) => setData("name", e.target.value)}
                                            required
                                        />
                                        <InputError message={errors.name} className="mt-2" />
                                    </div>

                                    <div>
                                        <InputLabel htmlFor="code" value="Kod" />
                                        <TextInput
                                            id="code"
                                            type="text"
                                            className="mt-1 block w-full"
                                            value={data.code}
                                            onChange={(e) => setData("code", e.target.value)}
                                            required
                                        />
                                        <InputError message={errors.code} className="mt-2" />
                                        <p className="text-sm text-gray-500 mt-1">
                                            Benzersiz bir kod (örn: yurtici, aras, mng)
                                        </p>
                                    </div>



                                    <div>
                                        <InputLabel htmlFor="logo" value="Logo" />
                                        <input
                                            id="logo"
                                            type="file"
                                            className="mt-1 block w-full"
                                            onChange={handleLogoChange}
                                            accept="image/*"
                                        />
                                        <InputError message={errors.logo} className="mt-2" />

                                        {logoPreview && (
                                            <div className="mt-2">
                                                <img
                                                    src={logoPreview}
                                                    alt="Logo Önizleme"
                                                    className="h-20 w-auto"
                                                />
                                            </div>
                                        )}
                                    </div>

                                    <div className="md:col-span-2">
                                        <InputLabel htmlFor="description" value="Açıklama" />
                                        <TextArea
                                            id="description"
                                            className="mt-1 block w-full"
                                            value={data.description}
                                            onChange={(e) => setData("description", e.target.value)}
                                        />
                                        <InputError message={errors.description} className="mt-2" />
                                    </div>



                                    <div className="md:col-span-2">
                                        <label className="flex items-center">
                                            <Checkbox
                                                name="is_active"
                                                checked={data.is_active}
                                                onChange={(e) =>
                                                    setData("is_active", e.target.checked)
                                                }
                                            />
                                            <span className="ml-2 text-sm text-gray-600">
                                                Aktif
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div className="flex items-center justify-end mt-6">
                                    <button
                                        type="submit"
                                        className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition"
                                        disabled={processing}
                                    >
                                        Kaydet
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
