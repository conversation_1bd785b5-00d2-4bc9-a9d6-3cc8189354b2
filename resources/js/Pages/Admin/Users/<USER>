import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import Pagination from "@/Components/Pagination";
import { toast } from "react-hot-toast";
import { safeDelete } from "@/Utils/inertiaHelper";

export default function Index({ users, filters, flash }) {
    const [searchTerm, setSearchTerm] = useState(filters.search || "");
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [userToDelete, setUserToDelete] = useState(null);

    // Flash mesajı varsa göster
    React.useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
        if (flash.error) {
            toast.error(flash.error);
        }
    }, [flash.success, flash.error]);

    function handleSearch(e) {
        e.preventDefault();
        router.get(
            route("admin.users.index"),
            { search: searchTerm, per_page: filters.per_page },
            { preserveState: true }
        );
    }

    function handlePerPageChange(e) {
        router.get(
            route("admin.users.index"),
            { search: searchTerm, per_page: e.target.value },
            { preserveState: true }
        );
    }

    function confirmDelete(user) {
        setUserToDelete(user);
        setDeleteModalOpen(true);
    }

    function handleDelete() {
        if (!userToDelete) return;

        // router.delete yerine safeDelete kullan
        safeDelete(route("admin.users.destroy", userToDelete.id), {
            onSuccess: () => {
                setDeleteModalOpen(false);
                setUserToDelete(null);
                toast.success("Kullanıcı başarıyla silindi");
            },
            onError: (errors) => {
                setDeleteModalOpen(false);
                setUserToDelete(null);
                if (errors.error) {
                    toast.error(errors.error);
                } else {
                    toast.error("Kullanıcı silinirken bir hata oluştu");
                }
            },
        });
    }

    return (
        <AdminLayout title="Kullanıcılar">
            <Head title="Kullanıcılar" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-medium">
                                Kullanıcı Listesi
                            </h3>
                            <Link
                                href={route("admin.users.create")}
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Yeni Kullanıcı Ekle
                            </Link>
                        </div>

                        {/* Arama ve Filtreleme */}
                        <div className="mb-6">
                            <form
                                onSubmit={handleSearch}
                                className="flex gap-2"
                            >
                                <div className="flex-1">
                                    <input
                                        type="text"
                                        placeholder="Kullanıcı ara..."
                                        className="w-full px-4 py-2 border rounded-lg"
                                        value={searchTerm}
                                        onChange={(e) =>
                                            setSearchTerm(e.target.value)
                                        }
                                    />
                                </div>
                                <button
                                    type="submit"
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg"
                                >
                                    Ara
                                </button>
                                <select
                                    className="px-4 py-2 border rounded-lg"
                                    value={filters.per_page}
                                    onChange={handlePerPageChange}
                                >
                                    <option value="5">5 / sayfa</option>
                                    <option value="10">10 / sayfa</option>
                                    <option value="25">25 / sayfa</option>
                                    <option value="50">50 / sayfa</option>
                                </select>
                            </form>
                        </div>

                        <div className="overflow-x-auto">
                            <table className="min-w-full bg-white">
                                <thead>
                                    <tr>
                                        <th className="py-2 px-4 border-b">
                                            ID
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Ad Soyad
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            E-posta
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            Kayıt Tarihi
                                        </th>
                                        <th className="py-2 px-4 border-b">
                                            İşlemler
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {users.data.map((user) => (
                                        <tr key={user.id}>
                                            <td className="py-2 px-4 border-b">
                                                {user.id}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                {user.name}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                {user.email}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                {new Date(
                                                    user.created_at
                                                ).toLocaleDateString("tr-TR")}
                                            </td>
                                            <td className="py-2 px-4 border-b">
                                                <Link
                                                    href={route(
                                                        "admin.users.show",
                                                        user.id
                                                    )}
                                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                                                >
                                                    Görüntüle
                                                </Link>
                                                <Link
                                                    href={route(
                                                        "admin.users.edit",
                                                        user.id
                                                    )}
                                                    className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                                                >
                                                    Düzenle
                                                </Link>
                                                <button
                                                    onClick={() =>
                                                        confirmDelete(user)
                                                    }
                                                    className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                                >
                                                    Sil
                                                </button>
                                            </td>
                                        </tr>
                                    ))}

                                    {users.data.length === 0 && (
                                        <tr>
                                            <td
                                                colSpan="5"
                                                className="py-4 text-center text-gray-500"
                                            >
                                                Kullanıcı bulunamadı
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>

                        {/* Sayfalama */}
                        <div className="mt-6">
                            <Pagination links={users.links} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Silme Onay Modalı */}
            {deleteModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Kullanıcıyı Sil
                        </h3>
                        <p className="mb-4 text-gray-600">
                            <strong>{userToDelete?.name}</strong> adlı
                            kullanıcıyı silmek istediğinize emin misiniz? Bu
                            işlem geri alınamaz.
                        </p>
                        <div className="flex justify-end">
                            <button
                                type="button"
                                className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                onClick={() => setDeleteModalOpen(false)}
                            >
                                İptal
                            </button>
                            <button
                                type="button"
                                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                onClick={handleDelete}
                            >
                                Sil
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </AdminLayout>
    );
}
