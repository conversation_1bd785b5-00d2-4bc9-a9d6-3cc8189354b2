import React from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Show({ user }) {
    return (
        <AdminLayout title={`Kullanıcı: ${user.name}`}>
            <Head title={`Kullanıcı: ${user.name}`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-medium">
                                Kullanıcı Detayları
                            </h3>
                            <div>
                                <Link
                                    href={route("admin.users.index")}
                                    className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded mr-2"
                                >
                                    <PERSON><PERSON>
                                </Link>
                                <Link
                                    href={route("admin.users.edit", user.id)}
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    D<PERSON>zenle
                                </Link>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-gray-700 mb-4">Temel Bilgiler</h4>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500">ID</span>
                                    <span className="block mt-1">{user.id}</span>
                                </div>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500">Ad Soyad</span>
                                    <span className="block mt-1">{user.name}</span>
                                </div>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500">E-posta</span>
                                    <span className="block mt-1">{user.email}</span>
                                </div>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500">E-posta Doğrulama</span>
                                    <span className="block mt-1">
                                        {user.email_verified_at 
                                            ? `Doğrulanmış (${new Date(user.email_verified_at).toLocaleDateString('tr-TR')})` 
                                            : 'Doğrulanmamış'}
                                    </span>
                                </div>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500">Kayıt Tarihi</span>
                                    <span className="block mt-1">{new Date(user.created_at).toLocaleDateString('tr-TR')}</span>
                                </div>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500">Son Güncelleme</span>
                                    <span className="block mt-1">{new Date(user.updated_at).toLocaleDateString('tr-TR')}</span>
                                </div>
                            </div>
                            
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-gray-700 mb-4">Roller ve İzinler</h4>
                                
                                <div className="mb-4">
                                    <span className="block text-sm font-medium text-gray-500 mb-2">Roller</span>
                                    {user.roles && user.roles.length > 0 ? (
                                        <div className="flex flex-wrap gap-2">
                                            {user.roles.map(role => (
                                                <span 
                                                    key={role.id} 
                                                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                >
                                                    {role.name}
                                                </span>
                                            ))}
                                        </div>
                                    ) : (
                                        <span className="text-gray-500 italic">Rol atanmamış</span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
