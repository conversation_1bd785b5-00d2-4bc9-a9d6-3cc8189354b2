import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import { FaPlus, FaEdit, FaTrash, FaArrowLeft } from "react-icons/fa";
import Modal from "@/Components/Modal";
import InputError from "@/Components/InputError";
import { formatMoney } from "@/utils";

export default function ShippingZoneMethodRates({ auth, zoneMethod, rates }) {
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [currentRate, setCurrentRate] = useState(null);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        min_desi: "",
        max_desi: "",
        cost: "",
        condition: "",
        estimated_delivery_days: "",
        is_active: true,
    });

    const openAddModal = () => {
        reset();
        setIsAddModalOpen(true);
    };

    const openEditModal = (rate) => {
        setCurrentRate(rate);
        setData({
            min_desi: rate.min_desi,
            max_desi: rate.max_desi,
            cost: rate.cost,
            condition: rate.condition || "",
            estimated_delivery_days: rate.estimated_delivery_days || "",
            is_active: rate.is_active,
        });
        setIsEditModalOpen(true);
    };

    const openDeleteModal = (rate) => {
        setCurrentRate(rate);
        setIsDeleteModalOpen(true);
    };

    const closeModals = () => {
        setIsAddModalOpen(false);
        setIsEditModalOpen(false);
        setIsDeleteModalOpen(false);
        reset();
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (isAddModalOpen) {
            post(route("admin.shipping.zones.methods.rates.store", zoneMethod.id), {
                onSuccess: () => closeModals(),
            });
        } else if (isEditModalOpen && currentRate) {
            put(route("admin.shipping.zones.methods.rates.update", [zoneMethod.id, currentRate.id]), {
                onSuccess: () => closeModals(),
            });
        }
    };

    const handleDelete = () => {
        if (currentRate) {
            destroy(route("admin.shipping.zones.methods.rates.destroy", [zoneMethod.id, currentRate.id]), {
                onSuccess: () => closeModals(),
            });
        }
    };

    return (
        <AdminLayout user={auth.user}>
            <Head title="Desi Bazlı Fiyatlandırma" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <div className="flex justify-between items-center mb-6">
                                <div>
                                    <h1 className="text-2xl font-semibold text-gray-900">
                                        Desi Bazlı Fiyatlandırma
                                    </h1>
                                    <p className="mt-1 text-sm text-gray-600">
                                        Bölge: {zoneMethod.zone.name} | Kargo Metodu: {zoneMethod.method.name}
                                    </p>
                                </div>
                                <div className="flex space-x-4">
                                    <Link
                                        href={route("admin.shipping.zones.methods", zoneMethod.zone_id)}
                                        className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:shadow-outline-gray transition ease-in-out duration-150"
                                    >
                                        <FaArrowLeft className="mr-2" />
                                        Geri Dön
                                    </Link>
                                    <button
                                        onClick={openAddModal}
                                        className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-700 focus:shadow-outline-blue transition ease-in-out duration-150"
                                    >
                                        <FaPlus className="mr-2" />
                                        Yeni Fiyatlandırma Ekle
                                    </button>
                                </div>
                            </div>

                            {rates.length === 0 ? (
                                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                                    <div className="flex">
                                        <div className="flex-shrink-0">
                                            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-sm text-yellow-700">
                                                Bu kargo metodu için henüz desi bazlı fiyatlandırma tanımlanmamış. Yeni bir fiyatlandırma eklemek için "Yeni Fiyatlandırma Ekle" butonunu kullanın.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Sıra No
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Desi Aralığı
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Fiyat
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Koşul
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Tahmini Teslimat
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Durum
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    İşlemler
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {rates.map((rate, index) => (
                                                <tr key={rate.id} className={rate.is_active ? "" : "bg-gray-100"}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {index + 1}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {rate.min_desi} - {rate.max_desi} desi
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {formatMoney(rate.cost)} TL
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {rate.condition || "-"}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {rate.estimated_delivery_days ? `${rate.estimated_delivery_days} gün` : "-"}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${rate.is_active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
                                                            {rate.is_active ? "Aktif" : "Pasif"}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <button
                                                            onClick={() => openEditModal(rate)}
                                                            className="text-indigo-600 hover:text-indigo-900 mr-4"
                                                        >
                                                            <FaEdit className="inline" /> Düzenle
                                                        </button>
                                                        <button
                                                            onClick={() => openDeleteModal(rate)}
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            <FaTrash className="inline" /> Sil
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Ekleme Modalı */}
            <Modal show={isAddModalOpen} onClose={closeModals} maxWidth="md">
                <div className="p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">
                        Yeni Desi Bazlı Fiyatlandırma Ekle
                    </h2>
                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label htmlFor="min_desi" className="block text-sm font-medium text-gray-700">
                                    Minimum Desi
                                </label>
                                <input
                                    type="number"
                                    id="min_desi"
                                    step="0.01"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.min_desi}
                                    onChange={(e) => setData("min_desi", e.target.value)}
                                    required
                                />
                                <InputError message={errors.min_desi} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="max_desi" className="block text-sm font-medium text-gray-700">
                                    Maksimum Desi
                                </label>
                                <input
                                    type="number"
                                    id="max_desi"
                                    step="0.01"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.max_desi}
                                    onChange={(e) => setData("max_desi", e.target.value)}
                                    required
                                />
                                <InputError message={errors.max_desi} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="cost" className="block text-sm font-medium text-gray-700">
                                    Fiyat (TL)
                                </label>
                                <input
                                    type="number"
                                    id="cost"
                                    step="0.01"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.cost}
                                    onChange={(e) => setData("cost", e.target.value)}
                                    required
                                />
                                <InputError message={errors.cost} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="estimated_delivery_days" className="block text-sm font-medium text-gray-700">
                                    Tahmini Teslimat Süresi (Gün)
                                </label>
                                <input
                                    type="number"
                                    id="estimated_delivery_days"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.estimated_delivery_days}
                                    onChange={(e) => setData("estimated_delivery_days", e.target.value)}
                                />
                                <InputError message={errors.estimated_delivery_days} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="condition" className="block text-sm font-medium text-gray-700">
                                    Koşul
                                </label>
                                <input
                                    type="text"
                                    id="condition"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.condition}
                                    onChange={(e) => setData("condition", e.target.value)}
                                    placeholder="Örn: Aynı Gün Teslimat"
                                />
                                <InputError message={errors.condition} className="mt-2" />
                            </div>
                            <div className="flex items-center mt-4">
                                <input
                                    id="is_active"
                                    type="checkbox"
                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    checked={data.is_active}
                                    onChange={(e) => setData("is_active", e.target.checked)}
                                />
                                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                                    Aktif
                                </label>
                                <InputError message={errors.is_active} className="mt-2" />
                            </div>
                        </div>
                        <div className="flex justify-end mt-6">
                            <button
                                type="button"
                                className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:shadow-outline-gray transition ease-in-out duration-150 mr-3"
                                onClick={closeModals}
                            >
                                İptal
                            </button>
                            <button
                                type="submit"
                                className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-700 focus:shadow-outline-blue transition ease-in-out duration-150"
                                disabled={processing}
                            >
                                {processing ? "İşleniyor..." : "Kaydet"}
                            </button>
                        </div>
                    </form>
                </div>
            </Modal>

            {/* Düzenleme Modalı */}
            <Modal show={isEditModalOpen} onClose={closeModals} maxWidth="md">
                <div className="p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">
                        Desi Bazlı Fiyatlandırma Düzenle
                    </h2>
                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label htmlFor="min_desi" className="block text-sm font-medium text-gray-700">
                                    Minimum Desi
                                </label>
                                <input
                                    type="number"
                                    id="min_desi"
                                    step="0.01"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.min_desi}
                                    onChange={(e) => setData("min_desi", e.target.value)}
                                    required
                                />
                                <InputError message={errors.min_desi} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="max_desi" className="block text-sm font-medium text-gray-700">
                                    Maksimum Desi
                                </label>
                                <input
                                    type="number"
                                    id="max_desi"
                                    step="0.01"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.max_desi}
                                    onChange={(e) => setData("max_desi", e.target.value)}
                                    required
                                />
                                <InputError message={errors.max_desi} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="cost" className="block text-sm font-medium text-gray-700">
                                    Fiyat (TL)
                                </label>
                                <input
                                    type="number"
                                    id="cost"
                                    step="0.01"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.cost}
                                    onChange={(e) => setData("cost", e.target.value)}
                                    required
                                />
                                <InputError message={errors.cost} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="estimated_delivery_days" className="block text-sm font-medium text-gray-700">
                                    Tahmini Teslimat Süresi (Gün)
                                </label>
                                <input
                                    type="number"
                                    id="estimated_delivery_days"
                                    min="0"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.estimated_delivery_days}
                                    onChange={(e) => setData("estimated_delivery_days", e.target.value)}
                                />
                                <InputError message={errors.estimated_delivery_days} className="mt-2" />
                            </div>
                            <div>
                                <label htmlFor="condition" className="block text-sm font-medium text-gray-700">
                                    Koşul
                                </label>
                                <input
                                    type="text"
                                    id="condition"
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    value={data.condition}
                                    onChange={(e) => setData("condition", e.target.value)}
                                    placeholder="Örn: Aynı Gün Teslimat"
                                />
                                <InputError message={errors.condition} className="mt-2" />
                            </div>
                            <div className="flex items-center mt-4">
                                <input
                                    id="is_active"
                                    type="checkbox"
                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    checked={data.is_active}
                                    onChange={(e) => setData("is_active", e.target.checked)}
                                />
                                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                                    Aktif
                                </label>
                                <InputError message={errors.is_active} className="mt-2" />
                            </div>
                        </div>
                        <div className="flex justify-end mt-6">
                            <button
                                type="button"
                                className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:shadow-outline-gray transition ease-in-out duration-150 mr-3"
                                onClick={closeModals}
                            >
                                İptal
                            </button>
                            <button
                                type="submit"
                                className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-700 focus:shadow-outline-blue transition ease-in-out duration-150"
                                disabled={processing}
                            >
                                {processing ? "İşleniyor..." : "Güncelle"}
                            </button>
                        </div>
                    </form>
                </div>
            </Modal>

            {/* Silme Modalı */}
            <Modal show={isDeleteModalOpen} onClose={closeModals} maxWidth="sm">
                <div className="p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">
                        Desi Bazlı Fiyatlandırma Sil
                    </h2>
                    <p className="mb-4 text-sm text-gray-600">
                        Bu fiyatlandırma kaydını silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
                    </p>
                    <div className="flex justify-end">
                        <button
                            type="button"
                            className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:shadow-outline-gray transition ease-in-out duration-150 mr-3"
                            onClick={closeModals}
                        >
                            İptal
                        </button>
                        <button
                            type="button"
                            className="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-800 focus:outline-none focus:border-red-700 focus:shadow-outline-red transition ease-in-out duration-150"
                            onClick={handleDelete}
                            disabled={processing}
                        >
                            {processing ? "İşleniyor..." : "Sil"}
                        </button>
                    </div>
                </div>
            </Modal>
        </AdminLayout>
    );
}
