import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import { toast } from "react-hot-toast";
import { axiosDelete } from "@/Utils/inertiaHelper";

export default function ShippingZoneMethods({
    zone,
    zoneMethods,
    availableMethods,
}) {
    const { data, setData, post, processing, errors, reset } = useForm({
        method_id: "",
        cost: 0,
        cost_per_order_percent: 0,
        cost_per_weight: 0,
        cost_per_desi: 0,
        min_order_amount: "",
        max_order_amount: "",
        min_weight: "",
        max_weight: "",
        min_desi: "",
        max_desi: "",
        is_free_shipping: false,
        free_shipping_min_amount: "",
        estimated_delivery_days: "",
        is_active: true,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route("admin.shipping.zones.methods.add", zone.id), {
            onSuccess: () => {
                reset();
                toast.success("Kargo metodu başarıyla eklendi");
            },
        });
    };

    const handleRemoveMethod = (methodId) => {
        if (confirm("Bu kargo metodunu silmek istediğinize emin misiniz?")) {
            const url = route("admin.shipping.zones.methods.remove", [
                zone.id,
                methodId,
            ]);

            // axiosDelete yardımcı fonksiyonu ile silme işlemi yap
            axiosDelete(url)
                .then((response) => {
                    toast.success("Kargo metodu başarıyla silindi");
                    window.location.reload();
                })
                .catch((error) => {
                    toast.error("Kargo metodu silinirken bir hata oluştu");
                    console.error(error);
                });
        }
    };

    return (
        <AdminLayout>
            <Head title={`Kargo Bölgesi Metodları: ${zone.name}`} />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Kargo Bölgesi Metodları: {zone.name}
                    </h1>
                    <Link
                        href={route("admin.shipping.zones.index")}
                        className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        Geri Dön
                    </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Kargo Metodu Ekleme Formu */}
                    <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">
                            Yeni Kargo Metodu Ekle
                        </h2>

                        {availableMethods && availableMethods.length > 0 ? (
                            <form onSubmit={handleSubmit}>
                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="method_id"
                                        value="Kargo Metodu"
                                    />
                                    <select
                                        id="method_id"
                                        name="method_id"
                                        value={data.method_id}
                                        className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        onChange={(e) =>
                                            setData("method_id", e.target.value)
                                        }
                                        required
                                    >
                                        <option value="">
                                            Kargo Metodu Seçin
                                        </option>
                                        {availableMethods.map((method) => (
                                            <option
                                                key={method.id}
                                                value={method.id}
                                            >
                                                {method.name}
                                            </option>
                                        ))}
                                    </select>
                                    <InputError
                                        message={errors.method_id}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="cost"
                                        value="Sabit Kargo Ücreti (₺)"
                                    />
                                    <TextInput
                                        id="cost"
                                        type="number"
                                        name="cost"
                                        value={data.cost}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData("cost", e.target.value)
                                        }
                                        step="0.01"
                                        min="0"
                                        required
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Temel kargo ücreti (diğer ücretlere ek
                                        olarak)
                                    </p>
                                    <InputError
                                        message={errors.cost}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="cost_per_order_percent"
                                        value="Sipariş Tutarına Göre Ek Ücret (%)"
                                    />
                                    <TextInput
                                        id="cost_per_order_percent"
                                        type="number"
                                        name="cost_per_order_percent"
                                        value={data.cost_per_order_percent}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "cost_per_order_percent",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Sipariş tutarının yüzdesi olarak ek
                                        ücret (örn: %2)
                                    </p>
                                    <InputError
                                        message={errors.cost_per_order_percent}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="cost_per_weight"
                                        value="Ağırlık Başına Ek Ücret (₺/kg)"
                                    />
                                    <TextInput
                                        id="cost_per_weight"
                                        type="number"
                                        name="cost_per_weight"
                                        value={data.cost_per_weight}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "cost_per_weight",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Kilogram başına ek ücret (örn: 5 ₺/kg)
                                    </p>
                                    <InputError
                                        message={errors.cost_per_weight}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="cost_per_desi"
                                        value="Desi Başına Ek Ücret (₺/desi)"
                                    />
                                    <TextInput
                                        id="cost_per_desi"
                                        type="number"
                                        name="cost_per_desi"
                                        value={data.cost_per_desi}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "cost_per_desi",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Desi başına ek ücret (örn: 10 ₺/desi)
                                    </p>
                                    <InputError
                                        message={errors.cost_per_desi}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="is_free_shipping"
                                            name="is_free_shipping"
                                            checked={data.is_free_shipping}
                                            onChange={(e) =>
                                                setData(
                                                    "is_free_shipping",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="is_free_shipping"
                                            value="Ücretsiz Kargo"
                                            className="ml-2"
                                        />
                                    </div>
                                    <InputError
                                        message={errors.is_free_shipping}
                                        className="mt-2"
                                    />
                                </div>

                                {data.is_free_shipping && (
                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="free_shipping_min_amount"
                                            value="Ücretsiz Kargo Minimum Sipariş Tutarı (₺)"
                                        />
                                        <TextInput
                                            id="free_shipping_min_amount"
                                            type="number"
                                            name="free_shipping_min_amount"
                                            value={
                                                data.free_shipping_min_amount
                                            }
                                            className="mt-1 block w-full"
                                            onChange={(e) =>
                                                setData(
                                                    "free_shipping_min_amount",
                                                    e.target.value
                                                )
                                            }
                                            step="0.01"
                                            min="0"
                                        />
                                        <InputError
                                            message={
                                                errors.free_shipping_min_amount
                                            }
                                            className="mt-2"
                                        />
                                    </div>
                                )}

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="min_order_amount"
                                        value="Minimum Sipariş Tutarı (₺, opsiyonel)"
                                    />
                                    <TextInput
                                        id="min_order_amount"
                                        type="number"
                                        name="min_order_amount"
                                        value={data.min_order_amount}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "min_order_amount",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <InputError
                                        message={errors.min_order_amount}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="max_order_amount"
                                        value="Maksimum Sipariş Tutarı (₺, opsiyonel)"
                                    />
                                    <TextInput
                                        id="max_order_amount"
                                        type="number"
                                        name="max_order_amount"
                                        value={data.max_order_amount}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "max_order_amount",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <InputError
                                        message={errors.max_order_amount}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="min_weight"
                                        value="Minimum Ağırlık (kg, opsiyonel)"
                                    />
                                    <TextInput
                                        id="min_weight"
                                        type="number"
                                        name="min_weight"
                                        value={data.min_weight}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "min_weight",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <InputError
                                        message={errors.min_weight}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="max_weight"
                                        value="Maksimum Ağırlık (kg, opsiyonel)"
                                    />
                                    <TextInput
                                        id="max_weight"
                                        type="number"
                                        name="max_weight"
                                        value={data.max_weight}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "max_weight",
                                                e.target.value
                                            )
                                        }
                                        step="0.01"
                                        min="0"
                                    />
                                    <InputError
                                        message={errors.max_weight}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="min_desi"
                                        value="Minimum Desi (opsiyonel)"
                                    />
                                    <TextInput
                                        id="min_desi"
                                        type="number"
                                        name="min_desi"
                                        value={data.min_desi}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData("min_desi", e.target.value)
                                        }
                                        step="1"
                                        min="0"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Bu desi değerinden küçük ürünler için bu
                                        kargo metodu kullanılmaz
                                    </p>
                                    <InputError
                                        message={errors.min_desi}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="max_desi"
                                        value="Maksimum Desi (opsiyonel)"
                                    />
                                    <TextInput
                                        id="max_desi"
                                        type="number"
                                        name="max_desi"
                                        value={data.max_desi}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData("max_desi", e.target.value)
                                        }
                                        step="1"
                                        min="0"
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Bu desi değerinden büyük ürünler için bu
                                        kargo metodu kullanılmaz
                                    </p>
                                    <InputError
                                        message={errors.max_desi}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="estimated_delivery_days"
                                        value="Tahmini Teslimat Süresi (gün, opsiyonel)"
                                    />
                                    <TextInput
                                        id="estimated_delivery_days"
                                        type="number"
                                        name="estimated_delivery_days"
                                        value={data.estimated_delivery_days}
                                        className="mt-1 block w-full"
                                        onChange={(e) =>
                                            setData(
                                                "estimated_delivery_days",
                                                e.target.value
                                            )
                                        }
                                        min="0"
                                    />
                                    <InputError
                                        message={errors.estimated_delivery_days}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="mb-4">
                                    <div className="flex items-center">
                                        <Checkbox
                                            id="is_active"
                                            name="is_active"
                                            checked={data.is_active}
                                            onChange={(e) =>
                                                setData(
                                                    "is_active",
                                                    e.target.checked
                                                )
                                            }
                                        />
                                        <InputLabel
                                            htmlFor="is_active"
                                            value="Aktif"
                                            className="ml-2"
                                        />
                                    </div>
                                    <InputError
                                        message={errors.is_active}
                                        className="mt-2"
                                    />
                                </div>

                                <div className="flex items-center justify-end mt-4">
                                    <PrimaryButton
                                        type="submit"
                                        disabled={processing}
                                    >
                                        Kargo Metodu Ekle
                                    </PrimaryButton>
                                </div>
                            </form>
                        ) : (
                            <div className="text-center py-4 text-gray-500">
                                Eklenebilecek kargo metodu kalmadı. Önce yeni
                                bir kargo metodu oluşturun.
                            </div>
                        )}
                    </div>

                    {/* Mevcut Kargo Metodları Listesi */}
                    <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">
                            Mevcut Kargo Metodları
                        </h2>

                        {zoneMethods && zoneMethods.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Metod
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Ücret
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Desi Bazlı Fiyatlandırma
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Durum
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                İşlemler
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {zoneMethods.map((zoneMethod) => (
                                            <tr key={zoneMethod.id}>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {zoneMethod.method?.name ||
                                                        "-"}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {zoneMethod.is_free_shipping ? (
                                                        <span className="text-green-600">
                                                            Ücretsiz
                                                            {zoneMethod.free_shipping_min_amount && (
                                                                <span className="text-gray-500">
                                                                    {" "}
                                                                    (Min:{" "}
                                                                    {
                                                                        zoneMethod.free_shipping_min_amount
                                                                    }{" "}
                                                                    ₺)
                                                                </span>
                                                            )}
                                                        </span>
                                                    ) : (
                                                        <div className="flex flex-col">
                                                            <span className="font-medium">
                                                                {
                                                                    zoneMethod.cost
                                                                }{" "}
                                                                ₺ (Sabit)
                                                            </span>
                                                            {zoneMethod.cost_per_desi >
                                                                0 && (
                                                                <span className="text-xs">
                                                                    +{" "}
                                                                    {
                                                                        zoneMethod.cost_per_desi
                                                                    }{" "}
                                                                    ₺/desi
                                                                </span>
                                                            )}
                                                            {zoneMethod.cost_per_weight >
                                                                0 && (
                                                                <span className="text-xs">
                                                                    +{" "}
                                                                    {
                                                                        zoneMethod.cost_per_weight
                                                                    }{" "}
                                                                    ₺/kg
                                                                </span>
                                                            )}
                                                            {zoneMethod.cost_per_order_percent >
                                                                0 && (
                                                                <span className="text-xs">
                                                                    + %
                                                                    {
                                                                        zoneMethod.cost_per_order_percent
                                                                    }{" "}
                                                                    sipariş
                                                                    tutarı
                                                                </span>
                                                            )}
                                                            {zoneMethod.min_desi && (
                                                                <span className="text-xs text-gray-400">
                                                                    Min:{" "}
                                                                    {
                                                                        zoneMethod.min_desi
                                                                    }{" "}
                                                                    desi
                                                                </span>
                                                            )}
                                                            {zoneMethod.max_desi && (
                                                                <span className="text-xs text-gray-400">
                                                                    Max:{" "}
                                                                    {
                                                                        zoneMethod.max_desi
                                                                    }{" "}
                                                                    desi
                                                                </span>
                                                            )}
                                                        </div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {zoneMethod.rates &&
                                                    zoneMethod.rates.length >
                                                        0 ? (
                                                        <span className="text-green-600">
                                                            {
                                                                zoneMethod.rates
                                                                    .length
                                                            }{" "}
                                                            adet fiyatlandırma
                                                        </span>
                                                    ) : (
                                                        <span className="text-gray-500">
                                                            Tanımlanmamış
                                                        </span>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <span
                                                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                            zoneMethod.is_active
                                                                ? "bg-green-100 text-green-800"
                                                                : "bg-red-100 text-red-800"
                                                        }`}
                                                    >
                                                        {zoneMethod.is_active
                                                            ? "Aktif"
                                                            : "Pasif"}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div className="flex justify-end space-x-2">
                                                        <Link
                                                            href={route(
                                                                "admin.shipping.zones.methods.rates.index",
                                                                zoneMethod.id
                                                            )}
                                                            className="text-green-600 hover:text-green-900"
                                                        >
                                                            Desi Fiyatlandırma
                                                        </Link>
                                                        <Link
                                                            href={route(
                                                                "admin.shipping.zones.methods.edit",
                                                                [
                                                                    zone.id,
                                                                    zoneMethod.id,
                                                                ]
                                                            )}
                                                            className="text-yellow-600 hover:text-yellow-900"
                                                        >
                                                            Düzenle
                                                        </Link>
                                                        <button
                                                            onClick={() =>
                                                                handleRemoveMethod(
                                                                    zoneMethod.id
                                                                )
                                                            }
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            Sil
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500">
                                Bu bölge için henüz kargo metodu eklenmemiş.
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
