import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";

export default function ShippingZoneEdit({ zone }) {
  const { data, setData, put, processing, errors } = useForm({
    name: zone.name || "",
    description: zone.description || "",
    is_active: zone.is_active || false,
    order: zone.order || 0,
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    put(route("admin.shipping.zones.update", zone.id));
  };

  return (
    <AdminLayout>
      <Head title={`Kargo Bölgesi Düzenle: ${zone.name}`} />

      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold text-gray-900">
            Kargo Bölgesi Düzenle: {zone.name}
          </h1>
        </div>

        <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <InputLabel htmlFor="name" value="Bölge Adı" />
              <TextInput
                id="name"
                type="text"
                name="name"
                value={data.name}
                className="mt-1 block w-full"
                onChange={(e) => setData("name", e.target.value)}
                required
              />
              <InputError message={errors.name} className="mt-2" />
            </div>

            <div className="mb-4">
              <InputLabel htmlFor="description" value="Açıklama" />
              <textarea
                id="description"
                name="description"
                value={data.description}
                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                onChange={(e) => setData("description", e.target.value)}
                rows={3}
              />
              <InputError message={errors.description} className="mt-2" />
            </div>

            <div className="mb-4">
              <InputLabel htmlFor="order" value="Sıra" />
              <TextInput
                id="order"
                type="number"
                name="order"
                value={data.order}
                className="mt-1 block w-full"
                onChange={(e) => setData("order", e.target.value)}
                min={0}
              />
              <InputError message={errors.order} className="mt-2" />
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <Checkbox
                  id="is_active"
                  name="is_active"
                  checked={data.is_active}
                  onChange={(e) => setData("is_active", e.target.checked)}
                />
                <InputLabel
                  htmlFor="is_active"
                  value="Aktif"
                  className="ml-2"
                />
              </div>
              <InputError message={errors.is_active} className="mt-2" />
            </div>

            <div className="flex items-center justify-end mt-4 space-x-2">
              <Link href={route("admin.shipping.zones.index")}>
                <SecondaryButton type="button">İptal</SecondaryButton>
              </Link>
              <PrimaryButton type="submit" disabled={processing}>
                Güncelle
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
