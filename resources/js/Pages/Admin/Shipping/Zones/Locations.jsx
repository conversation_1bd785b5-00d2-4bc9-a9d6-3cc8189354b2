import React, { useState } from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import { toast } from "react-hot-toast";
import { axiosDelete } from "@/Utils/inertiaHelper";

export default function ShippingZoneLocations({ zone, locations, countries }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        location_type: "country",
        location_id: "",
        location_code: "",
        location_name: "",
    });

    const [states, setStates] = useState([]);
    const [cities, setCities] = useState([]);
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [selectedState, setSelectedState] = useState(null);

    // Sayfa yüklendiğinde countries değişkenini kontrol et
    React.useEffect(() => {
        if (!Array.isArray(countries)) {
            console.error("countries değişkeni bir dizi değil:", countries);
            // Boş dizi olarak ayarla
            countries = [];
        }
    }, []);

    const handleCountryChange = async (countryId) => {
        // Eğer countryId boş string ise, null olarak ayarla
        const id = countryId === "" ? null : countryId;

        setData("location_id", id);
        setSelectedCountry(id);

        if (id) {
            try {
                const response = await fetch(
                    `/api/locations/countries/${id}/states`
                );
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                setStates(data);
            } catch (error) {
                console.error("Eyaletler alınırken hata oluştu:", error);
                toast.error("Eyaletler alınırken bir hata oluştu");
                setStates([]);
            }
        } else {
            setStates([]);
        }
        setSelectedState(null);
        setCities([]);
    };

    const handleStateChange = async (stateId) => {
        // Eğer stateId boş string ise, null olarak ayarla
        const id = stateId === "" ? null : stateId;

        setData("location_id", id);
        setSelectedState(id);

        if (id) {
            try {
                const response = await fetch(
                    `/api/locations/states/${id}/cities`
                );
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                setCities(data);
            } catch (error) {
                console.error("Şehirler alınırken hata oluştu:", error);
                toast.error("Şehirler alınırken bir hata oluştu");
                setCities([]);
            }
        } else {
            setCities([]);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Form verilerini kontrol et
        if (data.location_type === "country" && !data.location_id) {
            toast.error("Lütfen bir ülke seçin");
            return;
        }

        if (data.location_type === "state" && !data.location_id) {
            toast.error("Lütfen bir eyalet/il seçin");
            return;
        }

        if (data.location_type === "city" && !data.location_id) {
            toast.error("Lütfen bir şehir/ilçe seçin");
            return;
        }

        if (data.location_type === "postal_code" && !data.location_code) {
            toast.error("Lütfen bir posta kodu girin");
            return;
        }

        post(route("admin.shipping.zones.locations.add", zone.id), {
            onSuccess: () => {
                reset();
                toast.success("Lokasyon başarıyla eklendi");
            },
            onError: (errors) => {
                console.error("Form gönderim hatası:", errors);
                if (errors.location_type) {
                    toast.error(errors.location_type);
                } else if (errors.location_id) {
                    toast.error(errors.location_id);
                } else if (errors.location_code) {
                    toast.error(errors.location_code);
                } else {
                    toast.error("Lokasyon eklenirken bir hata oluştu");
                }
            },
        });
    };

    const handleRemoveLocation = (locationId) => {
        if (confirm("Bu lokasyonu silmek istediğinize emin misiniz?")) {
            const url = route("admin.shipping.zones.locations.remove", [
                zone.id,
                locationId,
            ]);

            // axiosDelete yardımcı fonksiyonu ile silme işlemi yap
            axiosDelete(url)
                .then((response) => {
                    toast.success("Lokasyon başarıyla silindi");
                    window.location.reload();
                })
                .catch((error) => {
                    toast.error("Lokasyon silinirken bir hata oluştu");
                    console.error(error);
                });
        }
    };

    return (
        <AdminLayout>
            <Head title={`Kargo Bölgesi Lokasyonları: ${zone.name}`} />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Kargo Bölgesi Lokasyonları: {zone.name}
                    </h1>
                    <Link
                        href={route("admin.shipping.zones.index")}
                        className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        Geri Dön
                    </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Lokasyon Ekleme Formu */}
                    <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">
                            Yeni Lokasyon Ekle
                        </h2>

                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <InputLabel
                                    htmlFor="location_type"
                                    value="Lokasyon Türü"
                                />
                                <select
                                    id="location_type"
                                    name="location_type"
                                    value={data.location_type}
                                    className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    onChange={(e) => {
                                        setData(
                                            "location_type",
                                            e.target.value
                                        );
                                        setData("location_id", "");
                                        setData("location_code", "");
                                        setData("location_name", "");
                                    }}
                                    required
                                >
                                    <option value="country">Ülke</option>
                                    <option value="state">Eyalet/İl</option>
                                    <option value="city">Şehir/İlçe</option>
                                    <option value="postal_code">
                                        Posta Kodu
                                    </option>
                                </select>
                                <InputError
                                    message={errors.location_type}
                                    className="mt-2"
                                />
                            </div>

                            {data.location_type === "country" && (
                                <div className="mb-4">
                                    <InputLabel
                                        htmlFor="location_id"
                                        value="Ülke"
                                    />
                                    <select
                                        id="location_id"
                                        name="location_id"
                                        value={data.location_id}
                                        className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        onChange={(e) =>
                                            setData(
                                                "location_id",
                                                e.target.value
                                            )
                                        }
                                        required
                                    >
                                        <option value="">Ülke Seçin</option>
                                        {Array.isArray(countries) &&
                                        countries.length > 0 ? (
                                            countries.map((country) => (
                                                <option
                                                    key={country.id}
                                                    value={country.id}
                                                >
                                                    {country.name}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="" disabled>
                                                Ülke bulunamadı
                                            </option>
                                        )}
                                    </select>
                                    <InputError
                                        message={errors.location_id}
                                        className="mt-2"
                                    />
                                </div>
                            )}

                            {data.location_type === "state" && (
                                <>
                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="country_id"
                                            value="Ülke"
                                        />
                                        <select
                                            id="country_id"
                                            name="country_id"
                                            value={selectedCountry || ""}
                                            className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                            onChange={(e) =>
                                                handleCountryChange(
                                                    e.target.value
                                                )
                                            }
                                            required
                                        >
                                            <option value="">Ülke Seçin</option>
                                            {Array.isArray(countries) &&
                                            countries.length > 0 ? (
                                                countries.map((country) => (
                                                    <option
                                                        key={country.id}
                                                        value={country.id}
                                                    >
                                                        {country.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>
                                                    Ülke bulunamadı
                                                </option>
                                            )}
                                        </select>
                                    </div>

                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="location_id"
                                            value="Eyalet/İl"
                                        />
                                        <select
                                            id="location_id"
                                            name="location_id"
                                            value={data.location_id}
                                            className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                            onChange={(e) =>
                                                setData(
                                                    "location_id",
                                                    e.target.value
                                                )
                                            }
                                            required
                                        >
                                            <option value="">
                                                Eyalet/İl Seçin
                                            </option>
                                            {Array.isArray(states) &&
                                            states.length > 0 ? (
                                                states.map((state) => (
                                                    <option
                                                        key={state.id}
                                                        value={state.id}
                                                    >
                                                        {state.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>
                                                    Eyalet/İl bulunamadı
                                                </option>
                                            )}
                                        </select>
                                        <InputError
                                            message={errors.location_id}
                                            className="mt-2"
                                        />
                                    </div>
                                </>
                            )}

                            {data.location_type === "city" && (
                                <>
                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="country_id"
                                            value="Ülke"
                                        />
                                        <select
                                            id="country_id"
                                            name="country_id"
                                            value={selectedCountry || ""}
                                            className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                            onChange={(e) =>
                                                handleCountryChange(
                                                    e.target.value
                                                )
                                            }
                                            required
                                        >
                                            <option value="">Ülke Seçin</option>
                                            {Array.isArray(countries) &&
                                            countries.length > 0 ? (
                                                countries.map((country) => (
                                                    <option
                                                        key={country.id}
                                                        value={country.id}
                                                    >
                                                        {country.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>
                                                    Ülke bulunamadı
                                                </option>
                                            )}
                                        </select>
                                    </div>

                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="state_id"
                                            value="Eyalet/İl"
                                        />
                                        <select
                                            id="state_id"
                                            name="state_id"
                                            value={selectedState || ""}
                                            className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                            onChange={(e) =>
                                                handleStateChange(
                                                    e.target.value
                                                )
                                            }
                                            required
                                        >
                                            <option value="">
                                                Eyalet/İl Seçin
                                            </option>
                                            {Array.isArray(states) &&
                                            states.length > 0 ? (
                                                states.map((state) => (
                                                    <option
                                                        key={state.id}
                                                        value={state.id}
                                                    >
                                                        {state.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>
                                                    Eyalet/İl bulunamadı
                                                </option>
                                            )}
                                        </select>
                                    </div>

                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="location_id"
                                            value="Şehir/İlçe"
                                        />
                                        <select
                                            id="location_id"
                                            name="location_id"
                                            value={data.location_id}
                                            className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                            onChange={(e) =>
                                                setData(
                                                    "location_id",
                                                    e.target.value
                                                )
                                            }
                                            required
                                        >
                                            <option value="">
                                                Şehir/İlçe Seçin
                                            </option>
                                            {Array.isArray(cities) &&
                                            cities.length > 0 ? (
                                                cities.map((city) => (
                                                    <option
                                                        key={city.id}
                                                        value={city.id}
                                                    >
                                                        {city.name}
                                                    </option>
                                                ))
                                            ) : (
                                                <option value="" disabled>
                                                    Şehir/İlçe bulunamadı
                                                </option>
                                            )}
                                        </select>
                                        <InputError
                                            message={errors.location_id}
                                            className="mt-2"
                                        />
                                    </div>
                                </>
                            )}

                            {data.location_type === "postal_code" && (
                                <>
                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="location_code"
                                            value="Posta Kodu"
                                        />
                                        <TextInput
                                            id="location_code"
                                            type="text"
                                            name="location_code"
                                            value={data.location_code}
                                            className="mt-1 block w-full"
                                            onChange={(e) =>
                                                setData(
                                                    "location_code",
                                                    e.target.value
                                                )
                                            }
                                            required
                                        />
                                        <InputError
                                            message={errors.location_code}
                                            className="mt-2"
                                        />
                                    </div>

                                    <div className="mb-4">
                                        <InputLabel
                                            htmlFor="location_name"
                                            value="Açıklama (Opsiyonel)"
                                        />
                                        <TextInput
                                            id="location_name"
                                            type="text"
                                            name="location_name"
                                            value={data.location_name}
                                            className="mt-1 block w-full"
                                            onChange={(e) =>
                                                setData(
                                                    "location_name",
                                                    e.target.value
                                                )
                                            }
                                        />
                                        <InputError
                                            message={errors.location_name}
                                            className="mt-2"
                                        />
                                    </div>
                                </>
                            )}

                            <div className="flex items-center justify-end mt-4">
                                <PrimaryButton
                                    type="submit"
                                    disabled={processing}
                                >
                                    Lokasyon Ekle
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>

                    {/* Mevcut Lokasyonlar Listesi */}
                    <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">
                            Mevcut Lokasyonlar
                        </h2>

                        {locations && locations.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Tür
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Lokasyon
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                İşlemler
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {Array.isArray(locations) &&
                                        locations.length > 0 ? (
                                            locations.map((location) => (
                                                <tr key={location.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {location.location_type ===
                                                        "country"
                                                            ? "Ülke"
                                                            : location.location_type ===
                                                              "state"
                                                            ? "Eyalet/İl"
                                                            : location.location_type ===
                                                              "city"
                                                            ? "Şehir/İlçe"
                                                            : "Posta Kodu"}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        {location.location_name ||
                                                            location.location_code ||
                                                            "-"}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <button
                                                            onClick={() =>
                                                                handleRemoveLocation(
                                                                    location.id
                                                                )
                                                            }
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            Sil
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td
                                                    colSpan="3"
                                                    className="px-6 py-4 text-center text-sm text-gray-500"
                                                >
                                                    Lokasyon bulunamadı
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-4 text-gray-500">
                                Bu bölge için henüz lokasyon eklenmemiş.
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
