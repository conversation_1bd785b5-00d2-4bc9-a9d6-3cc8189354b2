import React from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";

export default function ShippingZoneEditMethod({ zone, zoneMethod }) {
    const { data, setData, put, processing, errors } = useForm({
        cost: zoneMethod.cost || 0,
        cost_per_order_percent: zoneMethod.cost_per_order_percent || 0,
        cost_per_weight: zoneMethod.cost_per_weight || 0,
        cost_per_desi: zoneMethod.cost_per_desi || 0,
        min_order_amount: zoneMethod.min_order_amount || "",
        max_order_amount: zoneMethod.max_order_amount || "",
        min_weight: zoneMethod.min_weight || "",
        max_weight: zoneMethod.max_weight || "",
        min_desi: zoneMethod.min_desi || "",
        max_desi: zoneMethod.max_desi || "",
        is_free_shipping: zoneMethod.is_free_shipping || false,
        free_shipping_min_amount: zoneMethod.free_shipping_min_amount || "",
        estimated_delivery_days: zoneMethod.estimated_delivery_days || "",
        is_active: zoneMethod.is_active || false,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(
            route("admin.shipping.zones.methods.update", [
                zone.id,
                zoneMethod.id,
            ])
        );
    };

    return (
        <AdminLayout>
            <Head title={`Kargo Metodu Düzenle: ${zoneMethod.method?.name}`} />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Kargo Metodu Düzenle: {zoneMethod.method?.name}
                    </h1>
                    <Link
                        href={route("admin.shipping.zones.methods", zone.id)}
                        className="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        Geri Dön
                    </Link>
                </div>

                <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <InputLabel
                                htmlFor="cost"
                                value="Sabit Kargo Ücreti (₺)"
                            />
                            <TextInput
                                id="cost"
                                type="number"
                                name="cost"
                                value={data.cost}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("cost", e.target.value)
                                }
                                step="0.01"
                                min="0"
                                required
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Temel kargo ücreti (diğer ücretlere ek olarak)
                            </p>
                            <InputError
                                message={errors.cost}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="cost_per_order_percent"
                                value="Sipariş Tutarına Göre Ek Ücret (%)"
                            />
                            <TextInput
                                id="cost_per_order_percent"
                                type="number"
                                name="cost_per_order_percent"
                                value={data.cost_per_order_percent}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData(
                                        "cost_per_order_percent",
                                        e.target.value
                                    )
                                }
                                step="0.01"
                                min="0"
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Sipariş tutarının yüzdesi olarak ek ücret (örn:
                                %2)
                            </p>
                            <InputError
                                message={errors.cost_per_order_percent}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="cost_per_weight"
                                value="Ağırlık Başına Ek Ücret (₺/kg)"
                            />
                            <TextInput
                                id="cost_per_weight"
                                type="number"
                                name="cost_per_weight"
                                value={data.cost_per_weight}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("cost_per_weight", e.target.value)
                                }
                                step="0.01"
                                min="0"
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Kilogram başına ek ücret (örn: 5 ₺/kg)
                            </p>
                            <InputError
                                message={errors.cost_per_weight}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="cost_per_desi"
                                value="Desi Başına Ek Ücret (₺/desi)"
                            />
                            <TextInput
                                id="cost_per_desi"
                                type="number"
                                name="cost_per_desi"
                                value={data.cost_per_desi}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("cost_per_desi", e.target.value)
                                }
                                step="0.01"
                                min="0"
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Desi başına ek ücret (örn: 10 ₺/desi)
                            </p>
                            <InputError
                                message={errors.cost_per_desi}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <div className="flex items-center">
                                <Checkbox
                                    id="is_free_shipping"
                                    name="is_free_shipping"
                                    checked={data.is_free_shipping}
                                    onChange={(e) =>
                                        setData(
                                            "is_free_shipping",
                                            e.target.checked
                                        )
                                    }
                                />
                                <InputLabel
                                    htmlFor="is_free_shipping"
                                    value="Ücretsiz Kargo"
                                    className="ml-2"
                                />
                            </div>
                            <InputError
                                message={errors.is_free_shipping}
                                className="mt-2"
                            />
                        </div>

                        {data.is_free_shipping && (
                            <div className="mb-4">
                                <InputLabel
                                    htmlFor="free_shipping_min_amount"
                                    value="Ücretsiz Kargo Minimum Sipariş Tutarı (₺)"
                                />
                                <TextInput
                                    id="free_shipping_min_amount"
                                    type="number"
                                    name="free_shipping_min_amount"
                                    value={data.free_shipping_min_amount}
                                    className="mt-1 block w-full"
                                    onChange={(e) =>
                                        setData(
                                            "free_shipping_min_amount",
                                            e.target.value
                                        )
                                    }
                                    step="0.01"
                                    min="0"
                                />
                                <InputError
                                    message={errors.free_shipping_min_amount}
                                    className="mt-2"
                                />
                            </div>
                        )}

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="min_order_amount"
                                value="Minimum Sipariş Tutarı (₺, opsiyonel)"
                            />
                            <TextInput
                                id="min_order_amount"
                                type="number"
                                name="min_order_amount"
                                value={data.min_order_amount}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("min_order_amount", e.target.value)
                                }
                                step="0.01"
                                min="0"
                            />
                            <InputError
                                message={errors.min_order_amount}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="max_order_amount"
                                value="Maksimum Sipariş Tutarı (₺, opsiyonel)"
                            />
                            <TextInput
                                id="max_order_amount"
                                type="number"
                                name="max_order_amount"
                                value={data.max_order_amount}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("max_order_amount", e.target.value)
                                }
                                step="0.01"
                                min="0"
                            />
                            <InputError
                                message={errors.max_order_amount}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="min_weight"
                                value="Minimum Ağırlık (kg, opsiyonel)"
                            />
                            <TextInput
                                id="min_weight"
                                type="number"
                                name="min_weight"
                                value={data.min_weight}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("min_weight", e.target.value)
                                }
                                step="0.01"
                                min="0"
                            />
                            <InputError
                                message={errors.min_weight}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="max_weight"
                                value="Maksimum Ağırlık (kg, opsiyonel)"
                            />
                            <TextInput
                                id="max_weight"
                                type="number"
                                name="max_weight"
                                value={data.max_weight}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("max_weight", e.target.value)
                                }
                                step="0.01"
                                min="0"
                            />
                            <InputError
                                message={errors.max_weight}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="min_desi"
                                value="Minimum Desi (opsiyonel)"
                            />
                            <TextInput
                                id="min_desi"
                                type="number"
                                name="min_desi"
                                value={data.min_desi}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("min_desi", e.target.value)
                                }
                                step="1"
                                min="0"
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Bu desi değerinden küçük ürünler için bu kargo
                                metodu kullanılmaz
                            </p>
                            <InputError
                                message={errors.min_desi}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="max_desi"
                                value="Maksimum Desi (opsiyonel)"
                            />
                            <TextInput
                                id="max_desi"
                                type="number"
                                name="max_desi"
                                value={data.max_desi}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("max_desi", e.target.value)
                                }
                                step="1"
                                min="0"
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                Bu desi değerinden büyük ürünler için bu kargo
                                metodu kullanılmaz
                            </p>
                            <InputError
                                message={errors.max_desi}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="estimated_delivery_days"
                                value="Tahmini Teslimat Süresi (gün, opsiyonel)"
                            />
                            <TextInput
                                id="estimated_delivery_days"
                                type="number"
                                name="estimated_delivery_days"
                                value={data.estimated_delivery_days}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData(
                                        "estimated_delivery_days",
                                        e.target.value
                                    )
                                }
                                min="0"
                            />
                            <InputError
                                message={errors.estimated_delivery_days}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <div className="flex items-center">
                                <Checkbox
                                    id="is_active"
                                    name="is_active"
                                    checked={data.is_active}
                                    onChange={(e) =>
                                        setData("is_active", e.target.checked)
                                    }
                                />
                                <InputLabel
                                    htmlFor="is_active"
                                    value="Aktif"
                                    className="ml-2"
                                />
                            </div>
                            <InputError
                                message={errors.is_active}
                                className="mt-2"
                            />
                        </div>

                        <div className="flex items-center justify-end mt-4 space-x-2">
                            <Link
                                href={route(
                                    "admin.shipping.zones.methods",
                                    zone.id
                                )}
                            >
                                <SecondaryButton type="button">
                                    İptal
                                </SecondaryButton>
                            </Link>
                            <PrimaryButton type="submit" disabled={processing}>
                                Güncelle
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
}
