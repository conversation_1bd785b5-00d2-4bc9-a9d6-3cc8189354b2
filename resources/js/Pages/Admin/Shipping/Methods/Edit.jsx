import React from "react";
import { Head, Link, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";

export default function ShippingMethodEdit({ method, companies }) {
    const { data, setData, put, processing, errors } = useForm({
        name: method.name || "",
        code: method.code || "",
        description: method.description || "",
        shipping_company_id: method.shipping_company_id || "",
        delivery_time: method.delivery_time || "",
        is_active: method.is_active || false,
        requires_address: method.requires_address || true,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route("admin.shipping.methods.update", method.id));
    };

    return (
        <AdminLayout>
            <Head title={`Kargo Metodu Düzenle: ${method.name}`} />

            <div className="container mx-auto py-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Kargo Metodu Düzenle: {method.name}
                    </h1>
                </div>

                <div className="bg-white shadow-md rounded-lg overflow-hidden p-6">
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <InputLabel htmlFor="name" value="Metod Adı" />
                            <TextInput
                                id="name"
                                type="text"
                                name="name"
                                value={data.name}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("name", e.target.value)
                                }
                                required
                            />
                            <InputError
                                message={errors.name}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel htmlFor="code" value="Kod" />
                            <TextInput
                                id="code"
                                type="text"
                                name="code"
                                value={data.code}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("code", e.target.value)
                                }
                                required
                            />
                            <p className="mt-1 text-sm text-gray-500">
                                Benzersiz bir kod girin (örn: standard, express,
                                same_day)
                            </p>
                            <InputError
                                message={errors.code}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="description"
                                value="Açıklama"
                            />
                            <textarea
                                id="description"
                                name="description"
                                value={data.description}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                onChange={(e) =>
                                    setData("description", e.target.value)
                                }
                                rows={3}
                            />
                            <InputError
                                message={errors.description}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="shipping_company_id"
                                value="Kargo Şirketi"
                            />
                            <select
                                id="shipping_company_id"
                                name="shipping_company_id"
                                value={data.shipping_company_id}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                onChange={(e) =>
                                    setData(
                                        "shipping_company_id",
                                        e.target.value
                                    )
                                }
                                required
                            >
                                <option value="">Kargo şirketi seçin</option>
                                {companies.map((company) => (
                                    <option key={company.id} value={company.id}>
                                        {company.name}
                                    </option>
                                ))}
                            </select>
                            <InputError
                                message={errors.shipping_company_id}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <InputLabel
                                htmlFor="delivery_time"
                                value="Teslimat Süresi"
                            />
                            <TextInput
                                id="delivery_time"
                                type="text"
                                name="delivery_time"
                                value={data.delivery_time}
                                className="mt-1 block w-full"
                                onChange={(e) =>
                                    setData("delivery_time", e.target.value)
                                }
                                placeholder="Örn: 1-3 iş günü"
                            />
                            <InputError
                                message={errors.delivery_time}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <div className="flex items-center">
                                <Checkbox
                                    id="requires_address"
                                    name="requires_address"
                                    checked={data.requires_address}
                                    onChange={(e) =>
                                        setData(
                                            "requires_address",
                                            e.target.checked
                                        )
                                    }
                                />
                                <InputLabel
                                    htmlFor="requires_address"
                                    value="Adres Gerektirir"
                                    className="ml-2"
                                />
                            </div>
                            <p className="mt-1 text-sm text-gray-500">
                                Bu kargo metodu için teslimat adresi gerekli mi?
                            </p>
                            <InputError
                                message={errors.requires_address}
                                className="mt-2"
                            />
                        </div>

                        <div className="mb-4">
                            <div className="flex items-center">
                                <Checkbox
                                    id="is_active"
                                    name="is_active"
                                    checked={data.is_active}
                                    onChange={(e) =>
                                        setData("is_active", e.target.checked)
                                    }
                                />
                                <InputLabel
                                    htmlFor="is_active"
                                    value="Aktif"
                                    className="ml-2"
                                />
                            </div>
                            <InputError
                                message={errors.is_active}
                                className="mt-2"
                            />
                        </div>

                        <div className="flex items-center justify-end mt-4 space-x-2">
                            <Link href={route("admin.shipping.methods.index")}>
                                <SecondaryButton type="button">
                                    İptal
                                </SecondaryButton>
                            </Link>
                            <PrimaryButton type="submit" disabled={processing}>
                                Güncelle
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
}
