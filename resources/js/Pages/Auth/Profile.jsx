import React, { useState } from "react";
import { Head, useForm } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Profile({ user }) {
    const [showPasswordForm, setShowPasswordForm] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        name: user.name,
        email: user.email,
        current_password: "",
        password: "",
        password_confirmation: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        put("/profile", {
            onSuccess: () => {
                if (showPasswordForm) {
                    setShowPasswordForm(false);
                    reset(
                        "current_password",
                        "password",
                        "password_confirmation"
                    );
                }
            },
        });
    };

    return (
        <AdminLayout title="Profil">
            <Head title="Profil" />

            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div className="p-6 bg-white border-b border-gray-200">
                    <h3 className="text-lg font-medium mb-6">
                        Profil Bilgileri
                    </h3>

                    <form onSubmit={handleSubmit} className="max-w-2xl">
                        <div className="mb-4">
                            <label
                                className="block text-gray-700 text-sm font-bold mb-2"
                                htmlFor="name"
                            >
                                Ad Soyad
                            </label>
                            <input
                                id="name"
                                type="text"
                                className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                                    errors.name ? "border-red-500" : ""
                                }`}
                                value={data.name}
                                onChange={(e) =>
                                    setData("name", e.target.value)
                                }
                                required
                            />
                            {errors.name && (
                                <p className="text-red-500 text-xs italic mt-1">
                                    {errors.name}
                                </p>
                            )}
                        </div>

                        <div className="mb-4">
                            <label
                                className="block text-gray-700 text-sm font-bold mb-2"
                                htmlFor="email"
                            >
                                E-posta
                            </label>
                            <input
                                id="email"
                                type="email"
                                className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                                    errors.email ? "border-red-500" : ""
                                }`}
                                value={data.email}
                                onChange={(e) =>
                                    setData("email", e.target.value)
                                }
                                required
                            />
                            {errors.email && (
                                <p className="text-red-500 text-xs italic mt-1">
                                    {errors.email}
                                </p>
                            )}
                        </div>

                        <div className="mb-6">
                            <button
                                type="button"
                                className="text-blue-500 hover:text-blue-700 text-sm font-medium"
                                onClick={() =>
                                    setShowPasswordForm(!showPasswordForm)
                                }
                            >
                                {showPasswordForm
                                    ? "Şifre değişikliğini iptal et"
                                    : "Şifremi değiştir"}
                            </button>
                        </div>

                        {showPasswordForm && (
                            <>
                                <div className="mb-4">
                                    <label
                                        className="block text-gray-700 text-sm font-bold mb-2"
                                        htmlFor="current_password"
                                    >
                                        Mevcut Şifre
                                    </label>
                                    <input
                                        id="current_password"
                                        type="password"
                                        className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                                            errors.current_password
                                                ? "border-red-500"
                                                : ""
                                        }`}
                                        value={data.current_password}
                                        onChange={(e) =>
                                            setData(
                                                "current_password",
                                                e.target.value
                                            )
                                        }
                                        required
                                    />
                                    {errors.current_password && (
                                        <p className="text-red-500 text-xs italic mt-1">
                                            {errors.current_password}
                                        </p>
                                    )}
                                </div>

                                <div className="mb-4">
                                    <label
                                        className="block text-gray-700 text-sm font-bold mb-2"
                                        htmlFor="password"
                                    >
                                        Yeni Şifre
                                    </label>
                                    <input
                                        id="password"
                                        type="password"
                                        className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
                                            errors.password
                                                ? "border-red-500"
                                                : ""
                                        }`}
                                        value={data.password}
                                        onChange={(e) =>
                                            setData("password", e.target.value)
                                        }
                                        required
                                    />
                                    {errors.password && (
                                        <p className="text-red-500 text-xs italic mt-1">
                                            {errors.password}
                                        </p>
                                    )}
                                </div>

                                <div className="mb-6">
                                    <label
                                        className="block text-gray-700 text-sm font-bold mb-2"
                                        htmlFor="password_confirmation"
                                    >
                                        Yeni Şifre Tekrar
                                    </label>
                                    <input
                                        id="password_confirmation"
                                        type="password"
                                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        value={data.password_confirmation}
                                        onChange={(e) =>
                                            setData(
                                                "password_confirmation",
                                                e.target.value
                                            )
                                        }
                                        required
                                    />
                                </div>
                            </>
                        )}

                        <div className="mb-6">
                            <button
                                type="submit"
                                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                disabled={processing}
                            >
                                {processing ? "Kaydediliyor..." : "Kaydet"}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
}
