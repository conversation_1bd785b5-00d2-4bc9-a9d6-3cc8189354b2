import api from "../api";

export const categoryApi = {
    getAll: (params = {}) => api.get("/categories", { params }),
    getById: (id) => api.get(`/categories/${id}`),
    getTree: () => api.get("/categories/tree"),
    create: (data) => api.post("/api/v1/categories", data),
    update: (id, data) => api.put(`/api/v1/categories/${id}`, data),
    delete: (id) => api.delete(`/api/v1/categories/${id}`),
};

export default categoryApi;
