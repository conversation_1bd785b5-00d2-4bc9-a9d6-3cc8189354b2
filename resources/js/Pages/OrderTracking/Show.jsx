import React from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import MainLayout from "@/Layouts/MainLayout";

export default function Show({ order }) {
    // Sipariş durumuna göre renk ve etiket belirle
    const getStatusColor = (status) => {
        switch (status) {
            case "pending":
                return "bg-yellow-100 text-yellow-800";
            case "processing":
                return "bg-blue-100 text-blue-800";
            case "shipped":
                return "bg-purple-100 text-purple-800";
            case "delivered":
                return "bg-green-100 text-green-800";
            case "cancelled":
                return "bg-red-100 text-red-800";
            case "refunded":
                return "bg-gray-100 text-gray-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const getStatusLabel = (status) => {
        switch (status) {
            case "pending":
                return "Beklemede";
            case "processing":
                return "İşleniyor";
            case "shipped":
                return "Kargoya Verildi";
            case "delivered":
                return "Teslim Edildi";
            case "cancelled":
                return "İptal Edildi";
            case "refunded":
                return "İade Edildi";
            default:
                return status;
        }
    };

    // Ödeme durumuna göre renk ve etiket belirle
    const getPaymentStatusColor = (status) => {
        switch (status) {
            case "pending":
                return "bg-yellow-100 text-yellow-800";
            case "paid":
                return "bg-green-100 text-green-800";
            case "failed":
                return "bg-red-100 text-red-800";
            case "refunded":
                return "bg-gray-100 text-gray-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const getPaymentStatusLabel = (status) => {
        switch (status) {
            case "pending":
                return "Beklemede";
            case "paid":
                return "Ödendi";
            case "failed":
                return "Başarısız";
            case "refunded":
                return "İade Edildi";
            default:
                return status;
        }
    };

    // Ödeme yöntemine göre etiket belirle
    const getPaymentMethodLabel = (method) => {
        switch (method) {
            case "bank_transfer":
                return "Havale/EFT";
            case "credit_card":
                return "Kredi Kartı";
            case "iyzico":
                return "iyzico";
            default:
                return method;
        }
    };

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Tarih formatla
    const formatDate = (dateString) => {
        if (!dateString) return "-";
        const date = new Date(dateString);
        return new Intl.DateTimeFormat("tr-TR", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        }).format(date);
    };

    return (
        <MainLayout>
            <Head title={`Sipariş #${order.order_number}`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="mb-6 flex justify-between items-center">
                        <h1 className="text-2xl font-bold">
                            Sipariş #{order.order_number}
                        </h1>
                        <Link
                            href={route("orders.track")}
                            className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded"
                        >
                            Başka Sipariş Ara
                        </Link>
                    </div>

                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6">
                            <div className="flex flex-wrap -mx-3 mb-6">
                                <div className="w-full md:w-1/4 px-3 mb-6 md:mb-0">
                                    <div className="font-bold text-gray-700 mb-2">
                                        Sipariş Durumu
                                    </div>
                                    <span
                                        className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(
                                            order.status
                                        )}`}
                                    >
                                        {getStatusLabel(order.status)}
                                    </span>
                                </div>
                                <div className="w-full md:w-1/4 px-3 mb-6 md:mb-0">
                                    <div className="font-bold text-gray-700 mb-2">
                                        Ödeme Durumu
                                    </div>
                                    <span
                                        className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${getPaymentStatusColor(
                                            order.payment_status
                                        )}`}
                                    >
                                        {getPaymentStatusLabel(
                                            order.payment_status
                                        )}
                                    </span>
                                </div>
                                <div className="w-full md:w-1/4 px-3 mb-6 md:mb-0">
                                    <div className="font-bold text-gray-700 mb-2">
                                        Ödeme Yöntemi
                                    </div>
                                    <div>
                                        {getPaymentMethodLabel(
                                            order.payment_method
                                        )}
                                    </div>
                                </div>
                                <div className="w-full md:w-1/4 px-3 mb-6 md:mb-0">
                                    <div className="font-bold text-gray-700 mb-2">
                                        Sipariş Tarihi
                                    </div>
                                    <div>{formatDate(order.created_at)}</div>
                                </div>
                            </div>

                            {order.tracking_number && (
                                <div className="border-t pt-4 mt-4">
                                    <div className="font-bold text-gray-700 mb-2">
                                        Kargo Bilgileri
                                    </div>
                                    <div className="flex flex-wrap -mx-3">
                                        <div className="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                                            <div className="text-sm text-gray-600">
                                                Kargo Firması
                                            </div>
                                            <div className="font-medium">
                                                {order.shipping_company || "-"}
                                            </div>
                                        </div>
                                        <div className="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                                            <div className="text-sm text-gray-600">
                                                Takip Numarası
                                            </div>
                                            <div className="font-medium">
                                                {order.tracking_number}
                                            </div>
                                        </div>
                                        <div className="w-full md:w-1/3 px-3 mb-6 md:mb-0">
                                            <div className="text-sm text-gray-600">
                                                Kargo Takip
                                            </div>
                                            {order.tracking_url ? (
                                                <a
                                                    href={order.tracking_url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-500 hover:text-blue-700"
                                                >
                                                    Kargo Takip Et
                                                </a>
                                            ) : (
                                                <span className="text-gray-500">
                                                    Takip linki yok
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div className="p-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Fatura Bilgileri
                                </h2>
                                <div className="space-y-2">
                                    <div>
                                        <span className="font-medium">
                                            Ad Soyad:
                                        </span>{" "}
                                        {order.billing_name}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            E-posta:
                                        </span>{" "}
                                        {order.billing_email}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Telefon:
                                        </span>{" "}
                                        {order.billing_phone}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Adres:
                                        </span>{" "}
                                        {order.billing_address}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            İlçe/İl:
                                        </span>{" "}
                                        {order.billing_state}/
                                        {order.billing_city}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Posta Kodu:
                                        </span>{" "}
                                        {order.billing_zipcode}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Ülke:
                                        </span>{" "}
                                        {order.billing_country}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div className="p-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Teslimat Bilgileri
                                </h2>
                                <div className="space-y-2">
                                    <div>
                                        <span className="font-medium">
                                            Ad Soyad:
                                        </span>{" "}
                                        {order.shipping_name}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            E-posta:
                                        </span>{" "}
                                        {order.shipping_email}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Telefon:
                                        </span>{" "}
                                        {order.shipping_phone}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Adres:
                                        </span>{" "}
                                        {order.shipping_address}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            İlçe/İl:
                                        </span>{" "}
                                        {order.shipping_state}/
                                        {order.shipping_city}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Posta Kodu:
                                        </span>{" "}
                                        {order.shipping_zipcode}
                                    </div>
                                    <div>
                                        <span className="font-medium">
                                            Ülke:
                                        </span>{" "}
                                        {order.shipping_country}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6">
                            <h2 className="text-lg font-bold mb-4">
                                Sipariş Öğeleri
                            </h2>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Ürün
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Fiyat
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Adet
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Toplam
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {order.items.map((item) => (
                                            <tr key={item.id}>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        {item.product && (
                                                            <div className="flex-shrink-0 h-10 w-10">
                                                                <img
                                                                    className="h-10 w-10 rounded-full object-cover"
                                                                    src={
                                                                        item
                                                                            .product
                                                                            .image ||
                                                                        "/images/placeholder.png"
                                                                    }
                                                                    alt={
                                                                        item.product_name
                                                                    }
                                                                />
                                                            </div>
                                                        )}
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {
                                                                    item.product_name
                                                                }
                                                            </div>
                                                            {item.options && (
                                                                <div className="text-sm text-gray-500">
                                                                    {Object.entries(
                                                                        item.options
                                                                    ).map(
                                                                        ([
                                                                            key,
                                                                            value,
                                                                        ]) => (
                                                                            <div
                                                                                key={
                                                                                    key
                                                                                }
                                                                            >
                                                                                {
                                                                                    key
                                                                                }

                                                                                :{" "}
                                                                                {
                                                                                    value
                                                                                }
                                                                            </div>
                                                                        )
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {formatPrice(
                                                            parseFloat(
                                                                item.price
                                                            ) || 0
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {item.quantity}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">
                                                        {formatPrice(
                                                            parseFloat(
                                                                item.subtotal
                                                            ) || 0
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div className="p-6">
                            <h2 className="text-lg font-bold mb-4">
                                Sipariş Özeti
                            </h2>
                            <div className="flex flex-col space-y-2">
                                <div className="flex justify-between">
                                    <span>Ara Toplam:</span>
                                    <span>
                                        {formatPrice(
                                            (parseFloat(order.total_amount) ||
                                                0) -
                                                (parseFloat(
                                                    order.shipping_cost
                                                ) || 0) -
                                                (parseFloat(order.tax_amount) ||
                                                    0) +
                                                (parseFloat(
                                                    order.discount_amount
                                                ) || 0)
                                        )}
                                    </span>
                                </div>
                                {parseFloat(order.discount_amount) > 0 && (
                                    <div className="flex justify-between text-green-600">
                                        <span>
                                            İndirim
                                            {order.coupon_code
                                                ? ` (${order.coupon_code})`
                                                : ""}
                                            :
                                        </span>
                                        <span>
                                            -
                                            {formatPrice(
                                                parseFloat(
                                                    order.discount_amount
                                                ) || 0
                                            )}
                                        </span>
                                    </div>
                                )}
                                <div className="flex justify-between">
                                    <span>Kargo:</span>
                                    <span>
                                        {formatPrice(
                                            parseFloat(order.shipping_cost) || 0
                                        )}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span>KDV:</span>
                                    <span>
                                        {formatPrice(
                                            parseFloat(order.tax_amount) || 0
                                        )}
                                    </span>
                                </div>
                                <div className="flex justify-between font-bold border-t pt-2 mt-2">
                                    <span>Toplam:</span>
                                    <span>
                                        {formatPrice(
                                            parseFloat(order.total_amount) || 0
                                        )}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {order.payment_method === "bank_transfer" &&
                        order.bank_account && (
                            <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                                <div className="p-6">
                                    <h2 className="text-lg font-bold mb-4">
                                        Banka Hesap Bilgileri
                                    </h2>
                                    <div className="space-y-2">
                                        <div>
                                            <span className="font-medium">
                                                Banka:
                                            </span>{" "}
                                            {order.bank_account.bank_name}
                                        </div>
                                        <div>
                                            <span className="font-medium">
                                                Hesap Sahibi:
                                            </span>{" "}
                                            {order.bank_account.account_name}
                                        </div>
                                        <div>
                                            <span className="font-medium">
                                                IBAN:
                                            </span>{" "}
                                            {order.bank_account.iban}
                                        </div>
                                        {order.bank_account.description && (
                                            <div>
                                                <span className="font-medium">
                                                    Açıklama:
                                                </span>{" "}
                                                {order.bank_account.description}
                                            </div>
                                        )}
                                        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                                            <p className="text-yellow-800">
                                                <span className="font-bold">
                                                    Önemli:
                                                </span>{" "}
                                                Havale/EFT yaparken açıklama
                                                kısmına sipariş numaranızı (
                                                {order.order_number}) yazmayı
                                                unutmayınız.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                    {order.notes && order.notes.length > 0 && (
                        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div className="p-6">
                                <h2 className="text-lg font-bold mb-4">
                                    Sipariş Notları
                                </h2>
                                <div className="space-y-4">
                                    {order.notes.map((note) => (
                                        <div
                                            key={note.id}
                                            className="border-b pb-3 last:border-b-0 last:pb-0"
                                        >
                                            <div className="text-sm text-gray-500">
                                                {formatDate(note.created_at)}
                                            </div>
                                            <div className="mt-1">
                                                {note.note}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
