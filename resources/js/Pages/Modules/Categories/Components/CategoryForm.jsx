import React, { useState, useEffect } from "react";
import categoryApi from "../../../Services/modules/categoryApi";

const CategoryForm = ({ category, onSubmit, onCancel }) => {
    const [formData, setFormData] = useState({
        name: "",
        description: "",
        parent_id: "",
        status: true,
    });
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        // Kategorileri yükle
        const fetchCategories = async () => {
            try {
                const response = await categoryApi.getAll();
                setCategories(response.data.data);
            } catch (error) {
                console.error("Kategoriler yüklenirken hata oluştu:", error);
            }
        };

        fetchCategories();

        // Eğer kategori varsa form verilerini doldur
        if (category) {
            setFormData({
                name: category.name || "",
                description: category.description || "",
                parent_id: category.parent_id || "",
                status: category.status || true,
            });
        }
    }, [category]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData({
            ...formData,
            [name]: type === "checkbox" ? checked : value,
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setErrors({});

        try {
            await onSubmit(formData);
            // Form başarıyla gönderildi
        } catch (error) {
            if (
                error.response &&
                error.response.data &&
                error.response.data.errors
            ) {
                setErrors(error.response.data.errors);
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700"
                >
                    Kategori Adı
                </label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                        errors.name ? "border-red-500" : ""
                    }`}
                />
                {errors.name && (
                    <p className="mt-1 text-sm text-red-600">
                        {errors.name[0]}
                    </p>
                )}
            </div>

            <div>
                <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700"
                >
                    Açıklama
                </label>
                <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows="3"
                    className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                        errors.description ? "border-red-500" : ""
                    }`}
                ></textarea>
                {errors.description && (
                    <p className="mt-1 text-sm text-red-600">
                        {errors.description[0]}
                    </p>
                )}
            </div>

            <div>
                <label
                    htmlFor="parent_id"
                    className="block text-sm font-medium text-gray-700"
                >
                    Üst Kategori
                </label>
                <select
                    id="parent_id"
                    name="parent_id"
                    value={formData.parent_id}
                    onChange={handleChange}
                    className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                        errors.parent_id ? "border-red-500" : ""
                    }`}
                >
                    <option value="">Üst Kategori Yok</option>
                    {categories
                        .filter((c) => c.id !== (category ? category.id : null)) // Kendisini üst kategori olarak seçmeyi engelle
                        .map((c) => (
                            <option key={c.id} value={c.id}>
                                {c.name}
                            </option>
                        ))}
                </select>
                {errors.parent_id && (
                    <p className="mt-1 text-sm text-red-600">
                        {errors.parent_id[0]}
                    </p>
                )}
            </div>

            <div className="flex items-center">
                <input
                    type="checkbox"
                    id="status"
                    name="status"
                    checked={formData.status}
                    onChange={handleChange}
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <label
                    htmlFor="status"
                    className="ml-2 block text-sm text-gray-700"
                >
                    Aktif
                </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
                <button
                    type="button"
                    onClick={onCancel}
                    className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                    İptal
                </button>
                <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
                >
                    {loading
                        ? "Kaydediliyor..."
                        : category
                        ? "Güncelle"
                        : "Kaydet"}
                </button>
            </div>
        </form>
    );
};

export default CategoryForm;
