import React from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import CustomerLayout from "@/Layouts/CustomerLayout";

export default function Index({ auth, stats }) {
    return (
        <CustomerLayout>
            <Head title="Müşteri Paneli" />

            <div className="py-6">
                <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <h2 className="mb-6 text-2xl font-semibold text-gray-900">
                        <PERSON><PERSON> Geldiniz, {auth.user.name}
                    </h2>

                    {/* İstatistikler */}
                    <div className="grid grid-cols-1 gap-6 mb-8 md:grid-cols-4">
                        <div className="overflow-hidden bg-white rounded-lg shadow-sm">
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 bg-blue-500 rounded-md">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="flex-1 w-0 ml-5">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Toplam Sipariş
                                            </dt>
                                            <dd>
                                                <div className="text-lg font-medium text-gray-900">
                                                    {stats?.orderCount || 0}
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="overflow-hidden bg-white rounded-lg shadow-sm">
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 bg-green-500 rounded-md">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="flex-1 w-0 ml-5">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Tamamlanan Siparişler
                                            </dt>
                                            <dd>
                                                <div className="text-lg font-medium text-gray-900">
                                                    {stats?.completedOrderCount ||
                                                        0}
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="overflow-hidden bg-white rounded-lg shadow-sm">
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 bg-yellow-500 rounded-md">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                            />
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="flex-1 w-0 ml-5">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                İncelenen Ürünler
                                            </dt>
                                            <dd>
                                                <div className="text-lg font-medium text-gray-900">
                                                    {stats?.viewedProductCount ||
                                                        0}
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="overflow-hidden bg-white rounded-lg shadow-sm">
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 bg-purple-500 rounded-md">
                                        <svg
                                            className="w-6 h-6 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="flex-1 w-0 ml-5">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Favori Ürünler
                                            </dt>
                                            <dd>
                                                <div className="text-lg font-medium text-gray-900">
                                                    {stats?.favoriteCount || 0}
                                                </div>
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Hızlı Erişim */}
                    <h3 className="mb-4 text-lg font-medium text-gray-900">
                        Hızlı Erişim
                    </h3>
                    <div className="grid grid-cols-1 gap-6 mb-8 md:grid-cols-3">
                        <Link
                            href={route("customer.orders")}
                            className="overflow-hidden transition bg-white rounded-lg shadow-sm hover:bg-gray-50"
                        >
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 text-blue-600 bg-blue-100 rounded-md">
                                        <svg
                                            className="w-6 h-6"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="ml-4">
                                        <h4 className="text-lg font-medium text-gray-900">
                                            Siparişlerim
                                        </h4>
                                        <p className="text-sm text-gray-500">
                                            Tüm siparişlerinizi görüntüleyin ve
                                            takip edin
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </Link>

                        <Link
                            href={route("customer.profile")}
                            className="overflow-hidden transition bg-white rounded-lg shadow-sm hover:bg-gray-50"
                        >
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 text-green-600 bg-green-100 rounded-md">
                                        <svg
                                            className="w-6 h-6"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="ml-4">
                                        <h4 className="text-lg font-medium text-gray-900">
                                            Profilim
                                        </h4>
                                        <p className="text-sm text-gray-500">
                                            Kişisel bilgilerinizi güncelleyin
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </Link>

                        <Link
                            href={route("customer.addresses")}
                            className="overflow-hidden transition bg-white rounded-lg shadow-sm hover:bg-gray-50"
                        >
                            <div className="p-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 p-3 text-yellow-600 bg-yellow-100 rounded-md">
                                        <svg
                                            className="w-6 h-6"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                            />
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                            />
                                        </svg>
                                    </div>
                                    <div className="ml-4">
                                        <h4 className="text-lg font-medium text-gray-900">
                                            Adreslerim
                                        </h4>
                                        <p className="text-sm text-gray-500">
                                            Teslimat adreslerinizi yönetin
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </Link>
                    </div>

                    {/* Son Siparişler */}
                    <h3 className="mb-4 text-lg font-medium text-gray-900">
                        Son Siparişlerim
                    </h3>
                    <div className="mb-8 overflow-hidden bg-white rounded-lg shadow-sm">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                                        >
                                            Sipariş No
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                                        >
                                            Tarih
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                                        >
                                            Tutar
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                                        >
                                            Durum
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
                                        >
                                            İşlem
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {stats?.recentOrders &&
                                    stats.recentOrders.length > 0 ? (
                                        stats.recentOrders.map((order) => (
                                            <tr key={order.id}>
                                                <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                                                    #{order.order_number}
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                                    {new Date(
                                                        order.created_at
                                                    ).toLocaleDateString(
                                                        "tr-TR"
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                                    {order.total_amount} TL
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span
                                                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                            order.status ===
                                                            "completed"
                                                                ? "bg-green-100 text-green-800"
                                                                : order.status ===
                                                                  "processing"
                                                                ? "bg-blue-100 text-blue-800"
                                                                : order.status ===
                                                                  "pending"
                                                                ? "bg-yellow-100 text-yellow-800"
                                                                : "bg-red-100 text-red-800"
                                                        }`}
                                                    >
                                                        {order.status ===
                                                        "completed"
                                                            ? "Tamamlandı"
                                                            : order.status ===
                                                              "processing"
                                                            ? "İşleniyor"
                                                            : order.status ===
                                                              "pending"
                                                            ? "Beklemede"
                                                            : "İptal Edildi"}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                                                    <Link
                                                        href={route(
                                                            "customer.orders.show",
                                                            order.order_number
                                                        )}
                                                        className="text-blue-600 hover:text-blue-900"
                                                    >
                                                        Detaylar
                                                    </Link>
                                                </td>
                                            </tr>
                                        ))
                                    ) : (
                                        <tr>
                                            <td
                                                colSpan="5"
                                                className="px-6 py-4 text-sm text-center text-gray-500"
                                            >
                                                Henüz sipariş bulunmamaktadır.
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                        <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
                            <Link
                                href={route("customer.orders")}
                                className="text-sm font-medium text-blue-600 hover:text-blue-500"
                            >
                                Tüm siparişleri görüntüle →
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </CustomerLayout>
    );
}
