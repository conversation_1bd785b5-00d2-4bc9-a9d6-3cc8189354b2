import React from "react";
import { Head, <PERSON> } from "@inertiajs/react";
import CustomerLayout from "@/Layouts/CustomerLayout";
import { formatPrice } from "@/Utils/cartUtils";

export default function Show({ order }) {
    // Sipariş durumuna göre renk ve metin belirle
    const getStatusInfo = (status) => {
        switch (status) {
            case "completed":
                return {
                    color: "bg-green-100 text-green-800",
                    text: "Tamamlandı",
                };
            case "processing":
                return {
                    color: "bg-blue-100 text-blue-800",
                    text: "İşleniyor",
                };
            case "pending":
                return {
                    color: "bg-yellow-100 text-yellow-800",
                    text: "Beklemede",
                };
            case "cancelled":
                return {
                    color: "bg-red-100 text-red-800",
                    text: "İptal Edildi",
                };
            default:
                return {
                    color: "bg-gray-100 text-gray-800",
                    text: status,
                };
        }
    };

    // Ödeme durumuna göre renk ve metin belirle
    const getPaymentStatusInfo = (status) => {
        switch (status) {
            case "paid":
                return {
                    color: "bg-green-100 text-green-800",
                    text: "Ödendi",
                };
            case "pending":
                return {
                    color: "bg-yellow-100 text-yellow-800",
                    text: "Beklemede",
                };
            case "failed":
                return {
                    color: "bg-red-100 text-red-800",
                    text: "Başarısız",
                };
            case "refunded":
                return {
                    color: "bg-purple-100 text-purple-800",
                    text: "İade Edildi",
                };
            default:
                return {
                    color: "bg-gray-100 text-gray-800",
                    text: status,
                };
        }
    };

    const statusInfo = getStatusInfo(order.status);
    const paymentStatusInfo = getPaymentStatusInfo(order.payment_status);

    return (
        <CustomerLayout>
            <Head title={`Sipariş #${order.order_number}`} />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-semibold text-gray-900">
                            Sipariş #{order.order_number}
                        </h2>
                        <Link
                            href={route("customer.orders")}
                            className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300"
                        >
                            Siparişlere Dön
                        </Link>
                    </div>

                    {/* Sipariş Özeti */}
                    <div className="bg-white overflow-hidden shadow-sm rounded-lg mb-6">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Sipariş Bilgileri
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Sipariş Tarihi:
                                        </span>{" "}
                                        {new Date(
                                            order.created_at
                                        ).toLocaleDateString("tr-TR")}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Durum:
                                        </span>
                                        <span
                                            className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusInfo.color}`}
                                        >
                                            {statusInfo.text}
                                        </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Ödeme Yöntemi:
                                        </span>{" "}
                                        {order.payment_method === "credit_card"
                                            ? "Kredi Kartı"
                                            : order.payment_method ===
                                              "bank_transfer"
                                            ? "Havale/EFT"
                                            : order.payment_method ||
                                              "Belirtilmemiş"}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Ödeme Durumu:
                                        </span>
                                        <span
                                            className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${paymentStatusInfo.color}`}
                                        >
                                            {paymentStatusInfo.text}
                                        </span>
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Ara Toplam:
                                        </span>{" "}
                                        {formatPrice(order.subtotal || 0)}
                                    </p>
                                    {order.discount_amount > 0 && (
                                        <p className="text-sm text-green-600">
                                            <span className="font-medium">
                                                İndirim:
                                            </span>{" "}
                                            -
                                            {formatPrice(order.discount_amount)}
                                        </p>
                                    )}
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Kargo Ücreti:
                                        </span>{" "}
                                        {formatPrice(order.shipping_cost || 0)}
                                    </p>
                                    <p className="text-sm font-bold text-gray-800">
                                        <span className="font-medium">
                                            Toplam:
                                        </span>{" "}
                                        {formatPrice(order.total_amount || 0)}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {order.payment_method === "bank_transfer" &&
                        order.payment_status === "pending" && (
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                <h3 className="font-medium text-yellow-800 mb-2">
                                    Ödeme Bilgileri
                                </h3>
                                <p className="text-yellow-800 text-sm mb-2">
                                    Siparişinizi tamamlamak için aşağıdaki banka
                                    hesabına ödeme yapmanız gerekmektedir. Ödeme
                                    açıklamasına sipariş numaranızı (
                                    {order.order_number}) yazmayı unutmayın.
                                </p>

                                {order.bank_account && (
                                    <div className="text-sm">
                                        <div>
                                            <strong>Banka:</strong>{" "}
                                            {order.bank_account.bank_name}
                                        </div>
                                        <div>
                                            <strong>Hesap Sahibi:</strong>{" "}
                                            {order.bank_account.account_name}
                                        </div>
                                        <div>
                                            <strong>IBAN:</strong>{" "}
                                            {order.bank_account.iban}
                                        </div>
                                        {order.bank_account.description && (
                                            <div className="mt-2 text-yellow-700">
                                                {order.bank_account.description}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}

                    {/* Kargo Bilgileri */}
                    {order.tracking_number && (
                        <div className="bg-blue-50 overflow-hidden shadow-sm rounded-lg mb-6">
                            <div className="p-6">
                                <h3 className="text-lg font-medium text-blue-800 mb-4">
                                    Kargo Bilgileri
                                </h3>
                                <div className="space-y-2 mb-4">
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Kargo Takip Numarası:
                                        </span>{" "}
                                        {order.tracking_number}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Kargo Şirketi:
                                        </span>{" "}
                                        {order.shipping_company_relation
                                            ?.name ||
                                            (order.shipping_company ===
                                            "yurtici"
                                                ? "Yurtiçi Kargo"
                                                : order.shipping_company ===
                                                  "aras"
                                                ? "Aras Kargo"
                                                : order.shipping_company ===
                                                  "mng"
                                                ? "MNG Kargo"
                                                : order.shipping_company ===
                                                  "ptt"
                                                ? "PTT Kargo"
                                                : order.shipping_company ===
                                                  "ups"
                                                ? "UPS"
                                                : order.shipping_company ===
                                                  "dhl"
                                                ? "DHL"
                                                : order.shipping_company ===
                                                  "fedex"
                                                ? "FedEx"
                                                : order.shipping_company ||
                                                  "Belirtilmemiş")}
                                    </p>
                                    {order.shipping_date && (
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">
                                                Kargoya Verilme Tarihi:
                                            </span>{" "}
                                            {new Date(
                                                order.shipping_date
                                            ).toLocaleDateString("tr-TR")}
                                        </p>
                                    )}
                                    {order.estimated_delivery_date && (
                                        <p className="text-sm text-gray-600">
                                            <span className="font-medium">
                                                Tahmini Teslimat Tarihi:
                                            </span>{" "}
                                            {new Date(
                                                order.estimated_delivery_date
                                            ).toLocaleDateString("tr-TR")}
                                        </p>
                                    )}
                                </div>
                                {order.tracking_url && (
                                    <div className="mt-4">
                                        <a
                                            href={order.tracking_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700"
                                        >
                                            Kargo Takip Sayfasına Git
                                        </a>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Teslimat Bilgileri */}
                    <div className="bg-white overflow-hidden shadow-sm rounded-lg mb-6">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Teslimat Bilgileri
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Ad Soyad:
                                        </span>{" "}
                                        {order.shipping_name || "Belirtilmemiş"}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Telefon:
                                        </span>{" "}
                                        {order.shipping_phone ||
                                            "Belirtilmemiş"}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            E-posta:
                                        </span>{" "}
                                        {order.shipping_email ||
                                            "Belirtilmemiş"}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Adres:
                                        </span>{" "}
                                        {order.shipping_address ||
                                            "Belirtilmemiş"}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            İlçe/İl:
                                        </span>{" "}
                                        {order.shipping_city
                                            ? `${
                                                  order.shipping_district || ""
                                              }, ${order.shipping_city}`
                                            : "Belirtilmemiş"}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Posta Kodu:
                                        </span>{" "}
                                        {order.shipping_zip || "Belirtilmemiş"}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Sipariş Ürünleri */}
                    <div className="bg-white overflow-hidden shadow-sm rounded-lg">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">
                                Sipariş Ürünleri
                            </h3>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Ürün
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Fiyat
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Adet
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Toplam
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {order.items &&
                                        order.items.length > 0 ? (
                                            order.items.map((item) => (
                                                <tr key={item.id}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className="flex-shrink-0 h-10 w-10">
                                                                <img
                                                                    className="h-10 w-10 rounded-full object-cover"
                                                                    src={
                                                                        item
                                                                            .product
                                                                            ?.image ||
                                                                        "/images/placeholder.png"
                                                                    }
                                                                    alt={
                                                                        item
                                                                            .product
                                                                            ?.name ||
                                                                        "Ürün"
                                                                    }
                                                                />
                                                            </div>
                                                            <div className="ml-4">
                                                                <div className="text-sm font-medium text-gray-900">
                                                                    {item
                                                                        .product
                                                                        ?.name ||
                                                                        "Ürün adı bulunamadı"}
                                                                </div>
                                                                {item.options &&
                                                                    Object.keys(
                                                                        item.options
                                                                    ).length >
                                                                        0 && (
                                                                        <div className="text-sm text-gray-500">
                                                                            {Object.entries(
                                                                                item.options
                                                                            ).map(
                                                                                ([
                                                                                    key,
                                                                                    value,
                                                                                ]) => (
                                                                                    <span
                                                                                        key={
                                                                                            key
                                                                                        }
                                                                                        className="mr-2"
                                                                                    >
                                                                                        {
                                                                                            key
                                                                                        }

                                                                                        :{" "}
                                                                                        {
                                                                                            value
                                                                                        }
                                                                                    </span>
                                                                                )
                                                                            )}
                                                                        </div>
                                                                    )}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                        {formatPrice(
                                                            item.price
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                        {item.quantity}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                                        {formatPrice(
                                                            item.price *
                                                                item.quantity
                                                        )}
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td
                                                    colSpan="4"
                                                    className="px-6 py-4 text-center text-sm text-gray-500"
                                                >
                                                    Bu siparişte ürün
                                                    bulunmamaktadır.
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                    <tfoot className="bg-gray-50">
                                        <tr>
                                            <td
                                                colSpan="3"
                                                className="px-6 py-4 text-right text-sm font-medium text-gray-500"
                                            >
                                                Ara Toplam:
                                            </td>
                                            <td className="px-6 py-4 text-right text-sm text-gray-900">
                                                {formatPrice(
                                                    order.subtotal || 0
                                                )}
                                            </td>
                                        </tr>
                                        {order.discount_amount > 0 && (
                                            <tr>
                                                <td
                                                    colSpan="3"
                                                    className="px-6 py-4 text-right text-sm font-medium text-green-600"
                                                >
                                                    İndirim:
                                                </td>
                                                <td className="px-6 py-4 text-right text-sm text-green-600">
                                                    -
                                                    {formatPrice(
                                                        order.discount_amount
                                                    )}
                                                </td>
                                            </tr>
                                        )}
                                        <tr>
                                            <td
                                                colSpan="3"
                                                className="px-6 py-4 text-right text-sm font-medium text-gray-500"
                                            >
                                                Kargo Ücreti:
                                            </td>
                                            <td className="px-6 py-4 text-right text-sm text-gray-900">
                                                {formatPrice(
                                                    order.shipping_cost || 0
                                                )}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                colSpan="3"
                                                className="px-6 py-4 text-right text-sm font-bold text-gray-900"
                                            >
                                                Toplam:
                                            </td>
                                            <td className="px-6 py-4 text-right text-sm font-bold text-gray-900">
                                                {formatPrice(
                                                    order.total_amount || 0
                                                )}
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </CustomerLayout>
    );
}
