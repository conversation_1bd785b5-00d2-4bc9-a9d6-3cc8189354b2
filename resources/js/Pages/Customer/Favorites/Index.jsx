import React, { useState } from "react";
import { Head, Link, router } from "@inertiajs/react";
import CustomerLayout from "@/Layouts/CustomerLayout";
import { toast } from "react-hot-toast";

export default function Index({ favorites }) {
    const [loading, setLoading] = useState(false);

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Favorilerden kaldır
    const removeFavorite = (id) => {
        setLoading(true);
        router.delete(route("customer.favorites.destroy", id), {
            preserveScroll: true,
            onSuccess: () => {
                toast.success("Ürün favorilerden kaldırıldı");
                setLoading(false);
            },
            onError: () => {
                toast.error("Bir hata oluştu");
                setLoading(false);
            },
        });
    };

    // Sepete ekle
    const addToCart = (productId) => {
        setLoading(true);
        router.post(
            route("cart.add"),
            {
                product_id: productId,
                quantity: 1,
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Ürün sepete eklendi");
                    setLoading(false);
                },
                onError: () => {
                    toast.error("Ürün sepete eklenirken bir hata oluştu");
                    setLoading(false);
                },
            }
        );
    };

    return (
        <CustomerLayout>
            <Head title="Favorilerim" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        Favorilerim
                    </h2>

                    {favorites.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {favorites.map((favorite) => (
                                <div
                                    key={favorite.id}
                                    className="bg-white overflow-hidden shadow-sm rounded-lg"
                                >
                                    <div className="relative">
                                        <Link
                                            href={route(
                                                "products.show",
                                                favorite.product.slug
                                            )}
                                        >
                                            <img
                                                src={
                                                    favorite.product.image ||
                                                    "/images/placeholder.png"
                                                }
                                                alt={favorite.product.name}
                                                className="w-full h-48 object-cover"
                                            />
                                        </Link>
                                        <button
                                            onClick={() =>
                                                removeFavorite(favorite.id)
                                            }
                                            disabled={loading}
                                            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow hover:bg-gray-100"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                className="h-6 w-6 text-red-500"
                                                fill="currentColor"
                                                viewBox="0 0 24 24"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                    <div className="p-4">
                                        <Link
                                            href={route(
                                                "products.show",
                                                favorite.product.slug
                                            )}
                                            className="text-lg font-medium text-gray-900 hover:text-blue-600"
                                        >
                                            {favorite.product.name}
                                        </Link>
                                        <p className="mt-2 text-gray-600 text-sm line-clamp-2">
                                            {favorite.product
                                                .short_description ||
                                                "Ürün açıklaması bulunmamaktadır."}
                                        </p>
                                        <div className="mt-4 flex justify-between items-center">
                                            <span className="text-lg font-bold text-gray-900">
                                                {formatPrice(
                                                    favorite.product.price
                                                )}
                                            </span>
                                            <button
                                                onClick={() =>
                                                    addToCart(
                                                        favorite.product.id
                                                    )
                                                }
                                                disabled={
                                                    loading ||
                                                    !favorite.product.in_stock
                                                }
                                                className={`px-3 py-1 rounded text-sm font-medium ${
                                                    favorite.product.in_stock
                                                        ? "bg-blue-500 text-white hover:bg-blue-600"
                                                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                                }`}
                                            >
                                                {favorite.product.in_stock
                                                    ? "Sepete Ekle"
                                                    : "Stokta Yok"}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="bg-white overflow-hidden shadow-sm rounded-lg">
                            <div className="p-6 text-center">
                                <p className="text-gray-500 mb-4">
                                    Henüz favori ürününüz bulunmamaktadır.
                                </p>
                                <Link
                                    href={route("products.index")}
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Ürünlere Göz At
                                </Link>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </CustomerLayout>
    );
}
