import React from 'react';
import { Head } from '@inertiajs/react';

const Dashboard = () => {
    return (
        <>
            <Head title="Dashboard" />
            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 bg-white border-b border-gray-200">
                            <h1 className="text-2xl font-bold mb-6">Modüler E-Ticaret Sistemi</h1>
                            <p className="mb-4">Hoş geldiniz! Bu modüler e-ticaret sisteminde aşağıdaki modüllere erişebilirsiniz:</p>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                                <a href="/admin/products" className="block p-6 bg-indigo-50 rounded-lg border border-indigo-200 hover:bg-indigo-100 transition">
                                    <h2 className="text-xl font-semibold text-indigo-700"><PERSON>rünler</h2>
                                    <p className="text-indigo-600 mt-2">Ürün yönetimi, ekleme, düzenleme ve silme işlemleri</p>
                                </a>
                                
                                <a href="/admin/categories" className="block p-6 bg-green-50 rounded-lg border border-green-200 hover:bg-green-100 transition">
                                    <h2 className="text-xl font-semibold text-green-700">Kategoriler</h2>
                                    <p className="text-green-600 mt-2">Kategori yönetimi, ekleme, düzenleme ve silme işlemleri</p>
                                </a>
                                
                                <div className="block p-6 bg-gray-50 rounded-lg border border-gray-200">
                                    <h2 className="text-xl font-semibold text-gray-700">Siparişler</h2>
                                    <p className="text-gray-600 mt-2">Yakında eklenecek...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Dashboard;
