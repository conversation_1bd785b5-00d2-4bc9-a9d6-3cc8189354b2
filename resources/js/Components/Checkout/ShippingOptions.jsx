import React, { useState, useEffect } from "react";
import axios from "axios";
import { RadioGroup } from "@headlessui/react";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

export default function ShippingOptions({
    address,
    cartTotal,
    onShippingMethodSelected,
    selectedShippingMethod,
}) {
    const [loading, setLoading] = useState(true);
    const [shippingOptions, setShippingOptions] = useState([]);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (!address || !address.country_id) {
            setLoading(false);
            setError("Lütfen önce adres bilgilerini girin.");
            return;
        }

        setLoading(true);
        setError(null);

        console.log("Kargo seçenekleri için adres bilgileri:", address);
        console.log("Sepet toplamı:", cartTotal);

        // Kargo seçeneklerini yükle
        axios
            .post("/api/shipping/available-methods", {
                country_id: address.country_id,
                state_id: address.state_id,
                city_id: address.city_id,
                postal_code: address.postal_code,
                cart_total: cartTotal,
                cart_desi: window.cartDesi || 0, // Sepet desi değerini al
            })
            .then((response) => {
                console.log("Kargo API yanıtı:", response.data);

                if (response.data.success) {
                    setShippingOptions(response.data.methods);

                    // Eğer daha önce seçilmiş bir kargo metodu yoksa ve seçenekler varsa
                    // ilk kargo metodunu otomatik olarak seç
                    if (
                        !selectedShippingMethod &&
                        response.data.methods.length > 0 &&
                        response.data.methods[0].methods.length > 0
                    ) {
                        onShippingMethodSelected(
                            response.data.methods[0].methods[0]
                        );
                    }
                } else {
                    setError("Kargo seçenekleri yüklenirken bir hata oluştu.");
                }
                setLoading(false);
            })
            .catch((error) => {
                console.error("Kargo API hatası:", error);
                setError("Kargo seçenekleri yüklenirken bir hata oluştu.");
                setLoading(false);
            });
    }, [address, cartTotal]);

    if (loading) {
        return (
            <div className="mt-4 p-4 border rounded-md">
                <div className="animate-pulse flex space-x-4">
                    <div className="flex-1 space-y-4 py-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded"></div>
                            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="mt-4 p-4 border rounded-md bg-red-50 text-red-600">
                {error}
            </div>
        );
    }

    if (shippingOptions.length === 0) {
        return (
            <div className="mt-4 p-4 border rounded-md bg-yellow-50 text-yellow-600">
                Seçilen adres için uygun kargo seçeneği bulunamadı.
            </div>
        );
    }

    return (
        <div className="mt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
                Kargo Seçenekleri
            </h3>

            {shippingOptions.map((company) => (
                <div key={company.company.id} className="mb-6">
                    <div className="flex items-center mb-2">
                        {company.company.logo ? (
                            <img
                                src={company.company.logo}
                                alt={company.company.name}
                                className="h-8 mr-2"
                            />
                        ) : (
                            <span className="font-medium text-gray-800 mr-2">
                                {company.company.name}
                            </span>
                        )}
                    </div>

                    <RadioGroup
                        value={selectedShippingMethod}
                        onChange={onShippingMethodSelected}
                        className="space-y-2"
                    >
                        {company.methods.map((method) => (
                            <RadioGroup.Option
                                key={method.id}
                                value={method}
                                className={({ active, checked }) =>
                                    `${
                                        active
                                            ? "ring-2 ring-indigo-500 ring-offset-2"
                                            : ""
                                    }
                  ${
                      checked
                          ? "bg-indigo-50 border-indigo-200 z-10"
                          : "border-gray-200"
                  }
                  relative rounded-lg border p-4 cursor-pointer flex focus:outline-none`
                                }
                            >
                                {({ active, checked }) => (
                                    <>
                                        <div className="flex items-center justify-between w-full">
                                            <div className="flex items-center">
                                                <div className="text-sm">
                                                    <RadioGroup.Label
                                                        as="p"
                                                        className={`font-medium ${
                                                            checked
                                                                ? "text-indigo-900"
                                                                : "text-gray-900"
                                                        }`}
                                                    >
                                                        {method.name}
                                                    </RadioGroup.Label>
                                                    <RadioGroup.Description
                                                        as="span"
                                                        className={`inline ${
                                                            checked
                                                                ? "text-indigo-700"
                                                                : "text-gray-500"
                                                        }`}
                                                    >
                                                        <span>
                                                            {method.description}
                                                        </span>
                                                        {method.delivery_time && (
                                                            <span className="block mt-1">
                                                                Tahmini
                                                                Teslimat:{" "}
                                                                {
                                                                    method.delivery_time
                                                                }
                                                            </span>
                                                        )}
                                                    </RadioGroup.Description>
                                                </div>
                                            </div>
                                            <div className="flex items-center">
                                                <span
                                                    className={`text-sm font-medium ${
                                                        method.is_free
                                                            ? "text-green-600"
                                                            : "text-gray-900"
                                                    }`}
                                                >
                                                    {method.is_free
                                                        ? "Ücretsiz"
                                                        : method.formatted_cost}
                                                </span>
                                                {checked && (
                                                    <CheckCircleIcon
                                                        className="h-5 w-5 text-indigo-600 ml-2"
                                                        aria-hidden="true"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </RadioGroup.Option>
                        ))}
                    </RadioGroup>
                </div>
            ))}
        </div>
    );
}
