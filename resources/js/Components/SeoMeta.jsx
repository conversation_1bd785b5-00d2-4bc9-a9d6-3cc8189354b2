import React, { useEffect } from "react";
import { usePage } from "@inertiajs/react";

// Meta etiketlerini doğrudan document.head'e ekleyen fonksiyon
const updateMetaTags = (meta) => {
    if (!meta) return;
    
    try {
        // Önceki meta etiketlerini temizle
        const existingMetaTags = document.querySelectorAll('meta[data-seo-meta="true"]');
        existingMetaTags.forEach(tag => tag.remove());
        
        const existingLinkTags = document.querySelectorAll('link[data-seo-meta="true"]');
        existingLinkTags.forEach(tag => tag.remove());
        
        const existingScriptTags = document.querySelectorAll('script[data-seo-meta="true"]');
        existingScriptTags.forEach(tag => tag.remove());
        
        // Başlığı güncelle
        if (meta.title) {
            document.title = meta.title;
        }
        
        // Temel meta etiketlerini ekle
        if (meta.description) {
            const descriptionTag = document.createElement('meta');
            descriptionTag.setAttribute('name', 'description');
            descriptionTag.setAttribute('content', meta.description);
            descriptionTag.setAttribute('data-seo-meta', 'true');
            document.head.appendChild(descriptionTag);
        }
        
        if (meta.keywords) {
            const keywordsTag = document.createElement('meta');
            keywordsTag.setAttribute('name', 'keywords');
            keywordsTag.setAttribute('content', meta.keywords);
            keywordsTag.setAttribute('data-seo-meta', 'true');
            document.head.appendChild(keywordsTag);
        }
        
        if (meta.robots) {
            const robotsTag = document.createElement('meta');
            robotsTag.setAttribute('name', 'robots');
            robotsTag.setAttribute('content', meta.robots);
            robotsTag.setAttribute('data-seo-meta', 'true');
            document.head.appendChild(robotsTag);
        }
        
        if (meta.canonical) {
            const canonicalTag = document.createElement('link');
            canonicalTag.setAttribute('rel', 'canonical');
            canonicalTag.setAttribute('href', meta.canonical);
            canonicalTag.setAttribute('data-seo-meta', 'true');
            document.head.appendChild(canonicalTag);
        }
        
        // Open Graph meta etiketlerini ekle
        if (meta.og) {
            if (meta.og.title) {
                const ogTitleTag = document.createElement('meta');
                ogTitleTag.setAttribute('property', 'og:title');
                ogTitleTag.setAttribute('content', meta.og.title);
                ogTitleTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(ogTitleTag);
            }
            
            if (meta.og.description) {
                const ogDescTag = document.createElement('meta');
                ogDescTag.setAttribute('property', 'og:description');
                ogDescTag.setAttribute('content', meta.og.description);
                ogDescTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(ogDescTag);
            }
            
            if (meta.og.type) {
                const ogTypeTag = document.createElement('meta');
                ogTypeTag.setAttribute('property', 'og:type');
                ogTypeTag.setAttribute('content', meta.og.type);
                ogTypeTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(ogTypeTag);
            }
            
            if (meta.og.url) {
                const ogUrlTag = document.createElement('meta');
                ogUrlTag.setAttribute('property', 'og:url');
                ogUrlTag.setAttribute('content', meta.og.url);
                ogUrlTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(ogUrlTag);
            }
            
            if (meta.og.image) {
                const ogImageTag = document.createElement('meta');
                ogImageTag.setAttribute('property', 'og:image');
                ogImageTag.setAttribute('content', meta.og.image);
                ogImageTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(ogImageTag);
            }
            
            if (meta.og.site_name) {
                const ogSiteNameTag = document.createElement('meta');
                ogSiteNameTag.setAttribute('property', 'og:site_name');
                ogSiteNameTag.setAttribute('content', meta.og.site_name);
                ogSiteNameTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(ogSiteNameTag);
            }
        }
        
        // Twitter Card meta etiketlerini ekle
        if (meta.twitter) {
            if (meta.twitter.card) {
                const twitterCardTag = document.createElement('meta');
                twitterCardTag.setAttribute('name', 'twitter:card');
                twitterCardTag.setAttribute('content', meta.twitter.card);
                twitterCardTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(twitterCardTag);
            }
            
            if (meta.twitter.title) {
                const twitterTitleTag = document.createElement('meta');
                twitterTitleTag.setAttribute('name', 'twitter:title');
                twitterTitleTag.setAttribute('content', meta.twitter.title);
                twitterTitleTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(twitterTitleTag);
            }
            
            if (meta.twitter.description) {
                const twitterDescTag = document.createElement('meta');
                twitterDescTag.setAttribute('name', 'twitter:description');
                twitterDescTag.setAttribute('content', meta.twitter.description);
                twitterDescTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(twitterDescTag);
            }
            
            if (meta.twitter.image) {
                const twitterImageTag = document.createElement('meta');
                twitterImageTag.setAttribute('name', 'twitter:image');
                twitterImageTag.setAttribute('content', meta.twitter.image);
                twitterImageTag.setAttribute('data-seo-meta', 'true');
                document.head.appendChild(twitterImageTag);
            }
        }
        
        // JSON-LD şemasını ekle
        if (meta.schema) {
            try {
                // Şemayı güvenli bir şekilde JSON'a dönüştür
                let schemaString;
                
                try {
                    // Önce JSON.stringify ile dene
                    schemaString = JSON.stringify(meta.schema);
                } catch (jsonError) {
                    console.error('Error stringifying schema with JSON.stringify:', jsonError);
                    
                    // Hata durumunda manuel olarak dönüştür
                    schemaString = JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "WebSite",
                        "name": document.title,
                        "url": window.location.href
                    });
                }
                
                const schemaScript = document.createElement('script');
                schemaScript.setAttribute('type', 'application/ld+json');
                schemaScript.setAttribute('data-seo-meta', 'true');
                schemaScript.textContent = schemaString;
                document.head.appendChild(schemaScript);
            } catch (error) {
                console.error('Error adding schema script:', error);
            }
        }
    } catch (error) {
        console.error('Error updating meta tags:', error);
    }
};

export default function SeoMeta({ meta }) {
    const { component } = usePage();
    
    useEffect(() => {
        if (meta) {
            updateMetaTags(meta);
        }
        
        // Temizleme fonksiyonu
        return () => {
            const existingMetaTags = document.querySelectorAll('meta[data-seo-meta="true"]');
            existingMetaTags.forEach(tag => tag.remove());
            
            const existingLinkTags = document.querySelectorAll('link[data-seo-meta="true"]');
            existingLinkTags.forEach(tag => tag.remove());
            
            const existingScriptTags = document.querySelectorAll('script[data-seo-meta="true"]');
            existingScriptTags.forEach(tag => tag.remove());
        };
    }, [meta, component]);
    
    // Bu bileşen artık DOM'a hiçbir şey render etmiyor
    return null;
}
