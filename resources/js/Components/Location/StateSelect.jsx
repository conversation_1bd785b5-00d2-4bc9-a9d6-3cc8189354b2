import React, { useState, useEffect } from "react";
import axios from "axios";

export default function StateSelect({
    countryId,
    value,
    onChange,
    className = "",
    required = false,
    disabled = false,
    id = "state_select",
}) {
    const [states, setStates] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchStates = async () => {
            // Eğer ülke seçilmemişse, il listesini temizle
            if (!countryId) {
                setStates([]);
                return;
            }

            try {
                setLoading(true);
                setError(null);

                // Türkiye için optimize edilmiş endpoint kullan
                const endpoint =
                    countryId === 225
                        ? "/api/locations/turkiye/states"
                        : `/api/locations/countries/${countryId}/states`;

                const response = await axios.get(endpoint);
                if (response.data && response.data.length > 0) {
                    setStates(response.data);
                } else {
                    console.warn("İl verisi bulunamadı veya boş.");
                }
                setLoading(false);
            } catch (err) {
                console.error("İller yüklenirken hata oluştu:", err);
                setError("İller yüklenirken bir hata oluştu.");
                setLoading(false);
            }
        };

        fetchStates();
    }, [countryId]);

    // Ülke değiştiğinde, seçili ili sıfırla
    useEffect(() => {
        onChange("");
    }, [countryId]);

    return (
        <div>
            <select
                id={id}
                value={value || ""}
                onChange={(e) =>
                    onChange(e.target.value ? parseInt(e.target.value) : "")
                }
                className={`w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 ${className}`}
                disabled={disabled || loading || !countryId}
                required={required}
            >
                <option value="">İl Seçiniz</option>
                {states.map((state) => (
                    <option key={state.id} value={state.id}>
                        {state.name}
                    </option>
                ))}
            </select>
            {loading && (
                <p className="text-sm text-gray-500 mt-1">Yükleniyor...</p>
            )}
            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
        </div>
    );
}
