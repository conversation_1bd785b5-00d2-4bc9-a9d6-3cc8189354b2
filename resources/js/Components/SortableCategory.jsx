import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

const SortableCategory = ({ category, isExpanded, hasChildren, onToggleExpand }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: category.id });
    
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        backgroundColor: isDragging ? '#f3f4f6' : 'white',
        paddingLeft: `${category.level * 1.5 + 1}rem`,
    };
    
    return (
        <div
            ref={setNodeRef}
            style={style}
            className="py-3 px-4 border-b last:border-b-0 cursor-move"
        >
            <div className="grid grid-cols-12 gap-4 items-center">
                <div className="col-span-8 flex items-center">
                    {hasChildren && (
                        <button
                            type="button"
                            onClick={(e) => {
                                e.stopPropagation();
                                onToggleExpand();
                            }}
                            className="mr-2 text-gray-500 hover:text-gray-700"
                        >
                            {isExpanded ? (
                                <ChevronDownIcon className="h-5 w-5" />
                            ) : (
                                <ChevronRightIcon className="h-5 w-5" />
                            )}
                        </button>
                    )}
                    
                    <div className="flex items-center" {...attributes} {...listeners}>
                        <div className="mr-2 text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                            </svg>
                        </div>
                        <span className="font-medium">{category.name}</span>
                    </div>
                </div>
                
                <div className="col-span-2">
                    {category.status ? (
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            Aktif
                        </span>
                    ) : (
                        <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                            Pasif
                        </span>
                    )}
                </div>
                
                <div className="col-span-2 flex space-x-2">
                    <a
                        href={route('admin.categories.edit', category.id)}
                        className="text-blue-600 hover:text-blue-800"
                    >
                        Düzenle
                    </a>
                </div>
            </div>
        </div>
    );
};

export default SortableCategory;
