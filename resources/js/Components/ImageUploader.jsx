import React, { useState, useRef } from 'react';

export default function ImageUploader({ initialImage, onImageChange, className = '' }) {
    const [image, setImage] = useState(initialImage || '');
    const [isUploading, setIsUploading] = useState(false);
    const [error, setError] = useState('');
    const fileInputRef = useRef(null);

    const handleImageClick = () => {
        fileInputRef.current.click();
    };

    const handleFileChange = async (e) => {
        const file = e.target.files[0];
        if (!file) return;

        // Dosya türü kontrolü
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            setError('Lütfen geçerli bir resim dosyası seçin (JPEG, PNG, GIF, WEBP)');
            return;
        }

        // Dosya boyutu kontrolü (5MB)
        if (file.size > 5 * 1024 * 1024) {
            setError('Resim dosyası 5MB\'dan küçük olmalıdır');
            return;
        }

        setError('');
        setIsUploading(true);

        // FormData oluştur
        const formData = new FormData();
        formData.append('image', file);

        try {
            // Resmi yükle
            const response = await fetch('/api/upload-image', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error('Resim yükleme başarısız');
            }

            const data = await response.json();
            setImage(data.url);
            onImageChange(data.url);
        } catch (error) {
            console.error('Resim yükleme hatası:', error);
            setError('Resim yüklenirken bir hata oluştu');
        } finally {
            setIsUploading(false);
        }
    };

    const handleRemoveImage = () => {
        setImage('');
        onImageChange('');
    };

    return (
        <div className={`image-uploader ${className}`}>
            <div 
                className="relative border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:bg-gray-50"
                onClick={handleImageClick}
            >
                {image ? (
                    <div className="relative">
                        <img 
                            src={image} 
                            alt="Yüklenen resim" 
                            className="max-h-48 mx-auto rounded"
                        />
                        <button
                            type="button"
                            className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 transform translate-x-1/2 -translate-y-1/2"
                            onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveImage();
                            }}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                        </button>
                    </div>
                ) : (
                    <div className="py-4">
                        {isUploading ? (
                            <div className="flex flex-col items-center">
                                <svg className="animate-spin h-8 w-8 text-gray-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span className="text-sm text-gray-500">Yükleniyor...</span>
                            </div>
                        ) : (
                            <div className="flex flex-col items-center">
                                <svg className="h-12 w-12 text-gray-400 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span className="text-sm text-gray-500">Resim yüklemek için tıklayın veya sürükleyin</span>
                                <span className="text-xs text-gray-400 mt-1">PNG, JPG, GIF, WEBP (maks. 5MB)</span>
                            </div>
                        )}
                    </div>
                )}
                <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    onChange={handleFileChange}
                />
            </div>
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
        </div>
    );
}
