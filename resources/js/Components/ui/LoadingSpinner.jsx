import React from 'react';

/**
 * Loading Spinner Component
 * Phase 4C: Component Architecture İyileştirmeleri
 */
function LoadingSpinner({ 
    size = 'md', 
    color = 'blue', 
    className = '',
    text = null 
}) {
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-6 h-6',
        lg: 'w-8 h-8',
        xl: 'w-12 h-12'
    };
    
    const colorClasses = {
        blue: 'text-blue-600',
        gray: 'text-gray-600',
        green: 'text-green-600',
        red: 'text-red-600',
        yellow: 'text-yellow-600',
        purple: 'text-purple-600',
        white: 'text-white'
    };
    
    const spinnerClass = `
        ${sizeClasses[size]} 
        ${colorClasses[color]} 
        animate-spin 
        ${className}
    `.trim();
    
    if (text) {
        return (
            <div className="flex items-center space-x-2">
                <svg 
                    className={spinnerClass}
                    fill="none" 
                    viewBox="0 0 24 24"
                >
                    <circle 
                        className="opacity-25" 
                        cx="12" 
                        cy="12" 
                        r="10" 
                        stroke="currentColor" 
                        strokeWidth="4"
                    />
                    <path 
                        className="opacity-75" 
                        fill="currentColor" 
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                </svg>
                <span className={`text-sm ${colorClasses[color]}`}>
                    {text}
                </span>
            </div>
        );
    }
    
    return (
        <svg 
            className={spinnerClass}
            fill="none" 
            viewBox="0 0 24 24"
        >
            <circle 
                className="opacity-25" 
                cx="12" 
                cy="12" 
                r="10" 
                stroke="currentColor" 
                strokeWidth="4"
            />
            <path 
                className="opacity-75" 
                fill="currentColor" 
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
        </svg>
    );
}

/**
 * Loading Overlay Component
 */
export function LoadingOverlay({ 
    isLoading, 
    children, 
    text = 'Yükleniyor...',
    className = '' 
}) {
    return (
        <div className={`relative ${className}`}>
            {children}
            {isLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                    <LoadingSpinner size="lg" text={text} />
                </div>
            )}
        </div>
    );
}

/**
 * Loading Button Component
 */
export function LoadingButton({ 
    isLoading, 
    children, 
    disabled,
    className = '',
    loadingText = 'Yükleniyor...',
    ...props 
}) {
    return (
        <button
            {...props}
            disabled={disabled || isLoading}
            className={`
                relative inline-flex items-center justify-center
                ${isLoading ? 'cursor-not-allowed' : ''}
                ${className}
            `}
        >
            {isLoading && (
                <LoadingSpinner 
                    size="sm" 
                    color="white" 
                    className="mr-2" 
                />
            )}
            {isLoading ? loadingText : children}
        </button>
    );
}

/**
 * Skeleton Loading Component
 */
export function Skeleton({ 
    width = '100%', 
    height = '1rem', 
    className = '',
    rounded = true 
}) {
    return (
        <div
            className={`
                animate-pulse bg-gray-200
                ${rounded ? 'rounded' : ''}
                ${className}
            `}
            style={{ width, height }}
        />
    );
}

/**
 * Card Skeleton Component
 */
export function CardSkeleton({ className = '' }) {
    return (
        <div className={`border border-gray-200 rounded-lg p-4 ${className}`}>
            <Skeleton height="12rem" className="mb-4" />
            <Skeleton height="1.5rem" width="75%" className="mb-2" />
            <Skeleton height="1rem" width="50%" className="mb-2" />
            <Skeleton height="1rem" width="25%" />
        </div>
    );
}

/**
 * Table Row Skeleton Component
 */
export function TableRowSkeleton({ columns = 4, className = '' }) {
    return (
        <tr className={className}>
            {Array.from({ length: columns }).map((_, index) => (
                <td key={index} className="px-6 py-4">
                    <Skeleton height="1rem" width={`${Math.random() * 40 + 60}%`} />
                </td>
            ))}
        </tr>
    );
}

/**
 * List Item Skeleton Component
 */
export function ListItemSkeleton({ className = '' }) {
    return (
        <div className={`flex items-center space-x-4 p-4 ${className}`}>
            <Skeleton width="3rem" height="3rem" className="flex-shrink-0" />
            <div className="flex-1">
                <Skeleton height="1.25rem" width="60%" className="mb-2" />
                <Skeleton height="1rem" width="40%" />
            </div>
        </div>
    );
}

export default LoadingSpinner;
