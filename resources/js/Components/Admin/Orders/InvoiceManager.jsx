import React, { useState } from "react";
import { router } from "@inertiajs/react";

export default function InvoiceManager({ order }) {
    const [invoiceNumber, setInvoiceNumber] = useState(order.invoice_number || "");
    const [invoiceDate, setInvoiceDate] = useState(order.invoice_date || "");
    const [invoicePdf, setInvoicePdf] = useState(null);
    const [invoiceNote, setInvoiceNote] = useState("");
    const [notifyCustomer, setNotifyCustomer] = useState(true);
    
    const handleUpdateInvoice = (e) => {
        e.preventDefault();
        
        if (!invoiceNumber || !invoiceDate) {
            alert("Lütfen fatura numarası ve tarihini girin");
            return;
        }
        
        const formData = new FormData();
        formData.append('invoice_number', invoiceNumber);
        formData.append('invoice_date', invoiceDate);
        formData.append('note', invoiceNote);
        formData.append('notify_customer', notifyCustomer);
        
        if (invoicePdf) {
            formData.append('invoice_pdf', invoicePdf);
        }
        
        router.post(route("admin.orders.invoice-info", order.id), formData, {
            forceFormData: true,
        });
    };
    
    const handleGenerateInvoice = () => {
        router.get(route("admin.orders.invoice", order.id));
    };

    return (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium mb-4">Fatura Bilgileri</h3>
            
            {order.invoice_number && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="font-medium">Fatura Numarası: {order.invoice_number}</p>
                            {order.invoice_date && (
                                <p>Fatura Tarihi: {new Date(order.invoice_date).toLocaleDateString('tr-TR')}</p>
                            )}
                        </div>
                        
                        <div className="flex space-x-2">
                            {order.invoice_pdf && (
                                <a
                                    href={order.invoice_pdf_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded text-sm"
                                >
                                    Faturayı Görüntüle
                                </a>
                            )}
                            
                            <button
                                onClick={handleGenerateInvoice}
                                className="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded text-sm"
                            >
                                Fatura Oluştur
                            </button>
                        </div>
                    </div>
                </div>
            )}
            
            <form onSubmit={handleUpdateInvoice}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Fatura Numarası
                        </label>
                        <input
                            type="text"
                            value={invoiceNumber}
                            onChange={(e) => setInvoiceNumber(e.target.value)}
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            placeholder="Fatura numarası"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Fatura Tarihi
                        </label>
                        <input
                            type="date"
                            value={invoiceDate}
                            onChange={(e) => setInvoiceDate(e.target.value)}
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                    </div>
                </div>
                
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Fatura PDF
                    </label>
                    <input
                        type="file"
                        onChange={(e) => setInvoicePdf(e.target.files[0])}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        accept=".pdf"
                    />
                    <p className="text-xs text-gray-500 mt-1">Maksimum dosya boyutu: 10MB</p>
                </div>
                
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Fatura Notu (Opsiyonel)
                    </label>
                    <textarea
                        value={invoiceNote}
                        onChange={(e) => setInvoiceNote(e.target.value)}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        rows="2"
                        placeholder="Fatura hakkında not ekleyin..."
                    ></textarea>
                </div>
                
                <div className="mb-4">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={notifyCustomer}
                            onChange={(e) => setNotifyCustomer(e.target.checked)}
                            className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm text-gray-600">Müşteriye bildirim gönder</span>
                    </label>
                </div>
                
                <button
                    type="submit"
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
                >
                    Fatura Bilgilerini Güncelle
                </button>
            </form>
        </div>
    );
}
