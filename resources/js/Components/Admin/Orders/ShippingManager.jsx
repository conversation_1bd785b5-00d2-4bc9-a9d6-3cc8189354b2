import React, { useState, useEffect } from "react";
import { router } from "@inertiajs/react";
import axios from "axios";

export default function ShippingManager({ order }) {
    const [shippingCompanies, setShippingCompanies] = useState([]);
    const [trackingNumber, setTrackingNumber] = useState(
        order.tracking_number || ""
    );
    const [shippingCompany, setShippingCompany] = useState(
        order.shipping_company || ""
    );
    const [shippingDate, setShippingDate] = useState(order.shipping_date || "");
    const [estimatedDeliveryDate, setEstimatedDeliveryDate] = useState(
        order.estimated_delivery_date || ""
    );
    const [shippingNote, setShippingNote] = useState("");
    const [notifyCustomer, setNotifyCustomer] = useState(true);

    const handleUpdateShipping = (e) => {
        e.preventDefault();

        if (!trackingNumber || !shippingCompany) {
            alert("Lütfen kargo takip numarası ve kargo firmasını girin");
            return;
        }

        router.put(route("admin.orders.shipping-info", order.id), {
            tracking_number: trackingNumber,
            shipping_company: shippingCompany,
            shipping_date: shippingDate,
            estimated_delivery_date: estimatedDeliveryDate,
            note: shippingNote,
            notify_customer: notifyCustomer,
        });
    };

    const handleMarkAsDelivered = (e) => {
        e.preventDefault();

        if (
            confirm(
                "Siparişi teslim edildi olarak işaretlemek istediğinizden emin misiniz?"
            )
        ) {
            router.put(route("admin.orders.mark-delivered", order.id), {
                delivery_date: new Date().toISOString().split("T")[0],
                note: "Sipariş teslim edildi olarak işaretlendi.",
                notify_customer: true,
            });
        }
    };

    // Kargo şirketlerini API'den al
    useEffect(() => {
        axios
            .get("/api/shipping-companies")
            .then((response) => {
                if (response.data && Array.isArray(response.data)) {
                    setShippingCompanies(
                        response.data.map((company) => ({
                            value: company.code,
                            label: company.name,
                        }))
                    );
                }
            })
            .catch((error) => {
                console.error("Kargo şirketleri alınamadı:", error);
                // Hata durumunda varsayılan listeyi kullan
                setShippingCompanies([
                    { value: "aras", label: "Aras Kargo" },
                    { value: "yurtici", label: "Yurtiçi Kargo" },
                    { value: "mng", label: "MNG Kargo" },
                    { value: "ptt", label: "PTT Kargo" },
                    { value: "ups", label: "UPS" },
                    { value: "dhl", label: "DHL" },
                    { value: "fedex", label: "FedEx" },
                    { value: "other", label: "Diğer" },
                ]);
            });
    }, []);

    return (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium mb-4">Kargo Bilgileri</h3>

            {order.tracking_number && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="font-medium">
                                Kargo Takip Numarası: {order.tracking_number}
                            </p>
                            <p>Kargo Firması: {order.shipping_company}</p>
                            {order.shipping_date && (
                                <p>
                                    Kargoya Verilme Tarihi:{" "}
                                    {new Date(
                                        order.shipping_date
                                    ).toLocaleDateString("tr-TR")}
                                </p>
                            )}
                            {order.estimated_delivery_date && (
                                <p>
                                    Tahmini Teslimat Tarihi:{" "}
                                    {new Date(
                                        order.estimated_delivery_date
                                    ).toLocaleDateString("tr-TR")}
                                </p>
                            )}
                        </div>

                        {order.tracking_url && (
                            <a
                                href={order.tracking_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded text-sm"
                            >
                                Kargo Takip
                            </a>
                        )}
                    </div>
                </div>
            )}

            <form onSubmit={handleUpdateShipping} className="mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Kargo Takip Numarası
                        </label>
                        <input
                            type="text"
                            value={trackingNumber}
                            onChange={(e) => setTrackingNumber(e.target.value)}
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            placeholder="Kargo takip numarası"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Kargo Firması
                        </label>
                        <select
                            value={shippingCompany}
                            onChange={(e) => setShippingCompany(e.target.value)}
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        >
                            <option value="">Kargo firması seçin</option>
                            {shippingCompanies.map((company) => (
                                <option
                                    key={company.value}
                                    value={company.value}
                                >
                                    {company.label}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Kargoya Verilme Tarihi
                        </label>
                        <input
                            type="date"
                            value={shippingDate}
                            onChange={(e) => setShippingDate(e.target.value)}
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Tahmini Teslimat Tarihi
                        </label>
                        <input
                            type="date"
                            value={estimatedDeliveryDate}
                            onChange={(e) =>
                                setEstimatedDeliveryDate(e.target.value)
                            }
                            className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                    </div>
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Kargo Notu (Opsiyonel)
                    </label>
                    <textarea
                        value={shippingNote}
                        onChange={(e) => setShippingNote(e.target.value)}
                        className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        rows="2"
                        placeholder="Kargo hakkında not ekleyin..."
                    ></textarea>
                </div>

                <div className="mb-4">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={notifyCustomer}
                            onChange={(e) =>
                                setNotifyCustomer(e.target.checked)
                            }
                            className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                            Müşteriye bildirim gönder
                        </span>
                    </label>
                </div>

                <button
                    type="submit"
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
                >
                    Kargo Bilgilerini Güncelle
                </button>
            </form>

            {order.status !== "delivered" && order.tracking_number && (
                <div className="border-t pt-4">
                    <button
                        onClick={handleMarkAsDelivered}
                        className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded"
                    >
                        Teslim Edildi Olarak İşaretle
                    </button>
                </div>
            )}
        </div>
    );
}
