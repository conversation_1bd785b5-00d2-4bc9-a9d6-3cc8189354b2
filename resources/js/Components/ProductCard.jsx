import React, { useState, useEffect } from "react";
import { Link, usePage } from "@inertiajs/react";
import { toast } from "react-hot-toast";
import axios from "axios";
import QuickView from "./QuickView";

export default function ProductCard({
    product,
    attributeFilters = {},
    forceFilteredUrl = false,
}) {
    const [quickViewOpen, setQuickViewOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [productUrl, setProductUrl] = useState(
        product.url || `/products/${product.id}`
    );

    // Hata ayıklama için
    console.log("ProductCard - Ürün:", product.name);
    console.log("ProductCard - Filtreler:", attributeFilters);
    console.log("ProductCard - forceFilteredUrl:", forceFilteredUrl);

    // Fiyat formatla
    const formatPrice = (price) => {
        return new Intl.NumberFormat("tr-TR", {
            style: "currency",
            currency: "TRY",
        }).format(price);
    };

    // Ürün URL'sini oluştur
    useEffect(() => {
        // Hata ayıklama için
        console.log("ProductCard useEffect - Ürün:", product.name);
        console.log("ProductCard useEffect - Filtreler:", attributeFilters);
        console.log("ProductCard useEffect - Varyantlar:", product.variants);

        // Ürün slug'ını al
        const slug =
            product.slug || product.name.toLowerCase().replace(/\s+/g, "-");
        let stockCode = product.stock_code || "main";
        let newUrl = product.url || `/products/${product.id}`;

        // Filtreleme parametrelerini artık URL'ye eklemiyoruz
        // Sadece filtreleme bilgisini kullanarak doğru varyantı buluyoruz

        // Ürün URL'sini oluştur
        if (product.has_variants) {
            // forceFilteredUrl parametresi true ise, filtreleme bilgisini kullanarak doğru varyantı bul
            if (
                forceFilteredUrl &&
                attributeFilters &&
                Object.keys(attributeFilters).length > 0
            ) {
                console.log(
                    "forceFilteredUrl aktif, filtreleme bilgisi kullanılıyor"
                );

                // Eğer ürünün varyantları varsa ve filtreleme yapılmışsa
                if (
                    attributeFilters &&
                    Object.keys(attributeFilters).length > 0
                ) {
                    console.log("Filtreleme var, eşleşen varyant aranıyor...");

                    // Seçilen filtrelere uygun varyant var mı kontrol et
                    let matchingVariant = null;

                    // Tüm varyantları kontrol et
                    if (product.variants && product.variants.length > 0) {
                        // Varyantları döngüye al
                        for (const variant of product.variants) {
                            console.log("Varyant kontrol ediliyor:", variant);

                            let isMatch = true;

                            // Her filtre için kontrol et
                            for (const [
                                attributeCode,
                                values,
                            ] of Object.entries(attributeFilters)) {
                                if (values && values.length > 0) {
                                    console.log(
                                        `Filtre kontrol ediliyor: ${attributeCode} = ${values}`
                                    );

                                    // Varyantın attribute_values özelliği var mı kontrol et
                                    if (!variant.attribute_values) {
                                        console.log(
                                            "Varyantın attribute_values özelliği yok! Varyant ID:",
                                            variant.id
                                        );
                                        // attribute_values yoksa bu varyantı atla ama diğer varyantları kontrol etmeye devam et
                                        isMatch = false;
                                        break;
                                    }

                                    // Varyantın bu özelliği var mı ve seçilen değerlerden biri mi?
                                    const variantValue =
                                        variant.attribute_values[attributeCode];
                                    console.log(
                                        `Varyant değeri: ${variantValue}, Aranan değerler: ${values}`
                                    );

                                    // Değerleri string'e çevirerek karşılaştır (tip uyumsuzluğu olabilir)
                                    const stringValues = values.map((v) =>
                                        String(v)
                                    );
                                    const stringVariantValue =
                                        String(variantValue);

                                    console.log(
                                        `String değerler - Varyant: "${stringVariantValue}", Aranan: [${stringValues.join(
                                            ", "
                                        )}]`
                                    );

                                    if (
                                        !variantValue ||
                                        !stringValues.includes(
                                            stringVariantValue
                                        )
                                    ) {
                                        console.log(
                                            "Eşleşme bulunamadı, sonraki varyanta geçiliyor"
                                        );
                                        isMatch = false;
                                        break;
                                    }
                                }
                            }

                            // Eğer tüm filtrelere uyuyorsa, bu varyantı kullan
                            if (isMatch) {
                                console.log(
                                    "Eşleşen varyant bulundu:",
                                    variant
                                );
                                matchingVariant = variant;
                                break;
                            }
                        }
                    } else {
                        console.log("Ürünün varyantları yok veya boş!");
                    }

                    // Eğer eşleşen varyant bulunduysa, onun stok kodunu kullan
                    if (matchingVariant) {
                        if (matchingVariant.stock_code) {
                            stockCode = matchingVariant.stock_code;
                            // /products önekini kaldırdık
                            newUrl = `/${slug}-p-${stockCode}`;
                            console.log(
                                "Eşleşen varyant stok kodu kullanılıyor:",
                                stockCode
                            );
                            console.log("Yeni URL:", newUrl);
                        } else if (matchingVariant.url) {
                            // Filtreleme parametrelerini URL'ye eklemeyi kaldırdık
                            newUrl = matchingVariant.url;
                            console.log(
                                "Eşleşen varyant URL'si kullanılıyor:",
                                newUrl
                            );
                        }
                    } else {
                        console.log(
                            "Eşleşen varyant bulunamadı, varsayılan kullanılacak"
                        );

                        // Eşleşen varyant yoksa, varsayılan varyantı kullan
                        const defaultVariant = product.variants?.find(
                            (v) => v.is_default
                        );
                        if (defaultVariant) {
                            if (defaultVariant.stock_code) {
                                stockCode = defaultVariant.stock_code;
                                // /products önekini kaldırdık
                                newUrl = `/${slug}-p-${stockCode}`;
                                console.log(
                                    "Varsayılan varyant stok kodu kullanılıyor:",
                                    stockCode
                                );
                                console.log("Yeni URL:", newUrl);
                            } else if (defaultVariant.url) {
                                // Filtreleme parametrelerini URL'ye eklemeyi kaldırdık
                                newUrl = defaultVariant.url;
                                console.log(
                                    "Varsayılan varyant URL'si kullanılıyor:",
                                    newUrl
                                );
                            }
                        }
                    }
                } else {
                    console.log(
                        "Filtreleme yok, varsayılan varyant kullanılacak"
                    );

                    // Filtreleme yoksa, varsayılan varyantın URL'sini kullan
                    const defaultVariant = product.variants?.find(
                        (v) => v.is_default
                    );
                    if (defaultVariant) {
                        if (defaultVariant.stock_code) {
                            stockCode = defaultVariant.stock_code;
                            newUrl = `/${slug}-p-${stockCode}`;
                            console.log(
                                "Varsayılan varyant stok kodu kullanılıyor:",
                                stockCode
                            );
                        } else if (defaultVariant.url) {
                            newUrl = defaultVariant.url;
                            console.log(
                                "Varsayılan varyant URL'si kullanılıyor:",
                                newUrl
                            );
                        }
                    }
                }
            } else {
                // Normal davranış - backend'in belirlediği URL'yi kullan
                console.log(
                    "Normal davranış - backend'in belirlediği URL kullanılıyor"
                );

                // Eğer ürünün varyantları varsa ve filtreleme yapılmışsa
                if (
                    attributeFilters &&
                    Object.keys(attributeFilters).length > 0
                ) {
                    console.log("Filtreleme var, eşleşen varyant aranıyor...");

                    // Seçilen filtrelere uygun varyant var mı kontrol et
                    let matchingVariant = null;

                    // Tüm varyantları kontrol et
                    if (product.variants && product.variants.length > 0) {
                        // Varyantları döngüye al
                        for (const variant of product.variants) {
                            console.log("Varyant kontrol ediliyor:", variant);

                            let isMatch = true;

                            // Her filtre için kontrol et
                            for (const [
                                attributeCode,
                                values,
                            ] of Object.entries(attributeFilters)) {
                                if (values && values.length > 0) {
                                    console.log(
                                        `Filtre kontrol ediliyor: ${attributeCode} = ${values}`
                                    );

                                    // Varyantın attribute_values özelliği var mı kontrol et
                                    if (!variant.attribute_values) {
                                        console.log(
                                            "Varyantın attribute_values özelliği yok! Varyant ID:",
                                            variant.id
                                        );
                                        // attribute_values yoksa bu varyantı atla ama diğer varyantları kontrol etmeye devam et
                                        isMatch = false;
                                        break;
                                    }

                                    // Varyantın bu özelliği var mı ve seçilen değerlerden biri mi?
                                    const variantValue =
                                        variant.attribute_values[attributeCode];
                                    console.log(
                                        `Varyant değeri: ${variantValue}, Aranan değerler: ${values}`
                                    );

                                    // Değerleri string'e çevirerek karşılaştır (tip uyumsuzluğu olabilir)
                                    const stringValues = values.map((v) =>
                                        String(v)
                                    );
                                    const stringVariantValue =
                                        String(variantValue);

                                    console.log(
                                        `String değerler - Varyant: "${stringVariantValue}", Aranan: [${stringValues.join(
                                            ", "
                                        )}]`
                                    );

                                    if (
                                        !variantValue ||
                                        !stringValues.includes(
                                            stringVariantValue
                                        )
                                    ) {
                                        console.log(
                                            "Eşleşme bulunamadı, sonraki varyanta geçiliyor"
                                        );
                                        isMatch = false;
                                        break;
                                    }
                                }
                            }

                            // Eğer tüm filtrelere uyuyorsa, bu varyantı kullan
                            if (isMatch) {
                                console.log(
                                    "Eşleşen varyant bulundu:",
                                    variant
                                );
                                matchingVariant = variant;
                                break;
                            }
                        }
                    } else {
                        console.log("Ürünün varyantları yok veya boş!");
                    }

                    // Eğer eşleşen varyant bulunduysa, onun URL'sini kullan
                    if (matchingVariant) {
                        if (matchingVariant.stock_code) {
                            stockCode = matchingVariant.stock_code;
                            newUrl = `/${slug}-p-${stockCode}`;
                            console.log(
                                "Eşleşen varyant stok kodu kullanılıyor:",
                                stockCode
                            );
                        } else if (matchingVariant.url) {
                            newUrl = matchingVariant.url;
                            console.log(
                                "Eşleşen varyant URL'si kullanılıyor:",
                                newUrl
                            );
                        }
                    } else {
                        console.log(
                            "Eşleşen varyant bulunamadı, varsayılan kullanılacak"
                        );

                        // Eşleşen varyant yoksa, varsayılan varyantı kullan
                        const defaultVariant = product.variants?.find(
                            (v) => v.is_default
                        );
                        if (defaultVariant) {
                            if (defaultVariant.stock_code) {
                                stockCode = defaultVariant.stock_code;
                                newUrl = `/${slug}-p-${stockCode}`;
                                console.log(
                                    "Varsayılan varyant stok kodu kullanılıyor:",
                                    stockCode
                                );
                            } else if (defaultVariant.url) {
                                newUrl = defaultVariant.url;
                                console.log(
                                    "Varsayılan varyant URL'si kullanılıyor:",
                                    newUrl
                                );
                            }
                        }
                    }
                } else {
                    console.log(
                        "Filtreleme yok, varsayılan varyant kullanılacak"
                    );

                    // Filtreleme yoksa, varsayılan varyantın URL'sini kullan
                    const defaultVariant = product.variants?.find(
                        (v) => v.is_default
                    );
                    if (defaultVariant) {
                        if (defaultVariant.stock_code) {
                            stockCode = defaultVariant.stock_code;
                            newUrl = `/products/${slug}-p-${stockCode}`;
                            console.log(
                                "Varsayılan varyant stok kodu kullanılıyor:",
                                stockCode
                            );
                        } else if (defaultVariant.url) {
                            newUrl = defaultVariant.url;
                            console.log(
                                "Varsayılan varyant URL'si kullanılıyor:",
                                newUrl
                            );
                        }
                    }
                }
            }
        }

        // URL'yi güncelle
        console.log("Ürün URL'si güncelleniyor:", newUrl);
        setProductUrl(newUrl);
    }, [product, attributeFilters, forceFilteredUrl]);

    // Sepete ekle
    const addToCart = () => {
        if (loading) return;

        // Ürünün varyantları varsa hızlı bakış modalını aç
        if (product.has_variants) {
            setQuickViewOpen(true);
            return;
        }

        setLoading(true);

        // Sepete eklenecek verileri hazırla
        const cartData = {
            product_id: product.id,
            quantity: 1,
        };

        // Eğer varsayılan varyant varsa, onu ekle
        if (product.default_variant_id) {
            cartData.variant_id = product.default_variant_id;
        }

        // Sunucuya yük binmemesi için kısa bir gecikme ekle (500ms)
        setTimeout(() => {
            // Axios ile POST isteği gönder
            axios
                .post(route("cart.add"), cartData)
                .then((response) => {
                    toast.success("Ürün sepete eklendi");

                    // Sepet sayısını güncelle
                    if (
                        response.data &&
                        response.data.cartCount !== undefined
                    ) {
                        // Özel olay tetikle - Bu, tüm komponentlerin sepet sayısını güncellemesini sağlar
                        window.dispatchEvent(
                            new CustomEvent("cart-updated", {
                                detail: { cartCount: response.data.cartCount },
                            })
                        );
                    } else {
                        // Eğer response'da cartCount yoksa, API'den sepet sayısını al
                        import("@/Utils/cartUtils").then(
                            ({ updateCartCount }) => {
                                updateCartCount();
                            }
                        );
                    }

                    setLoading(false);
                })
                .catch((error) => {
                    console.error("Sepete ekleme hatası:", error);
                    toast.error("Ürün sepete eklenirken bir hata oluştu");
                    setLoading(false);
                });
        }, 500); // 500ms gecikme
    };

    return (
        <>
            <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                <div className="relative">
                    <Link href={productUrl}>
                        <img
                            src={product.image || "/images/placeholder.png"}
                            alt={product.name}
                            className="w-full h-48 object-cover"
                        />
                    </Link>
                </div>

                <div className="p-4">
                    <Link href={productUrl}>
                        <h3 className="font-semibold text-lg mb-1 hover:text-blue-600">
                            {product.name}
                        </h3>
                    </Link>

                    <p className="text-gray-600 text-sm mb-2">
                        {product.category
                            ? product.category.name
                            : "Kategorisiz"}
                    </p>

                    <div className="flex justify-between items-center">
                        <span className="font-bold text-lg">
                            {formatPrice(product.price)}
                        </span>

                        <button
                            onClick={addToCart}
                            disabled={loading}
                            className={`${
                                loading
                                    ? "bg-gray-400 cursor-wait"
                                    : "bg-blue-600 hover:bg-blue-700"
                            } text-white px-3 py-1 rounded`}
                        >
                            {loading ? "..." : "Sepete Ekle"}
                        </button>
                    </div>
                </div>
            </div>

            {/* Hızlı bakış modalı */}
            <QuickView
                productId={product.id}
                isOpen={quickViewOpen}
                onClose={() => setQuickViewOpen(false)}
            />
        </>
    );
}
