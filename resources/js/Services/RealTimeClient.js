import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

/**
 * Real-Time Client Service
 * WebSocket bağlantıları ve real-time event'leri yönetir
 */
class RealTimeClient {
    constructor() {
        this.echo = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.channels = new Map();
        this.eventListeners = new Map();
        this.config = {
            enabled: window.REALTIME_ENABLED || true,
            debug: window.REALTIME_DEBUG || false,
            host: window.REVERB_HOST || '127.0.0.1',
            port: window.REVERB_PORT || 8080,
            scheme: window.REVERB_SCHEME || 'http',
            authEndpoint: '/api/broadcasting/auth',
        };

        this.init();
    }

    /**
     * Real-time client'ı initialize et
     */
    init() {
        if (!this.config.enabled) {
            console.log('Real-time features disabled');
            return;
        }

        try {
            // Laravel Echo'yu configure et
            this.echo = new Echo({
                broadcaster: 'reverb',
                key: window.REVERB_APP_KEY || 'local-key',
                wsHost: this.config.host,
                wsPort: this.config.port,
                wssPort: this.config.port,
                forceTLS: this.config.scheme === 'https',
                enabledTransports: ['ws', 'wss'],
                authEndpoint: this.config.authEndpoint,
                auth: {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                        'Authorization': `Bearer ${this.getAuthToken()}`,
                    },
                },
            });

            this.setupConnectionHandlers();
            this.isConnected = true;

            if (this.config.debug) {
                console.log('Real-time client initialized successfully');
            }

        } catch (error) {
            console.error('Failed to initialize real-time client:', error);
            this.handleConnectionError(error);
        }
    }

    /**
     * Connection event handler'larını setup et
     */
    setupConnectionHandlers() {
        if (!this.echo) return;

        // Connection events
        this.echo.connector.pusher.connection.bind('connected', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.onConnectionEstablished();
        });

        this.echo.connector.pusher.connection.bind('disconnected', () => {
            this.isConnected = false;
            this.onConnectionLost();
        });

        this.echo.connector.pusher.connection.bind('error', (error) => {
            this.handleConnectionError(error);
        });

        // State change events
        this.echo.connector.pusher.connection.bind('state_change', (states) => {
            this.onConnectionStateChange(states.previous, states.current);
        });
    }

    /**
     * Product stock güncellemelerini dinle
     */
    listenToProductStock(productId, callback) {
        const channelName = `product.${productId}.stock`;
        const eventName = 'product.stock.updated';

        return this.subscribeToPublicChannel(channelName, eventName, callback);
    }

    /**
     * Product fiyat değişikliklerini dinle
     */
    listenToProductPrice(productId, callback) {
        const channelName = `product.${productId}.price`;
        const eventName = 'product.price.changed';

        return this.subscribeToPublicChannel(channelName, eventName, callback);
    }

    /**
     * Order status güncellemelerini dinle
     */
    listenToOrderStatus(orderId, callback) {
        const channelName = `order.${orderId}`;
        const eventName = 'order.status.changed';

        return this.subscribeToPrivateChannel(channelName, eventName, callback);
    }

    /**
     * User-specific notification'ları dinle
     */
    listenToUserNotifications(userId, callback) {
        const channelName = `user.${userId}`;
        const eventName = 'notification.received';

        return this.subscribeToPrivateChannel(channelName, eventName, callback);
    }

    /**
     * Cart güncellemelerini dinle
     */
    listenToCartUpdates(userId, callback) {
        const channelName = `cart.${userId}`;
        const eventName = 'cart.updated';

        return this.subscribeToPrivateChannel(channelName, eventName, callback);
    }

    /**
     * Admin alert'lerini dinle
     */
    listenToAdminAlerts(callback) {
        const channelName = 'admin';
        const eventName = 'admin.alert';

        return this.subscribeToPrivateChannel(channelName, eventName, callback);
    }

    /**
     * Public channel'a subscribe ol
     */
    subscribeToPublicChannel(channelName, eventName, callback) {
        if (!this.echo || !this.isConnected) {
            console.warn('Real-time client not connected');
            return null;
        }

        try {
            const channel = this.echo.channel(channelName);
            channel.listen(eventName, callback);

            this.channels.set(channelName, channel);
            this.addEventListener(channelName, eventName, callback);

            if (this.config.debug) {
                console.log(`Subscribed to public channel: ${channelName}, event: ${eventName}`);
            }

            return channel;

        } catch (error) {
            console.error(`Failed to subscribe to public channel ${channelName}:`, error);
            return null;
        }
    }

    /**
     * Private channel'a subscribe ol
     */
    subscribeToPrivateChannel(channelName, eventName, callback) {
        if (!this.echo || !this.isConnected) {
            console.warn('Real-time client not connected');
            return null;
        }

        try {
            const channel = this.echo.private(channelName);
            channel.listen(eventName, callback);

            this.channels.set(channelName, channel);
            this.addEventListener(channelName, eventName, callback);

            if (this.config.debug) {
                console.log(`Subscribed to private channel: ${channelName}, event: ${eventName}`);
            }

            return channel;

        } catch (error) {
            console.error(`Failed to subscribe to private channel ${channelName}:`, error);
            return null;
        }
    }

    /**
     * Presence channel'a subscribe ol
     */
    subscribeToPresenceChannel(channelName, callbacks = {}) {
        if (!this.echo || !this.isConnected) {
            console.warn('Real-time client not connected');
            return null;
        }

        try {
            const channel = this.echo.join(channelName);

            // Presence event'leri
            if (callbacks.here) {
                channel.here(callbacks.here);
            }

            if (callbacks.joining) {
                channel.joining(callbacks.joining);
            }

            if (callbacks.leaving) {
                channel.leaving(callbacks.leaving);
            }

            // Custom event'ler
            if (callbacks.events) {
                Object.entries(callbacks.events).forEach(([eventName, callback]) => {
                    channel.listen(eventName, callback);
                });
            }

            this.channels.set(channelName, channel);

            if (this.config.debug) {
                console.log(`Subscribed to presence channel: ${channelName}`);
            }

            return channel;

        } catch (error) {
            console.error(`Failed to subscribe to presence channel ${channelName}:`, error);
            return null;
        }
    }

    /**
     * Channel'dan unsubscribe ol
     */
    unsubscribe(channelName) {
        const channel = this.channels.get(channelName);
        
        if (channel) {
            this.echo.leave(channelName);
            this.channels.delete(channelName);
            this.removeEventListeners(channelName);

            if (this.config.debug) {
                console.log(`Unsubscribed from channel: ${channelName}`);
            }
        }
    }

    /**
     * Tüm channel'lardan unsubscribe ol
     */
    unsubscribeAll() {
        this.channels.forEach((channel, channelName) => {
            this.unsubscribe(channelName);
        });
    }

    /**
     * Connection durumunu kontrol et
     */
    isConnectionActive() {
        return this.isConnected && this.echo && this.echo.connector.pusher.connection.state === 'connected';
    }

    /**
     * Reconnect dene
     */
    reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        
        setTimeout(() => {
            if (this.config.debug) {
                console.log(`Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
            }

            this.disconnect();
            this.init();
        }, this.reconnectDelay * this.reconnectAttempts);
    }

    /**
     * Disconnect
     */
    disconnect() {
        if (this.echo) {
            this.unsubscribeAll();
            this.echo.disconnect();
            this.echo = null;
        }
        
        this.isConnected = false;
    }

    /**
     * Auth token'ı al
     */
    getAuthToken() {
        // Laravel Sanctum token'ı localStorage'dan al
        return localStorage.getItem('auth_token') || 
               document.querySelector('meta[name="api-token"]')?.getAttribute('content') || 
               '';
    }

    /**
     * Event listener ekle
     */
    addEventListener(channelName, eventName, callback) {
        const key = `${channelName}:${eventName}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        this.eventListeners.get(key).push(callback);
    }

    /**
     * Event listener'ları kaldır
     */
    removeEventListeners(channelName) {
        const keysToRemove = [];
        this.eventListeners.forEach((listeners, key) => {
            if (key.startsWith(`${channelName}:`)) {
                keysToRemove.push(key);
            }
        });

        keysToRemove.forEach(key => {
            this.eventListeners.delete(key);
        });
    }

    /**
     * Connection established handler
     */
    onConnectionEstablished() {
        if (this.config.debug) {
            console.log('Real-time connection established');
        }

        // Custom event dispatch et
        window.dispatchEvent(new CustomEvent('realtime:connected'));
    }

    /**
     * Connection lost handler
     */
    onConnectionLost() {
        if (this.config.debug) {
            console.log('Real-time connection lost');
        }

        // Custom event dispatch et
        window.dispatchEvent(new CustomEvent('realtime:disconnected'));

        // Auto-reconnect dene
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnect();
        }
    }

    /**
     * Connection error handler
     */
    handleConnectionError(error) {
        console.error('Real-time connection error:', error);
        
        // Custom event dispatch et
        window.dispatchEvent(new CustomEvent('realtime:error', { detail: error }));
    }

    /**
     * Connection state change handler
     */
    onConnectionStateChange(previousState, currentState) {
        if (this.config.debug) {
            console.log(`Real-time connection state changed: ${previousState} -> ${currentState}`);
        }

        // Custom event dispatch et
        window.dispatchEvent(new CustomEvent('realtime:state-change', {
            detail: { previousState, currentState }
        }));
    }
}

// Global instance oluştur
const realTimeClient = new RealTimeClient();

// Export et
export default realTimeClient;
