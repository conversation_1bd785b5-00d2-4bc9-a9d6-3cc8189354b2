import api from "../api";

export const categoryApi = {
    getAll: (params = {}) => api.get("/categories", { params }),
    getById: (id) => api.get(`/categories/${id}`),
    getTree: () => api.get("/categories/tree"),
    create: (data) => api.post("/api/categories", data),
    update: (id, data) => api.put(`/api/categories/${id}`, data),
    delete: (id) => api.delete(`/api/categories/${id}`),
};

export default categoryApi;
