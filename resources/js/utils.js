/**
 * Para birimini formatlar
 * @param {number} amount - Formatlanacak miktar
 * @param {number} decimals - Ondalık basamak sayısı
 * @param {string} decimalSeparator - Ondalık ayırıcı
 * @param {string} thousandsSeparator - Binlik ayırıcı
 * @returns {string} Formatlanmış para birimi
 */
export function formatMoney(amount, decimals = 2, decimalSeparator = ',', thousandsSeparator = '.') {
    try {
        const number = parseFloat(amount);
        if (isNaN(number)) {
            return '0,00';
        }
        
        const fixedNumber = number.toFixed(decimals);
        const parts = fixedNumber.split('.');
        
        const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
        const decimalPart = parts[1] || '0'.repeat(decimals);
        
        return `${integerPart}${decimalSeparator}${decimalPart}`;
    } catch (error) {
        console.error('formatMoney error:', error);
        return '0,00';
    }
}

/**
 * Tarih formatlar
 * @param {string} dateString - Formatlanacak tarih
 * @param {boolean} includeTime - Saat bilgisi eklensin mi
 * @returns {string} Formatlanmış tarih
 */
export function formatDate(dateString, includeTime = false) {
    try {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '';
        }
        
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        
        let result = `${day}.${month}.${year}`;
        
        if (includeTime) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            result += ` ${hours}:${minutes}`;
        }
        
        return result;
    } catch (error) {
        console.error('formatDate error:', error);
        return '';
    }
}

/**
 * Metni kısaltır
 * @param {string} text - Kısaltılacak metin
 * @param {number} maxLength - Maksimum uzunluk
 * @returns {string} Kısaltılmış metin
 */
export function truncateText(text, maxLength = 100) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    
    return text.substring(0, maxLength) + '...';
}

/**
 * Slug oluşturur
 * @param {string} text - Slug oluşturulacak metin
 * @returns {string} Slug
 */
export function slugify(text) {
    if (!text) return '';
    
    const trMap = {
        'çÇ': 'c',
        'ğĞ': 'g',
        'şŞ': 's',
        'üÜ': 'u',
        'ıİ': 'i',
        'öÖ': 'o'
    };
    
    for (let key in trMap) {
        text = text.replace(new RegExp('[' + key + ']', 'g'), trMap[key]);
    }
    
    return text
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
}
