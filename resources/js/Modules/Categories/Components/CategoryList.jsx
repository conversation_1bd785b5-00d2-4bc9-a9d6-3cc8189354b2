import React from 'react';

const CategoryList = ({ categories, onEdit, onDelete }) => {
    return (
        <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
                <thead className="bg-gray-100">
                    <tr>
                        <th className="py-2 px-4 border-b text-left">ID</th>
                        <th className="py-2 px-4 border-b text-left"><PERSON><PERSON><PERSON></th>
                        <th className="py-2 px-4 border-b text-left">Üst Kategori</th>
                        <th className="py-2 px-4 border-b text-left">Durum</th>
                        <th className="py-2 px-4 border-b text-left"><PERSON><PERSON><PERSON><PERSON></th>
                    </tr>
                </thead>
                <tbody>
                    {categories.map(category => (
                        <tr key={category.id}>
                            <td className="py-2 px-4 border-b">{category.id}</td>
                            <td className="py-2 px-4 border-b">{category.name}</td>
                            <td className="py-2 px-4 border-b">{category.parent_id ? `#${category.parent_id}` : '-'}</td>
                            <td className="py-2 px-4 border-b">
                                <span className={`px-2 py-1 rounded text-xs ${category.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {category.status ? 'Aktif' : 'Pasif'}
                                </span>
                            </td>
                            <td className="py-2 px-4 border-b">
                                <button 
                                    onClick={() => onEdit(category)} 
                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                                >
                                    Düzenle
                                </button>
                                <button 
                                    onClick={() => onDelete(category.id)} 
                                    className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                >
                                    Sil
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default CategoryList;
