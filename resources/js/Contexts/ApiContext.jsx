import React, { createContext, useContext, useReducer, useCallback } from 'react';
import apiAdapter from '../Services/ApiAdapter.js';

/**
 * API Context for State Management
 * Phase 4B: State Management Modernizasyonu
 */

// Initial state
const initialState = {
    // Loading states
    loading: {
        products: false,
        categories: false,
        orders: false,
        global: false
    },
    
    // Error states
    errors: {
        products: null,
        categories: null,
        orders: null,
        global: null
    },
    
    // Data cache
    cache: {
        products: {},
        categories: {},
        orders: {},
        productsList: null,
        categoriesList: null,
        ordersList: null
    },
    
    // Pagination
    pagination: {
        products: { current_page: 1, total: 0, per_page: 10 },
        categories: { current_page: 1, total: 0, per_page: 10 },
        orders: { current_page: 1, total: 0, per_page: 10 }
    },
    
    // Filters
    filters: {
        products: {},
        categories: {},
        orders: {}
    },
    
    // API configuration
    config: {
        useCQRS: false,
        useNewAPI: true,
        version: 'v1.1'
    }
};

// Action types
const ActionTypes = {
    // Loading actions
    SET_LOADING: 'SET_LOADING',
    
    // Error actions
    SET_ERROR: 'SET_ERROR',
    CLEAR_ERROR: 'CLEAR_ERROR',
    
    // Data actions
    SET_PRODUCTS: 'SET_PRODUCTS',
    SET_PRODUCT: 'SET_PRODUCT',
    SET_CATEGORIES: 'SET_CATEGORIES',
    SET_CATEGORY: 'SET_CATEGORY',
    SET_ORDERS: 'SET_ORDERS',
    SET_ORDER: 'SET_ORDER',
    
    // Cache actions
    UPDATE_CACHE: 'UPDATE_CACHE',
    CLEAR_CACHE: 'CLEAR_CACHE',
    
    // Pagination actions
    SET_PAGINATION: 'SET_PAGINATION',
    
    // Filter actions
    SET_FILTERS: 'SET_FILTERS',
    CLEAR_FILTERS: 'CLEAR_FILTERS',
    
    // Config actions
    UPDATE_CONFIG: 'UPDATE_CONFIG'
};

// Reducer function
function apiReducer(state, action) {
    switch (action.type) {
        case ActionTypes.SET_LOADING:
            return {
                ...state,
                loading: {
                    ...state.loading,
                    [action.payload.key]: action.payload.value
                }
            };
            
        case ActionTypes.SET_ERROR:
            return {
                ...state,
                errors: {
                    ...state.errors,
                    [action.payload.key]: action.payload.error
                },
                loading: {
                    ...state.loading,
                    [action.payload.key]: false
                }
            };
            
        case ActionTypes.CLEAR_ERROR:
            return {
                ...state,
                errors: {
                    ...state.errors,
                    [action.payload.key]: null
                }
            };
            
        case ActionTypes.SET_PRODUCTS:
            return {
                ...state,
                cache: {
                    ...state.cache,
                    productsList: action.payload.data
                },
                pagination: {
                    ...state.pagination,
                    products: action.payload.pagination || state.pagination.products
                },
                loading: {
                    ...state.loading,
                    products: false
                },
                errors: {
                    ...state.errors,
                    products: null
                }
            };
            
        case ActionTypes.SET_PRODUCT:
            return {
                ...state,
                cache: {
                    ...state.cache,
                    products: {
                        ...state.cache.products,
                        [action.payload.id]: action.payload.data
                    }
                }
            };
            
        case ActionTypes.SET_CATEGORIES:
            return {
                ...state,
                cache: {
                    ...state.cache,
                    categoriesList: action.payload.data
                },
                pagination: {
                    ...state.pagination,
                    categories: action.payload.pagination || state.pagination.categories
                },
                loading: {
                    ...state.loading,
                    categories: false
                },
                errors: {
                    ...state.errors,
                    categories: null
                }
            };
            
        case ActionTypes.SET_ORDERS:
            return {
                ...state,
                cache: {
                    ...state.cache,
                    ordersList: action.payload.data
                },
                pagination: {
                    ...state.pagination,
                    orders: action.payload.pagination || state.pagination.orders
                },
                loading: {
                    ...state.loading,
                    orders: false
                },
                errors: {
                    ...state.errors,
                    orders: null
                }
            };
            
        case ActionTypes.UPDATE_CACHE:
            return {
                ...state,
                cache: {
                    ...state.cache,
                    [action.payload.key]: action.payload.data
                }
            };
            
        case ActionTypes.CLEAR_CACHE:
            return {
                ...state,
                cache: action.payload.key 
                    ? { ...state.cache, [action.payload.key]: null }
                    : initialState.cache
            };
            
        case ActionTypes.SET_PAGINATION:
            return {
                ...state,
                pagination: {
                    ...state.pagination,
                    [action.payload.key]: action.payload.pagination
                }
            };
            
        case ActionTypes.SET_FILTERS:
            return {
                ...state,
                filters: {
                    ...state.filters,
                    [action.payload.key]: action.payload.filters
                }
            };
            
        case ActionTypes.CLEAR_FILTERS:
            return {
                ...state,
                filters: {
                    ...state.filters,
                    [action.payload.key]: {}
                }
            };
            
        case ActionTypes.UPDATE_CONFIG:
            return {
                ...state,
                config: {
                    ...state.config,
                    ...action.payload
                }
            };
            
        default:
            return state;
    }
}

// Create context
const ApiContext = createContext();

// Context provider component
export function ApiProvider({ children }) {
    const [state, dispatch] = useReducer(apiReducer, initialState);
    
    // Action creators
    const setLoading = useCallback((key, value) => {
        dispatch({
            type: ActionTypes.SET_LOADING,
            payload: { key, value }
        });
    }, []);
    
    const setError = useCallback((key, error) => {
        dispatch({
            type: ActionTypes.SET_ERROR,
            payload: { key, error }
        });
    }, []);
    
    const clearError = useCallback((key) => {
        dispatch({
            type: ActionTypes.CLEAR_ERROR,
            payload: { key }
        });
    }, []);
    
    const setProducts = useCallback((data, pagination) => {
        dispatch({
            type: ActionTypes.SET_PRODUCTS,
            payload: { data, pagination }
        });
    }, []);
    
    const setProduct = useCallback((id, data) => {
        dispatch({
            type: ActionTypes.SET_PRODUCT,
            payload: { id, data }
        });
    }, []);
    
    const setCategories = useCallback((data, pagination) => {
        dispatch({
            type: ActionTypes.SET_CATEGORIES,
            payload: { data, pagination }
        });
    }, []);
    
    const setOrders = useCallback((data, pagination) => {
        dispatch({
            type: ActionTypes.SET_ORDERS,
            payload: { data, pagination }
        });
    }, []);
    
    const updateCache = useCallback((key, data) => {
        dispatch({
            type: ActionTypes.UPDATE_CACHE,
            payload: { key, data }
        });
    }, []);
    
    const clearCache = useCallback((key = null) => {
        dispatch({
            type: ActionTypes.CLEAR_CACHE,
            payload: { key }
        });
    }, []);
    
    const setFilters = useCallback((key, filters) => {
        dispatch({
            type: ActionTypes.SET_FILTERS,
            payload: { key, filters }
        });
    }, []);
    
    const updateConfig = useCallback((config) => {
        dispatch({
            type: ActionTypes.UPDATE_CONFIG,
            payload: config
        });
        
        // Update API adapter configuration
        apiAdapter.updateFeatureFlags(config);
    }, []);
    
    // Context value
    const value = {
        // State
        ...state,
        
        // Actions
        setLoading,
        setError,
        clearError,
        setProducts,
        setProduct,
        setCategories,
        setOrders,
        updateCache,
        clearCache,
        setFilters,
        updateConfig
    };
    
    return (
        <ApiContext.Provider value={value}>
            {children}
        </ApiContext.Provider>
    );
}

// Custom hook to use API context
export function useApi() {
    const context = useContext(ApiContext);
    
    if (!context) {
        throw new Error('useApi must be used within an ApiProvider');
    }
    
    return context;
}

export { ActionTypes };
export default ApiContext;
