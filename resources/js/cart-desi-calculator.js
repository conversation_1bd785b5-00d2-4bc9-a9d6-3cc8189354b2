/**
 * Sepet desi değerini hesaplar ve global değişkene atar
 * @param {Array} cartItems - Sepet öğeleri
 */
export function calculateCartDesi(cartItems) {
    if (!cartItems || cartItems.length === 0) {
        window.cartDesi = 0;
        return 0;
    }
    
    let totalDesi = 0;
    
    cartItems.forEach(item => {
        const quantity = item.quantity;
        let itemDesi = 0;
        
        if (item.options && item.options.variant_id) {
            // Varyant desi değerini bul
            const variantId = parseInt(item.options.variant_id);
            const variant = item.product.variants?.find(v => v.id === variantId);
            
            if (variant && variant.desi) {
                itemDesi = parseFloat(variant.desi);
            }
        } else if (item.product && item.product.desi) {
            // Ürün desi değerini kullan
            itemDesi = parseFloat(item.product.desi);
        }
        
        // Varsayılan desi değeri 1
        if (!itemDesi || isNaN(itemDesi)) {
            itemDesi = 1;
        }
        
        totalDesi += (itemDesi * quantity);
    });
    
    // Global değişkene ata
    window.cartDesi = totalDesi;
    console.log("Toplam sepet desi değeri:", totalDesi);
    
    return totalDesi;
}
