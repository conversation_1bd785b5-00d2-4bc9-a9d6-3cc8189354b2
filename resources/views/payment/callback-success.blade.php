<!DOCTYPE html>
<html lang="tr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Başarılı</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 20px;
            text-align: center;
        }

        .success-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 500px;
            margin: 0 auto;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background-color: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .success-icon svg {
            width: 40px;
            height: 40px;
            fill: white;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
    </style>
</head>

<body>
    <div class="success-container">
        <div class="success-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M0 0h24v24H0z" fill="none" />
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
            </svg>
        </div>
        <h1>Ödeme Başarılı!</h1>
        <p>Ödeme işleminiz başarıyla tamamlandı. Siparişiniz hazırlanıyor.</p>
        <p>Sipariş numaranız: <strong>{{ $order_number }}</strong></p>

        <script>
            // Parent window'a mesaj gönder
            function sendMessageToParent() {
                try {
                    console.log('Callback sayfası yüklendi, parent window\'a mesaj gönderiliyor');

                    // Eğer bu sayfa bir iframe içinde açıldıysa (popup)
                    if (window.self !== window.top) {
                        console.log('İframe içinde çalışıyor');

                        // Parent window'a mesaj gönder (obje formatı)
                        window.parent.postMessage({
                            type: 'iyzico:success',
                            redirectUrl: '{{ $redirect_url }}',
                            orderNumber: '{{ $order_number }}'
                        }, '*');
                        console.log('Mesaj 1 gönderildi (obje formatı)');

                        // Eski formatla da mesaj gönder (string formatı)
                        window.parent.postMessage('iyzico:success:{{ $redirect_url }}', '*');
                        console.log('Mesaj 2 gönderildi (string formatı - URL)');

                        // Doğrudan sipariş numarası formatıyla da gönder
                        window.parent.postMessage('iyzico:success:order_number={{ $order_number }}', '*');
                        console.log('Mesaj 3 gönderildi (string formatı - sipariş numarası)');

                        // Basit bir mesaj daha gönder
                        window.parent.postMessage('success', '*');
                        console.log('Mesaj 4 gönderildi (basit string)');

                        // Doğrudan window.top'a da mesaj gönder
                        window.top.postMessage({
                            type: 'iyzico:success',
                            redirectUrl: '{{ $redirect_url }}',
                            orderNumber: '{{ $order_number }}'
                        }, '*');
                        console.log('Mesaj 5 gönderildi (window.top - obje formatı)');

                        // Doğrudan parent'a yönlendirme yap
                        try {
                            window.parent.location.href = '{{ $redirect_url }}';
                            console.log('Parent window yönlendirildi');
                        } catch (e) {
                            console.error('Parent window yönlendirilemedi:', e);
                        }
                    } else {
                        console.log('Normal sayfada çalışıyor, yönlendirme yapılıyor');

                        // Normal sayfa ise doğrudan yönlendir
                        window.location.href = "{{ $redirect_url }}";
                    }
                } catch (e) {
                    console.error('Hata oluştu:', e);

                    // Hata durumunda yine de yönlendir
                    window.location.href = "{{ $redirect_url }}";
                }
            }

            // Sayfa yüklendiğinde mesaj gönder
            window.onload = function() {
                // Hemen mesaj gönder
                sendMessageToParent();

                // 1 saniye sonra tekrar dene
                setTimeout(sendMessageToParent, 1000);

                // 2 saniye sonra tekrar dene
                setTimeout(sendMessageToParent, 2000);

                // 3 saniye sonra doğrudan yönlendir
                setTimeout(function() {
                    window.top.location.href = "{{ $redirect_url }}";
                }, 3000);
            };
        </script>

        <p><a href="{{ $redirect_url }}" class="btn"
                style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">Sipariş
                Detaylarına Git</a></p>
    </div>
</body>

</html>
