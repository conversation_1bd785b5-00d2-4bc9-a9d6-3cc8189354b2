<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $config['title'] ?? 'API Documentation' }}</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@4.15.5/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@4.15.5/favicon-16x16.png" sizes="16x16" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin: 0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #2c3e50;
        }
        .swagger-ui .topbar .download-url-wrapper {
            display: none;
        }
        .version-selector {
            position: fixed;
            top: 10px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .version-selector select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background: white;
        }
        .format-selector {
            position: fixed;
            top: 60px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .format-selector a {
            display: inline-block;
            margin: 0 5px;
            padding: 5px 10px;
            text-decoration: none;
            background: #3498db;
            color: white;
            border-radius: 3px;
            font-size: 12px;
        }
        .format-selector a:hover {
            background: #2980b9;
        }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .custom-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .api-info {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-info h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .api-info .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        .api-info .info-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .api-info .info-item strong {
            display: block;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .download-links {
            margin: 20px;
            text-align: center;
        }
        .download-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #27ae60;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .download-links a:hover {
            background: #219a52;
        }
    </style>
</head>
<body>
    <!-- Custom Header -->
    <div class="custom-header">
        <h1>{{ $config['title'] ?? 'API Documentation' }}</h1>
        <p>{{ $config['description'] ?? 'Interactive API Documentation' }}</p>
        <p><strong>Version:</strong> {{ $version }}</p>
    </div>

    <!-- API Information -->
    <div class="api-info">
        <h3>API Information</h3>
        <div class="info-grid">
            <div class="info-item">
                <strong>Current Version</strong>
                {{ $version }}
            </div>
            <div class="info-item">
                <strong>Available Versions</strong>
                {{ implode(', ', $available_versions) }}
            </div>
            @if(isset($config['contact']['email']))
            <div class="info-item">
                <strong>Contact</strong>
                <a href="mailto:{{ $config['contact']['email'] }}">{{ $config['contact']['email'] }}</a>
            </div>
            @endif
            @if(isset($config['license']['name']))
            <div class="info-item">
                <strong>License</strong>
                @if(isset($config['license']['url']))
                    <a href="{{ $config['license']['url'] }}" target="_blank">{{ $config['license']['name'] }}</a>
                @else
                    {{ $config['license']['name'] }}
                @endif
            </div>
            @endif
        </div>
    </div>

    <!-- Download Links -->
    <div class="download-links">
        <a href="{{ route('api.docs.download', ['version' => $version, 'format' => 'json']) }}">
            📄 Download JSON
        </a>
        <a href="{{ route('api.docs.download', ['version' => $version, 'format' => 'yaml']) }}">
            📄 Download YAML
        </a>
        <a href="{{ route('api.docs.postman', ['version' => $version]) }}">
            📦 Postman Collection
        </a>
        <a href="{{ route('api.docs.compare') }}?from={{ $version }}&to={{ $available_versions[array_key_last($available_versions)] ?? $version }}">
            🔍 Compare Versions
        </a>
    </div>

    <!-- Version Selector -->
    <div class="version-selector">
        <label for="version-select"><strong>API Version:</strong></label>
        <select id="version-select" onchange="changeVersion(this.value)">
            @foreach($available_versions as $v)
                <option value="{{ $v }}" {{ $v === $version ? 'selected' : '' }}>
                    v{{ $v }}
                </option>
            @endforeach
        </select>
    </div>

    <!-- Format Selector -->
    <div class="format-selector">
        <strong>View Format:</strong>
        <a href="{{ route('api.docs.index', ['version' => $version, 'format' => 'swagger-ui']) }}" 
           class="{{ $format === 'swagger-ui' ? 'active' : '' }}">Swagger UI</a>
        <a href="{{ route('api.docs.index', ['version' => $version, 'format' => 'redoc']) }}">ReDoc</a>
        <a href="{{ route('api.docs.index', ['version' => $version, 'format' => 'rapidoc']) }}">RapiDoc</a>
    </div>

    <!-- Swagger UI Container -->
    <div id="swagger-ui"></div>

    <!-- Swagger UI JavaScript -->
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // Initialize Swagger UI
            const ui = SwaggerUIBundle({
                url: '{{ $spec_url }}',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                tryItOutEnabled: {{ $config['try_it_out'] ? 'true' : 'false' }},
                requestInterceptor: function(request) {
                    // Add custom headers if needed
                    request.headers['X-Requested-With'] = 'SwaggerUI';
                    return request;
                },
                responseInterceptor: function(response) {
                    // Handle responses if needed
                    return response;
                },
                onComplete: function() {
                    console.log('Swagger UI loaded successfully');
                },
                onFailure: function(error) {
                    console.error('Failed to load Swagger UI:', error);
                },
                docExpansion: "{{ $config['doc_expansion'] ?? 'list' }}",
                apisSorter: "alpha",
                operationsSorter: "alpha",
                showExtensions: {{ $config['show_extensions'] ? 'true' : 'false' }},
                showCommonExtensions: {{ $config['show_common_extensions'] ? 'true' : 'false' }},
                defaultModelsExpandDepth: {{ $config['default_models_expand_depth'] ?? 1 }},
                defaultModelExpandDepth: {{ $config['default_model_expand_depth'] ?? 1 }},
                displayOperationId: {{ $config['display_operation_id'] ?? 'false' }},
                displayRequestDuration: {{ $config['display_request_duration'] ?? 'true' }},
                filter: {{ $config['filter'] ?? 'false' }},
                maxDisplayedTags: {{ $config['max_displayed_tags'] ?? -1 }},
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'],
                validatorUrl: null, // Disable validator
            });

            // Add custom CSS
            const style = document.createElement('style');
            style.textContent = `
                .swagger-ui .topbar { display: none; }
                .swagger-ui .info { margin-top: 0; }
                .swagger-ui .scheme-container { margin: 0; padding: 20px 0; }
            `;
            document.head.appendChild(style);
        };

        // Version change handler
        function changeVersion(version) {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('version', version);
            window.location.href = currentUrl.toString();
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+K to focus search
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('.swagger-ui .filter input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });

        // Add version comparison link
        function compareVersions() {
            const currentVersion = '{{ $version }}';
            const latestVersion = '{{ $available_versions[array_key_last($available_versions)] ?? $version }}';
            const compareUrl = '{{ route("api.docs.compare") }}?from=' + currentVersion + '&to=' + latestVersion;
            window.open(compareUrl, '_blank');
        }

        // Add refresh functionality
        function refreshDocs() {
            window.location.reload();
        }

        // Add export functionality
        function exportDocs(format) {
            const version = '{{ $version }}';
            const exportUrl = '{{ route("api.docs.download") }}?version=' + version + '&format=' + format;
            window.open(exportUrl, '_blank');
        }

        // Add health check
        function checkHealth() {
            fetch('{{ route("api.docs.health") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'healthy') {
                        alert('Documentation system is healthy ✅');
                    } else {
                        alert('Documentation system has issues ⚠️\n' + JSON.stringify(data.checks, null, 2));
                    }
                })
                .catch(error => {
                    alert('Failed to check health: ' + error.message);
                });
        }

        // Add console commands for developers
        console.log('🚀 API Documentation Console Commands:');
        console.log('- changeVersion(version): Change API version');
        console.log('- compareVersions(): Compare current version with latest');
        console.log('- refreshDocs(): Refresh documentation');
        console.log('- exportDocs(format): Export docs (json/yaml)');
        console.log('- checkHealth(): Check documentation system health');
    </script>
</body>
</html>
