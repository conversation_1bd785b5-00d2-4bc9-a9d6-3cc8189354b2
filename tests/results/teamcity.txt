##teamcity[testCount count='9' flowId='25192']
##teamcity[testSuiteStarted name='CLI Arguments' flowId='25192']
##teamcity[testSuiteStarted name='Tests\Unit\CQRS\CommandBusTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest' flowId='25192']
##teamcity[testStarted name='it_can_dispatch_command' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_can_dispatch_command' flowId='25192']
##teamcity[testFinished name='it_can_dispatch_command' duration='38' flowId='25192']
##teamcity[testStarted name='it_throws_exception_for_unregistered_command' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_throws_exception_for_unregistered_command' flowId='25192']
##teamcity[testFinished name='it_throws_exception_for_unregistered_command' duration='2' flowId='25192']
##teamcity[testStarted name='it_can_register_command_handler' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_can_register_command_handler' flowId='25192']
##teamcity[testFinished name='it_can_register_command_handler' duration='2' flowId='25192']
##teamcity[testStarted name='it_can_add_middleware' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\CommandBusTest.php::\Tests\Unit\CQRS\CommandBusTest::it_can_add_middleware' flowId='25192']
##teamcity[testFinished name='it_can_add_middleware' duration='2' flowId='25192']
##teamcity[testSuiteFinished name='Tests\Unit\CQRS\CommandBusTest' flowId='25192']
##teamcity[testSuiteStarted name='Tests\Unit\CQRS\QueryBusTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php::\Tests\Unit\CQRS\QueryBusTest' flowId='25192']
##teamcity[testStarted name='it_can_register_query_handler' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php::\Tests\Unit\CQRS\QueryBusTest::it_can_register_query_handler' flowId='25192']
##teamcity[testFinished name='it_can_register_query_handler' duration='3' flowId='25192']
##teamcity[testStarted name='it_can_dispatch_query' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php::\Tests\Unit\CQRS\QueryBusTest::it_can_dispatch_query' flowId='25192']
##teamcity[testFinished name='it_can_dispatch_query' duration='3' flowId='25192']
##teamcity[testStarted name='it_throws_exception_for_unregistered_query' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php::\Tests\Unit\CQRS\QueryBusTest::it_throws_exception_for_unregistered_query' flowId='25192']
##teamcity[testFinished name='it_throws_exception_for_unregistered_query' duration='2' flowId='25192']
##teamcity[testStarted name='it_can_cache_query_results' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php::\Tests\Unit\CQRS\QueryBusTest::it_can_cache_query_results' flowId='25192']
##teamcity[testFinished name='it_can_cache_query_results' duration='6' flowId='25192']
##teamcity[testStarted name='it_returns_cached_result_when_available' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\CQRS\QueryBusTest.php::\Tests\Unit\CQRS\QueryBusTest::it_returns_cached_result_when_available' flowId='25192']
##teamcity[testFinished name='it_returns_cached_result_when_available' duration='7' flowId='25192']
##teamcity[testSuiteFinished name='Tests\Unit\CQRS\QueryBusTest' flowId='25192']
##teamcity[testSuiteFinished name='CLI Arguments' flowId='25192']
