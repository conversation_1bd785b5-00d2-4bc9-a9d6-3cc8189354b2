<?php

namespace Tests\Unit;

use Tests\TestCase;
use Mockery;

/**
 * UnitTestCase
 * Unit test'ler için specialized base class
 */
abstract class UnitTestCase extends TestCase
{
    /**
     * Mock object'leri
     */
    protected array $mocks = [];

    /**
     * Unit test setup
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Unit test'ler için database'i disable et
        $this->disableDatabase();
        
        // External service'leri mock'la
        $this->mockAllExternalServices();
    }

    /**
     * Unit test teardown
     */
    protected function tearDown(): void
    {
        // Mock'ları temizle
        $this->clearAllMocks();
        
        parent::tearDown();
    }

    /**
     * Database'i disable et (unit test'ler için)
     */
    protected function disableDatabase(): void
    {
        // Database trait'ini kullanma
        $this->refreshDatabase = false;
    }

    /**
     * Mock object oluştur
     */
    protected function createMockObject(string $className): \Mockery\MockInterface
    {
        $mock = Mockery::mock($className);
        $this->mocks[] = $mock;

        return $mock;
    }

    /**
     * Partial mock oluştur
     */
    protected function createPartialMockObject(string $className, array $methods = []): \Mockery\MockInterface
    {
        $mock = Mockery::mock($className)->makePartial();
        
        foreach ($methods as $method => $return) {
            $mock->shouldReceive($method)->andReturn($return);
        }
        
        $this->mocks[] = $mock;
        
        return $mock;
    }

    /**
     * Spy oluştur
     */
    protected function createSpy(string $className): \Mockery\MockInterface
    {
        $spy = Mockery::spy($className);
        $this->mocks[] = $spy;
        
        return $spy;
    }

    /**
     * Interface mock'ı oluştur
     */
    protected function mockInterface(string $interfaceName): \Mockery\MockInterface
    {
        $mock = Mockery::mock($interfaceName);
        $this->mocks[] = $mock;
        
        // Interface'i container'a bind et
        $this->app->bind($interfaceName, function () use ($mock) {
            return $mock;
        });
        
        return $mock;
    }

    /**
     * Repository mock'ı oluştur
     */
    protected function mockRepository(string $repositoryInterface): \Mockery\MockInterface
    {
        return $this->mockInterface($repositoryInterface);
    }

    /**
     * Service mock'ı oluştur
     */
    protected function mockServiceObject(string $serviceClass): \Mockery\MockInterface
    {
        $mock = $this->createMockObject($serviceClass);

        // Service'i container'a bind et
        $this->app->bind($serviceClass, function () use ($mock) {
            return $mock;
        });

        return $mock;
    }

    /**
     * Value Object test helper'ı
     */
    protected function assertValueObjectEquality($valueObject1, $valueObject2): void
    {
        $this->assertTrue(
            $valueObject1->equals($valueObject2),
            'Value objects are not equal'
        );
    }

    /**
     * Value Object immutability test'i
     */
    protected function assertValueObjectImmutability($valueObject, string $method, ...$args): void
    {
        $original = clone $valueObject;
        $result = $valueObject->$method(...$args);
        
        // Original object değişmemeli
        $this->assertEquals($original, $valueObject);
        
        // Yeni object döndürülmeli
        $this->assertNotSame($valueObject, $result);
    }

    /**
     * Domain Entity test helper'ı
     */
    protected function assertDomainEntityBehavior($entity): void
    {
        // Entity'nin ID'si olmalı
        $this->assertNotNull($entity->getId());
        
        // Entity'nin string representation'ı olmalı
        $this->assertIsString((string) $entity);
        
        // Entity'nin equality check'i çalışmalı
        $sameEntity = clone $entity;
        $this->assertTrue($entity->equals($sameEntity));
    }

    /**
     * Domain Event test helper'ı
     */
    protected function assertDomainEventStructure($event, array $expectedProperties = []): void
    {
        // Event'in timestamp'i olmalı
        $this->assertObjectHasAttribute('occurredOn', $event);
        $this->assertInstanceOf(\DateTimeInterface::class, $event->occurredOn());
        
        // Event'in aggregate ID'si olmalı
        $this->assertObjectHasAttribute('aggregateId', $event);
        $this->assertNotNull($event->aggregateId());
        
        // Expected property'leri kontrol et
        foreach ($expectedProperties as $property) {
            $this->assertObjectHasAttribute($property, $event);
        }
    }

    /**
     * Use Case test helper'ı
     */
    protected function assertUseCaseExecution($useCase, $input, $expectedOutput = null): void
    {
        $result = $useCase->execute($input);
        
        if ($expectedOutput !== null) {
            $this->assertEquals($expectedOutput, $result);
        }
        
        // Use case'in side effect'leri kontrol et
        $this->assertUseCaseSideEffects($useCase);
    }

    /**
     * Use Case side effect'lerini kontrol et
     */
    protected function assertUseCaseSideEffects($useCase): void
    {
        // Override edilebilir method
        // Her use case kendi side effect'lerini test edebilir
    }

    /**
     * Exception test helper'ı
     */
    protected function assertExceptionThrown(string $exceptionClass, callable $callback, string $message = null): void
    {
        $this->expectException($exceptionClass);
        
        if ($message) {
            $this->expectExceptionMessage($message);
        }
        
        $callback();
    }

    /**
     * Method call assertion
     */
    protected function assertMethodCalled(\Mockery\MockInterface $mock, string $method, array $arguments = null, int $times = 1): void
    {
        if ($arguments !== null) {
            $mock->shouldHaveReceived($method)->with(...$arguments)->times($times);
        } else {
            $mock->shouldHaveReceived($method)->times($times);
        }
    }

    /**
     * Method not called assertion
     */
    protected function assertMethodNotCalled(\Mockery\MockInterface $mock, string $method): void
    {
        $mock->shouldNotHaveReceived($method);
    }

    /**
     * Tüm external service'leri mock'la
     */
    protected function mockAllExternalServices(): void
    {
        // Parent method'u çağır ama database interaction'ları disable et
        if (method_exists($this, 'mockExternalServices')) {
            $this->mockExternalServices();
        }
    }

    /**
     * Tüm mock'ları temizle
     */
    protected function clearAllMocks(): void
    {
        foreach ($this->mocks as $mock) {
            if ($mock instanceof \Mockery\MockInterface) {
                $mock->mockery_teardown();
            }
        }
        
        $this->mocks = [];
        Mockery::close();
    }

    /**
     * Performance test helper'ı
     */
    protected function assertExecutionTime(callable $callback, float $maxTime = 0.1): void
    {
        $startTime = microtime(true);
        $callback();
        $endTime = microtime(true);
        
        $executionTime = $endTime - $startTime;
        
        $this->assertLessThanOrEqual(
            $maxTime,
            $executionTime,
            "Execution took too long: {$executionTime}s (max: {$maxTime}s)"
        );
    }

    /**
     * Memory usage test helper'ı
     */
    protected function assertMemoryUsage(callable $callback, int $maxMemoryMB = 10): void
    {
        $startMemory = memory_get_usage(true);
        $callback();
        $endMemory = memory_get_usage(true);
        
        $memoryUsed = ($endMemory - $startMemory) / 1024 / 1024; // MB
        
        $this->assertLessThanOrEqual(
            $maxMemoryMB,
            $memoryUsed,
            "Memory usage too high: {$memoryUsed}MB (max: {$maxMemoryMB}MB)"
        );
    }
}
