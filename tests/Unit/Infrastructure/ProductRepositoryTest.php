<?php

namespace Tests\Unit\Infrastructure;

use Tests\TestCase;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Infrastructure\Products\Repositories\EloquentProductRepository;
use App\Models\Product as EloquentProduct;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ProductRepositoryInterface $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new EloquentProductRepository(new EloquentProduct());
    }

    /** @test */
    public function it_can_save_a_product()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        $sku = new SKU('TEST-001');
        $price = new Price(100.00, 'TRY');
        $stock = new Stock(10);

        $product = new Product(
            'Test Product',
            'test-product',
            $sku,
            $price,
            $stock,
            $category->id,
            'Test description',
            true,
            false
        );

        // Act
        $savedProduct = $this->repository->save($product);

        // Assert
        $this->assertInstanceOf(Product::class, $savedProduct);
        $this->assertNotNull($savedProduct->getId());
        $this->assertEquals('Test Product', $savedProduct->getName());
        $this->assertEquals('test-product', $savedProduct->getSlug());
        $this->assertEquals('TEST-001', $savedProduct->getSku()->getValue());
        $this->assertEquals(100.00, $savedProduct->getPrice()->getAmount());
        $this->assertEquals(10, $savedProduct->getStock()->getQuantity());
    }

    /** @test */
    public function it_can_find_product_by_id()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        $eloquentProduct = EloquentProduct::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test description',
            'stock_code' => 'TEST-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => false,
        ]);

        // Act
        $product = $this->repository->findById($eloquentProduct->id);

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals($eloquentProduct->id, $product->getId());
        $this->assertEquals('Test Product', $product->getName());
    }

    /** @test */
    public function it_returns_null_when_product_not_found()
    {
        // Act
        $product = $this->repository->findById(999);

        // Assert
        $this->assertNull($product);
    }

    /** @test */
    public function it_can_find_product_by_slug()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        $eloquentProduct = EloquentProduct::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test description',
            'stock_code' => 'TEST-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => false,
        ]);

        // Act
        $product = $this->repository->findBySlug('test-product');

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('test-product', $product->getSlug());
    }

    /** @test */
    public function it_can_find_product_by_sku()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        $eloquentProduct = EloquentProduct::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test description',
            'stock_code' => 'TEST-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => false,
        ]);

        $sku = new SKU('TEST-001');

        // Act
        $product = $this->repository->findBySKU($sku);

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('TEST-001', $product->getSku()->getValue());
    }

    /** @test */
    public function it_can_find_products_by_category()
    {
        // Arrange
        $category1 = Category::create([
            'name' => 'Category 1',
            'slug' => 'category-1',
            'status' => true,
        ]);

        $category2 = Category::create([
            'name' => 'Category 2',
            'slug' => 'category-2',
            'status' => true,
        ]);

        EloquentProduct::create([
            'name' => 'Product 1',
            'slug' => 'product-1',
            'description' => 'Description 1',
            'stock_code' => 'TEST-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category1->id,
            'status' => true,
            'is_featured' => false,
        ]);

        EloquentProduct::create([
            'name' => 'Product 2',
            'slug' => 'product-2',
            'description' => 'Description 2',
            'stock_code' => 'TEST-002',
            'price' => 200.00,
            'stock' => 5,
            'category_id' => $category1->id,
            'status' => true,
            'is_featured' => false,
        ]);

        EloquentProduct::create([
            'name' => 'Product 3',
            'slug' => 'product-3',
            'description' => 'Description 3',
            'stock_code' => 'TEST-003',
            'price' => 300.00,
            'stock' => 15,
            'category_id' => $category2->id,
            'status' => true,
            'is_featured' => false,
        ]);

        // Act
        $products = $this->repository->findByCategoryId($category1->id);

        // Assert
        $this->assertCount(2, $products);
        $this->assertContainsOnlyInstancesOf(Product::class, $products);
    }

    /** @test */
    public function it_can_find_active_products()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        EloquentProduct::create([
            'name' => 'Active Product',
            'slug' => 'active-product',
            'description' => 'Active description',
            'stock_code' => 'ACTIVE-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => false,
        ]);

        EloquentProduct::create([
            'name' => 'Inactive Product',
            'slug' => 'inactive-product',
            'description' => 'Inactive description',
            'stock_code' => 'INACTIVE-001',
            'price' => 200.00,
            'stock' => 5,
            'category_id' => $category->id,
            'status' => false,
            'is_featured' => false,
        ]);

        // Act
        $products = $this->repository->findActive();

        // Assert
        $this->assertCount(1, $products);
        $this->assertEquals('Active Product', $products[0]->getName());
    }

    /** @test */
    public function it_can_count_products()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        EloquentProduct::create([
            'name' => 'Product 1',
            'slug' => 'product-1',
            'description' => 'Description 1',
            'stock_code' => 'TEST-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => false,
        ]);

        EloquentProduct::create([
            'name' => 'Product 2',
            'slug' => 'product-2',
            'description' => 'Description 2',
            'stock_code' => 'TEST-002',
            'price' => 200.00,
            'stock' => 5,
            'category_id' => $category->id,
            'status' => false,
            'is_featured' => false,
        ]);

        // Act
        $count = $this->repository->count();

        // Assert
        $this->assertEquals(1, $count); // Only active products are counted
    }

    /** @test */
    public function it_can_check_if_product_exists()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        $eloquentProduct = EloquentProduct::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'Test description',
            'stock_code' => 'TEST-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => false,
        ]);

        // Act & Assert
        $this->assertTrue($this->repository->exists($eloquentProduct->id));
        $this->assertFalse($this->repository->exists(999));
    }

    /** @test */
    public function it_can_get_statistics()
    {
        // Arrange
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'status' => true,
        ]);

        EloquentProduct::create([
            'name' => 'Active Product',
            'slug' => 'active-product',
            'description' => 'Active description',
            'stock_code' => 'ACTIVE-001',
            'price' => 100.00,
            'stock' => 10,
            'category_id' => $category->id,
            'status' => true,
            'is_featured' => true,
        ]);

        EloquentProduct::create([
            'name' => 'Inactive Product',
            'slug' => 'inactive-product',
            'description' => 'Inactive description',
            'stock_code' => 'INACTIVE-001',
            'price' => 200.00,
            'stock' => 0,
            'category_id' => $category->id,
            'status' => false,
            'is_featured' => false,
        ]);

        // Act
        $stats = $this->repository->getStatistics();

        // Assert
        $this->assertIsArray($stats);
        $this->assertEquals(2, $stats['total']);
        $this->assertEquals(1, $stats['active']);
        $this->assertEquals(1, $stats['featured']);
        $this->assertEquals(1, $stats['in_stock']);
        $this->assertEquals(1, $stats['out_of_stock']);
    }
}
