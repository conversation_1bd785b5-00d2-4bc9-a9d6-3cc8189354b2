<?php

namespace Tests\Unit\Infrastructure\Shipping;

use Tests\TestCase;
use App\Infrastructure\Shipping\Repositories\EloquentShipmentRepository;
use App\Infrastructure\Shipping\Mappers\ShipmentMapper;
use App\Infrastructure\Shipping\Models\EloquentShipment;
use App\Infrastructure\Shipping\Models\EloquentCarrierIntegration;
use App\Domain\Shipping\Entities\Shipment;
use App\Domain\Shipping\ValueObjects\TrackingNumber;
use App\Domain\Shipping\ValueObjects\DeliveryStatus;
use App\Domain\Shipping\ValueObjects\ShippingAddress;
use App\Domain\Shipping\ValueObjects\PackageInfo;
use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

/**
 * EloquentShipmentRepositoryTest
 * EloquentShipmentRepository unit test'leri
 */
class EloquentShipmentRepositoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private EloquentShipmentRepository $repository;
    private ShipmentMapper $mapper;
    private EloquentCarrierIntegration $carrier;
    private Order $order;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mapper = new ShipmentMapper();
        $this->repository = new EloquentShipmentRepository($this->mapper);

        // Test data oluştur
        $this->createShippingTestData();
    }

    /** @test */
    public function it_can_save_and_retrieve_shipment()
    {
        // Arrange
        $shipment = $this->createTestShipment();

        // Act
        $this->repository->save($shipment);
        $retrievedShipment = $this->repository->findById($shipment->getId());

        // Assert
        $this->assertNotNull($retrievedShipment);
        $this->assertEquals($shipment->getOrderId(), $retrievedShipment->getOrderId());
        $this->assertEquals($shipment->getCarrierCode(), $retrievedShipment->getCarrierCode());
        $this->assertEquals($shipment->getServiceType(), $retrievedShipment->getServiceType());
        $this->assertEquals($shipment->getStatus()->getValue(), $retrievedShipment->getStatus()->getValue());
    }

    /** @test */
    public function it_can_find_shipment_by_shipment_number()
    {
        // Arrange
        $shipment = $this->createTestShipment();
        $this->repository->save($shipment);

        // Get shipment number from database
        $eloquentShipment = EloquentShipment::where('order_id', $shipment->getOrderId())->first();
        $shipmentNumber = $eloquentShipment->shipment_number;

        // Act
        $foundShipment = $this->repository->findByShipmentNumber($shipmentNumber);

        // Assert
        $this->assertNotNull($foundShipment);
        $this->assertEquals($shipment->getOrderId(), $foundShipment->getOrderId());
    }

    /** @test */
    public function it_can_find_shipment_by_tracking_number()
    {
        // Arrange
        $trackingNumber = new TrackingNumber('TEST123456789');
        $shipment = $this->createTestShipment(['tracking_number' => $trackingNumber]);
        $this->repository->save($shipment);

        // Act
        $foundShipment = $this->repository->findByTrackingNumber($trackingNumber);

        // Assert
        $this->assertNotNull($foundShipment);
        $this->assertEquals($trackingNumber->getValue(), $foundShipment->getTrackingNumber()->getValue());
    }

    /** @test */
    public function it_can_find_shipments_by_order_id()
    {
        // Arrange
        $shipment1 = $this->createTestShipment();
        $shipment2 = $this->createTestShipment(['service_type' => 'express']);
        
        $this->repository->save($shipment1);
        $this->repository->save($shipment2);

        // Act
        $shipments = $this->repository->findByOrderId($this->order->id);

        // Assert
        $this->assertCount(2, $shipments);
        $this->assertContainsOnlyInstancesOf(Shipment::class, $shipments);
    }

    /** @test */
    public function it_can_find_shipments_by_status()
    {
        // Arrange
        $pendingShipment = $this->createTestShipment(['status' => new DeliveryStatus('pending')]);
        $shippedShipment = $this->createTestShipment(['status' => new DeliveryStatus('shipped')]);
        
        $this->repository->save($pendingShipment);
        $this->repository->save($shippedShipment);

        // Act
        $pendingShipments = $this->repository->findByStatus(new DeliveryStatus('pending'));

        // Assert
        $this->assertCount(1, $pendingShipments);
        $this->assertEquals('pending', $pendingShipments[0]->getStatus()->getValue());
    }

    /** @test */
    public function it_can_find_shipments_by_carrier()
    {
        // Arrange
        $shipment = $this->createTestShipment();
        $this->repository->save($shipment);

        // Act
        $shipments = $this->repository->findByCarrier($this->carrier->carrier_code);

        // Assert
        $this->assertCount(1, $shipments);
        $this->assertEquals($this->carrier->carrier_code, $shipments[0]->getCarrierCode());
    }

    /** @test */
    public function it_can_find_shipments_by_date_range()
    {
        // Arrange
        $startDate = Carbon::now()->subDays(7);
        $endDate = Carbon::now();
        
        $shipment = $this->createTestShipment();
        $this->repository->save($shipment);

        // Act
        $shipments = $this->repository->findByDateRange($startDate, $endDate);

        // Assert
        $this->assertCount(1, $shipments);
        $this->assertInstanceOf(Shipment::class, $shipments[0]);
    }

    /** @test */
    public function it_can_find_pending_shipments()
    {
        // Arrange
        $pendingShipment = $this->createTestShipment(['status' => new DeliveryStatus('pending')]);
        $processingShipment = $this->createTestShipment(['status' => new DeliveryStatus('processing')]);
        $deliveredShipment = $this->createTestShipment(['status' => new DeliveryStatus('delivered')]);
        
        $this->repository->save($pendingShipment);
        $this->repository->save($processingShipment);
        $this->repository->save($deliveredShipment);

        // Act
        $pendingShipments = $this->repository->findPendingShipments();

        // Assert
        $this->assertCount(2, $pendingShipments); // pending + processing
        foreach ($pendingShipments as $shipment) {
            $this->assertContains($shipment->getStatus()->getValue(), ['pending', 'processing']);
        }
    }

    /** @test */
    public function it_can_find_in_transit_shipments()
    {
        // Arrange
        $shippedShipment = $this->createTestShipment(['status' => new DeliveryStatus('shipped')]);
        $inTransitShipment = $this->createTestShipment(['status' => new DeliveryStatus('in_transit')]);
        $outForDeliveryShipment = $this->createTestShipment(['status' => new DeliveryStatus('out_for_delivery')]);
        $deliveredShipment = $this->createTestShipment(['status' => new DeliveryStatus('delivered')]);
        
        $this->repository->save($shippedShipment);
        $this->repository->save($inTransitShipment);
        $this->repository->save($outForDeliveryShipment);
        $this->repository->save($deliveredShipment);

        // Act
        $inTransitShipments = $this->repository->findInTransitShipments();

        // Assert
        $this->assertCount(3, $inTransitShipments); // shipped + in_transit + out_for_delivery
        foreach ($inTransitShipments as $shipment) {
            $this->assertContains($shipment->getStatus()->getValue(), ['shipped', 'in_transit', 'out_for_delivery']);
        }
    }

    /** @test */
    public function it_can_find_delayed_shipments()
    {
        // Arrange
        $delayedShipment = $this->createTestShipment([
            'status' => new DeliveryStatus('in_transit'),
            'estimated_delivery_at' => Carbon::now()->subDays(1) // Past due
        ]);
        $onTimeShipment = $this->createTestShipment([
            'status' => new DeliveryStatus('in_transit'),
            'estimated_delivery_at' => Carbon::now()->addDays(1) // Future
        ]);
        
        $this->repository->save($delayedShipment);
        $this->repository->save($onTimeShipment);

        // Act
        $delayedShipments = $this->repository->findDelayedShipments();

        // Assert
        $this->assertCount(1, $delayedShipments);
        $this->assertEquals('in_transit', $delayedShipments[0]->getStatus()->getValue());
    }

    /** @test */
    public function it_can_get_shipment_statistics()
    {
        // Arrange
        $pendingShipment = $this->createTestShipment(['status' => new DeliveryStatus('pending')]);
        $shippedShipment = $this->createTestShipment(['status' => new DeliveryStatus('shipped')]);
        $deliveredShipment = $this->createTestShipment(['status' => new DeliveryStatus('delivered')]);
        
        $this->repository->save($pendingShipment);
        $this->repository->save($shippedShipment);
        $this->repository->save($deliveredShipment);

        // Act
        $statistics = $this->repository->getStatistics();

        // Assert
        $this->assertIsArray($statistics);
        $this->assertArrayHasKey('total_shipments', $statistics);
        $this->assertArrayHasKey('pending_count', $statistics);
        $this->assertArrayHasKey('shipped_count', $statistics);
        $this->assertArrayHasKey('delivered_count', $statistics);
        $this->assertArrayHasKey('delivery_success_rate', $statistics);
        
        $this->assertEquals(3, $statistics['total_shipments']);
        $this->assertEquals(1, $statistics['pending_count']);
        $this->assertEquals(1, $statistics['shipped_count']);
        $this->assertEquals(1, $statistics['delivered_count']);
    }

    /** @test */
    public function it_can_paginate_shipments()
    {
        // Arrange
        for ($i = 0; $i < 25; $i++) {
            $shipment = $this->createTestShipment();
            $this->repository->save($shipment);
        }

        // Act
        $page1 = $this->repository->paginate(1, 10);
        $page2 = $this->repository->paginate(2, 10);

        // Assert
        $this->assertIsArray($page1);
        $this->assertArrayHasKey('data', $page1);
        $this->assertArrayHasKey('total', $page1);
        $this->assertArrayHasKey('per_page', $page1);
        $this->assertArrayHasKey('current_page', $page1);
        
        $this->assertCount(10, $page1['data']);
        $this->assertEquals(25, $page1['total']);
        $this->assertEquals(1, $page1['current_page']);
        
        $this->assertCount(10, $page2['data']);
        $this->assertEquals(2, $page2['current_page']);
    }

    /** @test */
    public function it_can_search_shipments()
    {
        // Arrange
        $trackingNumber = 'SEARCH123456';
        $shipment = $this->createTestShipment([
            'tracking_number' => new TrackingNumber($trackingNumber)
        ]);
        $this->repository->save($shipment);

        // Act
        $results = $this->repository->search($trackingNumber);

        // Assert
        $this->assertIsArray($results);
        $this->assertCount(1, $results);
        $this->assertInstanceOf(Shipment::class, $results[0]);
        $this->assertEquals($trackingNumber, $results[0]->getTrackingNumber()->getValue());
    }

    /** @test */
    public function it_can_delete_shipment()
    {
        // Arrange
        $shipment = $this->createTestShipment();
        $this->repository->save($shipment);
        $shipmentId = $shipment->getId();

        // Act
        $this->repository->delete($shipment);
        $deletedShipment = $this->repository->findById($shipmentId);

        // Assert
        $this->assertNull($deletedShipment);
    }

    /** @test */
    public function it_can_save_batch_shipments()
    {
        // Arrange
        $shipments = [
            $this->createTestShipment(['service_type' => 'standard']),
            $this->createTestShipment(['service_type' => 'express']),
            $this->createTestShipment(['service_type' => 'overnight'])
        ];

        // Act
        $this->repository->saveBatch($shipments);

        // Assert
        foreach ($shipments as $shipment) {
            $savedShipment = $this->repository->findById($shipment->getId());
            $this->assertNotNull($savedShipment);
        }
    }

    /**
     * Test data oluştur
     */
    protected function createShippingTestData(): void
    {
        // User oluştur
        $user = User::factory()->create();

        // Order oluştur
        $this->order = Order::factory()->create([
            'user_id' => $user->id
        ]);

        // Carrier integration oluştur
        $this->carrier = EloquentCarrierIntegration::create([
            'carrier_code' => 'test_carrier',
            'carrier_name' => 'Test Carrier',
            'integration_type' => 'api',
            'api_endpoint' => 'https://api.testcarrier.com',
            'is_active' => true,
            'supports_tracking' => true,
            'supports_rate_calculation' => true,
            'supports_label_generation' => true,
            'priority' => 10
        ]);
    }

    /**
     * Test shipment entity oluştur
     */
    private function createTestShipment(array $overrides = []): Shipment
    {
        $defaults = [
            'tracking_number' => null,
            'service_type' => 'standard',
            'status' => new DeliveryStatus('pending'),
            'estimated_delivery_at' => null
        ];

        $data = array_merge($defaults, $overrides);

        // Addresses
        $originAddress = new ShippingAddress(
            'Test Sender',
            'Test Company',
            '123 Test Street',
            null,
            'Istanbul',
            'Istanbul',
            '34000',
            'TR',
            '+905551234567',
            '<EMAIL>'
        );

        $destinationAddress = new ShippingAddress(
            'Test Recipient',
            'Test Recipient Company',
            '456 Test Avenue',
            null,
            'Ankara',
            'Ankara',
            '06000',
            'TR',
            '+905559876543',
            '<EMAIL>'
        );

        // Package info
        $packageInfo = new PackageInfo(
            2.5, // weight
            'kg',
            30, // length
            20, // width
            15, // height
            'cm',
            'box'
        );

        $shipment = Shipment::create(
            $this->order->id,
            $this->carrier->carrier_code,
            $originAddress,
            $destinationAddress,
            $packageInfo,
            $data['service_type'],
            $data['status']
        );

        // Set tracking number if provided
        if ($data['tracking_number']) {
            $reflection = new \ReflectionClass($shipment);
            $prop = $reflection->getProperty('trackingNumber');
            $prop->setAccessible(true);
            $prop->setValue($shipment, $data['tracking_number']);
        }

        // Set estimated delivery if provided
        if ($data['estimated_delivery_at']) {
            $reflection = new \ReflectionClass($shipment);
            $prop = $reflection->getProperty('estimatedDeliveryAt');
            $prop->setAccessible(true);
            $prop->setValue($shipment, $data['estimated_delivery_at']);
        }

        return $shipment;
    }
}
