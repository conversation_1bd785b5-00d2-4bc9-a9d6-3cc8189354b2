<?php

namespace Tests\Unit\Infrastructure\Categories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Domain\Categories\Repositories\CategoryRepositoryInterface;
use App\Infrastructure\Categories\Repositories\EloquentCategoryRepository;
use App\Infrastructure\Categories\Repositories\CacheCategoryRepository;
use App\Infrastructure\Categories\Services\CategoryTreeService;
use App\Infrastructure\Categories\Services\CategoryBulkService;
use App\Domain\Categories\Entities\Category;
use App\Domain\Categories\ValueObjects\CategorySlug;

/**
 * Category Infrastructure Test
 * Categories modülü infrastructure katmanını test eder
 */
class CategoryInfrastructureTest extends TestCase
{
    use RefreshDatabase;

    private CategoryRepositoryInterface $repository;
    private CategoryTreeService $treeService;
    private CategoryBulkService $bulkService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = app(CategoryRepositoryInterface::class);
        $this->treeService = app(CategoryTreeService::class);
        $this->bulkService = app(CategoryBulkService::class);
    }

    /** @test */
    public function it_can_resolve_category_repository_from_container()
    {
        $this->assertInstanceOf(CategoryRepositoryInterface::class, $this->repository);

        // Cache decorator kullanılıyorsa
        if (config('cache.default') !== 'array') {
            $this->assertInstanceOf(CacheCategoryRepository::class, $this->repository);
        } else {
            $this->assertInstanceOf(EloquentCategoryRepository::class, $this->repository);
        }
    }

    /** @test */
    public function it_can_resolve_category_services_from_container()
    {
        $this->assertInstanceOf(CategoryTreeService::class, $this->treeService);
        $this->assertInstanceOf(CategoryBulkService::class, $this->bulkService);
    }

    /** @test */
    public function repository_can_save_and_retrieve_category()
    {
        // Test category oluştur
        $category = $this->createTestCategory();

        // Kaydet
        $savedCategory = $this->repository->save($category);

        $this->assertNotNull($savedCategory->getId());
        $this->assertEquals($category->getName(), $savedCategory->getName());
        $this->assertEquals($category->getSlug()->getValue(), $savedCategory->getSlug()->getValue());

        // Geri al
        $retrievedCategory = $this->repository->findById($savedCategory->getId());

        $this->assertNotNull($retrievedCategory);
        $this->assertEquals($savedCategory->getId(), $retrievedCategory->getId());
        $this->assertEquals($savedCategory->getName(), $retrievedCategory->getName());
    }

    /** @test */
    public function repository_can_find_category_by_slug()
    {
        $category = $this->createTestCategory();
        $savedCategory = $this->repository->save($category);

        $foundCategory = $this->repository->findBySlug($category->getSlug());

        $this->assertNotNull($foundCategory);
        $this->assertEquals($savedCategory->getId(), $foundCategory->getId());
    }

    /** @test */
    public function repository_can_find_root_categories()
    {
        // Root kategoriler oluştur
        $rootCategory1 = $this->createTestCategory('Electronics Category', null);
        $rootCategory2 = $this->createTestCategory('Clothing Category', null);

        $this->repository->save($rootCategory1);
        $this->repository->save($rootCategory2);

        $rootCategories = $this->repository->findRootCategories();

        $this->assertCount(2, $rootCategories);
    }

    /** @test */
    public function repository_can_find_categories_by_parent()
    {
        // Parent kategori oluştur
        $parentCategory = $this->createTestCategory('Parent Category', null);
        $savedParent = $this->repository->save($parentCategory);

        // Child kategoriler oluştur
        $childCategory1 = $this->createTestCategory('Child Category 1', $savedParent->getId());
        $childCategory2 = $this->createTestCategory('Child Category 2', $savedParent->getId());

        $this->repository->save($childCategory1);
        $this->repository->save($childCategory2);

        $childCategories = $this->repository->findByParentId($savedParent->getId());

        $this->assertCount(2, $childCategories);
    }

    /** @test */
    public function repository_can_check_existence()
    {
        $category = $this->createTestCategory();
        $savedCategory = $this->repository->save($category);

        $this->assertTrue($this->repository->exists($savedCategory->getId()));
        $this->assertTrue($this->repository->existsBySlug($category->getSlug()));

        $this->assertFalse($this->repository->exists(99999));
        $this->assertFalse($this->repository->existsBySlug(new CategorySlug('nonexistent-slug')));
    }

    /** @test */
    public function repository_can_check_children_and_products()
    {
        $category = $this->createTestCategory();
        $savedCategory = $this->repository->save($category);

        // Başlangıçta children ve product yok
        $this->assertFalse($this->repository->hasChildren($savedCategory->getId()));
        $this->assertFalse($this->repository->hasProducts($savedCategory->getId()));
    }

    /** @test */
    public function repository_can_get_category_level()
    {
        // Root kategori
        $rootCategory = $this->createTestCategory('Electronics', null);
        $savedRoot = $this->repository->save($rootCategory);

        // Child kategori
        $childCategory = $this->createTestCategory('Computers', $savedRoot->getId());
        $savedChild = $this->repository->save($childCategory);

        $this->assertEquals(0, $this->repository->getLevel($savedRoot->getId()));
        $this->assertEquals(1, $this->repository->getLevel($savedChild->getId()));
    }

    /** @test */
    public function repository_can_delete_category()
    {
        $category = $this->createTestCategory();
        $savedCategory = $this->repository->save($category);

        $this->assertTrue($this->repository->exists($savedCategory->getId()));

        $deleted = $this->repository->delete($savedCategory);

        $this->assertTrue($deleted);
        $this->assertFalse($this->repository->exists($savedCategory->getId()));
    }

    /** @test */
    public function tree_service_can_get_breadcrumb()
    {
        // Hierarchical kategoriler oluştur
        $rootCategory = $this->createTestCategory('Electronics', null);
        $savedRoot = $this->repository->save($rootCategory);

        $childCategory = $this->createTestCategory('Computers', $savedRoot->getId());
        $savedChild = $this->repository->save($childCategory);

        $breadcrumb = $this->treeService->getBreadcrumb($savedChild->getId());

        $this->assertIsArray($breadcrumb);
        $this->assertGreaterThan(0, count($breadcrumb));
    }

    /** @test */
    public function tree_service_can_get_tree_statistics()
    {
        // Test kategorileri oluştur
        $category1 = $this->createTestCategory('Category 1', null);
        $category2 = $this->createTestCategory('Category 2', null);

        $this->repository->save($category1);
        $this->repository->save($category2);

        $stats = $this->treeService->getTreeStatistics();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_categories', $stats);
        $this->assertArrayHasKey('active_categories', $stats);
        $this->assertArrayHasKey('max_depth', $stats);
        $this->assertArrayHasKey('tree_health', $stats);
    }

    /** @test */
    public function tree_service_can_check_tree_health()
    {
        $health = $this->treeService->checkTreeHealth();

        $this->assertIsArray($health);
        $this->assertArrayHasKey('healthy', $health);
        $this->assertArrayHasKey('issues', $health);
        $this->assertArrayHasKey('total_issues', $health);
        $this->assertIsBool($health['healthy']);
    }

    /** @test */
    public function bulk_service_can_update_status()
    {
        // Test kategorileri oluştur
        $category1 = $this->createTestCategory('Category 1');
        $category2 = $this->createTestCategory('Category 2');

        $saved1 = $this->repository->save($category1);
        $saved2 = $this->repository->save($category2);

        $categoryIds = [$saved1->getId(), $saved2->getId()];

        $result = $this->bulkService->bulkUpdateStatus($categoryIds, false);

        $this->assertTrue($result);
    }

    /** @test */
    public function bulk_service_can_recalculate_product_counts()
    {
        // Test kategorisi oluştur
        $category = $this->createTestCategory();
        $savedCategory = $this->repository->save($category);

        $result = $this->bulkService->recalculateProductCounts([$savedCategory->getId()]);

        $this->assertTrue($result);
    }

    /**
     * Test category oluştur
     */
    private function createTestCategory(
        string $name = 'Test Category',
        ?int $parentId = null
    ): Category {
        return Category::create(
            name: $name,
            slug: new CategorySlug(str($name)->slug()),
            parentId: $parentId,
            description: 'Test category description',
            position: 0,
            status: true,
            featured: false,
            showInMenu: true
        );
    }
}
