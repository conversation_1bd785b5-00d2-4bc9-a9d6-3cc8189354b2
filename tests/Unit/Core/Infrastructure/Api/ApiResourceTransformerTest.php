<?php

namespace Tests\Unit\Core\Infrastructure\Api;

use Tests\TestCase;
use App\Core\Infrastructure\Api\Services\ApiResourceTransformer;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class ApiResourceTransformerTest extends TestCase
{
    private ApiResourceTransformer $transformer;

    protected function setUp(): void
    {
        parent::setUp();
        $this->transformer = new ApiResourceTransformer();
    }

    /** @test */
    public function it_can_transform_array_data()
    {
        $data = ['id' => 1, 'name' => 'Test', 'email' => '<EMAIL>'];

        $result = $this->transformer->transform($data);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('name', $result);
        $this->assertArrayHasKey('email', $result);
    }

    /** @test */
    public function it_can_transform_collection_data()
    {
        $collection = collect([
            ['id' => 1, 'name' => 'Item 1'],
            ['id' => 2, 'name' => 'Item 2'],
            ['id' => 3, 'name' => 'Item 3']
        ]);

        $result = $this->transformer->transformCollection($collection);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertEquals(['id' => 1, 'name' => 'Item 1'], $result[0]);
    }

    /** @test */
    public function it_can_transform_object_with_to_array_method()
    {
        $object = new class {
            public function toArray() {
                return ['id' => 1, 'name' => 'Test Object'];
            }
        };

        $result = $this->transformer->transform($object);

        $this->assertEquals(['id' => 1, 'name' => 'Test Object'], $result);
    }

    /** @test */
    public function it_can_transform_carbon_dates()
    {
        $date = Carbon::parse('2023-01-01 12:00:00');

        $result = $this->transformer->transform($date);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('value', $result);
        $this->assertIsString($result['value']);
        $this->assertStringContains('2023-01-01T12:00:00', $result['value']);
    }

    /** @test */
    public function it_can_transform_datetime_objects()
    {
        $date = new \DateTime('2023-01-01 12:00:00');

        $result = $this->transformer->transform($date);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('value', $result);
        $this->assertIsString($result['value']);
        $this->assertStringContains('2023-01-01T12:00:00', $result['value']);
    }

    /** @test */
    public function it_can_set_and_get_includes()
    {
        $includes = ['user', 'category', 'tags'];

        $this->transformer->setIncludes($includes);

        $this->assertEquals($includes, $this->transformer->getIncludes());
    }

    /** @test */
    public function it_can_set_and_get_excludes()
    {
        $excludes = ['password', 'secret_key', 'internal_id'];

        $this->transformer->setExcludes($excludes);

        $this->assertEquals($excludes, $this->transformer->getExcludes());
    }

    /** @test */
    public function it_can_set_and_get_context()
    {
        $context = ['user_id' => 123, 'role' => 'admin'];

        $this->transformer->setContext($context);

        $this->assertEquals($context, $this->transformer->getContext());
    }

    /** @test */
    public function it_applies_includes_correctly()
    {
        $data = [
            'id' => 1,
            'name' => 'Test',
            'email' => '<EMAIL>',
            'password' => 'secret',
            'created_at' => '2023-01-01'
        ];

        $this->transformer->setIncludes(['id', 'name', 'email']);

        $result = $this->transformer->transform($data);

        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('name', $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertArrayNotHasKey('password', $result);
        $this->assertArrayNotHasKey('created_at', $result);
    }

    /** @test */
    public function it_applies_excludes_correctly()
    {
        $data = [
            'id' => 1,
            'name' => 'Test',
            'email' => '<EMAIL>',
            'password' => 'secret',
            'created_at' => '2023-01-01'
        ];

        $this->transformer->setExcludes(['password', 'created_at']);

        $result = $this->transformer->transform($data);

        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('name', $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertArrayNotHasKey('password', $result);
        $this->assertArrayNotHasKey('created_at', $result);
    }

    /** @test */
    public function it_can_check_if_data_can_be_transformed()
    {
        $this->assertTrue($this->transformer->canTransform(['data']));
        $this->assertTrue($this->transformer->canTransform(collect(['data'])));
        $this->assertTrue($this->transformer->canTransform('string'));
        $this->assertFalse($this->transformer->canTransform(null));
    }

    /** @test */
    public function it_can_get_and_set_transform_type()
    {
        $this->assertEquals('default', $this->transformer->getTransformType());

        $this->transformer->setTransformType('custom');

        $this->assertEquals('custom', $this->transformer->getTransformType());
    }

    /** @test */
    public function it_transforms_meta_data_correctly()
    {
        $meta = ['custom_field' => 'custom_value'];

        $result = $this->transformer->transformMeta($meta);

        $this->assertArrayHasKey('custom_field', $result);
        $this->assertArrayHasKey('timestamp', $result);
        $this->assertArrayHasKey('timezone', $result);
        $this->assertArrayHasKey('locale', $result);
        $this->assertEquals('custom_value', $result['custom_field']);
    }

    /** @test */
    public function it_transforms_error_data_correctly()
    {
        $message = 'Validation failed';
        $errors = [
            'name' => ['Name is required'],
            'email' => ['Email is required', 'Email must be valid']
        ];
        $meta = ['request_id' => '123'];

        $result = $this->transformer->transformError($message, $errors, $meta);

        $this->assertEquals($message, $result['message']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertArrayHasKey('meta', $result);
        $this->assertEquals(['Name is required'], $result['errors']['name']);
        $this->assertEquals(['Email is required', 'Email must be valid'], $result['errors']['email']);
    }

    /** @test */
    public function it_transforms_success_data_correctly()
    {
        $data = ['id' => 1, 'name' => 'Test'];
        $message = 'Success';
        $meta = ['total' => 1];

        $result = $this->transformer->transformSuccess($data, $message, $meta);

        $this->assertEquals($data, $result['data']);
        $this->assertEquals($message, $result['message']);
        $this->assertArrayHasKey('meta', $result);
        $this->assertEquals(1, $result['meta']['total']);
    }

    /** @test */
    public function it_transforms_nested_data()
    {
        $data = [
            'user' => ['id' => 1, 'name' => 'John'],
            'posts' => [
                ['id' => 1, 'title' => 'Post 1'],
                ['id' => 2, 'title' => 'Post 2']
            ]
        ];

        $result = $this->transformer->transformNested($data, 'user');

        $this->assertEquals(['id' => 1, 'name' => 'John'], $result);
    }

    /** @test */
    public function it_transforms_conditionally()
    {
        $data = ['id' => 1, 'name' => 'Test'];

        $resultTrue = $this->transformer->transformWhen(true, $data, 'default');
        $resultFalse = $this->transformer->transformWhen(false, $data, 'default');

        $this->assertEquals($data, $resultTrue);
        $this->assertEquals('default', $resultFalse);
    }

    /** @test */
    public function it_transforms_with_custom_callback()
    {
        $data = ['name' => 'john doe'];

        $result = $this->transformer->transformWith(function ($transformed) {
            $transformed['name'] = strtoupper($transformed['name']);
            return $transformed;
        }, $data);

        $this->assertEquals('JOHN DOE', $result['name']);
    }

    /** @test */
    public function it_handles_empty_collections()
    {
        $emptyCollection = collect([]);

        $result = $this->transformer->transformCollection($emptyCollection);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /** @test */
    public function it_handles_null_data()
    {
        $result = $this->transformer->transform(null);

        $this->assertEquals([], $result);
    }

    /** @test */
    public function it_includes_context_in_meta_when_set()
    {
        $context = ['user_id' => 123, 'role' => 'admin'];
        $this->transformer->setContext($context);

        $result = $this->transformer->transformMeta();

        $this->assertArrayHasKey('context', $result);
        $this->assertEquals($context, $result['context']);
    }
}
