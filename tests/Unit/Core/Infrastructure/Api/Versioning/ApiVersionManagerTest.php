<?php

namespace Tests\Unit\Core\Infrastructure\Api\Versioning;

use App\Core\Infrastructure\Api\Versioning\Services\ApiVersionManager;
use Illuminate\Http\Request;
use Tests\TestCase;
use Mockery;

/**
 * ApiVersionManagerTest
 * API version manager için unit test'ler
 */
class ApiVersionManagerTest extends TestCase
{
    private ApiVersionManager $versionManager;
    private array $testConfig;

    protected function setUp(): void
    {
        parent::setUp();

        $this->testConfig = [
            'default_version' => '1.0',
            'latest_version' => '1.1',
            'supported_versions' => [
                '1.0' => [
                    'status' => 'stable',
                    'released_at' => '2024-01-01',
                    'deprecated_at' => null,
                    'sunset_at' => null,
                ],
                '1.1' => [
                    'status' => 'stable',
                    'released_at' => '2024-06-01',
                    'deprecated_at' => null,
                    'sunset_at' => null,
                ],
                '0.9' => [
                    'status' => 'deprecated',
                    'released_at' => '2023-01-01',
                    'deprecated_at' => '2024-01-01',
                    'sunset_at' => '2024-12-31',
                ],
            ],
            'cache' => ['enabled' => false],
        ];

        $this->versionManager = new ApiVersionManager($this->testConfig);
    }

    /** @test */
    public function it_can_detect_version_from_header()
    {
        $request = Request::create('/api/test', 'GET');
        $request->headers->set('X-API-Version', '1.1');

        $version = $this->versionManager->detectVersion($request);

        $this->assertEquals('1.1', $version);
    }

    /** @test */
    public function it_returns_default_version_when_no_version_specified()
    {
        $request = Request::create('/api/test', 'GET');

        $version = $this->versionManager->detectVersion($request);

        $this->assertEquals('1.0', $version);
    }

    /** @test */
    public function it_normalizes_version_correctly()
    {
        $this->assertEquals('1.0', $this->versionManager->normalizeVersion('1'));
        $this->assertEquals('1.0', $this->versionManager->normalizeVersion('v1.0'));
        $this->assertEquals('1.1', $this->versionManager->normalizeVersion(' 1.1 '));
    }

    /** @test */
    public function it_validates_supported_versions()
    {
        $this->assertTrue($this->versionManager->isVersionSupported('1.0'));
        $this->assertTrue($this->versionManager->isVersionSupported('1.1'));
        $this->assertFalse($this->versionManager->isVersionSupported('2.0'));
    }

    /** @test */
    public function it_detects_deprecated_versions()
    {
        $this->assertTrue($this->versionManager->isVersionDeprecated('0.9'));
        $this->assertFalse($this->versionManager->isVersionDeprecated('1.0'));
        $this->assertFalse($this->versionManager->isVersionDeprecated('1.1'));
    }

    /** @test */
    public function it_compares_versions_correctly()
    {
        $this->assertEquals(-1, $this->versionManager->compareVersions('1.0', '1.1'));
        $this->assertEquals(0, $this->versionManager->compareVersions('1.0', '1.0'));
        $this->assertEquals(1, $this->versionManager->compareVersions('1.1', '1.0'));
    }

    /** @test */
    public function it_returns_correct_version_info()
    {
        $info = $this->versionManager->getVersionInfo('1.0');

        $this->assertIsArray($info);
        $this->assertEquals('stable', $info['status']);
        $this->assertEquals('2024-01-01', $info['released_at']);
    }

    /** @test */
    public function it_returns_supported_versions_list()
    {
        $versions = $this->versionManager->getSupportedVersions();

        $this->assertIsArray($versions);
        $this->assertContains('1.0', $versions);
        $this->assertContains('1.1', $versions);
        $this->assertContains('0.9', $versions);
    }

    /** @test */
    public function it_generates_deprecation_warning()
    {
        $warning = $this->versionManager->getDeprecationWarning('0.9');

        $this->assertIsString($warning);
        $this->assertStringContainsString('deprecated', $warning);
        $this->assertStringContainsString('0.9', $warning);
    }

    /** @test */
    public function it_validates_version_format()
    {
        $this->assertTrue($this->versionManager->validateVersion('1.0'));
        $this->assertTrue($this->versionManager->validateVersion('2.5'));
        $this->assertFalse($this->versionManager->validateVersion('invalid'));
        $this->assertFalse($this->versionManager->validateVersion('1.0.0'));
    }

    /** @test */
    public function it_can_add_and_retrieve_strategies()
    {
        $mockStrategy = Mockery::mock(\App\Core\Infrastructure\Api\Versioning\Contracts\VersionStrategyInterface::class);
        $mockStrategy->shouldReceive('getName')->andReturn('test');

        $this->versionManager->addStrategy('test', $mockStrategy);

        $retrievedStrategy = $this->versionManager->getStrategy('test');
        $this->assertSame($mockStrategy, $retrievedStrategy);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
