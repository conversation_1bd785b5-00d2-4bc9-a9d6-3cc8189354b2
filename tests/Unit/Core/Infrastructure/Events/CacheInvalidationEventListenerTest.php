<?php

namespace Tests\Unit\Core\Infrastructure\Events;

use Tests\TestCase;
use Mockery;
use App\Core\Infrastructure\Events\Listeners\CacheInvalidationEventListener;
use App\Core\Infrastructure\Cache\Contracts\CacheKeyGeneratorInterface;
use App\Core\Infrastructure\Cache\Contracts\CacheTagManagerInterface;
use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Products\Events\ProductCreated;
use App\Domain\Products\Entities\Product;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class CacheInvalidationEventListenerTest extends TestCase
{
    private CacheInvalidationEventListener $listener;
    private $keyGenerator;
    private $tagManager;

    protected function setUp(): void
    {
        parent::setUp();

        $this->keyGenerator = Mockery::mock(CacheKeyGeneratorInterface::class);
        $this->tagManager = Mockery::mock(CacheTagManagerInterface::class);

        $this->listener = new CacheInvalidationEventListener(
            $this->keyGenerator,
            $this->tagManager
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_handle_domain_events()
    {
        $event = $this->createMockDomainEvent();

        $this->assertTrue($this->listener->canHandle($event));
    }

    /** @test */
    public function it_returns_cache_tags_to_invalidate()
    {
        $event = $this->createMockDomainEvent();

        $this->tagManager->shouldReceive('getEntityTags')
            ->with('product', '123')
            ->andReturn(['product_123', 'products']);

        $this->tagManager->shouldReceive('getGlobalTags')
            ->with('product')
            ->andReturn(['products', 'catalog']);

        // Related entity tags için de mock ekle
        $this->tagManager->shouldReceive('getGlobalTags')
            ->with('category')
            ->andReturn(['categories']);

        $tags = $this->listener->getCacheTagsToInvalidate($event);

        $this->assertIsArray($tags);
        $this->assertContains('product_123', $tags);
        $this->assertContains('products', $tags);
        $this->assertContains('catalog', $tags);
    }

    /** @test */
    public function it_returns_cache_keys_to_invalidate()
    {
        $event = $this->createMockDomainEvent();

        $this->keyGenerator->shouldReceive('generateEntityKey')
            ->with('product', '123')
            ->andReturn('product:entity:123');

        // Query key'ler için mock'lar
        $this->keyGenerator->shouldReceive('generateQueryKey')
            ->andReturn('product:query:key');

        $this->keyGenerator->shouldReceive('generateListKey')
            ->andReturn('product:list:criteria_hash');

        $this->keyGenerator->shouldReceive('generateStatsKey')
            ->andReturn('product:stats:count');

        $keys = $this->listener->getCacheKeysToInvalidate($event);

        $this->assertIsArray($keys);
        $this->assertContains('product:entity:123', $keys);
    }

    /** @test */
    public function it_determines_invalidation_strategy()
    {
        $event = $this->createMockDomainEvent();

        $strategy = $this->listener->getInvalidationStrategy($event);

        $this->assertIsString($strategy);
        $this->assertContains($strategy, ['immediate', 'async', 'batch', 'lazy']);
    }

    /** @test */
    public function it_handles_immediate_invalidation()
    {
        // Cache facade mock'unu düzelt
        Cache::shouldReceive('forget')->atLeast()->once();

        $event = $this->createMockDomainEvent();

        $this->tagManager->shouldReceive('getEntityTags')->andReturn(['product_123']);
        $this->tagManager->shouldReceive('getGlobalTags')->with('product')->andReturn(['products']);
        $this->tagManager->shouldReceive('getGlobalTags')->with('category')->andReturn(['categories']);
        $this->tagManager->shouldReceive('clearByTags')->with(Mockery::type('array'))->once();

        $this->keyGenerator->shouldReceive('generateEntityKey')->andReturn('product:entity:123');
        $this->keyGenerator->shouldReceive('generateQueryKey')->andReturn('product:query:key');
        $this->keyGenerator->shouldReceive('generateListKey')->andReturn('product:list:hash');
        $this->keyGenerator->shouldReceive('generateStatsKey')->andReturn('product:stats:count');

        $this->listener->handle($event);

        $this->assertTrue(true); // Test passed if no exceptions
    }

    /** @test */
    public function it_checks_if_bulk_invalidation_required()
    {
        $event = $this->createMockDomainEvent('category.updated');

        $requiresBulk = $this->listener->requiresBulkInvalidation($event);

        $this->assertTrue($requiresBulk);
    }

    /** @test */
    public function it_checks_if_related_entities_should_be_invalidated()
    {
        $event = $this->createMockDomainEvent();

        $shouldInvalidateRelated = $this->listener->shouldInvalidateRelatedEntities($event);

        $this->assertIsBool($shouldInvalidateRelated);
    }

    /** @test */
    public function it_checks_if_async_invalidation_needed()
    {
        $event = $this->createMockDomainEvent();

        $shouldInvalidateAsync = $this->listener->shouldInvalidateAsync($event);

        $this->assertIsBool($shouldInvalidateAsync);
    }

    /** @test */
    public function it_checks_if_cache_warming_needed()
    {
        $event = $this->createMockDomainEvent();

        $shouldWarmCache = $this->listener->shouldWarmCache($event);

        $this->assertIsBool($shouldWarmCache);
    }

    /** @test */
    public function it_has_correct_priority()
    {
        $this->assertEquals(10, $this->listener->getPriority());
    }

    /** @test */
    public function it_is_enabled_by_default()
    {
        $this->assertTrue($this->listener->isEnabled());
    }

    /** @test */
    public function it_can_be_disabled()
    {
        $this->listener->setEnabled(false);
        $this->assertFalse($this->listener->isEnabled());
    }

    /** @test */
    public function it_tracks_stats()
    {
        $stats = $this->listener->getStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('handled', $stats);
        $this->assertArrayHasKey('failed', $stats);
        $this->assertArrayHasKey('skipped', $stats);
    }

    /** @test */
    public function it_handles_errors_gracefully()
    {
        $event = $this->createMockDomainEvent();

        // Mock an exception during cache invalidation
        $this->tagManager->shouldReceive('getEntityTags')->andThrow(new \Exception('Cache error'));
        $this->tagManager->shouldReceive('getGlobalTags')->andReturn([]);
        $this->keyGenerator->shouldReceive('generateEntityKey')->andReturn('key');
        $this->keyGenerator->shouldReceive('generateListKey')->andReturn('list_key');
        $this->keyGenerator->shouldReceive('generateStatsKey')->andReturn('stats_key');

        // Should not throw exception
        $this->listener->handle($event);

        $stats = $this->listener->getStats();
        $this->assertEquals(1, $stats['failed']);
    }

    private function createMockDomainEvent(string $eventName = 'product.created'): DomainEvent
    {
        $event = Mockery::mock(DomainEvent::class);
        $event->shouldReceive('getEventName')->andReturn($eventName);
        $event->shouldReceive('getAggregateId')->andReturn('123');
        $event->shouldReceive('getAggregateType')->andReturn('Product');
        $event->shouldReceive('occurredOn')->andReturn(Carbon::now());
        $event->shouldReceive('getEventData')->andReturn([
            'product_id' => '123',
            'name' => 'Test Product',
            'category_id' => '456',
            'is_featured' => true,
            'price' => 99.99,
            'status' => 'active'
        ]);

        return $event;
    }
}
