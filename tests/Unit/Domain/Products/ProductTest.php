<?php

namespace Tests\Unit\Domain\Products;

use Tests\Unit\UnitTestCase;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\Events\ProductCreated;
use Tests\Builders\ProductBuilder;

/**
 * ProductTest
 * Product domain entity unit test
 */
class ProductTest extends UnitTestCase
{
    /**
     * Test product creation
     */
    public function test_it_can_create_a_product(): void
    {
        // Arrange
        $productData = ProductBuilder::create()
            ->withName('Test Product')
            ->withPrice(100.00)
            ->withSku('TEST-SKU-001')
            ->getData();

        // Act
        $product = new Product(
            $productData['name'],
            new Price($productData['price'], 'TRY'),
            new SKU($productData['sku'])
        );

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('Test Product', $product->getName());
        $this->assertEquals(100.00, $product->getPrice()->getAmount());
        $this->assertEquals('TEST-SKU-001', $product->getSku()->getValue());
    }

    /**
     * Test product domain behavior
     */
    public function test_it_has_proper_domain_entity_behavior(): void
    {
        // Arrange & Act
        $product = ProductBuilder::create()->build();

        // Assert
        $this->assertDomainEntityBehavior($product);
    }

    /**
     * Test product price change
     */
    public function test_it_can_change_price(): void
    {
        // Arrange
        $product = ProductBuilder::create()
            ->withPrice(100.00)
            ->build();

        $newPrice = new Price(150.00, 'TRY');

        // Act
        $product->changePrice($newPrice);

        // Assert
        $this->assertEquals(150.00, $product->getPrice()->getAmount());
    }

    /**
     * Test product stock management
     */
    public function test_it_can_manage_stock(): void
    {
        // Arrange
        $product = ProductBuilder::create()
            ->withStock(10)
            ->build();

        // Act & Assert - Increase stock
        $product->increaseStock(5);
        $this->assertEquals(15, $product->getStock());

        // Act & Assert - Decrease stock
        $product->decreaseStock(3);
        $this->assertEquals(12, $product->getStock());
    }

    /**
     * Test product stock cannot go negative
     */
    public function test_it_cannot_have_negative_stock(): void
    {
        // Arrange
        $product = ProductBuilder::create()
            ->withStock(5)
            ->build();

        // Act & Assert
        $this->assertExceptionThrown(
            \DomainException::class,
            fn() => $product->decreaseStock(10),
            'Stock cannot be negative'
        );
    }

    /**
     * Test product activation
     */
    public function test_it_can_be_activated_and_deactivated(): void
    {
        // Arrange
        $product = ProductBuilder::create()
            ->inactive()
            ->build();

        // Act & Assert - Activate
        $product->activate();
        $this->assertTrue($product->isActive());

        // Act & Assert - Deactivate
        $product->deactivate();
        $this->assertFalse($product->isActive());
    }

    /**
     * Test product featured status
     */
    public function test_it_can_be_featured(): void
    {
        // Arrange
        $product = ProductBuilder::create()
            ->notFeatured()
            ->build();

        // Act
        $product->markAsFeatured();

        // Assert
        $this->assertTrue($product->isFeatured());
    }

    /**
     * Test product domain events
     */
    public function test_it_dispatches_domain_events(): void
    {
        // Arrange
        $product = ProductBuilder::create()->build();

        // Act
        $product->recordEvent(new ProductCreated($product));

        // Assert
        $this->assertDomainEventStructure(
            new ProductCreated($product),
            ['aggregateId', 'occurredOn']
        );
    }

    /**
     * Test product equality
     */
    public function test_it_can_check_equality(): void
    {
        // Arrange
        $product1 = ProductBuilder::create()
            ->withName('Test Product')
            ->withSku('TEST-001')
            ->build();

        $product2 = ProductBuilder::create()
            ->withName('Test Product')
            ->withSku('TEST-001')
            ->build();

        $product3 = ProductBuilder::create()
            ->withName('Different Product')
            ->withSku('TEST-002')
            ->build();

        // Act & Assert
        $this->assertTrue($product1->equals($product2));
        $this->assertFalse($product1->equals($product3));
    }

    /**
     * Test product validation
     */
    public function test_it_validates_required_fields(): void
    {
        // Test empty name
        $this->assertExceptionThrown(
            \InvalidArgumentException::class,
            fn() => new Product('', new Price(100, 'TRY'), new SKU('TEST-001')),
            'Product name cannot be empty'
        );

        // Test invalid price
        $this->assertExceptionThrown(
            \InvalidArgumentException::class,
            fn() => new Product('Test', new Price(-10, 'TRY'), new SKU('TEST-001')),
            'Price cannot be negative'
        );

        // Test invalid SKU
        $this->assertExceptionThrown(
            \InvalidArgumentException::class,
            fn() => new Product('Test', new Price(100, 'TRY'), new SKU('')),
            'SKU cannot be empty'
        );
    }

    /**
     * Test product categories
     */
    public function test_it_can_have_categories(): void
    {
        // Arrange
        $product = ProductBuilder::create()->build();
        $categoryId = 1;

        // Act
        $product->assignToCategory($categoryId);

        // Assert
        $this->assertEquals($categoryId, $product->getCategoryId());
        $this->assertTrue($product->hasCategory());
    }

    /**
     * Test product variants
     */
    public function test_it_can_have_variants(): void
    {
        // Arrange
        $product = ProductBuilder::create()->build();
        $variant = [
            'name' => 'Size: Large',
            'sku' => 'TEST-001-L',
            'price' => 110.00,
            'stock' => 5,
        ];

        // Act
        $product->addVariant($variant);

        // Assert
        $this->assertTrue($product->hasVariants());
        $this->assertCount(1, $product->getVariants());
    }

    /**
     * Test product attributes
     */
    public function test_it_can_have_attributes(): void
    {
        // Arrange
        $product = ProductBuilder::create()->build();
        $attributes = [
            'color' => 'Red',
            'material' => 'Cotton',
            'brand' => 'Test Brand',
        ];

        // Act
        foreach ($attributes as $name => $value) {
            $product->addAttribute($name, $value);
        }

        // Assert
        $this->assertEquals('Red', $product->getAttribute('color'));
        $this->assertEquals('Cotton', $product->getAttribute('material'));
        $this->assertEquals('Test Brand', $product->getAttribute('brand'));
    }

    /**
     * Test product SEO data
     */
    public function test_it_can_have_seo_data(): void
    {
        // Arrange
        $product = ProductBuilder::create()->build();
        $seoData = [
            'meta_title' => 'Test Product - Best Quality',
            'meta_description' => 'High quality test product for testing purposes',
            'meta_keywords' => 'test, product, quality',
        ];

        // Act
        $product->setSeoData($seoData);

        // Assert
        $this->assertEquals($seoData['meta_title'], $product->getMetaTitle());
        $this->assertEquals($seoData['meta_description'], $product->getMetaDescription());
        $this->assertEquals($seoData['meta_keywords'], $product->getMetaKeywords());
    }

    /**
     * Test product performance
     */
    public function test_product_operations_are_performant(): void
    {
        // Test product creation performance
        $this->assertExecutionTime(function () {
            for ($i = 0; $i < 100; $i++) {
                ProductBuilder::create()->build();
            }
        }, 0.1); // Should create 100 products in less than 0.1 seconds

        // Test product method calls performance
        $product = ProductBuilder::create()->build();
        
        $this->assertExecutionTime(function () use ($product) {
            for ($i = 0; $i < 1000; $i++) {
                $product->getName();
                $product->getPrice();
                $product->getSku();
                $product->isActive();
            }
        }, 0.01); // Should handle 1000 method calls in less than 0.01 seconds
    }

    /**
     * Test product memory usage
     */
    public function test_product_memory_usage_is_optimal(): void
    {
        $this->assertMemoryUsage(function () {
            $products = [];
            for ($i = 0; $i < 1000; $i++) {
                $products[] = ProductBuilder::create()->build();
            }
        }, 10); // Should use less than 10MB for 1000 products
    }
}
