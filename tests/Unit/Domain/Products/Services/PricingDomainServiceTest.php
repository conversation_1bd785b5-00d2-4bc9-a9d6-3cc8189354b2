<?php

namespace Tests\Unit\Domain\Products\Services;

use Tests\TestCase;
use App\Domain\Products\Services\PricingDomainService;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\Entities\ProductVariant;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use Carbon\Carbon;
use Mockery;

class PricingDomainServiceTest extends TestCase
{
    private PricingDomainService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new PricingDomainService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_calculates_current_price_without_sale()
    {
        // Arrange
        $product = $this->createTestProduct();

        // Act
        $currentPrice = $this->service->calculateCurrentPrice($product);

        // Assert
        $this->assertEquals(100.0, $currentPrice->getAmount());
        $this->assertEquals('TRY', $currentPrice->getCurrency());
    }

    /** @test */
    public function it_calculates_current_price_with_active_sale()
    {
        // Arrange
        $product = $this->createTestProduct();
        $salePrice = Price::fromAmount(80.0, 'TRY');
        $product->setSale($salePrice, Carbon::now()->subDay(), Carbon::now()->addDay());

        // Act
        $currentPrice = $this->service->calculateCurrentPrice($product);

        // Assert
        $this->assertEquals(80.0, $currentPrice->getAmount());
    }

    /** @test */
    public function it_calculates_variant_price()
    {
        // Arrange
        $basePrice = Price::fromAmount(100.0, 'TRY');
        $variant = $this->createTestVariant();

        // Act
        $variantPrice = $this->service->calculateVariantPrice($basePrice, $variant);

        // Assert
        $this->assertEquals(120.0, $variantPrice->getAmount()); // 100 + 20
    }

    /** @test */
    public function it_calculates_discount_percentage()
    {
        // Arrange
        $product = $this->createTestProduct();
        $salePrice = Price::fromAmount(80.0, 'TRY');
        $product->setSale($salePrice, Carbon::now()->subDay(), Carbon::now()->addDay());

        // Act
        $discountPercentage = $this->service->calculateDiscountPercentage($product);

        // Assert
        $this->assertEquals(20.0, $discountPercentage); // (100-80)/100 * 100 = 20%
    }

    /** @test */
    public function it_calculates_discount_amount()
    {
        // Arrange
        $product = $this->createTestProduct();
        $salePrice = Price::fromAmount(80.0, 'TRY');
        $product->setSale($salePrice, Carbon::now()->subDay(), Carbon::now()->addDay());

        // Act
        $discountAmount = $this->service->calculateDiscountAmount($product);

        // Assert
        $this->assertEquals(20.0, $discountAmount->getAmount()); // 100 - 80 = 20
    }

    /** @test */
    public function it_calculates_bulk_price_with_discount()
    {
        // Arrange
        $product = $this->createTestProduct();
        $quantity = 25; // %5 indirim alacak

        // Act
        $bulkPrice = $this->service->calculateBulkPrice($product, $quantity);

        // Assert
        $expectedUnitPrice = 100.0 * 0.95; // %5 indirim
        $expectedTotalPrice = $expectedUnitPrice * $quantity;
        $this->assertEquals($expectedTotalPrice, $bulkPrice->getAmount());
    }

    /** @test */
    public function it_calculates_price_with_tax()
    {
        // Arrange
        $price = Price::fromAmount(100.0, 'TRY');
        $taxRate = 0.18; // %18 KDV

        // Act
        $priceWithTax = $this->service->calculatePriceWithTax($price, $taxRate);

        // Assert
        $this->assertEquals(118.0, $priceWithTax->getAmount()); // 100 + (100 * 0.18)
    }

    /** @test */
    public function it_calculates_tax_amount()
    {
        // Arrange
        $price = Price::fromAmount(100.0, 'TRY');
        $taxRate = 0.18;

        // Act
        $taxAmount = $this->service->calculateTaxAmount($price, $taxRate);

        // Assert
        $this->assertEquals(18.0, $taxAmount->getAmount());
    }

    /** @test */
    public function it_compares_prices_correctly()
    {
        // Arrange
        $price1 = Price::fromAmount(100.0, 'TRY');
        $price2 = Price::fromAmount(150.0, 'TRY');

        // Act & Assert
        $this->assertEquals(-1, $this->service->comparePrices($price1, $price2)); // price1 < price2
        $this->assertEquals(1, $this->service->comparePrices($price2, $price1));  // price2 > price1
        $this->assertEquals(0, $this->service->comparePrices($price1, $price1));  // price1 == price1
    }

    /** @test */
    public function it_finds_lowest_price()
    {
        // Arrange
        $prices = [
            Price::fromAmount(150.0, 'TRY'),
            Price::fromAmount(100.0, 'TRY'),
            Price::fromAmount(200.0, 'TRY'),
        ];

        // Act
        $lowestPrice = $this->service->findLowestPrice($prices);

        // Assert
        $this->assertEquals(100.0, $lowestPrice->getAmount());
    }

    /** @test */
    public function it_finds_highest_price()
    {
        // Arrange
        $prices = [
            Price::fromAmount(150.0, 'TRY'),
            Price::fromAmount(100.0, 'TRY'),
            Price::fromAmount(200.0, 'TRY'),
        ];

        // Act
        $highestPrice = $this->service->findHighestPrice($prices);

        // Assert
        $this->assertEquals(200.0, $highestPrice->getAmount());
    }

    /** @test */
    public function it_calculates_average_price()
    {
        // Arrange
        $prices = [
            Price::fromAmount(100.0, 'TRY'),
            Price::fromAmount(150.0, 'TRY'),
            Price::fromAmount(200.0, 'TRY'),
        ];

        // Act
        $averagePrice = $this->service->calculateAveragePrice($prices);

        // Assert
        $this->assertEquals(150.0, $averagePrice->getAmount()); // (100 + 150 + 200) / 3
    }

    /** @test */
    public function it_checks_if_price_is_in_range()
    {
        // Arrange
        $price = Price::fromAmount(150.0, 'TRY');
        $minPrice = Price::fromAmount(100.0, 'TRY');
        $maxPrice = Price::fromAmount(200.0, 'TRY');

        // Act & Assert
        $this->assertTrue($this->service->isPriceInRange($price, $minPrice, $maxPrice));
        
        $outOfRangePrice = Price::fromAmount(250.0, 'TRY');
        $this->assertFalse($this->service->isPriceInRange($outOfRangePrice, $minPrice, $maxPrice));
    }

    private function createTestProduct(): Product
    {
        return Product::create(
            'Test Ürün',
            'test-urun',
            SKU::fromString('TEST-001'),
            Price::fromAmount(100.0, 'TRY'),
            Stock::fromQuantity(10),
            1
        );
    }

    private function createTestVariant(): ProductVariant
    {
        return ProductVariant::create(
            1, // product_id
            SKU::fromString('TEST-001-VAR'),
            ['color' => 'red', 'size' => 'L'],
            Price::fromAmount(20.0, 'TRY'), // additional price
            Stock::fromQuantity(5)
        );
    }
}
