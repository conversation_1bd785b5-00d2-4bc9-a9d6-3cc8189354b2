<?php

namespace Tests\Unit\Domain\Payment\ValueObjects;

use Tests\TestCase;
use App\Domain\Payment\ValueObjects\PaymentStatus;

class PaymentStatusTest extends TestCase
{
    public function test_can_create_pending_status()
    {
        $status = PaymentStatus::pending();
        
        $this->assertEquals(PaymentStatus::PENDING, $status->getValue());
        $this->assertTrue($status->isPending());
        $this->assertTrue($status->isInitial());
        $this->assertFalse($status->isFinal());
    }

    public function test_can_create_completed_status()
    {
        $status = PaymentStatus::completed();
        
        $this->assertEquals(PaymentStatus::COMPLETED, $status->getValue());
        $this->assertTrue($status->isCompleted());
        $this->assertTrue($status->isFinal());
        $this->assertTrue($status->isSuccessful());
    }

    public function test_can_create_failed_status_with_reason()
    {
        $reason = 'Insufficient funds';
        $status = PaymentStatus::failed($reason);
        
        $this->assertEquals(PaymentStatus::FAILED, $status->getValue());
        $this->assertEquals($reason, $status->getReason());
        $this->assertTrue($status->isFailed());
        $this->assertFalse($status->isSuccessful());
    }

    public function test_can_transition_from_pending_to_processing()
    {
        $status = PaymentStatus::pending();
        
        $this->assertTrue($status->canTransitionTo(PaymentStatus::PROCESSING));
        
        $newStatus = $status->transitionTo(PaymentStatus::PROCESSING, 'Gateway processing');
        
        $this->assertEquals(PaymentStatus::PROCESSING, $newStatus->getValue());
        $this->assertEquals('Gateway processing', $newStatus->getReason());
    }

    public function test_cannot_transition_from_completed_to_pending()
    {
        $status = PaymentStatus::completed();
        
        $this->assertFalse($status->canTransitionTo(PaymentStatus::PENDING));
        
        $this->expectException(\DomainException::class);
        $status->transitionTo(PaymentStatus::PENDING);
    }

    public function test_can_get_allowed_transitions()
    {
        $status = PaymentStatus::pending();
        $allowedTransitions = $status->getAllowedTransitions();
        
        $this->assertContains(PaymentStatus::PROCESSING, $allowedTransitions);
        $this->assertContains(PaymentStatus::COMPLETED, $allowedTransitions);
        $this->assertContains(PaymentStatus::FAILED, $allowedTransitions);
        $this->assertContains(PaymentStatus::CANCELLED, $allowedTransitions);
    }

    public function test_can_get_status_category()
    {
        $this->assertEquals(PaymentStatus::CATEGORY_INITIAL, PaymentStatus::pending()->getCategory());
        $this->assertEquals(PaymentStatus::CATEGORY_PROCESSING, PaymentStatus::processing()->getCategory());
        $this->assertEquals(PaymentStatus::CATEGORY_FINAL, PaymentStatus::completed()->getCategory());
        $this->assertEquals(PaymentStatus::CATEGORY_FAILED, PaymentStatus::failed('test')->getCategory());
    }

    public function test_can_get_display_name()
    {
        $this->assertEquals('Beklemede', PaymentStatus::pending()->getDisplayName());
        $this->assertEquals('İşleniyor', PaymentStatus::processing()->getDisplayName());
        $this->assertEquals('Tamamlandı', PaymentStatus::completed()->getDisplayName());
        $this->assertEquals('Başarısız', PaymentStatus::failed('test')->getDisplayName());
    }

    public function test_can_check_refund_status()
    {
        $refunded = PaymentStatus::refunded();
        $partiallyRefunded = PaymentStatus::partiallyRefunded();
        
        $this->assertTrue($refunded->isRefunded());
        $this->assertTrue($partiallyRefunded->isRefunded());
        $this->assertTrue($refunded->isSuccessful());
        $this->assertTrue($partiallyRefunded->isSuccessful());
    }

    public function test_can_check_disputed_status()
    {
        $disputed = PaymentStatus::disputed('Customer complaint');
        $chargeback = PaymentStatus::chargeback('Bank chargeback');
        
        $this->assertTrue($disputed->isDisputed());
        $this->assertTrue($chargeback->isDisputed());
        $this->assertTrue($disputed->isFailed());
        $this->assertTrue($chargeback->isFailed());
    }

    public function test_invalid_status_throws_exception()
    {
        $this->expectException(\InvalidArgumentException::class);
        PaymentStatus::fromString('invalid_status');
    }

    public function test_can_convert_to_array()
    {
        $status = PaymentStatus::completed('Payment successful');
        $array = $status->toArray();
        
        $this->assertEquals(PaymentStatus::COMPLETED, $array['value']);
        $this->assertEquals('Tamamlandı', $array['display_name']);
        $this->assertEquals(PaymentStatus::CATEGORY_FINAL, $array['category']);
        $this->assertEquals('Payment successful', $array['reason']);
        $this->assertTrue($array['is_final']);
        $this->assertTrue($array['is_successful']);
        $this->assertFalse($array['is_failed']);
    }

    public function test_can_convert_to_string()
    {
        $status = PaymentStatus::pending();
        $this->assertEquals(PaymentStatus::PENDING, (string) $status);
    }

    public function test_equals_method_works_correctly()
    {
        $status1 = PaymentStatus::pending();
        $status2 = PaymentStatus::pending();
        $status3 = PaymentStatus::completed();
        
        $this->assertTrue($status1->equals($status2));
        $this->assertFalse($status1->equals($status3));
    }

    public function test_can_create_expired_status()
    {
        $status = PaymentStatus::expired('Payment timeout');
        
        $this->assertEquals(PaymentStatus::EXPIRED, $status->getValue());
        $this->assertTrue($status->isExpired());
        $this->assertTrue($status->isFailed());
        $this->assertEquals('Payment timeout', $status->getReason());
    }

    public function test_can_retry_from_failed_status()
    {
        $status = PaymentStatus::failed('Network error');
        
        $this->assertTrue($status->canTransitionTo(PaymentStatus::PENDING));
        
        $retryStatus = $status->transitionTo(PaymentStatus::PENDING, 'Retry payment');
        
        $this->assertEquals(PaymentStatus::PENDING, $retryStatus->getValue());
        $this->assertEquals('Retry payment', $retryStatus->getReason());
    }

    public function test_can_get_all_statuses()
    {
        $allStatuses = PaymentStatus::getAllStatuses();
        
        $this->assertContains(PaymentStatus::PENDING, $allStatuses);
        $this->assertContains(PaymentStatus::PROCESSING, $allStatuses);
        $this->assertContains(PaymentStatus::COMPLETED, $allStatuses);
        $this->assertContains(PaymentStatus::FAILED, $allStatuses);
        $this->assertContains(PaymentStatus::CANCELLED, $allStatuses);
        $this->assertContains(PaymentStatus::REFUNDED, $allStatuses);
        $this->assertContains(PaymentStatus::PARTIALLY_REFUNDED, $allStatuses);
        $this->assertContains(PaymentStatus::EXPIRED, $allStatuses);
        $this->assertContains(PaymentStatus::DISPUTED, $allStatuses);
        $this->assertContains(PaymentStatus::CHARGEBACK, $allStatuses);
    }
}
