<?php

namespace Tests\Unit\Domain\Shared\Rules\Pricing;

use Tests\TestCase;
use App\Domain\Shared\Rules\Pricing\PricingPolicyService;
use App\Domain\Shared\Rules\Pricing\QuantityDiscountRule;
use App\Domain\Shared\Rules\Pricing\TaxCalculationRule;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Core\Domain\ValueObjects\Money;

class PricingPolicyServiceTest extends TestCase
{
    private PricingPolicyService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new PricingPolicyService();
    }

    /**
     * Test service initialization
     */
    public function test_service_initializes_correctly(): void
    {
        $this->assertEquals('pricing_policy_service', $this->service->getName());
        $this->assertEquals('1.0.0', $this->service->getVersion());
        $this->assertTrue($this->service->supportsOperation('calculate_price'));
        $this->assertTrue($this->service->supportsOperation('register_rule'));
    }

    /**
     * Test rule registration
     */
    public function test_can_register_pricing_rules(): void
    {
        $quantityRule = QuantityDiscountRule::standard();
        $taxRule = TaxCalculationRule::turkishVAT();

        $this->service->registerRule($quantityRule);
        $this->service->registerRule($taxRule);

        $rules = $this->service->getAllRules();
        $this->assertCount(2, $rules);
    }

    /**
     * Test price calculation with quantity discount
     */
    public function test_calculates_price_with_quantity_discount(): void
    {
        // Arrange
        $product = $this->createTestProduct();
        $basePrice = Money::fromAmount(100, 'TRY');
        $quantity = 10; // 10% indirim almalı

        $quantityRule = QuantityDiscountRule::standard();
        $this->service->registerRule($quantityRule);

        // Act
        $result = $this->service->calculatePrice($product, $basePrice, [
            'quantity' => $quantity
        ]);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($basePrice, $result->getBasePrice());

        // 10 adet için %10 indirim = 90 TRY
        $expectedPrice = Money::fromAmount(90, 'TRY');
        $this->assertTrue($result->getFinalPrice()->equals($expectedPrice));
        $this->assertTrue($result->hasDiscounts());
    }

    /**
     * Test price calculation with tax
     */
    public function test_calculates_price_with_tax(): void
    {
        // Arrange
        $product = $this->createTestProduct();
        $basePrice = Money::fromAmount(100, 'TRY');

        $taxRule = TaxCalculationRule::turkishVAT();
        $this->service->registerRule($taxRule);

        // Act
        $result = $this->service->calculatePrice($product, $basePrice, []);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($basePrice, $result->getBasePrice());

        // %18 KDV = 118 TRY
        $expectedPrice = Money::fromAmount(118, 'TRY');
        $this->assertTrue($result->getFinalPrice()->equals($expectedPrice));
        $this->assertTrue($result->hasTaxes());
    }

    /**
     * Test price calculation with multiple rules
     */
    public function test_calculates_price_with_multiple_rules(): void
    {
        // Arrange
        $product = $this->createTestProduct();
        $basePrice = Money::fromAmount(100, 'TRY');
        $quantity = 10;

        $quantityRule = QuantityDiscountRule::standard();
        $taxRule = TaxCalculationRule::turkishVAT();

        $this->service->registerRule($quantityRule);
        $this->service->registerRule($taxRule);

        // Act
        $result = $this->service->calculatePrice($product, $basePrice, [
            'quantity' => $quantity
        ]);

        // Assert
        $this->assertNotNull($result);
        $this->assertEquals($basePrice, $result->getBasePrice());

        // Önce %10 indirim (90 TRY), sonra %18 KDV (106.2 TRY)
        $expectedPrice = Money::fromAmount(106.2, 'TRY');
        $this->assertTrue($result->getFinalPrice()->equals($expectedPrice));
        $this->assertTrue($result->hasDiscounts());
        $this->assertTrue($result->hasTaxes());
    }

    /**
     * Test pricing validation
     */
    public function test_validates_pricing(): void
    {
        // Arrange
        $product = $this->createTestProduct();
        $proposedPrice = Money::fromAmount(120, 'TRY');

        $taxRule = TaxCalculationRule::turkishVAT();
        $this->service->registerRule($taxRule);

        // Act
        $result = $this->service->validatePricing($product, $proposedPrice, []);

        // Assert
        $this->assertNotNull($result);
        $this->assertTrue($result->isValid());
        $this->assertEquals($proposedPrice, $result->getProposedPrice());

        // Hesaplanan fiyat %18 KDV ile 141.6 TRY olmalı (120 + 120*0.18)
        $expectedCalculatedPrice = Money::fromAmount(141.6, 'TRY');
        $this->assertTrue($result->getCalculatedPrice()->equals($expectedCalculatedPrice));
    }

    /**
     * Test standard rules loading
     */
    public function test_loads_standard_rules(): void
    {
        // Act
        $this->service->loadStandardRules();

        // Assert
        $rules = $this->service->getAllRules();
        $this->assertCount(2, $rules); // Quantity discount + Turkish VAT

        $rulesByPriority = $this->service->getRulesByPriority();
        $this->assertCount(2, $rulesByPriority);
    }

    /**
     * Test rule unregistration
     */
    public function test_can_unregister_rules(): void
    {
        // Arrange
        $quantityRule = QuantityDiscountRule::standard();
        $this->service->registerRule($quantityRule);

        $rules = $this->service->getAllRules();
        $this->assertCount(1, $rules);

        $ruleId = array_keys($rules)[0];

        // Act
        $result = $this->service->unregisterRule($ruleId);

        // Assert
        $this->assertTrue($result);
        $this->assertCount(0, $this->service->getAllRules());
    }

    /**
     * Test health check
     */
    public function test_health_check_passes_with_rules(): void
    {
        // Arrange
        $this->service->loadStandardRules();

        // Act & Assert
        $this->assertTrue($this->service->healthCheck());
    }

    /**
     * Test health check fails without rules
     */
    public function test_health_check_fails_without_rules(): void
    {
        // Act & Assert
        $this->assertFalse($this->service->healthCheck());
    }

    /**
     * Test service statistics
     */
    public function test_provides_service_statistics(): void
    {
        // Arrange
        $this->service->loadStandardRules();

        // Act
        $stats = $this->service->getStatistics();

        // Assert
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_rules', $stats);
        $this->assertArrayHasKey('rules_by_priority', $stats);
        $this->assertEquals(2, $stats['total_rules']);
    }

    /**
     * Create test product
     */
    private function createTestProduct(): Product
    {
        return Product::create(
            'Test Product',
            'test-product',
            new SKU('TEST-001'),
            new Price(100, 'TRY'),
            new Stock(50),
            1,
            'Test product description'
        );
    }
}
