<?php

namespace Tests\Unit\Domain\Shared\Rules\Discount;

use Tests\TestCase;
use App\Domain\Shared\Rules\Discount\CouponValidationRule;
use App\Domain\Shared\Rules\Discount\DiscountRuleResult;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Entities\OrderItem;
use App\Domain\Customers\Entities\Customer;
use App\Domain\Products\Entities\Product;
use Carbon\Carbon;

class CouponValidationRuleTest extends TestCase
{
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_percentage_coupon_applies_discount()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0, // %20 indirim
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'SAVE20'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(80, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertEquals(20, $result->getDiscountAmount()->getAmountInMajorUnit());
        $this->assertEquals('percentage', $result->getDiscountType());
        $this->assertEquals('SAVE20', $result->getCouponCode());
        $this->assertStringContainsString('20%', $result->getReason());
    }

    public function test_fixed_amount_coupon_applies_discount()
    {
        $discountAmount = Money::fromAmount(25, 'TRY');
        $rule = CouponValidationRule::fixed(
            'SAVE25',
            $discountAmount,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'SAVE25'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(75, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertEquals(25, $result->getDiscountAmount()->getAmountInMajorUnit());
        $this->assertEquals('fixed', $result->getDiscountType());
        $this->assertEquals('SAVE25', $result->getCouponCode());
        $this->assertStringContainsString('25', $result->getReason());
    }

    public function test_coupon_code_mismatch_returns_no_discount()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'WRONG_CODE'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(100, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertEquals(0, $result->getDiscountAmount()->getAmountInMajorUnit());
        $this->assertEquals('none', $result->getDiscountType());
        $this->assertStringContainsString('does not match', $result->getReason());
    }

    public function test_expired_coupon_returns_no_discount()
    {
        $rule = CouponValidationRule::percentage(
            'EXPIRED',
            20.0,
            Carbon::now()->subWeek(),
            Carbon::now()->subDay() // Dün sona ermiş
        );

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'EXPIRED'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(100, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertStringContainsString('expired', $result->getReason());
    }

    public function test_not_yet_valid_coupon_returns_no_discount()
    {
        $rule = CouponValidationRule::percentage(
            'FUTURE',
            20.0,
            Carbon::now()->addDay(), // Yarın başlayacak
            Carbon::now()->addWeek()
        );

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'FUTURE'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(100, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertStringContainsString('not yet valid', $result->getReason());
    }

    public function test_minimum_amount_not_met_returns_no_discount()
    {
        $minimumAmount = Money::fromAmount(200, 'TRY');
        $rule = CouponValidationRule::percentage(
            'MIN200',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            null,
            $minimumAmount
        );

        $baseAmount = Money::fromAmount(100, 'TRY'); // Minimum altında
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'MIN200'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(100, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertStringContainsString('Minimum amount required', $result->getReason());
    }

    public function test_minimum_amount_met_applies_discount()
    {
        $minimumAmount = Money::fromAmount(200, 'TRY');
        $rule = CouponValidationRule::percentage(
            'MIN200',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            null,
            $minimumAmount
        );

        $baseAmount = Money::fromAmount(300, 'TRY'); // Minimum üstünde
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'MIN200'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->hasDiscount());
        $this->assertEquals(300, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(240, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertEquals(60, $result->getDiscountAmount()->getAmountInMajorUnit());
    }

    public function test_usage_limit_exceeded_returns_no_discount()
    {
        $rule = CouponValidationRule::percentage(
            'LIMITED',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            5 // 5 kullanım limiti
        );

        // Kullanım sayısını limite eşitle
        $rule->incrementUsageCount();
        $rule->incrementUsageCount();
        $rule->incrementUsageCount();
        $rule->incrementUsageCount();
        $rule->incrementUsageCount(); // 5 kullanım

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'LIMITED'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(100, $result->getDiscountedAmount()->getAmountInMajorUnit());
        $this->assertStringContainsString('usage limit exceeded', $result->getReason());
    }

    public function test_usage_limit_not_exceeded_applies_discount()
    {
        $rule = CouponValidationRule::percentage(
            'LIMITED',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            5 // 5 kullanım limiti
        );

        // Kullanım sayısını limitin altında tut
        $rule->incrementUsageCount();
        $rule->incrementUsageCount(); // 2 kullanım

        $baseAmount = Money::fromAmount(100, 'TRY');
        $result = $rule->applyDiscount($this->testOrder, $baseAmount, [
            'coupon_code' => 'LIMITED'
        ]);

        $this->assertInstanceOf(DiscountRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->hasDiscount());
        $this->assertEquals(100, $result->getOriginalAmount()->getAmountInMajorUnit());
        $this->assertEquals(80, $result->getDiscountedAmount()->getAmountInMajorUnit());
    }

    public function test_rule_properties()
    {
        $rule = CouponValidationRule::percentage(
            'TEST',
            15.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertEquals('coupon_validation', $rule->getName());
        $this->assertEquals('Coupon validation and discount rule', $rule->getDescription());
        $this->assertEquals(100, $rule->getPriority());
        $this->assertEquals('percentage', $rule->getDiscountType());
    }

    public function test_fixed_coupon_rule_properties()
    {
        $rule = CouponValidationRule::fixed(
            'FIXED10',
            Money::fromAmount(10, 'TRY'),
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertEquals('coupon_validation', $rule->getName());
        $this->assertEquals('fixed', $rule->getDiscountType());
    }

    public function test_is_applicable_with_matching_coupon_code()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertTrue($rule->isApplicable($this->testOrder, ['coupon_code' => 'SAVE20']));
        $this->assertFalse($rule->isApplicable($this->testOrder, ['coupon_code' => 'DIFFERENT']));
        $this->assertFalse($rule->isApplicable($this->testOrder, []));
    }

    public function test_is_applicable_with_coupon_context_key()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertTrue($rule->isApplicable($this->testOrder, ['coupon' => 'SAVE20']));
        $this->assertFalse($rule->isApplicable($this->testOrder, ['coupon' => 'DIFFERENT']));
    }

    public function test_check_applicability()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertTrue($rule->isApplicable($this->testOrder, ['coupon_code' => 'SAVE20']));
    }

    public function test_get_coupon_code()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertEquals('SAVE20', $rule->getCouponCode());
    }

    public function test_get_discount_percentage()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertEquals(20.0, $rule->getDiscountPercentage());
    }

    public function test_get_discount_amount()
    {
        $discountAmount = Money::fromAmount(25, 'TRY');
        $rule = CouponValidationRule::fixed(
            'SAVE25',
            $discountAmount,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        $this->assertEquals(25, $rule->getDiscountAmount()->getAmountInMajorUnit());
    }

    public function test_get_minimum_amount()
    {
        $minimumAmount = Money::fromAmount(100, 'TRY');
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            null,
            $minimumAmount
        );

        $this->assertEquals(100, $rule->getMinimumAmount()->getAmountInMajorUnit());
    }

    public function test_get_usage_limit()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            10 // Usage limit
        );

        $this->assertEquals(10, $rule->getUsageLimit());
    }

    public function test_get_used_count()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            10
        );

        $this->assertEquals(0, $rule->getUsedCount());
        
        $rule->incrementUsageCount();
        $this->assertEquals(1, $rule->getUsedCount());
    }

    public function test_is_valid_method()
    {
        // Geçerli kupon
        $validRule = CouponValidationRule::percentage(
            'VALID',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            10
        );
        $this->assertTrue($validRule->isValid());

        // Süresi dolmuş kupon
        $expiredRule = CouponValidationRule::percentage(
            'EXPIRED',
            20.0,
            Carbon::now()->subWeek(),
            Carbon::now()->subDay()
        );
        $this->assertFalse($expiredRule->isValid());

        // Henüz başlamamış kupon
        $futureRule = CouponValidationRule::percentage(
            'FUTURE',
            20.0,
            Carbon::now()->addDay(),
            Carbon::now()->addWeek()
        );
        $this->assertFalse($futureRule->isValid());

        // Kullanım limiti dolmuş kupon
        $limitExceededRule = CouponValidationRule::percentage(
            'LIMITED',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay(),
            1
        );
        $limitExceededRule->incrementUsageCount();
        $this->assertFalse($limitExceededRule->isValid());
    }

    public function test_custom_priority()
    {
        $rule = CouponValidationRule::percentage(
            'SAVE20',
            20.0,
            Carbon::now()->subDay(),
            Carbon::now()->addDay()
        );

        // Default priority
        $this->assertEquals(100, $rule->getPriority());

        // Custom priority ile yeni rule
        $customRule = new CouponValidationRule([
            'code' => 'CUSTOM',
            'type' => 'percentage',
            'discount_percentage' => 15.0,
            'valid_from' => Carbon::now()->subDay(),
            'valid_until' => Carbon::now()->addDay(),
        ], 200); // Custom priority

        $this->assertEquals(200, $customRule->getPriority());
    }

    public function test_percentage_validation_bounds()
    {
        // %100'den fazla indirim olamaz
        $rule = new CouponValidationRule([
            'code' => 'OVER100',
            'type' => 'percentage',
            'discount_percentage' => 150.0, // %150 - geçersiz
            'valid_from' => Carbon::now()->subDay(),
            'valid_until' => Carbon::now()->addDay(),
        ]);

        $this->assertEquals(100.0, $rule->getDiscountPercentage()); // Max %100

        // Negatif indirim olamaz
        $negativeRule = new CouponValidationRule([
            'code' => 'NEGATIVE',
            'type' => 'percentage',
            'discount_percentage' => -10.0, // Negatif - geçersiz
            'valid_from' => Carbon::now()->subDay(),
            'valid_until' => Carbon::now()->addDay(),
        ]);

        $this->assertEquals(0.0, $negativeRule->getDiscountPercentage()); // Min %0
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');

        $product = new Product();
        $product->setId(1);
        $product->setName('Test Product');

        $orderItem = new OrderItem();
        $orderItem->setProduct($product);
        $orderItem->setQuantity(2);
        $orderItem->setUnitPrice(Money::fromAmount(50, 'TRY'));

        $order = new Order();
        $order->setId(1);
        $order->setOrderNumber(new OrderNumber('ORD-2024-001'));
        $order->setCustomer($customer);
        $order->setStatus('pending');
        $order->setTotalAmount(Money::fromAmount(100, 'TRY'));
        $order->addItem($orderItem);

        $shippingAddress = new Address(
            'Test Street 123',
            'Test City',
            'Test State',
            '12345',
            'Turkey'
        );
        $order->setShippingAddress($shippingAddress);

        return $order;
    }
}
