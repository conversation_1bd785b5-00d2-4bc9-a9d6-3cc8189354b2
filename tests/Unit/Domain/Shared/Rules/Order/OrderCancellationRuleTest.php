<?php

namespace Tests\Unit\Domain\Shared\Rules\Order;

use Tests\TestCase;
use App\Domain\Shared\Rules\Order\OrderCancellationRule;
use App\Domain\Shared\Rules\Order\OrderRuleResult;
use App\Domain\Orders\Entities\Order;
use App\Core\Domain\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Address;
use App\Domain\Orders\Entities\OrderItem;
use App\Enums\OrderStatus;
use App\Domain\Customers\Entities\Customer;
use Carbon\Carbon;

class OrderCancellationRuleTest extends TestCase
{
    private OrderCancellationRule $rule;
    private Order $testOrder;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = OrderCancellationRule::standard();
        $this->testOrder = $this->createTestOrder();
    }

    public function test_allows_cancellation_for_pending_order()
    {
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('cancelled', $result->getNewStatus());
        $this->assertEmpty($result->getErrors());
    }

    public function test_allows_cancellation_for_confirmed_order()
    {
        $this->testOrder->setStatus('confirmed');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        $this->assertEquals('cancelled', $result->getNewStatus());
    }

    public function test_denies_cancellation_for_shipped_order()
    {
        $this->testOrder->setStatus('shipped');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('shipped', $result->getErrors()[0]);
    }

    public function test_denies_cancellation_for_delivered_order()
    {
        $this->testOrder->setStatus('delivered');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('delivered', $result->getErrors()[0]);
    }

    public function test_denies_cancellation_for_completed_order()
    {
        $this->testOrder->setStatus('completed');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('completed', $result->getErrors()[0]);
    }

    public function test_requires_cancellation_reason()
    {
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel'
            // cancellation_reason eksik
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertContains('cancellation_reason', $result->getRequiredActions());
        $this->assertNotEmpty($result->getWarnings());
    }

    public function test_calculates_cancellation_fee_for_high_amount_order()
    {
        $this->testOrder->setStatus('pending');
        $this->testOrder->setTotalAmount(Money::fromAmount(1000, 'TRY')); // Eşik üstü

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        
        $metadata = $result->getMetadata();
        $this->assertArrayHasKey('cancellation_fee', $metadata);
        $this->assertNotEmpty($result->getWarnings());
        $this->assertStringContainsString('Cancellation fee', $result->getWarnings()[0]);
    }

    public function test_no_cancellation_fee_for_low_amount_order()
    {
        $this->testOrder->setStatus('pending');
        $this->testOrder->setTotalAmount(Money::fromAmount(100, 'TRY')); // Eşik altı

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
        
        $metadata = $result->getMetadata();
        $this->assertArrayNotHasKey('cancellation_fee', $metadata);
    }

    public function test_requires_refund_for_paid_order()
    {
        $this->testOrder->setStatus('confirmed');
        $this->testOrder->setTotalAmount(Money::fromAmount(1000, 'TRY'));
        $this->testOrder->markAsPaid();

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isOrderAllowed()); // Requires action
        $this->assertContains('process_refund', $result->getRequiredActions());
        
        $metadata = $result->getMetadata();
        $this->assertArrayHasKey('refund_amount', $metadata);
        $this->assertNotEmpty($result->getWarnings());
    }

    public function test_requires_inventory_restoration_for_processing_order()
    {
        $this->testOrder->setStatus('processing');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isOrderAllowed()); // Requires action
        $this->assertContains('restore_inventory', $result->getRequiredActions());
        $this->assertStringContainsString('Inventory will be restored', implode(' ', $result->getWarnings()));
    }

    public function test_denies_cancellation_for_non_order_entity()
    {
        $nonOrder = new \stdClass();

        $result = $this->rule->applyRule($nonOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Test'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertStringContainsString('Entity is not an order', $result->getErrors()[0]);
    }

    public function test_time_limit_check_within_limit()
    {
        // Sipariş 30 dakika önce oluşturulmuş (limit 60 dakika)
        $this->testOrder->setCreatedAt(Carbon::now()->subMinutes(30));
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->isOrderAllowed());
    }

    public function test_time_limit_exceeded_requires_approval()
    {
        // Sipariş 90 dakika önce oluşturulmuş (limit 60 dakika, ama 120 dakika içinde)
        $this->testOrder->setCreatedAt(Carbon::now()->subMinutes(90));
        $this->testOrder->setStatus('confirmed');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertTrue($result->isValid());
        $this->assertFalse($result->isOrderAllowed()); // Requires approval
        $this->assertContains('manager_approval', $result->getRequiredActions());
        $this->assertNotEmpty($result->getWarnings());
    }

    public function test_time_limit_exceeded_completely_denied()
    {
        // Sipariş 150 dakika önce oluşturulmuş (limit 60 dakika, 120 dakika da aşıldı)
        $this->testOrder->setCreatedAt(Carbon::now()->subMinutes(150));
        $this->testOrder->setStatus('confirmed');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Customer request'
        ]);

        $this->assertInstanceOf(OrderRuleResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertFalse($result->isOrderAllowed());
        $this->assertNotEmpty($result->getErrors());
        $this->assertStringContainsString('time limit exceeded', $result->getErrors()[0]);
    }

    public function test_rule_properties()
    {
        $this->assertEquals('order_cancellation', $this->rule->getName());
        $this->assertEquals(300, $this->rule->getPriority());
        $this->assertStringContainsString('cancellation', $this->rule->getDescription());
    }

    public function test_is_applicable_for_cancel_action()
    {
        $this->assertTrue($this->rule->isApplicable($this->testOrder, ['action' => 'cancel']));
        $this->assertFalse($this->rule->isApplicable($this->testOrder, ['action' => 'update']));
        $this->assertFalse($this->rule->isApplicable($this->testOrder, []));
    }

    public function test_can_set_cancellable_statuses()
    {
        $this->rule->setCancellableStatuses(['pending']);
        
        $this->testOrder->setStatus('confirmed');
        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Test'
        ]);

        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('not in cancellable statuses', $result->getErrors()[0]);
    }

    public function test_can_set_cancellation_time_limit()
    {
        $this->rule->setCancellationTimeLimit(30); // 30 dakika
        
        $this->testOrder->setCreatedAt(Carbon::now()->subMinutes(45));
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Test'
        ]);

        $this->assertFalse($result->isValid());
        $this->assertStringContainsString('time limit exceeded', $result->getErrors()[0]);
    }

    public function test_can_set_cancellation_fee_threshold()
    {
        $this->rule->setCancellationFeeThreshold(Money::fromAmount(200, 'TRY'));
        
        $this->testOrder->setTotalAmount(Money::fromAmount(300, 'TRY'));
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Test'
        ]);

        $metadata = $result->getMetadata();
        $this->assertArrayHasKey('cancellation_fee', $metadata);
    }

    public function test_can_set_cancellation_fee_percentage()
    {
        $this->rule->setCancellationFeePercentage(10.0); // %10
        
        $this->testOrder->setTotalAmount(Money::fromAmount(1000, 'TRY'));
        $this->testOrder->setStatus('pending');

        $result = $this->rule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Test'
        ]);

        $metadata = $result->getMetadata();
        $this->assertArrayHasKey('cancellation_fee', $metadata);
        
        // %10 ücret = 100 TRY
        $fee = Money::fromArray($metadata['cancellation_fee']);
        $this->assertEquals(100, $fee->getAmountInMajorUnit());
    }

    public function test_standard_rule_creation()
    {
        $standardRule = OrderCancellationRule::standard();
        
        $this->assertInstanceOf(OrderCancellationRule::class, $standardRule);
        $this->assertEquals('order_cancellation', $standardRule->getName());
        $this->assertEquals(300, $standardRule->getPriority());
    }

    public function test_strict_rule_creation()
    {
        $strictRule = OrderCancellationRule::strict();
        
        $this->assertInstanceOf(OrderCancellationRule::class, $strictRule);
        $this->assertEquals('order_cancellation', $strictRule->getName());
        $this->assertEquals(300, $strictRule->getPriority());
        
        // Strict rule sadece pending durumunda iptal edilebilir
        $this->testOrder->setStatus('confirmed');
        $result = $strictRule->applyRule($this->testOrder, [
            'action' => 'cancel',
            'cancellation_reason' => 'Test'
        ]);
        
        $this->assertFalse($result->isValid());
    }

    public function test_flexible_rule_creation()
    {
        $flexibleRule = OrderCancellationRule::flexible();
        
        $this->assertInstanceOf(OrderCancellationRule::class, $flexibleRule);
        $this->assertEquals('order_cancellation', $flexibleRule->getName());
        
        // Flexible rule processing durumunda da iptal edilebilir
        $this->testOrder->setStatus('processing');
        $result = $flexibleRule->applyRule($this->testOrder, [
            'action' => 'cancel'
            // cancellation_reason gerekmiyor flexible rule'da
        ]);
        
        $this->assertTrue($result->isValid());
    }

    private function createTestOrder(): Order
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setName('Test Customer');
        $customer->setEmail('<EMAIL>');

        $order = new Order();
        $order->setId(1);
        $order->setOrderNumber(new OrderNumber('ORD-2024-001'));
        $order->setCustomer($customer);
        $order->setStatus('pending');
        $order->setTotalAmount(Money::fromAmount(500, 'TRY'));
        $order->setCreatedAt(Carbon::now()->subMinutes(30));

        $shippingAddress = new Address(
            'Test Street 123',
            'Test City',
            'Test State',
            '12345',
            'Turkey'
        );
        $order->setShippingAddress($shippingAddress);

        return $order;
    }
}
