<?php

namespace Tests\Unit\Domain\Shared\Rules\Inventory;

use Tests\TestCase;
use App\Domain\Shared\Rules\Inventory\InventoryPolicyService;
use App\Domain\Shared\Rules\Inventory\InventoryPolicyResult;
use App\Domain\Shared\Rules\Inventory\StockValidationRule;
use App\Domain\Shared\Rules\Inventory\ReorderPointRule;
use App\Domain\Shared\Rules\Inventory\BackorderRule;
use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\SKU;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\Stock;
use App\Domain\Products\ValueObjects\Weight;
use App\Domain\Products\ValueObjects\Dimensions;
use App\Domain\Products\ValueObjects\SEOData;
use App\Core\Domain\ValueObjects\Money;
use App\Enums\ProductStatus;
use Carbon\Carbon;

class InventoryPolicyServiceTest extends TestCase
{
    private InventoryPolicyService $service;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new InventoryPolicyService();
        
        // Test ürünü oluştur
        $this->product = new Product(
            'Test Product',
            'test-product',
            'Test Description',
            new SKU('TEST-001'),
            new Price(Money::fromAmount(100, 'TRY')),
            new Stock(50, 5, true, true, 10, 100), // quantity: 50, reserved: 5, tracking: true, backorder: true, reorder_point: 10, reorder_quantity: 100
            1, // category_id
            ProductStatus::ACTIVE,
            false, // is_featured
            new Weight(1.0),
            new Dimensions(10, 10, 10),
            new SEOData('Test Product', 'Test Description', ['test']),
            Carbon::now(),
            Carbon::now()
        );
    }

    public function test_service_initializes_correctly()
    {
        $this->assertInstanceOf(InventoryPolicyService::class, $this->service);
        
        $rules = $this->service->getRules();
        $this->assertCount(3, $rules);
        $this->assertArrayHasKey('stock_validation', $rules);
        $this->assertArrayHasKey('reorder_point', $rules);
        $this->assertArrayHasKey('backorder', $rules);
    }

    public function test_checks_stock_successfully()
    {
        $result = $this->service->checkStock($this->product, 10);
        
        $this->assertInstanceOf(InventoryPolicyResult::class, $result);
        $this->assertEquals($this->product, $result->getProduct());
        $this->assertEquals(10, $result->getRequestedQuantity());
        $this->assertTrue($result->isValid());
        $this->assertTrue($result->canFulfill());
    }

    public function test_checks_stock_with_insufficient_quantity()
    {
        $result = $this->service->checkStock($this->product, 100); // Mevcut: 45, talep: 100
        
        $this->assertInstanceOf(InventoryPolicyResult::class, $result);
        $this->assertFalse($result->isValid());
        $this->assertTrue($result->canFulfill()); // Backorder allowed olduğu için fulfill edilebilir
        $this->assertNotEmpty($result->getWarnings());
    }

    public function test_can_reserve_stock()
    {
        $this->assertTrue($this->service->canReserveStock($this->product, 10));
        $this->assertFalse($this->service->canReserveStock($this->product, 100));
    }

    public function test_detects_reorder_requirement()
    {
        // Mevcut stok (45) reorder point'in (10) üstünde
        $this->assertFalse($this->service->isReorderRequired($this->product));
        
        // Düşük stoklu ürün oluştur
        $lowStockProduct = new Product(
            'Low Stock Product',
            'low-stock-product',
            'Low Stock Description',
            new SKU('LOW-001'),
            new Price(Money::fromAmount(100, 'TRY')),
            new Stock(8, 0, true, true, 10, 100), // quantity: 8, reorder_point: 10
            1,
            ProductStatus::ACTIVE,
            false,
            new Weight(1.0),
            new Dimensions(10, 10, 10),
            new SEOData('Low Stock Product', 'Low Stock Description', ['low']),
            Carbon::now(),
            Carbon::now()
        );
        
        $this->assertTrue($this->service->isReorderRequired($lowStockProduct));
    }

    public function test_gets_backorder_info()
    {
        $info = $this->service->getBackorderInfo($this->product, 100);
        
        $this->assertIsArray($info);
        $this->assertArrayHasKey('backorder_allowed', $info);
        $this->assertArrayHasKey('available_stock', $info);
        $this->assertArrayHasKey('shortfall', $info);
        $this->assertTrue($info['backorder_allowed']);
        $this->assertEquals(45, $info['available_stock']);
        $this->assertEquals(55, $info['shortfall']);
    }

    public function test_gets_stock_warnings()
    {
        $warnings = $this->service->getStockWarnings($this->product);
        
        $this->assertIsArray($warnings);
        // Normal stok seviyesinde uyarı olmamalı
        $this->assertEmpty($warnings);
    }

    public function test_checks_multiple_products()
    {
        $product2 = new Product(
            'Test Product 2',
            'test-product-2',
            'Test Description 2',
            new SKU('TEST-002'),
            new Price(Money::fromAmount(200, 'TRY')),
            new Stock(30, 0, true, true),
            1,
            ProductStatus::ACTIVE,
            false,
            new Weight(2.0),
            new Dimensions(20, 20, 20),
            new SEOData('Test Product 2', 'Test Description 2', ['test']),
            Carbon::now(),
            Carbon::now()
        );

        $items = [
            ['product' => $this->product, 'quantity' => 5],
            ['product' => $product2, 'quantity' => 10],
        ];

        $results = $this->service->checkMultipleProducts($items);
        
        $this->assertCount(2, $results);
        $this->assertContainsOnlyInstancesOf(InventoryPolicyResult::class, $results);
        
        foreach ($results as $result) {
            $this->assertTrue($result->isValid());
            $this->assertTrue($result->canFulfill());
        }
    }

    public function test_can_register_and_unregister_rules()
    {
        $customRule = new StockValidationRule(500); // Farklı priority ile
        
        $this->service->registerRule($customRule);
        $rules = $this->service->getRules();
        $this->assertCount(4, $rules); // 3 standart + 1 custom
        
        $this->service->unregisterRule('stock_validation');
        $rules = $this->service->getRules();
        $this->assertCount(3, $rules); // Custom rule kaldı, standart stock_validation gitti
    }

    public function test_gets_specific_rule()
    {
        $rule = $this->service->getRule('stock_validation');
        $this->assertInstanceOf(StockValidationRule::class, $rule);
        
        $nonExistentRule = $this->service->getRule('non_existent');
        $this->assertNull($nonExistentRule);
    }

    public function test_provides_service_statistics()
    {
        $stats = $this->service->getServiceStatistics();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_rules', $stats);
        $this->assertArrayHasKey('rule_names', $stats);
        $this->assertArrayHasKey('engine_stats', $stats);
        $this->assertEquals(3, $stats['total_rules']);
        $this->assertContains('stock_validation', $stats['rule_names']);
        $this->assertContains('reorder_point', $stats['rule_names']);
        $this->assertContains('backorder', $stats['rule_names']);
    }

    public function test_health_check_passes_with_rules()
    {
        $health = $this->service->healthCheck();
        
        $this->assertIsArray($health);
        $this->assertArrayHasKey('healthy', $health);
        $this->assertArrayHasKey('issues', $health);
        $this->assertArrayHasKey('total_rules', $health);
        $this->assertTrue($health['healthy']);
        $this->assertEmpty($health['issues']);
        $this->assertEquals(3, $health['total_rules']);
    }

    public function test_health_check_fails_without_rules()
    {
        $emptyService = new InventoryPolicyService();
        
        // Tüm kuralları kaldır
        $emptyService->unregisterRule('stock_validation');
        $emptyService->unregisterRule('reorder_point');
        $emptyService->unregisterRule('backorder');
        
        $health = $emptyService->healthCheck();
        
        $this->assertFalse($health['healthy']);
        $this->assertNotEmpty($health['issues']);
        $this->assertContains('No inventory rules registered', $health['issues']);
    }

    public function test_inventory_policy_result_methods()
    {
        $result = $this->service->checkStock($this->product, 10);
        
        // Test result methods
        $this->assertEquals($this->product, $result->getProduct());
        $this->assertEquals(10, $result->getRequestedQuantity());
        $this->assertIsArray($result->getRuleResults());
        $this->assertIsArray($result->getErrors());
        $this->assertIsArray($result->getWarnings());
        $this->assertIsArray($result->toArray());
        
        $array = $result->toArray();
        $this->assertArrayHasKey('product_id', $array);
        $this->assertArrayHasKey('requested_quantity', $array);
        $this->assertArrayHasKey('valid', $array);
        $this->assertArrayHasKey('can_fulfill', $array);
        $this->assertArrayHasKey('rule_results', $array);
    }
}
