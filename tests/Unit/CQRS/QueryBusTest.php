<?php

namespace Tests\Unit\CQRS;

use Tests\TestCase;
use App\Infrastructure\CQRS\QueryBus;
use App\Core\CQRS\Contracts\QueryInterface;
use App\Core\CQRS\Contracts\QueryHandlerInterface;
use App\Core\CQRS\Base\BaseQuery;
use App\Core\CQRS\Base\BaseQueryHandler;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Cache;
use Mockery;

class QueryBusTest extends TestCase
{
    private QueryBus $queryBus;
    private Container $container;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->container = new Container();
        $this->queryBus = new QueryBus($this->container);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_register_query_handler(): void
    {
        // Arrange
        $queryClass = TestQuery::class;
        $handlerClass = TestQueryHandler::class;

        // Act
        $this->queryBus->registerHandler($queryClass, $handlerClass);

        // Assert
        $handlers = $this->queryBus->getRegisteredHandlers();
        $this->assertArrayHasKey($queryClass, $handlers);
        $this->assertEquals($handlerClass, $handlers[$queryClass]);
    }

    /** @test */
    public function it_can_dispatch_query(): void
    {
        // Arrange
        $query = new TestQuery('test data');
        $handler = Mockery::mock(TestQueryHandler::class);
        $handler->shouldReceive('handle')
            ->once()
            ->with($query)
            ->andReturn(['result' => 'success']);
        $handler->shouldReceive('isCacheable')
            ->andReturn(false);

        $this->container->instance(TestQueryHandler::class, $handler);
        $this->queryBus->registerHandler(TestQuery::class, TestQueryHandler::class);

        // Act
        $result = $this->queryBus->dispatch($query);

        // Assert
        $this->assertEquals(['result' => 'success'], $result);
    }

    /** @test */
    public function it_throws_exception_for_unregistered_query(): void
    {
        // Arrange
        $query = new TestQuery('test data');

        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No handler registered for query: ' . TestQuery::class);

        // Act
        $this->queryBus->dispatch($query);
    }

    /** @test */
    public function it_can_cache_query_results(): void
    {
        // Arrange
        Cache::shouldReceive('get')
            ->once()
            ->with('test_cache_key')
            ->andReturn(null);

        Cache::shouldReceive('put')
            ->once()
            ->with('test_cache_key', ['result' => 'cached'], 300);

        $query = new CacheableTestQuery('test data');
        $handler = Mockery::mock(CacheableTestQueryHandler::class);
        $handler->shouldReceive('handle')
            ->once()
            ->with($query)
            ->andReturn(['result' => 'cached']);
        $handler->shouldReceive('isCacheable')
            ->andReturn(true);

        $this->container->instance(CacheableTestQueryHandler::class, $handler);
        $this->queryBus->registerHandler(CacheableTestQuery::class, CacheableTestQueryHandler::class);

        // Act
        $result = $this->queryBus->dispatch($query);

        // Assert
        $this->assertEquals(['result' => 'cached'], $result);
    }

    /** @test */
    public function it_returns_cached_result_when_available(): void
    {
        // Arrange
        Cache::shouldReceive('get')
            ->once()
            ->with('test_cache_key')
            ->andReturn(['result' => 'from_cache']);

        $query = new CacheableTestQuery('test data');
        $handler = Mockery::mock(CacheableTestQueryHandler::class);
        $handler->shouldReceive('isCacheable')
            ->andReturn(true);
        $handler->shouldNotReceive('handle'); // Handler çağrılmamalı

        $this->container->instance(CacheableTestQueryHandler::class, $handler);
        $this->queryBus->registerHandler(CacheableTestQuery::class, CacheableTestQueryHandler::class);

        // Act
        $result = $this->queryBus->dispatch($query);

        // Assert
        $this->assertEquals(['result' => 'from_cache'], $result);
    }
}

// Test sınıfları
class TestQuery extends BaseQuery
{
    public function __construct(
        public readonly string $data
    ) {
        parent::__construct();
    }

    public function rules(): array
    {
        return [
            'data' => 'required|string'
        ];
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'data' => $this->data
        ]);
    }
}

class TestQueryHandler extends BaseQueryHandler
{
    public function getQueryClass(): string
    {
        return TestQuery::class;
    }

    protected function execute(QueryInterface $query)
    {
        return ['result' => 'success'];
    }
}

class CacheableTestQuery extends BaseQuery
{
    public function __construct(
        public readonly string $data
    ) {
        parent::__construct();
    }

    public function getCacheKey(): ?string
    {
        return 'test_cache_key';
    }

    public function getCacheTtl(): ?int
    {
        return 300;
    }

    public function rules(): array
    {
        return [
            'data' => 'required|string'
        ];
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'data' => $this->data
        ]);
    }
}

class CacheableTestQueryHandler extends BaseQueryHandler
{
    public function getQueryClass(): string
    {
        return CacheableTestQuery::class;
    }

    public function isCacheable(): bool
    {
        return true;
    }

    protected function execute(QueryInterface $query)
    {
        return ['result' => 'cached'];
    }
}
