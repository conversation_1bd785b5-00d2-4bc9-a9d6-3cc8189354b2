<?php

namespace Tests\Unit\Application\Orders\Handlers;

use Tests\TestCase;
use App\Application\Orders\Commands\AddOrderNoteCommand;
use App\Application\Orders\Handlers\AddOrderNoteHandler;
use App\Application\Orders\DTOs\OrderDTO;
use App\Domain\Orders\Entities\Order;
use App\Domain\Orders\Entities\OrderNote;
use App\Domain\Orders\Repositories\OrderRepositoryInterface;
use App\Domain\Orders\Exceptions\OrderNotFoundException;
use App\Domain\Shared\Events\DomainEventDispatcher;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Core\Domain\ValueObjects\Money;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use Mockery;

class AddOrderNoteHandlerTest extends TestCase
{
    private OrderRepositoryInterface $orderRepository;
    private DomainEventDispatcher $eventDispatcher;
    private AddOrderNoteHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = Mockery::mock(OrderRepositoryInterface::class);
        $this->eventDispatcher = Mockery::mock(DomainEventDispatcher::class);
        $this->handler = new AddOrderNoteHandler(
            $this->orderRepository,
            $this->eventDispatcher
        );
    }

    public function test_it_can_add_note_to_order(): void
    {
        // Arrange
        $orderId = 1;
        $noteText = "Test note";
        $isPublic = true;
        $userId = 123;
        $type = "general";

        $command = new AddOrderNoteCommand(
            orderId: $orderId,
            note: $noteText,
            isPublic: $isPublic,
            userId: $userId,
            type: $type
        );

        // Gerçek Order entity oluştur
        $order = Order::create(
            userId: $userId,
            orderNumber: new OrderNumber('ORD-123'),
            totalAmount: new Money(100.0),
            paymentMethod: 'credit_card',
            shippingMethod: 'standard'
        );
        $order->setId($orderId);

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        $this->orderRepository
            ->shouldReceive('save')
            ->with($order)
            ->once()
            ->andReturn($order);

        $this->eventDispatcher
            ->shouldReceive('dispatchEventsFor')
            ->with($order)
            ->once();

        // Act
        $result = $this->handler->handle($command);

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $result);
        $this->assertEquals($orderId, $result->id);
        $this->assertEquals($userId, $result->userId);
    }

    public function test_it_throws_exception_when_order_not_found(): void
    {
        // Arrange
        $orderId = 999;
        $command = new AddOrderNoteCommand(
            orderId: $orderId,
            note: "Test note"
        );

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn(null);

        // Assert
        $this->expectException(OrderNotFoundException::class);
        $this->expectExceptionMessage("Order with ID {$orderId} not found");

        // Act
        $this->handler->handle($command);
    }

    public function test_it_throws_exception_when_user_mismatch(): void
    {
        // Arrange
        $orderId = 1;
        $userId = 123;
        $orderUserId = 456;

        $command = new AddOrderNoteCommand(
            orderId: $orderId,
            note: "Test note",
            userId: $userId
        );

        $order = Mockery::mock(Order::class);
        $order->shouldReceive('getUserId')->andReturn($orderUserId);

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        // Assert
        $this->expectException(OrderNotFoundException::class);
        $this->expectExceptionMessage("Order not found for this user");

        // Act
        $this->handler->handle($command);
    }

    public function test_it_can_add_note_without_user_check(): void
    {
        // Arrange
        $orderId = 1;
        $noteText = "Admin note";
        $userId = 456;

        $command = new AddOrderNoteCommand(
            orderId: $orderId,
            note: $noteText,
            userId: null // No user check
        );

        // Gerçek Order entity oluştur
        $order = Order::create(
            userId: $userId,
            orderNumber: new OrderNumber('ORD-456'),
            totalAmount: new Money(200.0),
            paymentMethod: 'bank_transfer',
            shippingMethod: 'express'
        );
        $order->setId($orderId);

        $this->orderRepository
            ->shouldReceive('findById')
            ->with($orderId)
            ->once()
            ->andReturn($order);

        $this->orderRepository
            ->shouldReceive('save')
            ->with($order)
            ->once()
            ->andReturn($order);

        $this->eventDispatcher
            ->shouldReceive('dispatchEventsFor')
            ->with($order)
            ->once();

        // Act
        $result = $this->handler->handle($command);

        // Assert
        $this->assertInstanceOf(OrderDTO::class, $result);
        $this->assertEquals($orderId, $result->id);
        $this->assertEquals($userId, $result->userId);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
