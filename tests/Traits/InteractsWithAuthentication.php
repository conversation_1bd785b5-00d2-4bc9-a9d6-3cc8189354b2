<?php

namespace Tests\Traits;

use App\Models\User;
use Illuminate\Contracts\Auth\Authenticatable;
use <PERSON><PERSON>\Sanctum\Sanctum;

/**
 * InteractsWithAuthentication Trait
 * Authentication test helper'ları
 */
trait InteractsWithAuthentication
{
    /**
     * Test user'ı
     */
    protected ?User $testUser = null;

    /**
     * Admin user'ı
     */
    protected ?User $adminUser = null;

    /**
     * Test user'ı oluştur ve authenticate et
     */
    protected function actingAsUser(array $attributes = [], array $permissions = []): User
    {
        $user = $this->createTestUser($attributes);
        
        if (!empty($permissions)) {
            $this->givePermissionsToUser($user, $permissions);
        }
        
        $this->actingAs($user);
        $this->testUser = $user;
        
        return $user;
    }

    /**
     * Admin user olarak authenticate et
     */
    protected function actingAsAdmin(array $attributes = []): User
    {
        $admin = $this->createAdminUser($attributes);
        $this->actingAs($admin);
        $this->adminUser = $admin;
        
        return $admin;
    }

    /**
     * API token ile authenticate et
     */
    protected function actingAsApiUser(array $attributes = [], array $scopes = ['*']): User
    {
        $user = $this->createTestUser($attributes);
        
        // Sanctum token oluştur
        $token = $user->createToken('test-token', $scopes);
        
        // API authentication header'ını set et
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ]);
        
        $this->testUser = $user;
        
        return $user;
    }

    /**
     * Guest user olarak test et
     */
    protected function actingAsGuest(): self
    {
        auth()->logout();
        $this->testUser = null;
        $this->adminUser = null;
        
        return $this;
    }

    /**
     * Test user'ı oluştur
     */
    protected function createTestUser(array $attributes = []): User
    {
        $defaultAttributes = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
        ];

        return User::factory()->create(array_merge($defaultAttributes, $attributes));
    }

    /**
     * Admin user'ı oluştur
     */
    protected function createAdminUser(array $attributes = []): User
    {
        $admin = $this->createTestUser(array_merge([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
        ], $attributes));

        // Admin role'ü ver
        $admin->assignRole('admin');
        
        return $admin;
    }

    /**
     * User'a permission'lar ver
     */
    protected function givePermissionsToUser(User $user, array $permissions): void
    {
        foreach ($permissions as $permission) {
            $user->givePermissionTo($permission);
        }
    }

    /**
     * User'ın permission'ını kontrol et
     */
    protected function assertUserHasPermission(User $user, string $permission): void
    {
        $this->assertTrue(
            $user->hasPermissionTo($permission),
            "User does not have permission: {$permission}"
        );
    }

    /**
     * User'ın role'ünü kontrol et
     */
    protected function assertUserHasRole(User $user, string $role): void
    {
        $this->assertTrue(
            $user->hasRole($role),
            "User does not have role: {$role}"
        );
    }

    /**
     * Authentication response'unu assert et
     */
    protected function assertAuthenticationResponse($response, bool $shouldBeAuthenticated = true): void
    {
        if ($shouldBeAuthenticated) {
            $response->assertStatus(200);
            $this->assertAuthenticated();
        } else {
            $response->assertStatus(401);
            $this->assertGuest();
        }
    }

    /**
     * API authentication header'larını al
     */
    protected function getApiAuthHeaders(User $user = null): array
    {
        if (!$user) {
            $user = $this->testUser ?? $this->createTestUser();
        }

        $token = $user->createToken('test-token');

        return [
            'Authorization' => 'Bearer ' . $token->plainTextToken,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * Multiple user'ları oluştur
     */
    protected function createMultipleUsers(int $count = 5, array $attributes = []): \Illuminate\Database\Eloquent\Collection
    {
        return User::factory()->count($count)->create($attributes);
    }

    /**
     * User authentication state'ini reset et
     */
    protected function resetAuthenticationState(): void
    {
        auth()->logout();
        $this->testUser = null;
        $this->adminUser = null;
        
        // Session'ı temizle
        session()->flush();
        session()->regenerate();
    }

    /**
     * Test sonunda authentication'ı temizle
     */
    protected function cleanupAuthentication(): void
    {
        $this->resetAuthenticationState();
        
        // Test user'ları sil
        if ($this->testUser) {
            $this->testUser->delete();
        }
        
        if ($this->adminUser) {
            $this->adminUser->delete();
        }
    }
}
