<?php

namespace Tests\Traits;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;

/**
 * InteractsWithExternalServices Trait
 * External service mock'lama ve test helper'ları
 */
trait InteractsWithExternalServices
{
    /**
     * Mock'lanmış service'ler
     */
    protected array $mockedServices = [];

    /**
     * External service'leri mock'la
     */
    protected function mockExternalServices(): void
    {
        $this->mockHttpRequests();
        $this->mockPaymentGateway();
        $this->mockShippingServices();
        $this->mockEmailServices();
        $this->mockNotificationServices();
        $this->mockFileStorage();
        $this->mockQueueServices();
    }

    /**
     * HTTP request'leri mock'la
     */
    protected function mockHttpRequests(): void
    {
        Http::fake([
            'api.external-service.com/*' => Http::response([
                'status' => 'success',
                'data' => ['mocked' => true]
            ], 200),
            
            'payment-gateway.com/*' => Http::response([
                'transaction_id' => 'mock_transaction_123',
                'status' => 'completed'
            ], 200),
            
            'shipping-api.com/*' => Http::response([
                'tracking_number' => 'MOCK_TRACK_123',
                'status' => 'shipped'
            ], 200),
        ]);

        $this->mockedServices['http'] = true;
    }

    /**
     * Payment gateway'i mock'la
     */
    protected function mockPaymentGateway(): void
    {
        // Payment gateway mock'ları
        $this->app->bind('payment.gateway', function () {
            return new class {
                public function charge($amount, $token) {
                    return [
                        'success' => true,
                        'transaction_id' => 'mock_' . uniqid(),
                        'amount' => $amount,
                        'status' => 'completed'
                    ];
                }

                public function refund($transactionId, $amount) {
                    return [
                        'success' => true,
                        'refund_id' => 'refund_' . uniqid(),
                        'amount' => $amount,
                        'status' => 'refunded'
                    ];
                }

                public function getTransaction($transactionId) {
                    return [
                        'id' => $transactionId,
                        'status' => 'completed',
                        'amount' => 100.00
                    ];
                }
            };
        });

        $this->mockedServices['payment'] = true;
    }

    /**
     * Shipping service'leri mock'la
     */
    protected function mockShippingServices(): void
    {
        $this->app->bind('shipping.service', function () {
            return new class {
                public function calculateShipping($from, $to, $weight) {
                    return [
                        'cost' => 15.00,
                        'estimated_days' => 3,
                        'service_type' => 'standard'
                    ];
                }

                public function createShipment($orderData) {
                    return [
                        'tracking_number' => 'MOCK_' . strtoupper(uniqid()),
                        'label_url' => 'https://mock-shipping.com/label.pdf',
                        'estimated_delivery' => now()->addDays(3)->toDateString()
                    ];
                }

                public function trackShipment($trackingNumber) {
                    return [
                        'tracking_number' => $trackingNumber,
                        'status' => 'in_transit',
                        'location' => 'Mock Distribution Center',
                        'estimated_delivery' => now()->addDays(2)->toDateString()
                    ];
                }
            };
        });

        $this->mockedServices['shipping'] = true;
    }

    /**
     * Email service'leri mock'la
     */
    protected function mockEmailServices(): void
    {
        Mail::fake();
        $this->mockedServices['mail'] = true;
    }

    /**
     * Notification service'leri mock'la
     */
    protected function mockNotificationServices(): void
    {
        Notification::fake();
        $this->mockedServices['notification'] = true;
    }

    /**
     * File storage'ı mock'la
     */
    protected function mockFileStorage(): void
    {
        Storage::fake('public');
        Storage::fake('s3');
        $this->mockedServices['storage'] = true;
    }

    /**
     * Queue service'leri mock'la
     */
    protected function mockQueueServices(): void
    {
        Queue::fake();
        $this->mockedServices['queue'] = true;
    }

    /**
     * Event system'ini mock'la
     */
    protected function mockEventSystem(): void
    {
        Event::fake();
        $this->mockedServices['events'] = true;
    }

    /**
     * Specific service'i mock'la
     */
    protected function mockService(string $serviceName, $mockImplementation): void
    {
        $this->app->bind($serviceName, function () use ($mockImplementation) {
            return $mockImplementation;
        });

        $this->mockedServices[$serviceName] = true;
    }

    /**
     * HTTP response'unu assert et
     */
    protected function assertHttpRequestMade(string $url, string $method = 'GET'): void
    {
        Http::assertSent(function ($request) use ($url, $method) {
            return $request->url() === $url && $request->method() === strtoupper($method);
        });
    }

    /**
     * Payment transaction'ını assert et
     */
    protected function assertPaymentProcessed(float $amount, string $status = 'completed'): void
    {
        $paymentGateway = app('payment.gateway');
        
        // Mock payment gateway'den transaction'ı kontrol et
        $this->assertTrue(
            isset($this->mockedServices['payment']),
            'Payment gateway is not mocked'
        );
    }

    /**
     * Email gönderimini assert et
     */
    protected function assertEmailSent(string $mailable, $recipient = null): void
    {
        if ($recipient) {
            Mail::assertSent($mailable, function ($mail) use ($recipient) {
                return $mail->hasTo($recipient);
            });
        } else {
            Mail::assertSent($mailable);
        }
    }

    /**
     * Notification gönderimini assert et
     */
    protected function assertNotificationSent($notifiable, string $notification): void
    {
        Notification::assertSentTo($notifiable, $notification);
    }

    /**
     * Queue job'ının dispatch edildiğini assert et
     */
    protected function assertJobDispatched(string $jobClass): void
    {
        Queue::assertPushed($jobClass);
    }

    /**
     * File upload'ını assert et
     */
    protected function assertFileUploaded(string $disk, string $path): void
    {
        Storage::disk($disk)->assertExists($path);
    }

    /**
     * Event dispatch'ini assert et
     */
    protected function assertEventDispatched(string $eventClass): void
    {
        Event::assertDispatched($eventClass);
    }

    /**
     * External service error'unu simulate et
     */
    protected function simulateExternalServiceError(string $service, int $statusCode = 500): void
    {
        switch ($service) {
            case 'payment':
                $this->app->bind('payment.gateway', function () {
                    return new class {
                        public function charge($amount, $token) {
                            throw new \Exception('Payment gateway error');
                        }
                    };
                });
                break;

            case 'shipping':
                $this->app->bind('shipping.service', function () {
                    return new class {
                        public function calculateShipping($from, $to, $weight) {
                            throw new \Exception('Shipping service error');
                        }
                    };
                });
                break;

            case 'http':
                Http::fake([
                    '*' => Http::response(['error' => 'Service unavailable'], $statusCode)
                ]);
                break;
        }
    }

    /**
     * Mock'ları temizle
     */
    protected function clearServiceMocks(): void
    {
        foreach ($this->mockedServices as $service => $mocked) {
            if ($mocked) {
                switch ($service) {
                    case 'http':
                        Http::fake([]);
                        break;
                    case 'mail':
                        Mail::fake();
                        break;
                    case 'notification':
                        Notification::fake();
                        break;
                    case 'queue':
                        Queue::fake();
                        break;
                    case 'events':
                        Event::fake();
                        break;
                    case 'storage':
                        Storage::fake('public');
                        Storage::fake('s3');
                        break;
                }
            }
        }

        $this->mockedServices = [];
    }

    /**
     * Service mock'ının aktif olduğunu kontrol et
     */
    protected function isServiceMocked(string $service): bool
    {
        return isset($this->mockedServices[$service]) && $this->mockedServices[$service];
    }
}
