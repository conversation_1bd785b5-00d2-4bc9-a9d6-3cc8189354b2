<?php

namespace Tests\Api;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Testing\TestResponse;

/**
 * ApiTestCase
 * API test'ler için specialized base class
 */
abstract class ApiTestCase extends TestCase
{
    use RefreshDatabase;

    /**
     * API base URL
     */
    protected string $apiBaseUrl = '/api/v1';

    /**
     * Default API headers
     */
    protected array $defaultApiHeaders = [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
    ];

    /**
     * API test setup
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // API test environment'ını setup et
        $this->setupApiTestEnvironment();
        
        // API test data'sını seed et
        $this->seedApiTestData();
    }

    /**
     * API test teardown
     */
    protected function tearDown(): void
    {
        // API test cleanup
        $this->cleanupApiTestEnvironment();
        
        parent::tearDown();
    }

    /**
     * API test environment'ını setup et
     */
    protected function setupApiTestEnvironment(): void
    {
        // API rate limiting'i disable et
        config(['api_rate_limiting.general.enabled' => false]);
        
        // API security headers'ı disable et
        config(['api_infrastructure.security.include_security_headers' => false]);
        
        // API validation cache'ini disable et
        config(['api_infrastructure.validation.cache_enabled' => false]);
    }

    /**
     * API test data'sını seed et
     */
    protected function seedApiTestData(): void
    {
        // Override edilebilir method
        // Her API test kendi data'sını oluşturabilir
    }

    /**
     * API test environment'ını temizle
     */
    protected function cleanupApiTestEnvironment(): void
    {
        // API-specific cleanup
    }

    /**
     * API request helper'ları
     */
    
    /**
     * GET API request
     */
    protected function getApi(string $uri, array $headers = []): TestResponse
    {
        return $this->json('GET', $this->apiBaseUrl . $uri, [], $this->mergeHeaders($headers));
    }

    /**
     * POST API request
     */
    protected function postApi(string $uri, array $data = [], array $headers = []): TestResponse
    {
        return $this->json('POST', $this->apiBaseUrl . $uri, $data, $this->mergeHeaders($headers));
    }

    /**
     * PUT API request
     */
    protected function putApi(string $uri, array $data = [], array $headers = []): TestResponse
    {
        return $this->json('PUT', $this->apiBaseUrl . $uri, $data, $this->mergeHeaders($headers));
    }

    /**
     * PATCH API request
     */
    protected function patchApi(string $uri, array $data = [], array $headers = []): TestResponse
    {
        return $this->json('PATCH', $this->apiBaseUrl . $uri, $data, $this->mergeHeaders($headers));
    }

    /**
     * DELETE API request
     */
    protected function deleteApi(string $uri, array $headers = []): TestResponse
    {
        return $this->json('DELETE', $this->apiBaseUrl . $uri, [], $this->mergeHeaders($headers));
    }

    /**
     * Authenticated API request
     */
    protected function authenticatedRequest(string $method, string $uri, array $data = [], array $headers = []): TestResponse
    {
        $user = $this->actingAsApiUser();
        $authHeaders = $this->getApiAuthHeaders($user);
        
        return $this->json($method, $this->apiBaseUrl . $uri, $data, $this->mergeHeaders($headers, $authHeaders));
    }

    /**
     * Admin API request
     */
    protected function adminRequest(string $method, string $uri, array $data = [], array $headers = []): TestResponse
    {
        $admin = $this->actingAsAdmin();
        $authHeaders = $this->getApiAuthHeaders($admin);
        
        return $this->json($method, $this->apiBaseUrl . $uri, $data, $this->mergeHeaders($headers, $authHeaders));
    }

    /**
     * Header'ları merge et
     */
    protected function mergeHeaders(array ...$headerArrays): array
    {
        return array_merge($this->defaultApiHeaders, ...$headerArrays);
    }

    /**
     * API Response assertion helper'ları
     */
    
    /**
     * Successful API response'unu assert et
     */
    protected function assertApiSuccess(TestResponse $response, int $statusCode = 200): void
    {
        $response->assertStatus($statusCode);
        $response->assertJsonStructure([
            'success',
            'data',
            'meta'
        ]);
        $response->assertJson(['success' => true]);
    }

    /**
     * API error response'unu assert et
     */
    protected function assertApiError(TestResponse $response, int $statusCode = 400, string $message = null): void
    {
        $response->assertStatus($statusCode);
        $response->assertJsonStructure([
            'success',
            'message',
            'errors'
        ]);
        $response->assertJson(['success' => false]);
        
        if ($message) {
            $response->assertJson(['message' => $message]);
        }
    }

    /**
     * API validation error'unu assert et
     */
    protected function assertApiValidationError(TestResponse $response, array $expectedErrors = []): void
    {
        $response->assertStatus(422);
        $response->assertJsonStructure([
            'success',
            'message',
            'errors'
        ]);
        $response->assertJson(['success' => false]);
        
        if (!empty($expectedErrors)) {
            foreach ($expectedErrors as $field) {
                $response->assertJsonValidationErrors($field);
            }
        }
    }

    /**
     * API authentication error'unu assert et
     */
    protected function assertApiAuthenticationError(TestResponse $response): void
    {
        $response->assertStatus(401);
        $response->assertJson(['success' => false]);
    }

    /**
     * API authorization error'unu assert et
     */
    protected function assertApiAuthorizationError(TestResponse $response): void
    {
        $response->assertStatus(403);
        $response->assertJson(['success' => false]);
    }

    /**
     * API not found error'unu assert et
     */
    protected function assertApiNotFound(TestResponse $response): void
    {
        $response->assertStatus(404);
        $response->assertJson(['success' => false]);
    }

    /**
     * API pagination response'unu assert et
     */
    protected function assertApiPagination(TestResponse $response, array $expectedStructure = []): void
    {
        $defaultStructure = [
            'data' => ['*' => $expectedStructure],
            'meta' => [
                'pagination' => [
                    'current_page',
                    'per_page',
                    'total',
                    'last_page',
                    'from',
                    'to'
                ]
            ]
        ];
        
        $response->assertJsonStructure($defaultStructure);
    }

    /**
     * API resource response'unu assert et
     */
    protected function assertApiResource(TestResponse $response, array $expectedStructure): void
    {
        $response->assertJsonStructure([
            'data' => $expectedStructure,
            'meta'
        ]);
    }

    /**
     * API collection response'unu assert et
     */
    protected function assertApiCollection(TestResponse $response, array $expectedItemStructure): void
    {
        $response->assertJsonStructure([
            'data' => ['*' => $expectedItemStructure],
            'meta'
        ]);
    }

    /**
     * API versioning assertion'ları
     */
    
    /**
     * API version header'ını assert et
     */
    protected function assertApiVersion(TestResponse $response, string $expectedVersion): void
    {
        $response->assertHeader('X-API-Version', $expectedVersion);
    }

    /**
     * API deprecation warning'ini assert et
     */
    protected function assertApiDeprecationWarning(TestResponse $response): void
    {
        $response->assertHeader('X-API-Deprecation-Warning');
    }

    /**
     * Rate limiting assertion'ları
     */
    
    /**
     * Rate limit header'larını assert et
     */
    protected function assertRateLimitHeaders(TestResponse $response): void
    {
        $response->assertHeader('X-RateLimit-Limit');
        $response->assertHeader('X-RateLimit-Remaining');
        $response->assertHeader('X-RateLimit-Reset');
    }

    /**
     * Rate limit exceeded'ı assert et
     */
    protected function assertRateLimitExceeded(TestResponse $response): void
    {
        $response->assertStatus(429);
        $response->assertJson(['success' => false]);
    }

    /**
     * API test helper'ları
     */
    
    /**
     * API endpoint'ini test et (CRUD operations)
     */
    protected function testApiCrudOperations(string $resourceName, array $createData, array $updateData): void
    {
        // Create
        $createResponse = $this->authenticatedRequest('POST', "/{$resourceName}", $createData);
        $this->assertApiSuccess($createResponse, 201);
        $resourceId = $createResponse->json('data.id');
        
        // Read (single)
        $showResponse = $this->authenticatedRequest('GET', "/{$resourceName}/{$resourceId}");
        $this->assertApiSuccess($showResponse);
        
        // Read (collection)
        $indexResponse = $this->authenticatedRequest('GET', "/{$resourceName}");
        $this->assertApiSuccess($indexResponse);
        
        // Update
        $updateResponse = $this->authenticatedRequest('PUT', "/{$resourceName}/{$resourceId}", $updateData);
        $this->assertApiSuccess($updateResponse);
        
        // Delete
        $deleteResponse = $this->authenticatedRequest('DELETE', "/{$resourceName}/{$resourceId}");
        $this->assertApiSuccess($deleteResponse, 204);
    }

    /**
     * API error handling'i test et
     */
    protected function testApiErrorHandling(string $endpoint): void
    {
        // Unauthenticated request
        $unauthResponse = $this->getApi($endpoint);
        $this->assertApiAuthenticationError($unauthResponse);
        
        // Invalid data
        $invalidResponse = $this->authenticatedRequest('POST', $endpoint, ['invalid' => 'data']);
        $this->assertApiValidationError($invalidResponse);
        
        // Not found
        $notFoundResponse = $this->authenticatedRequest('GET', $endpoint . '/999999');
        $this->assertApiNotFound($notFoundResponse);
    }
}
