<?php

namespace Tests\Performance;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

/**
 * PerformanceTestCase
 * Performance test'ler için specialized base class
 */
abstract class PerformanceTestCase extends TestCase
{
    use RefreshDatabase;

    /**
     * Performance metrics
     */
    protected array $metrics = [];

    /**
     * Performance thresholds
     */
    protected array $thresholds = [
        'max_execution_time' => 1.0, // seconds
        'max_memory_usage' => 50, // MB
        'max_database_queries' => 10,
        'max_response_time' => 500, // milliseconds
    ];

    /**
     * Performance test setup
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Performance test environment'ını setup et
        $this->setupPerformanceTestEnvironment();
        
        // Metrics'i reset et
        $this->resetMetrics();
    }

    /**
     * Performance test teardown
     */
    protected function tearDown(): void
    {
        // Performance metrics'i kaydet
        $this->recordMetrics();
        
        // Performance test cleanup
        $this->cleanupPerformanceTestEnvironment();
        
        parent::tearDown();
    }

    /**
     * Performance test environment'ını setup et
     */
    protected function setupPerformanceTestEnvironment(): void
    {
        // Query logging'i enable et
        \DB::enableQueryLog();
        
        // Memory tracking'i başlat
        $this->startMemoryTracking();
        
        // Performance monitoring'i enable et
        $this->enablePerformanceMonitoring();
    }

    /**
     * Performance test environment'ını temizle
     */
    protected function cleanupPerformanceTestEnvironment(): void
    {
        // Query logging'i disable et
        \DB::disableQueryLog();
        
        // Memory tracking'i durdur
        $this->stopMemoryTracking();
    }

    /**
     * Metrics'i reset et
     */
    protected function resetMetrics(): void
    {
        $this->metrics = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'peak_memory' => 0,
            'query_count' => 0,
            'execution_time' => 0,
            'memory_usage' => 0,
        ];
    }

    /**
     * Memory tracking'i başlat
     */
    protected function startMemoryTracking(): void
    {
        $this->metrics['start_memory'] = memory_get_usage(true);
    }

    /**
     * Memory tracking'i durdur
     */
    protected function stopMemoryTracking(): void
    {
        $this->metrics['peak_memory'] = memory_get_peak_usage(true);
        $this->metrics['memory_usage'] = $this->metrics['peak_memory'] - $this->metrics['start_memory'];
    }

    /**
     * Performance monitoring'i enable et
     */
    protected function enablePerformanceMonitoring(): void
    {
        // Custom performance monitoring logic
    }

    /**
     * Performance test helper'ları
     */
    
    /**
     * Execution time'ını measure et
     */
    protected function measureExecutionTime(callable $callback): float
    {
        $startTime = microtime(true);
        $callback();
        $endTime = microtime(true);
        
        return $endTime - $startTime;
    }

    /**
     * Memory usage'ını measure et
     */
    protected function measureMemoryUsage(callable $callback): int
    {
        $startMemory = memory_get_usage(true);
        $callback();
        $endMemory = memory_get_usage(true);
        
        return $endMemory - $startMemory;
    }

    /**
     * Database query count'unu measure et
     */
    protected function measureQueryCount(callable $callback): int
    {
        \DB::flushQueryLog();
        $callback();
        
        return count(\DB::getQueryLog());
    }

    /**
     * Response time'ını measure et
     */
    protected function measureResponseTime(string $method, string $uri, array $data = []): float
    {
        $startTime = microtime(true);
        $this->json($method, $uri, $data);
        $endTime = microtime(true);
        
        return ($endTime - $startTime) * 1000; // milliseconds
    }

    /**
     * Performance assertion'ları
     */
    
    /**
     * Execution time'ını assert et
     */
    protected function assertExecutionTime(callable $callback, float $maxTime = null): void
    {
        $maxTime = $maxTime ?? $this->thresholds['max_execution_time'];
        $executionTime = $this->measureExecutionTime($callback);
        
        $this->assertLessThanOrEqual(
            $maxTime,
            $executionTime,
            "Execution time exceeded threshold: {$executionTime}s (max: {$maxTime}s)"
        );
    }

    /**
     * Memory usage'ını assert et
     */
    protected function assertMemoryUsage(callable $callback, int $maxMemoryMB = null): void
    {
        $maxMemoryMB = $maxMemoryMB ?? $this->thresholds['max_memory_usage'];
        $memoryUsage = $this->measureMemoryUsage($callback);
        $memoryUsageMB = $memoryUsage / 1024 / 1024;
        
        $this->assertLessThanOrEqual(
            $maxMemoryMB,
            $memoryUsageMB,
            "Memory usage exceeded threshold: {$memoryUsageMB}MB (max: {$maxMemoryMB}MB)"
        );
    }

    /**
     * Database query count'unu assert et
     */
    protected function assertQueryCount(callable $callback, int $maxQueries = null): void
    {
        $maxQueries = $maxQueries ?? $this->thresholds['max_database_queries'];
        $queryCount = $this->measureQueryCount($callback);
        
        $this->assertLessThanOrEqual(
            $maxQueries,
            $queryCount,
            "Query count exceeded threshold: {$queryCount} (max: {$maxQueries})"
        );
    }

    /**
     * Response time'ını assert et
     */
    protected function assertResponseTime(string $method, string $uri, array $data = [], float $maxTimeMs = null): void
    {
        $maxTimeMs = $maxTimeMs ?? $this->thresholds['max_response_time'];
        $responseTime = $this->measureResponseTime($method, $uri, $data);
        
        $this->assertLessThanOrEqual(
            $maxTimeMs,
            $responseTime,
            "Response time exceeded threshold: {$responseTime}ms (max: {$maxTimeMs}ms)"
        );
    }

    /**
     * Combined performance assertion
     */
    protected function assertPerformance(callable $callback, array $customThresholds = []): void
    {
        $thresholds = array_merge($this->thresholds, $customThresholds);
        
        // Measure all metrics
        $executionTime = $this->measureExecutionTime($callback);
        $memoryUsage = $this->measureMemoryUsage($callback) / 1024 / 1024; // MB
        $queryCount = $this->measureQueryCount($callback);
        
        // Assert all thresholds
        $this->assertLessThanOrEqual(
            $thresholds['max_execution_time'],
            $executionTime,
            "Execution time exceeded: {$executionTime}s"
        );
        
        $this->assertLessThanOrEqual(
            $thresholds['max_memory_usage'],
            $memoryUsage,
            "Memory usage exceeded: {$memoryUsage}MB"
        );
        
        $this->assertLessThanOrEqual(
            $thresholds['max_database_queries'],
            $queryCount,
            "Query count exceeded: {$queryCount}"
        );
    }

    /**
     * Load testing helper'ları
     */
    
    /**
     * Concurrent request'leri simulate et
     */
    protected function simulateConcurrentRequests(string $endpoint, int $concurrency = 10, int $requests = 100): array
    {
        $results = [];
        $startTime = microtime(true);
        
        // Simulate concurrent requests (simplified)
        for ($i = 0; $i < $requests; $i++) {
            $requestStartTime = microtime(true);
            $response = $this->get($endpoint);
            $requestEndTime = microtime(true);
            
            $results[] = [
                'status' => $response->getStatusCode(),
                'time' => ($requestEndTime - $requestStartTime) * 1000, // ms
                'memory' => memory_get_usage(true),
            ];
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        return [
            'total_time' => $totalTime,
            'requests_per_second' => $requests / $totalTime,
            'average_response_time' => array_sum(array_column($results, 'time')) / count($results),
            'results' => $results,
        ];
    }

    /**
     * Database performance'ını test et
     */
    protected function testDatabasePerformance(callable $databaseOperations, array $thresholds = []): void
    {
        $defaultThresholds = [
            'max_queries' => 5,
            'max_time' => 0.5,
        ];
        
        $thresholds = array_merge($defaultThresholds, $thresholds);
        
        $startTime = microtime(true);
        \DB::flushQueryLog();
        
        $databaseOperations();
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        $queryCount = count(\DB::getQueryLog());
        
        $this->assertLessThanOrEqual(
            $thresholds['max_queries'],
            $queryCount,
            "Database query count exceeded: {$queryCount}"
        );
        
        $this->assertLessThanOrEqual(
            $thresholds['max_time'],
            $executionTime,
            "Database operation time exceeded: {$executionTime}s"
        );
    }

    /**
     * Cache performance'ını test et
     */
    protected function testCachePerformance(string $cacheKey, callable $cacheOperation): void
    {
        // Cache miss performance
        \Cache::forget($cacheKey);
        $missTime = $this->measureExecutionTime($cacheOperation);
        
        // Cache hit performance
        $hitTime = $this->measureExecutionTime($cacheOperation);
        
        // Cache hit should be significantly faster
        $this->assertLessThan(
            $missTime * 0.1, // Cache hit should be at least 10x faster
            $hitTime,
            "Cache hit performance is not optimal"
        );
    }

    /**
     * API endpoint performance'ını test et
     */
    protected function testApiEndpointPerformance(string $endpoint, array $data = []): void
    {
        // Test GET performance
        $this->assertResponseTime('GET', $endpoint);
        
        // Test POST performance (if data provided)
        if (!empty($data)) {
            $this->assertResponseTime('POST', $endpoint, $data);
        }
        
        // Test with authentication
        $user = $this->actingAsApiUser();
        $this->assertResponseTime('GET', $endpoint);
    }

    /**
     * Metrics'i kaydet
     */
    protected function recordMetrics(): void
    {
        $this->metrics['execution_time'] = microtime(true) - $this->metrics['start_time'];
        $this->metrics['query_count'] = count(\DB::getQueryLog());
        $this->stopMemoryTracking();
        
        // Metrics'i log'la veya database'e kaydet
        $this->logMetrics();
    }

    /**
     * Metrics'i log'la
     */
    protected function logMetrics(): void
    {
        $testName = $this->getName();
        $metricsJson = json_encode($this->metrics, JSON_PRETTY_PRINT);
        
        \Log::info("Performance metrics for {$testName}:", ['metrics' => $this->metrics]);
        
        // Optionally save to file
        $metricsFile = storage_path('testing/performance_metrics.json');
        $existingMetrics = file_exists($metricsFile) ? json_decode(file_get_contents($metricsFile), true) : [];
        $existingMetrics[$testName] = $this->metrics;
        file_put_contents($metricsFile, json_encode($existingMetrics, JSON_PRETTY_PRINT));
    }

    /**
     * Performance report oluştur
     */
    protected function generatePerformanceReport(): array
    {
        return [
            'test_name' => $this->getName(),
            'execution_time' => $this->metrics['execution_time'],
            'memory_usage_mb' => $this->metrics['memory_usage'] / 1024 / 1024,
            'peak_memory_mb' => $this->metrics['peak_memory'] / 1024 / 1024,
            'query_count' => $this->metrics['query_count'],
            'thresholds' => $this->thresholds,
            'passed' => $this->checkThresholds(),
        ];
    }

    /**
     * Threshold'ları kontrol et
     */
    protected function checkThresholds(): bool
    {
        $executionTime = $this->metrics['execution_time'];
        $memoryUsageMB = $this->metrics['memory_usage'] / 1024 / 1024;
        $queryCount = $this->metrics['query_count'];
        
        return $executionTime <= $this->thresholds['max_execution_time'] &&
               $memoryUsageMB <= $this->thresholds['max_memory_usage'] &&
               $queryCount <= $this->thresholds['max_database_queries'];
    }
}
