<?php

namespace Tests\Builders;

/**
 * TestDataBuilder
 * Test data oluşturmak için base builder class
 */
abstract class TestDataBuilder
{
    /**
     * Builder data
     */
    protected array $data = [];

    /**
     * Default values
     */
    protected array $defaults = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->data = $this->getDefaults();
    }

    /**
     * Static factory method
     */
    public static function create(): static
    {
        return new static();
    }

    /**
     * Default values'ları al
     */
    protected function getDefaults(): array
    {
        return $this->defaults;
    }

    /**
     * Data'yı set et
     */
    public function with(string $key, $value): static
    {
        $this->data[$key] = $value;
        return $this;
    }

    /**
     * Multiple data'yı set et
     */
    public function withData(array $data): static
    {
        $this->data = array_merge($this->data, $data);
        return $this;
    }

    /**
     * Data'yı override et
     */
    public function override(array $data): static
    {
        $this->data = $data;
        return $this;
    }

    /**
     * Data'yı al
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * Specific field'ı al
     */
    public function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Build method - override edilmeli
     */
    abstract public function build();

    /**
     * Build and create - database'e kaydet
     */
    public function create()
    {
        return $this->build();
    }

    /**
     * Build multiple instances
     */
    public function buildMany(int $count): array
    {
        $instances = [];
        for ($i = 0; $i < $count; $i++) {
            $instances[] = $this->build();
        }
        return $instances;
    }

    /**
     * Create multiple instances
     */
    public function createMany(int $count): array
    {
        $instances = [];
        for ($i = 0; $i < $count; $i++) {
            $instances[] = $this->create();
        }
        return $instances;
    }

    /**
     * Random data generator helper'ları
     */
    
    /**
     * Random string oluştur
     */
    protected function randomString(int $length = 10): string
    {
        return \Str::random($length);
    }

    /**
     * Random email oluştur
     */
    protected function randomEmail(): string
    {
        return fake()->unique()->safeEmail();
    }

    /**
     * Random phone oluştur
     */
    protected function randomPhone(): string
    {
        return fake()->phoneNumber();
    }

    /**
     * Random date oluştur
     */
    protected function randomDate(string $format = 'Y-m-d'): string
    {
        return fake()->date($format);
    }

    /**
     * Random datetime oluştur
     */
    protected function randomDateTime(string $format = 'Y-m-d H:i:s'): string
    {
        return fake()->dateTime()->format($format);
    }

    /**
     * Random number oluştur
     */
    protected function randomNumber(int $min = 1, int $max = 100): int
    {
        return fake()->numberBetween($min, $max);
    }

    /**
     * Random float oluştur
     */
    protected function randomFloat(int $decimals = 2, float $min = 0, float $max = 1000): float
    {
        return fake()->randomFloat($decimals, $min, $max);
    }

    /**
     * Random boolean oluştur
     */
    protected function randomBoolean(): bool
    {
        return fake()->boolean();
    }

    /**
     * Random array element seç
     */
    protected function randomElement(array $array)
    {
        return fake()->randomElement($array);
    }

    /**
     * Random array elements seç
     */
    protected function randomElements(array $array, int $count = 1): array
    {
        return fake()->randomElements($array, $count);
    }

    /**
     * Validation helper'ları
     */
    
    /**
     * Required field'ları validate et
     */
    protected function validateRequiredFields(array $requiredFields): void
    {
        foreach ($requiredFields as $field) {
            if (!isset($this->data[$field]) || $this->data[$field] === null) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }
    }

    /**
     * Field type'ını validate et
     */
    protected function validateFieldType(string $field, string $expectedType): void
    {
        if (!isset($this->data[$field])) {
            return;
        }

        $actualType = gettype($this->data[$field]);
        if ($actualType !== $expectedType) {
            throw new \InvalidArgumentException(
                "Field '{$field}' must be of type '{$expectedType}', '{$actualType}' given"
            );
        }
    }

    /**
     * Field value'yu validate et
     */
    protected function validateFieldValue(string $field, array $allowedValues): void
    {
        if (!isset($this->data[$field])) {
            return;
        }

        if (!in_array($this->data[$field], $allowedValues)) {
            throw new \InvalidArgumentException(
                "Field '{$field}' must be one of: " . implode(', ', $allowedValues)
            );
        }
    }

    /**
     * Transformation helper'ları
     */
    
    /**
     * Data'yı array'e transform et
     */
    public function toArray(): array
    {
        return $this->data;
    }

    /**
     * Data'yı JSON'a transform et
     */
    public function toJson(): string
    {
        return json_encode($this->data);
    }

    /**
     * Data'yı collection'a transform et
     */
    public function toCollection(): \Illuminate\Support\Collection
    {
        return collect($this->data);
    }

    /**
     * Conditional builder methods
     */
    
    /**
     * Condition'a göre data set et
     */
    public function when(bool $condition, callable $callback): static
    {
        if ($condition) {
            $callback($this);
        }
        return $this;
    }

    /**
     * Value varsa data set et
     */
    public function unless(bool $condition, callable $callback): static
    {
        if (!$condition) {
            $callback($this);
        }
        return $this;
    }

    /**
     * Debugging helper'ları
     */
    
    /**
     * Current data'yı dump et
     */
    public function dump(): static
    {
        dump($this->data);
        return $this;
    }

    /**
     * Current data'yı dd et
     */
    public function dd(): void
    {
        dd($this->data);
    }

    /**
     * Builder state'ini reset et
     */
    public function reset(): static
    {
        $this->data = $this->getDefaults();
        return $this;
    }

    /**
     * Builder'ı clone et
     */
    public function clone(): static
    {
        $clone = new static();
        $clone->data = $this->data;
        return $clone;
    }

    /**
     * Magic method - dynamic property access
     */
    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }

    /**
     * Magic method - dynamic property setting
     */
    public function __set(string $name, $value): void
    {
        $this->data[$name] = $value;
    }

    /**
     * Magic method - property existence check
     */
    public function __isset(string $name): bool
    {
        return isset($this->data[$name]);
    }

    /**
     * Magic method - property unsetting
     */
    public function __unset(string $name): void
    {
        unset($this->data[$name]);
    }
}
