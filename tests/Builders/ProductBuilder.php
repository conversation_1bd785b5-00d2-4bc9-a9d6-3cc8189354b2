<?php

namespace Tests\Builders;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Str;

/**
 * ProductBuilder
 * Product test data builder
 */
class ProductBuilder extends TestDataBuilder
{
    /**
     * Default values
     */
    protected array $defaults = [
        'name' => 'Test Product',
        'slug' => 'test-product',
        'description' => 'Test product description',
        'price' => 100.00,
        'stock' => 10,
        'sku' => 'TEST-SKU-001',
        'status' => 'active',
        'featured' => false,
        'weight' => 1.0,
        'dimensions' => null,
        'meta_title' => null,
        'meta_description' => null,
        'meta_keywords' => null,
    ];

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        
        // Dynamic defaults
        $name = fake()->words(3, true);
        $this->data['name'] = ucwords($name);
        $this->data['slug'] = Str::slug($name);
        $this->data['description'] = fake()->paragraph(3);
        $this->data['price'] = fake()->randomFloat(2, 10, 1000);
        $this->data['stock'] = fake()->numberBetween(0, 100);
        $this->data['sku'] = 'SKU-' . strtoupper(Str::random(8));
        $this->data['weight'] = fake()->randomFloat(2, 0.1, 10);
    }

    /**
     * Set product name
     */
    public function withName(string $name): static
    {
        return $this->with('name', $name)
                   ->with('slug', Str::slug($name));
    }

    /**
     * Set product slug
     */
    public function withSlug(string $slug): static
    {
        return $this->with('slug', $slug);
    }

    /**
     * Set product description
     */
    public function withDescription(string $description): static
    {
        return $this->with('description', $description);
    }

    /**
     * Set product price
     */
    public function withPrice(float $price): static
    {
        return $this->with('price', $price);
    }

    /**
     * Set product stock
     */
    public function withStock(int $stock): static
    {
        return $this->with('stock', $stock);
    }

    /**
     * Set product SKU
     */
    public function withSku(string $sku): static
    {
        return $this->with('sku', $sku);
    }

    /**
     * Set product category
     */
    public function withCategory($category): static
    {
        if (is_string($category)) {
            // Find category by name or create it
            $categoryModel = Category::where('name', $category)->first();
            if (!$categoryModel) {
                $categoryModel = Category::create([
                    'name' => $category,
                    'slug' => Str::slug($category),
                ]);
            }
            $this->data['category_id'] = $categoryModel->id;
        } elseif (is_int($category)) {
            $this->data['category_id'] = $category;
        } elseif ($category instanceof Category) {
            $this->data['category_id'] = $category->id;
        }

        return $this;
    }

    /**
     * Set product as active
     */
    public function active(): static
    {
        return $this->with('status', 'active');
    }

    /**
     * Set product as inactive
     */
    public function inactive(): static
    {
        return $this->with('status', 'inactive');
    }

    /**
     * Set product as draft
     */
    public function draft(): static
    {
        return $this->with('status', 'draft');
    }

    /**
     * Set product as featured
     */
    public function featured(): static
    {
        return $this->with('featured', true);
    }

    /**
     * Set product as not featured
     */
    public function notFeatured(): static
    {
        return $this->with('featured', false);
    }

    /**
     * Set product as out of stock
     */
    public function outOfStock(): static
    {
        return $this->with('stock', 0);
    }

    /**
     * Set product as in stock
     */
    public function inStock(int $stock = null): static
    {
        return $this->with('stock', $stock ?? fake()->numberBetween(1, 100));
    }

    /**
     * Set product as low stock
     */
    public function lowStock(int $stock = 2): static
    {
        return $this->with('stock', $stock);
    }

    /**
     * Set product weight
     */
    public function withWeight(float $weight): static
    {
        return $this->with('weight', $weight);
    }

    /**
     * Set product dimensions
     */
    public function withDimensions(array $dimensions): static
    {
        $defaultDimensions = [
            'length' => 10,
            'width' => 10,
            'height' => 10,
            'unit' => 'cm',
        ];

        return $this->with('dimensions', array_merge($defaultDimensions, $dimensions));
    }

    /**
     * Set product images
     */
    public function withImages(array $images): static
    {
        $this->data['images'] = $images;
        return $this;
    }

    /**
     * Set product main image
     */
    public function withMainImage(string $imagePath): static
    {
        return $this->with('image', $imagePath);
    }

    /**
     * Set product variants
     */
    public function withVariants(array $variants): static
    {
        $this->data['variants'] = $variants;
        return $this;
    }

    /**
     * Set product attributes
     */
    public function withAttributes(array $attributes): static
    {
        $this->data['attributes'] = $attributes;
        return $this;
    }

    /**
     * Set product tags
     */
    public function withTags(array $tags): static
    {
        $this->data['tags'] = $tags;
        return $this;
    }

    /**
     * Set product SEO data
     */
    public function withSeo(array $seoData = []): static
    {
        $defaultSeo = [
            'meta_title' => $this->data['name'],
            'meta_description' => Str::limit($this->data['description'], 160),
            'meta_keywords' => implode(', ', fake()->words(5)),
        ];

        $seo = array_merge($defaultSeo, $seoData);
        
        return $this->with('meta_title', $seo['meta_title'])
                   ->with('meta_description', $seo['meta_description'])
                   ->with('meta_keywords', $seo['meta_keywords']);
    }

    /**
     * Set product discount
     */
    public function withDiscount(float $discountPercentage): static
    {
        $originalPrice = $this->data['price'];
        $discountAmount = $originalPrice * ($discountPercentage / 100);
        $discountedPrice = $originalPrice - $discountAmount;

        return $this->with('original_price', $originalPrice)
                   ->with('price', $discountedPrice)
                   ->with('discount_percentage', $discountPercentage)
                   ->with('discount_amount', $discountAmount);
    }

    /**
     * Set product as digital
     */
    public function digital(): static
    {
        return $this->with('type', 'digital')
                   ->with('weight', 0)
                   ->with('dimensions', null);
    }

    /**
     * Set product as physical
     */
    public function physical(): static
    {
        return $this->with('type', 'physical');
    }

    /**
     * Set product as downloadable
     */
    public function downloadable(string $downloadUrl = null): static
    {
        return $this->digital()
                   ->with('downloadable', true)
                   ->with('download_url', $downloadUrl ?? 'https://example.com/download/test-file.pdf');
    }

    /**
     * Set product reviews
     */
    public function withReviews(int $count = 5, float $averageRating = null): static
    {
        $this->data['reviews_count'] = $count;
        $this->data['average_rating'] = $averageRating ?? fake()->randomFloat(1, 1, 5);
        return $this;
    }

    /**
     * Set product view count
     */
    public function withViewCount(int $viewCount = null): static
    {
        return $this->with('view_count', $viewCount ?? fake()->numberBetween(0, 1000));
    }

    /**
     * Build Product instance
     */
    public function build(): Product
    {
        // Validate required fields
        $this->validateRequiredFields(['name', 'slug', 'price', 'sku']);

        // Prepare data for Product model
        $productData = $this->prepareProductData();

        // Create Product instance
        $product = new Product($productData);

        return $product;
    }

    /**
     * Create Product in database
     */
    public function create(): Product
    {
        // Build product
        $productData = $this->prepareProductData();

        // Create product in database
        $product = Product::create($productData);

        // Save additional data
        $this->saveAdditionalData($product);

        return $product;
    }

    /**
     * Prepare product data for model
     */
    protected function prepareProductData(): array
    {
        $productData = [
            'name' => $this->data['name'],
            'slug' => $this->data['slug'],
            'description' => $this->data['description'],
            'price' => $this->data['price'],
            'stock' => $this->data['stock'],
            'sku' => $this->data['sku'],
            'status' => $this->data['status'],
            'featured' => $this->data['featured'],
            'weight' => $this->data['weight'],
        ];

        // Add optional fields
        $optionalFields = [
            'category_id', 'image', 'dimensions', 'meta_title', 
            'meta_description', 'meta_keywords', 'type', 'downloadable',
            'download_url', 'original_price', 'discount_percentage',
            'discount_amount', 'view_count', 'reviews_count', 'average_rating'
        ];

        foreach ($optionalFields as $field) {
            if (isset($this->data[$field])) {
                $productData[$field] = $this->data[$field];
            }
        }

        // Convert arrays to JSON
        if (isset($this->data['dimensions']) && is_array($this->data['dimensions'])) {
            $productData['dimensions'] = json_encode($this->data['dimensions']);
        }

        return $productData;
    }

    /**
     * Save additional data
     */
    protected function saveAdditionalData(Product $product): void
    {
        // Save images
        if (isset($this->data['images'])) {
            foreach ($this->data['images'] as $image) {
                // Assuming there's an images relationship
                // $product->images()->create(['path' => $image]);
            }
        }

        // Save variants
        if (isset($this->data['variants'])) {
            foreach ($this->data['variants'] as $variant) {
                // Assuming there's a variants relationship
                // $product->variants()->create($variant);
            }
        }

        // Save attributes
        if (isset($this->data['attributes'])) {
            foreach ($this->data['attributes'] as $attribute) {
                // Assuming there's an attributes relationship
                // $product->attributes()->create($attribute);
            }
        }

        // Save tags
        if (isset($this->data['tags'])) {
            // Assuming there's a tags relationship
            // $product->tags()->sync($this->data['tags']);
        }
    }

    /**
     * Create multiple products with different categories
     */
    public static function createWithCategories(array $categories): array
    {
        $products = [];
        foreach ($categories as $category) {
            $products[] = static::create()->withCategory($category)->create();
        }
        return $products;
    }

    /**
     * Create featured products
     */
    public static function createFeaturedProducts(int $count = 5): array
    {
        $products = [];
        for ($i = 0; $i < $count; $i++) {
            $products[] = static::create()->featured()->active()->create();
        }
        return $products;
    }

    /**
     * Create products with different stock levels
     */
    public static function createWithStockLevels(): array
    {
        return [
            'in_stock' => static::create()->inStock()->create(),
            'low_stock' => static::create()->lowStock()->create(),
            'out_of_stock' => static::create()->outOfStock()->create(),
        ];
    }
}
