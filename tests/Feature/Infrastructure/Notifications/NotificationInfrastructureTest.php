<?php

namespace Tests\Feature\Infrastructure\Notifications;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Infrastructure\Notifications\Repositories\EloquentNotificationRepository;
use App\Infrastructure\Notifications\Services\NotificationDeliveryService;
use App\Infrastructure\Notifications\Channels\ChannelAdapterFactory;
use App\Infrastructure\Notifications\Channels\EmailChannelAdapter;
use App\Infrastructure\Notifications\Listeners\NotificationEventListener;
use App\Domain\Notifications\Entities\Notification;
use App\Domain\Notifications\ValueObjects\NotificationType;
use App\Domain\Notifications\ValueObjects\NotificationChannel;
use App\Domain\Notifications\ValueObjects\NotificationPriority;
use App\Domain\Notifications\ValueObjects\RecipientInfo;
use App\Domain\Notifications\Events\NotificationCreated;
use App\Domain\Notifications\Events\NotificationSent;
use App\Domain\Notifications\Events\NotificationFailed;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

/**
 * NotificationInfrastructureTest
 * Notification Infrastructure Layer feature testleri
 */
class NotificationInfrastructureTest extends TestCase
{
    use RefreshDatabase;

    private EloquentNotificationRepository $repository;
    private NotificationDeliveryService $deliveryService;
    private ChannelAdapterFactory $channelFactory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->repository = app(EloquentNotificationRepository::class);
        $this->deliveryService = app(NotificationDeliveryService::class);
        $this->channelFactory = app(ChannelAdapterFactory::class);
    }

    /** @test */
    public function it_can_create_and_store_notification_with_events()
    {
        // Arrange
        Event::fake();
        $notification = $this->createTestNotification();

        // Act
        $savedNotification = $this->repository->save($notification);

        // Assert
        $this->assertNotNull($savedNotification->getId());
        $this->assertDatabaseHas('notifications', [
            'type' => 'order.created',
            'channel' => 'email',
            'status' => 'pending',
            'title' => 'Test Notification',
        ]);

        // Event'in tetiklendiğini kontrol et
        Event::assertDispatched(NotificationCreated::class, function ($event) use ($savedNotification) {
            return $event->getNotification()->getId() === $savedNotification->getId();
        });
    }

    /** @test */
    public function it_can_deliver_email_notification()
    {
        // Arrange
        Mail::fake();
        Event::fake();
        
        $notification = $this->createTestNotification();
        $savedNotification = $this->repository->save($notification);

        // Act
        $result = $this->deliveryService->deliver($savedNotification);

        // Assert
        $this->assertTrue($result);
        
        // Email gönderildiğini kontrol et
        Mail::assertSent(\Illuminate\Mail\Mailable::class);
        
        // Event'in tetiklendiğini kontrol et
        Event::assertDispatched(NotificationSent::class);
        
        // Veritabanında status güncellendiğini kontrol et
        $this->assertDatabaseHas('notifications', [
            'id' => $savedNotification->getId(),
            'status' => 'sent',
        ]);
    }

    /** @test */
    public function it_can_handle_delivery_failure()
    {
        // Arrange
        Event::fake();
        
        // Geçersiz email adresi ile notification oluştur
        $recipient = RecipientInfo::create('user', 1, 'invalid-email');
        $notification = Notification::create(
            NotificationType::fromString('order.created'),
            NotificationChannel::email(),
            $recipient,
            'Test Notification',
            'Test message'
        );
        
        $savedNotification = $this->repository->save($notification);

        // Act
        $result = $this->deliveryService->deliver($savedNotification);

        // Assert
        $this->assertFalse($result);
        
        // Event'in tetiklendiğini kontrol et
        Event::assertDispatched(NotificationFailed::class);
        
        // Veritabanında status güncellendiğini kontrol et
        $this->assertDatabaseHas('notifications', [
            'id' => $savedNotification->getId(),
            'status' => 'retrying',
        ]);
    }

    /** @test */
    public function it_can_deliver_batch_notifications()
    {
        // Arrange
        Mail::fake();
        
        $notifications = [
            $this->repository->save($this->createTestNotification()),
            $this->repository->save($this->createTestNotification()),
            $this->repository->save($this->createTestNotification()),
        ];

        // Act
        $results = $this->deliveryService->deliverBatch($notifications);

        // Assert
        $this->assertCount(3, $results);
        $this->assertEquals(3, count(array_filter($results))); // Hepsi başarılı
        
        // Email'lerin gönderildiğini kontrol et
        Mail::assertSent(\Illuminate\Mail\Mailable::class, 3);
    }

    /** @test */
    public function it_can_schedule_notification_delivery()
    {
        // Arrange
        Queue::fake();
        
        $notification = $this->repository->save($this->createTestNotification());
        $scheduledAt = Carbon::now()->addHour();

        // Act
        $this->deliveryService->scheduleDelivery($notification, $scheduledAt);

        // Assert
        Queue::assertPushed(\App\Jobs\DeliverNotificationJob::class);
    }

    /** @test */
    public function it_can_retry_failed_notifications()
    {
        // Arrange
        Queue::fake();
        
        $notification = $this->createTestNotification();
        $savedNotification = $this->repository->save($notification);
        
        // Başarısız olarak işaretle
        $savedNotification->markAsFailed('Test failure');
        $this->repository->save($savedNotification);

        // Act
        $result = $this->deliveryService->retry($savedNotification);

        // Assert
        $this->assertTrue($result);
        Queue::assertPushed(\App\Jobs\DeliverNotificationJob::class);
    }

    /** @test */
    public function it_can_create_channel_adapters()
    {
        // Act
        $emailAdapter = $this->channelFactory->create(NotificationChannel::email());
        $smsAdapter = $this->channelFactory->create(NotificationChannel::sms());

        // Assert
        $this->assertInstanceOf(EmailChannelAdapter::class, $emailAdapter);
        $this->assertNull($smsAdapter); // SMS adapter henüz implement edilmedi
    }

    /** @test */
    public function it_can_check_channel_health()
    {
        // Arrange
        $emailAdapter = $this->channelFactory->create(NotificationChannel::email());

        // Act
        $health = $emailAdapter->healthCheck();

        // Assert
        $this->assertNotNull($health);
        $this->assertIsBool($health->isHealthy());
    }

    /** @test */
    public function it_can_get_channel_statistics()
    {
        // Arrange
        $emailAdapter = $this->channelFactory->create(NotificationChannel::email());

        // Act
        $stats = $emailAdapter->getStatistics();

        // Assert
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('sent', $stats);
        $this->assertArrayHasKey('failed', $stats);
        $this->assertArrayHasKey('success_rate', $stats);
    }

    /** @test */
    public function it_handles_notification_events_properly()
    {
        // Arrange
        $eventListener = app(NotificationEventListener::class);
        $notification = $this->createTestNotification();

        // Act & Assert - NotificationCreated event
        $createdEvent = new NotificationCreated($notification);
        $eventListener->handleNotificationCreated($createdEvent);

        // Act & Assert - NotificationSent event
        $notification->markAsSent();
        $sentEvent = new NotificationSent($notification);
        $eventListener->handleNotificationSent($sentEvent);

        // Act & Assert - NotificationFailed event
        $notification->markAsFailed('Test failure');
        $failedEvent = new NotificationFailed($notification, 'Test failure');
        $eventListener->handleNotificationFailed($failedEvent);

        // Hiçbir exception atılmamalı
        $this->assertTrue(true);
    }

    /** @test */
    public function it_can_find_notifications_with_complex_queries()
    {
        // Arrange
        $recipient1 = RecipientInfo::create('user', 1, '<EMAIL>');
        $recipient2 = RecipientInfo::create('user', 2, '<EMAIL>');
        
        $notifications = [
            $this->createTestNotification($recipient1, NotificationChannel::email(), NotificationPriority::high()),
            $this->createTestNotification($recipient1, NotificationChannel::sms(), NotificationPriority::normal()),
            $this->createTestNotification($recipient2, NotificationChannel::email(), NotificationPriority::low()),
        ];

        foreach ($notifications as $notification) {
            $this->repository->save($notification);
        }

        // Act & Assert - Recipient'a göre
        $user1Notifications = $this->repository->findByRecipient($recipient1);
        $this->assertCount(2, $user1Notifications);

        // Act & Assert - Channel'a göre
        $emailNotifications = $this->repository->findByChannel(NotificationChannel::email());
        $this->assertCount(2, $emailNotifications);

        // Act & Assert - Priority'ye göre
        $highPriorityNotifications = $this->repository->findByPriority(NotificationPriority::high());
        $this->assertCount(1, $highPriorityNotifications);
    }

    /** @test */
    public function it_can_get_comprehensive_statistics()
    {
        // Arrange
        $notifications = [];
        for ($i = 0; $i < 10; $i++) {
            $notification = $this->repository->save($this->createTestNotification());
            $notifications[] = $notification;
        }

        // Bazılarını sent, bazılarını failed olarak işaretle
        for ($i = 0; $i < 5; $i++) {
            $notifications[$i]->markAsSent();
            $this->repository->save($notifications[$i]);
        }

        for ($i = 5; $i < 7; $i++) {
            $notifications[$i]->markAsFailed('Test failure');
            $this->repository->save($notifications[$i]);
        }

        // Act
        $stats = $this->repository->getStatistics();
        $channelStats = $this->repository->getChannelStatistics();
        $typeStats = $this->repository->getTypeStatistics();

        // Assert
        $this->assertEquals(10, $stats['total']);
        $this->assertEquals(5, $stats['sent']);
        $this->assertEquals(2, $stats['failed']);
        $this->assertEquals(50.0, $stats['delivery_rate']);
        $this->assertEquals(20.0, $stats['failure_rate']);

        $this->assertIsArray($channelStats);
        $this->assertArrayHasKey('email', $channelStats);

        $this->assertIsArray($typeStats);
        $this->assertArrayHasKey('order.created', $typeStats);
    }

    /** @test */
    public function it_can_cleanup_old_notifications()
    {
        // Arrange
        $oldDate = Carbon::now()->subDays(35);
        
        // Eski bildirimler oluştur
        $oldNotifications = [];
        for ($i = 0; $i < 5; $i++) {
            $notification = $this->createTestNotification();
            $saved = $this->repository->save($notification);
            $saved->markAsSent();
            $this->repository->save($saved);
            $oldNotifications[] = $saved;
        }

        // Yeni bildirimler oluştur
        $newNotifications = [];
        for ($i = 0; $i < 3; $i++) {
            $newNotifications[] = $this->repository->save($this->createTestNotification());
        }

        // Eski bildirimlerin tarihini güncelle
        \App\Infrastructure\Notifications\Models\EloquentNotification::whereIn(
            'id', 
            array_map(fn($n) => $n->getId(), $oldNotifications)
        )->update(['created_at' => $oldDate]);

        // Act
        $deletedCount = $this->repository->deleteOlderThan(Carbon::now()->subDays(30));

        // Assert
        $this->assertEquals(5, $deletedCount);
        $this->assertDatabaseCount('notifications', 3); // Sadece yeni bildirimler kaldı
    }

    /**
     * Test notification oluştur
     */
    private function createTestNotification(
        ?RecipientInfo $recipient = null,
        ?NotificationChannel $channel = null,
        ?NotificationPriority $priority = null
    ): Notification {
        $recipient = $recipient ?: RecipientInfo::create('user', 1, '<EMAIL>');
        $channel = $channel ?: NotificationChannel::email();
        $priority = $priority ?: NotificationPriority::normal();

        return Notification::create(
            NotificationType::fromString('order.created'),
            $channel,
            $recipient,
            'Test Notification',
            'This is a test notification message.',
            ['order_id' => 123],
            $priority,
            null,
            [],
            null,
            null,
            3,
            ['test' => true]
        );
    }
}
