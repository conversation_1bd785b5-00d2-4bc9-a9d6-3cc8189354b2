<?php

namespace Tests\Feature\Orders;

use Tests\TestCase;
use App\Application\Orders\Services\OrderApplicationService;
use App\Domain\Orders\ValueObjects\OrderNumber;
use App\Domain\Orders\ValueObjects\Money;
use App\Domain\Orders\ValueObjects\Address;
use App\Enums\OrderStatus;
use App\Enums\PaymentStatus;
use App\Models\User;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class OrderManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private OrderApplicationService $orderService;
    private User $user;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->orderService = app(OrderApplicationService::class);
        
        // Test kullanıcısı oluştur
        $this->user = User::factory()->create();
        
        // Test ürünü oluştur
        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 100.00,
            'stock' => 10
        ]);
    }

    /** @test */
    public function it_can_create_an_order()
    {
        $items = [
            [
                'product_id' => $this->product->id,
                'product_name' => $this->product->name,
                'price' => 100.00,
                'quantity' => 2,
                'options' => []
            ]
        ];

        $billingAddress = [
            'name' => 'John Doe',
            'phone' => '+90 ************',
            'address' => 'Test Address 123',
            'city' => 'Istanbul',
            'state' => 'Istanbul',
            'country' => 'Turkey',
            'email' => '<EMAIL>',
            'zipcode' => '34000'
        ];

        $orderDTO = $this->orderService->createOrder(
            userId: $this->user->id,
            items: $items,
            paymentMethod: 'credit_card',
            shippingMethod: 'standard',
            billingAddress: $billingAddress,
            shippingAddress: $billingAddress,
            taxAmount: 18.00,
            notes: 'Test order'
        );

        $this->assertNotNull($orderDTO);
        $this->assertEquals($this->user->id, $orderDTO->userId);
        $this->assertEquals(OrderStatus::PENDING, $orderDTO->status);
        $this->assertEquals(PaymentStatus::PENDING, $orderDTO->paymentStatus);
        $this->assertEquals(218.00, $orderDTO->totalAmount); // 200 + 18 tax
        $this->assertCount(1, $orderDTO->items);
    }

    /** @test */
    public function it_can_update_order_status()
    {
        // Önce bir sipariş oluştur
        $orderDTO = $this->createTestOrder();

        // Durumu güncelle
        $updatedOrderDTO = $this->orderService->updateOrderStatus(
            orderId: $orderDTO->id,
            newStatus: OrderStatus::PROCESSING,
            note: 'Order is being processed',
            userId: $this->user->id
        );

        $this->assertEquals(OrderStatus::PROCESSING, $updatedOrderDTO->status);
        $this->assertCount(2, $updatedOrderDTO->notes); // Initial note + status change note
    }

    /** @test */
    public function it_can_cancel_an_order()
    {
        // Önce bir sipariş oluştur
        $orderDTO = $this->createTestOrder();

        // Siparişi iptal et
        $cancelledOrderDTO = $this->orderService->cancelOrder(
            orderId: $orderDTO->id,
            reason: 'Customer requested cancellation',
            userId: $this->user->id
        );

        $this->assertEquals(OrderStatus::CANCELLED, $cancelledOrderDTO->status);
    }

    /** @test */
    public function it_can_get_order_by_id()
    {
        // Önce bir sipariş oluştur
        $createdOrderDTO = $this->createTestOrder();

        // Siparişi ID ile al
        $orderDTO = $this->orderService->getOrder($createdOrderDTO->id);

        $this->assertEquals($createdOrderDTO->id, $orderDTO->id);
        $this->assertEquals($createdOrderDTO->orderNumber, $orderDTO->orderNumber);
        $this->assertEquals($createdOrderDTO->userId, $orderDTO->userId);
    }

    /** @test */
    public function it_can_get_user_orders()
    {
        // Birkaç sipariş oluştur
        $this->createTestOrder();
        $this->createTestOrder();
        $this->createTestOrder();

        // Kullanıcının siparişlerini al
        $result = $this->orderService->getUserOrders(
            userId: $this->user->id,
            limit: 10,
            offset: 0
        );

        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertEquals(3, $result['total']);
        $this->assertCount(3, $result['data']);
    }

    /** @test */
    public function it_can_get_orders_by_status()
    {
        // Farklı durumlarda siparişler oluştur
        $order1 = $this->createTestOrder();
        $order2 = $this->createTestOrder();
        
        // Birini processing yap
        $this->orderService->updateOrderStatus(
            orderId: $order2->id,
            newStatus: OrderStatus::PROCESSING
        );

        // Pending siparişleri al
        $pendingResult = $this->orderService->getPendingOrders();
        $this->assertEquals(1, $pendingResult['total']);

        // Processing siparişleri al
        $processingResult = $this->orderService->getProcessingOrders();
        $this->assertEquals(1, $processingResult['total']);
    }

    /** @test */
    public function it_can_search_orders()
    {
        // Sipariş oluştur
        $orderDTO = $this->createTestOrder();

        // Sipariş numarası ile ara
        $result = $this->orderService->searchOrders(
            search: $orderDTO->orderNumber,
            limit: 10,
            offset: 0
        );

        $this->assertEquals(1, $result['total']);
        $this->assertEquals($orderDTO->orderNumber, $result['data'][0]->orderNumber);
    }

    /** @test */
    public function it_validates_order_status_transitions()
    {
        // Sipariş oluştur
        $orderDTO = $this->createTestOrder();

        // Geçersiz durum geçişi dene (PENDING'den DELIVERED'a)
        $this->expectException(\InvalidArgumentException::class);
        
        $this->orderService->updateOrderStatus(
            orderId: $orderDTO->id,
            newStatus: OrderStatus::DELIVERED
        );
    }

    /** @test */
    public function it_prevents_cancelling_shipped_orders()
    {
        // Sipariş oluştur ve shipped durumuna getir
        $orderDTO = $this->createTestOrder();
        
        $this->orderService->updateOrderStatus($orderDTO->id, OrderStatus::PROCESSING);
        $this->orderService->updateOrderStatus($orderDTO->id, OrderStatus::PAYMENT_CONFIRMED);
        $this->orderService->updateOrderStatus($orderDTO->id, OrderStatus::PREPARING);
        $this->orderService->updateOrderStatus($orderDTO->id, OrderStatus::READY_TO_SHIP);
        $shippedOrderDTO = $this->orderService->updateOrderStatus($orderDTO->id, OrderStatus::SHIPPED);

        // Shipped siparişi iptal etmeye çalış
        $this->expectException(\InvalidArgumentException::class);
        
        $this->orderService->cancelOrder(
            orderId: $shippedOrderDTO->id,
            reason: 'Should not be allowed'
        );
    }

    private function createTestOrder()
    {
        $items = [
            [
                'product_id' => $this->product->id,
                'product_name' => $this->product->name,
                'price' => 100.00,
                'quantity' => 1,
                'options' => []
            ]
        ];

        $address = [
            'name' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'address' => $this->faker->address,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'country' => 'Turkey',
            'email' => $this->faker->email,
            'zipcode' => $this->faker->postcode
        ];

        return $this->orderService->createOrder(
            userId: $this->user->id,
            items: $items,
            paymentMethod: 'credit_card',
            shippingMethod: 'standard',
            billingAddress: $address,
            shippingAddress: $address,
            notes: 'Test order'
        );
    }
}
