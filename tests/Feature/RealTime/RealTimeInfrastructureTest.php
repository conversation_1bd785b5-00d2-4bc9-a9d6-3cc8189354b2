<?php

namespace Tests\Feature\RealTime;

use App\Core\Infrastructure\RealTime\Events\Product\ProductStockUpdatedBroadcast;
use App\Core\Infrastructure\RealTime\Events\Product\ProductPriceChangedBroadcast;
use App\Core\Infrastructure\RealTime\Events\Order\OrderStatusChangedBroadcast;
use App\Core\Infrastructure\RealTime\Services\RealTimeEventDispatcher;
use Illuminate\Broadcasting\BroadcastManager;
use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

/**
 * Real-Time Infrastructure Test
 */
class RealTimeInfrastructureTest extends TestCase
{
    use RefreshDatabase;

    private RealTimeEventDispatcher $dispatcher;

    protected function setUp(): void
    {
        parent::setUp();

        $this->dispatcher = new RealTimeEventDispatcher(
            app(BroadcastManager::class),
            app(CacheRepository::class),
            [
                'enabled' => true,
                'debug' => false,
                'rate_limiting' => false, // Test için disable
                'cache_enabled' => false, // Test için disable
            ]
        );
    }

    /** @test */
    public function it_can_dispatch_product_stock_updated_event()
    {
        // Arrange
        $event = new ProductStockUpdatedBroadcast(
            productId: 1,
            newStock: 10,
            oldStock: 20
        );

        // Act
        $result = $this->dispatcher->dispatch($event);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals('product.stock.updated', $event->broadcastAs());
        $this->assertArrayHasKey('product_id', $event->broadcastWith());
        $this->assertArrayHasKey('new_stock', $event->broadcastWith());
        $this->assertArrayHasKey('old_stock', $event->broadcastWith());
    }

    /** @test */
    public function it_can_dispatch_product_price_changed_event()
    {
        // Arrange
        $event = new ProductPriceChangedBroadcast(
            productId: 1,
            newPrice: 100.0,
            oldPrice: 120.0,
            currency: 'TRY'
        );

        // Act
        $result = $this->dispatcher->dispatch($event);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals('product.price.changed', $event->broadcastAs());
        $this->assertArrayHasKey('product_id', $event->broadcastWith());
        $this->assertArrayHasKey('new_price', $event->broadcastWith());
        $this->assertArrayHasKey('price_change_percentage', $event->broadcastWith());
    }

    /** @test */
    public function it_can_dispatch_order_status_changed_event()
    {
        // Arrange
        $event = new OrderStatusChangedBroadcast(
            orderId: 1,
            newStatus: 'shipped',
            oldStatus: 'processing',
            userId: 1
        );

        // Act
        $result = $this->dispatcher->dispatch($event);

        // Assert
        $this->assertTrue($result);
        $this->assertEquals('order.status.changed', $event->broadcastAs());
        $this->assertArrayHasKey('order_id', $event->broadcastWith());
        $this->assertArrayHasKey('new_status', $event->broadcastWith());
        $this->assertArrayHasKey('user_id', $event->broadcastWith());
    }

    /** @test */
    public function it_can_dispatch_multiple_events_in_batch()
    {
        // Arrange
        $events = [
            new ProductStockUpdatedBroadcast(1, 5, 10),
            new ProductPriceChangedBroadcast(1, 90.0, 100.0),
            new OrderStatusChangedBroadcast(1, 'delivered', 'shipped', 1),
        ];

        // Act
        $results = $this->dispatcher->dispatchBatch($events);

        // Assert
        $this->assertCount(3, $results);
        $this->assertTrue($results[0]);
        $this->assertTrue($results[1]);
        $this->assertTrue($results[2]);
    }

    /** @test */
    public function it_returns_correct_statistics()
    {
        // Arrange
        $event = new ProductStockUpdatedBroadcast(1, 5, 10);

        // Act
        $this->dispatcher->dispatch($event);
        $stats = $this->dispatcher->getStatistics();

        // Assert
        $this->assertArrayHasKey('dispatched', $stats);
        $this->assertArrayHasKey('cached', $stats);
        $this->assertArrayHasKey('failed', $stats);
        $this->assertArrayHasKey('filtered', $stats);
        $this->assertArrayHasKey('timestamp', $stats);
        $this->assertEquals(1, $stats['dispatched']);
    }

    /** @test */
    public function it_can_reset_statistics()
    {
        // Arrange
        $event = new ProductStockUpdatedBroadcast(1, 5, 10);
        $this->dispatcher->dispatch($event);

        // Act
        $this->dispatcher->resetStatistics();
        $stats = $this->dispatcher->getStatistics();

        // Assert
        $this->assertEquals(0, $stats['dispatched']);
        $this->assertEquals(0, $stats['cached']);
        $this->assertEquals(0, $stats['failed']);
        $this->assertEquals(0, $stats['filtered']);
    }

    /** @test */
    public function product_stock_event_has_correct_channels()
    {
        // Arrange
        $event = new ProductStockUpdatedBroadcast(1, 0, 10); // Out of stock

        // Act
        $channels = $event->broadcastOn();

        // Assert
        $this->assertIsArray($channels);
        $this->assertGreaterThan(0, count($channels));
        
        // Channel names'leri kontrol et
        $channelNames = array_map(function ($channel) {
            return method_exists($channel, 'name') ? $channel->name : (string) $channel;
        }, $channels);

        $this->assertContains('product.1.stock', $channelNames);
        $this->assertContains('cart.product.1', $channelNames);
    }

    /** @test */
    public function order_status_event_has_correct_priority()
    {
        // Arrange
        $deliveredEvent = new OrderStatusChangedBroadcast(1, 'delivered', 'shipped', 1);
        $processingEvent = new OrderStatusChangedBroadcast(2, 'processing', 'confirmed', 1);

        // Assert
        $this->assertEquals(9, $deliveredEvent->getPriority()); // High priority
        $this->assertEquals(7, $processingEvent->getPriority()); // Medium priority
    }

    /** @test */
    public function price_change_event_calculates_percentage_correctly()
    {
        // Arrange
        $event = new ProductPriceChangedBroadcast(1, 80.0, 100.0); // 20% decrease

        // Act
        $data = $event->broadcastWith();

        // Assert
        $this->assertEquals(-20.0, $data['price_change_percentage']);
        $this->assertTrue($data['is_price_decrease']);
        $this->assertFalse($data['is_price_increase']);
    }

    /** @test */
    public function stock_event_detects_low_stock_correctly()
    {
        // Arrange
        config(['inventory.alerts.low_stock_threshold' => 10]);
        $event = new ProductStockUpdatedBroadcast(1, 5, 20); // Low stock

        // Act
        $data = $event->broadcastWith();

        // Assert
        $this->assertTrue($data['is_low_stock']);
        $this->assertFalse($data['is_out_of_stock']);
        $this->assertTrue($data['is_in_stock']);
    }

    /** @test */
    public function events_should_broadcast_when_enabled()
    {
        // Arrange
        config(['broadcasting.features.enabled' => true]);
        config(['broadcasting.features.broadcast_events.product_stock_updated' => true]);
        
        $event = new ProductStockUpdatedBroadcast(1, 5, 10);

        // Assert
        $this->assertTrue($event->shouldBroadcast());
    }

    /** @test */
    public function events_should_not_broadcast_when_disabled()
    {
        // Arrange
        config(['broadcasting.features.enabled' => false]);
        
        $event = new ProductStockUpdatedBroadcast(1, 5, 10);

        // Assert
        $this->assertFalse($event->shouldBroadcast());
    }
}
