<?php

namespace Tests\Feature\Categories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Modules\Categories\Domain\Models\Category;
use App\Modules\Categories\Domain\Interfaces\CategoryRepositoryInterface;
use App\Modules\Categories\Infrastructure\Repositories\CategoryRepository;

/**
 * Clean Architecture Category Test
 * Categories modülü Clean Architecture implementasyonunu test eder
 */
class CleanArchitectureCategoryTest extends TestCase
{
    use RefreshDatabase;

    private CategoryRepositoryInterface $categoryRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->categoryRepository = app(CategoryRepositoryInterface::class);
    }

    /** @test */
    public function it_can_create_category_using_clean_architecture()
    {
        // Arrange
        $categoryData = [
            'name' => 'Test Kategori',
            'slug' => 'test-kategori',
            'description' => 'Test açıklaması',
            'status' => true,
            'position' => 1,
            'featured' => false,
            'show_in_menu' => true,
        ];

        // Act
        $category = $this->categoryRepository->create($categoryData);

        // Assert
        $this->assertInstanceOf(Category::class, $category);
        $this->assertEquals('Test Kategori', $category->name);
        $this->assertEquals('test-kategori', $category->slug);
        $this->assertTrue($category->status);
        $this->assertTrue($category->isActive());
        $this->assertFalse($category->isFeatured());
        $this->assertTrue($category->isShownInMenu());
        $this->assertTrue($category->isRoot());
    }

    /** @test */
    public function it_can_create_hierarchical_categories()
    {
        // Arrange
        $parentCategory = $this->categoryRepository->create([
            'name' => 'Üst Kategori',
            'slug' => 'ust-kategori',
            'status' => true,
            'position' => 1,
        ]);

        $childData = [
            'name' => 'Alt Kategori',
            'slug' => 'alt-kategori',
            'parent_id' => $parentCategory->getId(),
            'status' => true,
            'position' => 1,
        ];

        // Act
        $childCategory = $this->categoryRepository->create($childData);

        // Assert
        $this->assertFalse($childCategory->isRoot());
        $this->assertEquals($parentCategory->getId(), $childCategory->parent_id);
        $this->assertTrue($parentCategory->hasChildren());
        $this->assertEquals(1, $childCategory->getDepth());
    }

    /** @test */
    public function it_can_find_categories_by_different_criteria()
    {
        // Arrange
        $this->createTestCategories();

        // Act & Assert - Root categories (parent_id is null)
        $rootCategories = $this->categoryRepository->findRootCategories();
        $this->assertGreaterThanOrEqual(2, $rootCategories->count()); // En az 2 olmalı

        // Act & Assert - Active categories
        $activeCategories = $this->categoryRepository->findActive();
        $this->assertGreaterThanOrEqual(3, $activeCategories->count()); // En az 3 olmalı

        // Act & Assert - Featured categories
        $featuredCategories = $this->categoryRepository->findFeatured();
        $this->assertGreaterThanOrEqual(1, $featuredCategories->count()); // En az 1 olmalı

        // Act & Assert - Menu categories
        $menuCategories = $this->categoryRepository->findShownInMenu();
        $this->assertGreaterThanOrEqual(2, $menuCategories->count()); // En az 2 olmalı
    }

    /** @test */
    public function it_can_find_category_by_slug()
    {
        // Arrange
        $category = $this->categoryRepository->create([
            'name' => 'Slug Test',
            'slug' => 'slug-test',
            'status' => true,
        ]);

        // Act
        $foundCategory = $this->categoryRepository->findBySlug('slug-test');

        // Assert
        $this->assertNotNull($foundCategory);
        $this->assertEquals($category->getId(), $foundCategory->getId());
    }

    /** @test */
    public function it_can_check_slug_uniqueness()
    {
        // Arrange
        $this->categoryRepository->create([
            'name' => 'Existing Category',
            'slug' => 'existing-slug',
            'status' => true,
        ]);

        // Act & Assert
        $this->assertFalse($this->categoryRepository->isSlugUnique('existing-slug'));
        $this->assertTrue($this->categoryRepository->isSlugUnique('new-slug'));
    }

    /** @test */
    public function it_can_build_category_tree()
    {
        // Arrange
        $parent1 = $this->categoryRepository->create([
            'name' => 'Parent 1',
            'slug' => 'parent-1',
            'status' => true,
            'position' => 1,
        ]);

        $child1 = $this->categoryRepository->create([
            'name' => 'Child 1',
            'slug' => 'child-1',
            'parent_id' => $parent1->getId(),
            'status' => true,
            'position' => 1,
        ]);

        $parent2 = $this->categoryRepository->create([
            'name' => 'Parent 2',
            'slug' => 'parent-2',
            'status' => true,
            'position' => 2,
        ]);

        // Act
        $tree = $this->categoryRepository->getCategoryTree();

        // Assert
        $this->assertCount(2, $tree);
        $this->assertEquals('Parent 1', $tree[0]['name']);
        $this->assertCount(1, $tree[0]['children']);
        $this->assertEquals('Child 1', $tree[0]['children'][0]['name']);
    }

    /** @test */
    public function it_can_get_category_hierarchy()
    {
        // Arrange
        $grandParent = $this->categoryRepository->create([
            'name' => 'Grand Parent',
            'slug' => 'grand-parent',
            'status' => true,
        ]);

        $parent = $this->categoryRepository->create([
            'name' => 'Parent',
            'slug' => 'parent',
            'parent_id' => $grandParent->getId(),
            'status' => true,
        ]);

        $child = $this->categoryRepository->create([
            'name' => 'Child',
            'slug' => 'child',
            'parent_id' => $parent->getId(),
            'status' => true,
        ]);

        // Act
        $hierarchy = $this->categoryRepository->getCategoryHierarchy($child->getId());

        // Assert
        $this->assertCount(3, $hierarchy);
        $this->assertEquals('Grand Parent', $hierarchy->first()->name);
        $this->assertEquals('Child', $hierarchy->last()->name);
    }

    /** @test */
    public function it_can_manage_category_positions()
    {
        // Arrange
        $category1 = $this->categoryRepository->create([
            'name' => 'Category 1',
            'slug' => 'category-1',
            'status' => true,
            'position' => 1,
        ]);

        $category2 = $this->categoryRepository->create([
            'name' => 'Category 2',
            'slug' => 'category-2',
            'status' => true,
            'position' => 2,
        ]);

        // Act
        $maxPosition = $this->categoryRepository->getMaxPosition();
        $reorderResult = $this->categoryRepository->reorderCategories([
            ['id' => $category1->getId(), 'position' => 2],
            ['id' => $category2->getId(), 'position' => 1],
        ]);

        // Assert
        $this->assertEquals(2, $maxPosition);
        $this->assertTrue($reorderResult);
    }

    /** @test */
    public function it_can_check_if_category_can_be_deleted()
    {
        // Arrange
        $emptyCategory = $this->categoryRepository->create([
            'name' => 'Empty Category',
            'slug' => 'empty-category',
            'status' => true,
        ]);

        $parentCategory = $this->categoryRepository->create([
            'name' => 'Parent Category',
            'slug' => 'parent-category',
            'status' => true,
        ]);

        $this->categoryRepository->create([
            'name' => 'Child Category',
            'slug' => 'child-category',
            'parent_id' => $parentCategory->getId(),
            'status' => true,
        ]);

        // Act & Assert
        $this->assertTrue($this->categoryRepository->canBeDeleted($emptyCategory->getId()));
        $this->assertFalse($this->categoryRepository->canBeDeleted($parentCategory->getId()));
    }

    /** @test */
    public function it_can_handle_business_logic()
    {
        // Arrange
        $category = $this->categoryRepository->create([
            'name' => 'Business Logic Test',
            'slug' => 'business-logic-test',
            'status' => true,
            'featured' => true,
            'show_in_menu' => false,
        ]);

        // Act & Assert
        $this->assertTrue($category->isActive());
        $this->assertTrue($category->isFeatured());
        $this->assertFalse($category->isShownInMenu());
        $this->assertTrue($category->isRoot());
        $this->assertFalse($category->hasChildren());
        $this->assertEquals(0, $category->getDepth());
    }

    /**
     * Test kategorileri oluştur
     */
    private function createTestCategories(): void
    {
        // Root category 1 - active, featured, in menu
        $this->categoryRepository->create([
            'name' => 'Root 1',
            'slug' => 'root-1',
            'status' => true,
            'featured' => true,
            'show_in_menu' => true,
            'position' => 1,
        ]);

        // Root category 2 - active, not featured, in menu
        $this->categoryRepository->create([
            'name' => 'Root 2',
            'slug' => 'root-2',
            'status' => true,
            'featured' => false,
            'show_in_menu' => true,
            'position' => 2,
        ]);

        // Active category not in menu (3rd active category)
        $this->categoryRepository->create([
            'name' => 'Not in Menu',
            'slug' => 'not-in-menu',
            'status' => true,
            'featured' => false,
            'show_in_menu' => false,
            'position' => 3,
        ]);

        // Inactive category (not counted in active)
        $this->categoryRepository->create([
            'name' => 'Inactive',
            'slug' => 'inactive',
            'status' => false,
            'featured' => false,
            'show_in_menu' => false,
            'position' => 4,
        ]);
    }
}
