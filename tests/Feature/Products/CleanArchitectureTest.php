<?php

namespace Tests\Feature\Products;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Modules\Products\Application\Services\ProductService;
use App\Modules\Products\Application\DTOs\CreateProductDTO;
use App\Modules\Products\Application\DTOs\UpdateProductDTO;
use App\Modules\Products\Application\DTOs\ManageStockDTO;
use App\Modules\Products\Domain\Models\Product;
use App\Core\Domain\ValueObjects\Money;

/**
 * Clean Architecture Test
 * Clean Architecture implementasyonunu test eder
 */
class CleanArchitectureTest extends TestCase
{
    use RefreshDatabase;

    private ProductService $productService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productService = app(ProductService::class);
    }

    /** @test */
    public function it_can_create_product_using_clean_architecture()
    {
        // Arrange
        $productData = [
            'name' => 'Test Ürün',
            'description' => 'Test açıklaması',
            'price' => 99.99,
            'stock' => 10,
            'status' => true,
        ];

        // Act
        $product = $this->productService->createProduct($productData);

        // Assert
        $this->assertInstanceOf(Product::class, $product);
        $this->assertEquals('Test Ürün', $product->name);
        $this->assertEquals('test-urun', $product->slug);
        $this->assertNotNull($product->sku);
        $this->assertEquals(99.99, $product->price);
        $this->assertEquals(10, $product->stock);
        $this->assertTrue($product->status);
    }

    /** @test */
    public function it_can_update_product_using_clean_architecture()
    {
        // Arrange
        $product = $this->createTestProduct();
        $updateData = [
            'name' => 'Güncellenmiş Ürün',
            'price' => 149.99,
        ];

        // Act
        $updatedProduct = $this->productService->updateProduct($product->id, $updateData);

        // Assert
        $this->assertEquals('Güncellenmiş Ürün', $updatedProduct->name);
        $this->assertEquals('guncellenmis-urun', $updatedProduct->slug);
        $this->assertEquals(149.99, $updatedProduct->price);
    }

    /** @test */
    public function it_can_manage_stock_using_clean_architecture()
    {
        // Arrange
        $product = $this->createTestProduct(['stock' => 50]);

        // Act - Stok artırma
        $increasedProduct = $this->productService->increaseStock($product->id, 20, 'Test artırma');

        // Assert
        $this->assertEquals(70, $increasedProduct->stock);

        // Act - Stok azaltma
        $decreasedProduct = $this->productService->decreaseStock($product->id, 15, 'Test azaltma');

        // Assert
        $this->assertEquals(55, $decreasedProduct->stock);

        // Act - Stok belirleme
        $setProduct = $this->productService->setStock($product->id, 100, 'Test belirleme');

        // Assert
        $this->assertEquals(100, $setProduct->stock);
    }

    /** @test */
    public function it_can_check_stock_availability()
    {
        // Arrange
        $product = $this->createTestProduct(['stock' => 25]);

        // Act
        $availability = $this->productService->checkStockAvailability($product->id, 10);

        // Assert
        $this->assertTrue($availability['available']);
        $this->assertEquals(25, $availability['current_stock']);
        $this->assertEquals(10, $availability['requested_quantity']);
        $this->assertEquals(0, $availability['shortage']);
    }

    /** @test */
    public function it_can_calculate_product_price()
    {
        // Arrange
        $product = $this->createTestProduct(['price' => 100.00]);

        // Act
        $calculation = $this->productService->calculatePrice($product->id, 5);

        // Assert
        $this->assertEquals(100.00, $calculation['active_unit_price']['amount_major']);
        $this->assertEquals(500.00, $calculation['subtotal']['amount_major']);
        $this->assertArrayHasKey('tax_amount', $calculation);
        $this->assertArrayHasKey('total', $calculation);
    }

    /** @test */
    public function it_can_search_products()
    {
        // Arrange
        $this->createTestProduct(['name' => 'Laptop']);
        $this->createTestProduct(['name' => 'Mouse']);
        $this->createTestProduct(['name' => 'Keyboard']);

        // Act
        $results = $this->productService->searchProducts([
            'query' => 'Laptop',
            'per_page' => 10
        ]);

        // Assert
        $this->assertEquals(1, $results->count());
        $this->assertEquals('Laptop', $results->first()->name);
    }

    /** @test */
    public function it_can_get_featured_products()
    {
        // Arrange
        $this->createTestProduct(['name' => 'Normal Ürün', 'is_featured' => false]);
        $this->createTestProduct(['name' => 'Öne Çıkan Ürün', 'is_featured' => true]);

        // Act
        $featuredProducts = $this->productService->getFeaturedProducts(10);

        // Assert
        $this->assertEquals(1, $featuredProducts->count());
        $this->assertEquals('Öne Çıkan Ürün', $featuredProducts->first()->name);
    }

    /** @test */
    public function it_can_handle_value_objects()
    {
        // Arrange
        $product = $this->createTestProduct(['price' => 99.99]);

        // Act
        $priceAsMoney = $product->getPriceAsMoney();

        // Assert
        $this->assertInstanceOf(Money::class, $priceAsMoney);
        $this->assertEquals(99.99, $priceAsMoney->getAmountInMajorUnit());
        $this->assertEquals('TRY', $priceAsMoney->getCurrency());
    }

    /** @test */
    public function it_can_handle_business_logic()
    {
        // Arrange
        $product = $this->createTestProduct([
            'stock' => 5,
            'is_on_sale' => true,
            'sale_price' => 79.99,
            'price' => 99.99
        ]);

        // Act & Assert
        $this->assertTrue($product->isInStock());
        $this->assertTrue($product->isOnSale());
        $this->assertEquals(79.99, $product->getActivePriceAsMoney()->getAmountInMajorUnit());
    }

    /** @test */
    public function it_throws_exception_for_insufficient_stock()
    {
        // Arrange
        $product = $this->createTestProduct(['stock' => 5]);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Insufficient stock');

        $this->productService->decreaseStock($product->id, 10);
    }

    /**
     * Test ürünü oluştur
     *
     * @param array $overrides
     * @return Product
     */
    private function createTestProduct(array $overrides = []): Product
    {
        $defaultData = [
            'name' => 'Test Ürün',
            'description' => 'Test açıklaması',
            'price' => 99.99,
            'stock' => 10,
            'status' => true,
        ];

        $productData = array_merge($defaultData, $overrides);

        return $this->productService->createProduct($productData);
    }
}
