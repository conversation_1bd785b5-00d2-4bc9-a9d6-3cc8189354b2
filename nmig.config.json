{"README": ["This JSON document represents the main configuration options.", "Please, be mindful, read each option's description before setting its value."], "source_description": ["Connection parameters to your MySQL database", "Please ensure, that you have defined all parameters properly.", "Ensure, that details like 'charset' are included (if necessary).", "Note, any parameter, acceptable by the node mysql module can be placed here.", "Check the 'source.host' parameter, that can be either '127.0.0.1' or 'localhost', depends on your setup."], "source": {"host": "127.0.0.1", "port": 3306, "database": "world", "charset": "utf8mb4", "supportBigNumbers": true, "user": "root", "password": "root"}, "target_description": ["Connection parameters to your PostgreSQL database", "Please ensure, that you have defined all parameters properly.", "Ensure, that details like 'charset' are included (if necessary).", "Note, any parameter, acceptable by the node pg module can be placed here.", "Check the 'target.host' parameter, that can be either '127.0.0.1' or 'localhost', depends on your setup."], "target": {"host": "127.0.0.1", "port": 5432, "database": "world", "charset": "UTF8", "user": "postgres", "password": "postgres"}, "max_each_db_connection_pool_size_description": ["Maximal amount of simultaneous connections to your MySQL and PostgreSQL servers each.", "Example: 20 means, that Nmig will create a pool of 20 connections to the source MySQL server", "and 20 connections to the target PostgreSQL server.", "40 connections to both servers."], "max_each_db_connection_pool_size": 20, "number_of_simultaneously_running_loader_processes_description": ["Number of loader processes that will run simultaneously.", "Acceptable values:", "1. 'DEFAULT' - when set to 'DEFAULT', Nmig will run 2 loader processes.", "2. Any positive integer.", "Note:", "1.", "The 'loader process' is a term referred to a data-transfer (from MySQL to PostgreSQL) procedure.", "Note that each procedure uses two Nodejs processes: 'data-reader' to stream data out of MySQL", "and apply some transformations, and 'data-writer' to stream data to PostgreSQL.", "This way Nmig implements truly parallel streaming pipeline.", "Such an implementation guaranties significant performance boost in comparison with", "a regular, non-parallel streaming pipeline.", "2.", "Usually, migration gets accomplished faster with only 2 loader processes,", "even if more CPU cores are available.", "Yet, it is worth an effort to 'play' with this parameter prior running migration in production,", "to check how many processes work best in your case.", "When trying Nmig on your test-database, try both decreasing and increasing this number.", "3.", "'number_of_simultaneously_running_loader_processes' will never be greater", "than a number of logical CPU cores of the machine running Nmig.", "If greater number chosen - Nmig will run one loader process per each available CPU core."], "number_of_simultaneously_running_loader_processes": "DEFAULT", "loader_max_old_space_size_description": ["V8 memory limit of the 'loader process'.", "Note, all the 'loader process' params are equally applicable", "to both 'data-reader' and 'data-writer' processes.", "Possible values are:", "1. Any positive integer, representing memory limit (in MB).", "2. 'DEFAULT', representing V8 default memory limit of your current hardware."], "loader_max_old_space_size": "DEFAULT", "streams_high_water_mark_description": ["Buffer level when stream.write() starts returning false.", "This number is a number of JavaScript objects."], "streams_high_water_mark": 16384, "encoding_description": ["JavaScript encoding type.", "If not supplied, then utf8 will be used as a default."], "encoding": "utf8", "schema_description": ["A name of the schema, that will contain all migrated tables.", "If not supplied, then a new schema will be created automatically."], "schema": "public", "exclude_tables_description": ["List (Array) of tables, that will not be migrated.", "By default, nmig will migrate all tables."], "exclude_tables": [], "include_tables_description": ["List (Array) of tables, that will be migrated.", "By default, nmig will migrate all tables."], "include_tables": [], "migrate_only_data_description": ["In order to skip schema migration, and just migrate data into a preset tables", " - set this parameter true."], "migrate_only_data": false, "delimiter_description": ["Specifies the character, that separates columns within each record.", "This must be a single one-byte character.", "The default is comma."], "delimiter": ",", "enable_extra_config_description": ["In order to enable the additional configuration options, placed at extra_config.json", " - set this parameter true."], "enable_extra_config": false}