# Categories Modülü - Clean Architecture İmplementasyonu

Bu dokümantasyon, Categories modülü için Clean Architecture implementasyonunu açıklamaktadır.

## 📁 Dizin Yapısı

```
app/
├── Domain/Categories/                # Domain Layer
│   ├── Entities/                     # Domain Entities
│   │   ├── Category.php              # Ana kategori entity'si (Aggregate Root)
│   │   └── CategoryAttribute.php     # Kategori özelliği entity'si
│   ├── ValueObjects/                 # Value Objects
│   │   ├── CategorySlug.php          # Kategori slug'ı
│   │   ├── CategoryPath.php          # Kategori yolu (hierarchical)
│   │   └── SEOData.php               # SEO verileri
│   ├── Events/                       # Domain Events
│   │   ├── CategoryCreated.php       # Kategori oluşturuldu
│   │   ├── CategoryUpdated.php       # Kategori güncellendi
│   │   ├── CategoryMoved.php         # Kategori taşındı
│   │   └── CategoryDeleted.php       # Kategori silindi
│   ├── Repositories/                 # Repository Interfaces
│   │   └── CategoryRepositoryInterface.php
│   └── Exceptions/                   # Domain Exceptions
│       ├── CategoryNotFoundException.php
│       └── InvalidCategoryOperationException.php
├── Application/Categories/           # Application Layer
│   ├── Commands/                     # Command Objects
│   │   ├── CreateCategoryCommand.php
│   │   ├── UpdateCategoryCommand.php
│   │   ├── MoveCategoryCommand.php
│   │   └── DeleteCategoryCommand.php
│   ├── Queries/                      # Query Objects
│   │   ├── GetCategoryQuery.php
│   │   ├── GetCategoriesQuery.php
│   │   └── GetCategoryTreeQuery.php
│   ├── Handlers/                     # Command/Query Handlers
│   │   ├── CreateCategoryHandler.php
│   │   ├── UpdateCategoryHandler.php
│   │   ├── MoveCategoryHandler.php
│   │   ├── DeleteCategoryHandler.php
│   │   ├── GetCategoryHandler.php
│   │   ├── GetCategoriesHandler.php
│   │   └── GetCategoryTreeHandler.php
│   ├── DTOs/                         # Data Transfer Objects
│   │   ├── CategoryDTO.php
│   │   └── CategoryAttributeDTO.php
│   └── Services/                     # Application Services
│       └── CategoryApplicationService.php
└── Infrastructure/Categories/        # Infrastructure Layer (gelecekte)
    └── Repositories/
        └── EloquentCategoryRepository.php
```

## 🏗️ Mimari Katmanlar

### 1. Domain Layer (Domain/Categories/)

**Entities:**
- `Category`: Ana kategori aggregate root'u (AggregateRoot trait kullanır)
- `CategoryAttribute`: Kategori özellikleri

**Value Objects:**
- `CategorySlug`: SEO dostu URL slug'ı (validation, normalization)
- `CategoryPath`: Hierarchical kategori yolu (ancestor/descendant operations)
- `SEOData`: SEO meta verileri (structured data dahil)

**Domain Events:**
- `CategoryCreated`: Yeni kategori oluşturulduğunda
- `CategoryUpdated`: Kategori güncellendiğinde
- `CategoryMoved`: Kategori taşındığında (parent değişimi)
- `CategoryDeleted`: Kategori silindiğinde

### 2. Application Layer (Application/Categories/)

**Commands (Write Operations):**
- `CreateCategoryCommand`: Yeni kategori oluşturma
- `UpdateCategoryCommand`: Kategori güncelleme
- `MoveCategoryCommand`: Kategori taşıma (parent değiştirme)
- `DeleteCategoryCommand`: Kategori silme

**Queries (Read Operations):**
- `GetCategoryQuery`: Tek kategori getirme (ID, slug ile)
- `GetCategoriesQuery`: Kategori listesi (filtreleme, arama)
- `GetCategoryTreeQuery`: Kategori ağacı (hierarchical)

**Handlers:**
- Command ve Query handler'ları business logic'i yönetir
- Domain event'leri dispatch eder
- Tree operations yönetimi

## 🌳 Hierarchical Özellikler

### **Tree Operations:**
- **Parent-Child İlişkileri**: Sınırsız derinlik
- **Path Management**: Ancestor/descendant tracking
- **Level Calculation**: Otomatik seviye hesaplama
- **Move Operations**: Güvenli kategori taşıma
- **Tree Validation**: Circular reference kontrolü

### **CategoryPath Value Object:**
```php
// Kategori yolu işlemleri
$path = CategoryPath::fromArray([1, 5, 12]); // /1/5/12
$isDescendant = $childPath->isDescendantOf($parentPath);
$commonAncestor = $path1->getCommonAncestorPath($path2);
$depth = $path->getDepth(); // 3
```

### **Category Entity Methods:**
```php
// Hierarchical kontroller
$category->isRoot(); // Parent'ı yok mu?
$category->hasChildren(); // Alt kategorisi var mı?
$category->isDescendantOf($otherCategory);
$category->isAncestorOf($otherCategory);
$category->isSiblingOf($otherCategory);
$category->canMoveTo($newParentId); // Taşınabilir mi?
```

## 🚀 Kullanım Örnekleri

### Yeni Kategori Oluşturma

```php
use App\Application\Categories\Services\CategoryApplicationService;

$categoryService = app(CategoryApplicationService::class);

$categoryDTO = $categoryService->createCategory(
    name: 'Elektronik',
    slug: 'elektronik',
    parentId: null, // Root kategori
    description: 'Elektronik ürünler kategorisi',
    position: 1,
    status: true,
    featured: true,
    showInMenu: true,
    image: '/images/elektronik.jpg',
    metaTitle: 'Elektronik Ürünler',
    metaDescription: 'En kaliteli elektronik ürünleri burada bulabilirsiniz.',
    attributes: [
        [
            'attribute_id' => 1,
            'is_required' => true,
            'is_filterable' => true
        ]
    ]
);
```

### Kategori Taşıma

```php
$categoryDTO = $categoryService->moveCategory(
    categoryId: 5,
    newParentId: 2,
    newPosition: 3
);
```

### Kategori Ağacı Alma

```php
$tree = $categoryService->getCategoryTree(
    rootId: null, // Tüm ağaç
    maxDepth: 5,
    status: true,
    showInMenu: true,
    includeProductCount: true
);
```

## 🔧 Özellikler

### **Kategori Yönetimi:**
- Hierarchical kategori yapısı
- Sınırsız derinlik desteği
- Slug yönetimi (SEO dostu URL'ler)
- Pozisyon bazlı sıralama
- Durum yönetimi (aktif/pasif)

### **Tree Operations:**
- Güvenli kategori taşıma
- Circular reference kontrolü
- Path recalculation
- Level management
- Ancestor/descendant queries

### **SEO Optimizasyonu:**
- Meta title, description, keywords
- Open Graph verileri
- Structured data (JSON-LD)
- Canonical URL
- Breadcrumb navigation

### **Attribute System:**
- Kategori bazlı özellikler
- Required/optional attributes
- Filterable attributes
- Searchable attributes
- Validation rules

### **Display Options:**
- Featured categories
- Menu visibility
- Image ve icon desteği
- Position-based ordering

## 📊 Domain Events

Kategori işlemleri sırasında aşağıdaki domain event'ler tetiklenir:

1. **CategoryCreated**: Yeni kategori oluşturulduğunda
   - Search index güncelleme
   - Cache temizleme
   - Menu regeneration

2. **CategoryMoved**: Kategori taşındığında
   - Path recalculation
   - Child categories update
   - URL redirects

3. **CategoryUpdated**: Kategori güncellendiğinde
   - Cache invalidation
   - SEO data update
   - Menu refresh

4. **CategoryDeleted**: Kategori silindiğinde
   - Product reassignment
   - URL cleanup
   - Cache purge

## 🔍 Arama ve Filtreleme

- **Hierarchical Search**: Parent-child ilişkilerinde arama
- **Level-based Filtering**: Seviye bazlı filtreleme
- **Status Filtering**: Aktif/pasif kategori filtreleme
- **Featured Categories**: Öne çıkan kategoriler
- **Menu Categories**: Menüde gösterilen kategoriler

## 🛡️ Validasyon ve Business Rules

- **Slug Benzersizliği**: Sistem genelinde benzersiz slug
- **Circular Reference**: Kategori kendisinin alt kategorisi olamaz
- **Move Validation**: Geçerli taşıma işlemleri
- **Delete Constraints**: Alt kategori ve ürün kontrolü
- **Path Integrity**: Kategori yolu bütünlüğü

## 🔮 Gelecek Geliştirmeler

- [ ] Infrastructure Layer implementasyonu
- [ ] Advanced tree operations (bulk move, copy)
- [ ] Category templates
- [ ] Multi-language support
- [ ] Category analytics
- [ ] Auto-categorization
- [ ] Category recommendations
- [ ] Performance optimizations (nested sets)

## 📚 Referanslar

- [Nested Set Model](https://en.wikipedia.org/wiki/Nested_set_model)
- [Adjacency List Model](https://en.wikipedia.org/wiki/Adjacency_list)
- [Tree Traversal Algorithms](https://en.wikipedia.org/wiki/Tree_traversal)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
