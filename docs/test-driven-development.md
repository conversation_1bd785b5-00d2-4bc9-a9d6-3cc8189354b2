# Test Odaklı Geliştirme Rehberi

## 🎯 Test Stratejisi

### 1. Test Piramidi Yaklaşımı

```
        /\
       /  \
      / E2E \     <- <PERSON><PERSON> sayıda, yava<PERSON>, pahalı
     /______\
    /        \
   / Integration \ <- <PERSON><PERSON> say<PERSON><PERSON>, orta hızda
  /______________\
 /                \
/   Unit Tests     \ <- Çok sayıda, hızlı, ucuz
\__________________/
```

### 2. Test Türleri ve Kapsamları

#### Unit Tests (70%)
- Domain logic
- Service methods
- Value objects
- Utility functions

#### Integration Tests (20%)
- Repository implementations
- External service integrations
- Database operations
- API endpoints

#### End-to-End Tests (10%)
- Complete user workflows
- Critical business processes
- UI interactions

### 3. Test Klasör Yapısı

```
tests/
├── Unit/
│   ├── Domain/
│   │   ├── Products/
│   │   │   ├── ProductTest.php
│   │   │   ├── ProductServiceTest.php
│   │   │   └── ValueObjects/
│   │   │       └── PriceTest.php
│   │   ├── Orders/
│   │   └── Users/
│   ├── Application/
│   │   ├── Services/
│   │   └── Commands/
│   └── Infrastructure/
│       ├── Repositories/
│       └── Services/
├── Feature/
│   ├── Api/
│   │   ├── ProductApiTest.php
│   │   ├── OrderApiTest.php
│   │   └── AuthApiTest.php
│   ├── Web/
│   │   ├── ProductWebTest.php
│   │   └── CheckoutTest.php
│   └── Admin/
│       ├── ProductManagementTest.php
│       └── OrderManagementTest.php
├── Integration/
│   ├── Database/
│   ├── ExternalServices/
│   └── Cache/
└── Browser/
    ├── CustomerJourneyTest.php
    └── AdminWorkflowTest.php
```

### 4. Unit Test Örnekleri

#### Domain Entity Test

```php
// tests/Unit/Domain/Products/ProductTest.php
<?php

namespace Tests\Unit\Domain\Products;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\ProductName;
use PHPUnit\Framework\TestCase;

class ProductTest extends TestCase
{
    public function test_can_create_product_with_valid_data()
    {
        $name = new ProductName('Test Product');
        $price = new Price(100.00, 'TRY');
        
        $product = new Product($name, $price);
        
        $this->assertEquals('Test Product', $product->getName()->getValue());
        $this->assertEquals(100.00, $product->getPrice()->getAmount());
        $this->assertEquals('TRY', $product->getPrice()->getCurrency());
    }
    
    public function test_can_apply_discount()
    {
        $product = $this->createProduct();
        
        $product->applyDiscount(10); // 10% discount
        
        $this->assertEquals(90.00, $product->getDiscountedPrice()->getAmount());
        $this->assertTrue($product->hasDiscount());
    }
    
    public function test_cannot_apply_negative_discount()
    {
        $this->expectException(\InvalidArgumentException::class);
        
        $product = $this->createProduct();
        $product->applyDiscount(-5);
    }
    
    private function createProduct(): Product
    {
        return new Product(
            new ProductName('Test Product'),
            new Price(100.00, 'TRY')
        );
    }
}
```

#### Service Test

```php
// tests/Unit/Application/Services/ProductServiceTest.php
<?php

namespace Tests\Unit\Application\Services;

use App\Application\Services\ProductService;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Products\Entities\Product;
use Mockery;
use PHPUnit\Framework\TestCase;

class ProductServiceTest extends TestCase
{
    private ProductRepositoryInterface $repository;
    private ProductService $service;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->repository = Mockery::mock(ProductRepositoryInterface::class);
        $this->service = new ProductService($this->repository);
    }
    
    public function test_can_create_product()
    {
        $productData = [
            'name' => 'Test Product',
            'price' => 100.00,
            'currency' => 'TRY'
        ];
        
        $this->repository
            ->shouldReceive('save')
            ->once()
            ->andReturn(new Product(/* ... */));
        
        $result = $this->service->createProduct($productData);
        
        $this->assertInstanceOf(Product::class, $result);
    }
    
    public function test_throws_exception_for_invalid_product_data()
    {
        $this->expectException(\InvalidArgumentException::class);
        
        $invalidData = [
            'name' => '', // Invalid empty name
            'price' => -10 // Invalid negative price
        ];
        
        $this->service->createProduct($invalidData);
    }
    
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
```

### 5. Feature Test Örnekleri

#### API Test

```php
// tests/Feature/Api/ProductApiTest.php
<?php

namespace Tests\Feature\Api;

use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductApiTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_can_list_products()
    {
        $user = User::factory()->create();
        Product::factory()->count(3)->create();
        
        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/v1/products');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'price',
                        'created_at'
                    ]
                ]
            ])
            ->assertJsonCount(3, 'data');
    }
    
    public function test_can_create_product()
    {
        $admin = User::factory()->admin()->create();
        
        $productData = [
            'name' => 'New Product',
            'description' => 'Product description',
            'price' => 150.00,
            'stock' => 10
        ];
        
        $response = $this->actingAs($admin, 'sanctum')
            ->postJson('/api/v1/products', $productData);
        
        $response->assertStatus(201)
            ->assertJsonFragment([
                'name' => 'New Product',
                'price' => 150.00
            ]);
        
        $this->assertDatabaseHas('products', [
            'name' => 'New Product',
            'price' => 150.00
        ]);
    }
    
    public function test_unauthorized_user_cannot_create_product()
    {
        $user = User::factory()->create(); // Regular user, not admin
        
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/v1/products', [
                'name' => 'Unauthorized Product'
            ]);
        
        $response->assertStatus(403);
    }
}
```

### 6. Integration Test Örnekleri

#### Repository Test

```php
// tests/Integration/Database/ProductRepositoryTest.php
<?php

namespace Tests\Integration\Database;

use App\Infrastructure\Repositories\ProductRepository;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductRepositoryTest extends TestCase
{
    use RefreshDatabase;
    
    private ProductRepository $repository;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new ProductRepository();
    }
    
    public function test_can_find_products_by_category()
    {
        $category = Category::factory()->create();
        $products = Product::factory()->count(3)->create([
            'category_id' => $category->id
        ]);
        
        $result = $this->repository->findByCategory($category->id);
        
        $this->assertCount(3, $result);
        $this->assertEquals($products->pluck('id')->sort(), $result->pluck('id')->sort());
    }
    
    public function test_can_find_products_with_filters()
    {
        Product::factory()->create(['name' => 'iPhone 15', 'price' => 1000]);
        Product::factory()->create(['name' => 'Samsung Galaxy', 'price' => 800]);
        Product::factory()->create(['name' => 'iPhone 14', 'price' => 900]);
        
        $filters = [
            'name' => 'iPhone',
            'min_price' => 900
        ];
        
        $result = $this->repository->findWithFilters($filters);
        
        $this->assertCount(2, $result);
        $this->assertTrue($result->every(fn($product) => 
            str_contains($product->name, 'iPhone') && $product->price >= 900
        ));
    }
}
```

### 7. Test Utilities ve Helpers

#### Test Factory

```php
// tests/Factories/ProductFactory.php
<?php

namespace Tests\Factories;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\ValueObjects\Price;
use App\Domain\Products\ValueObjects\ProductName;

class ProductFactory
{
    public static function create(array $attributes = []): Product
    {
        $defaults = [
            'name' => 'Test Product',
            'price' => 100.00,
            'currency' => 'TRY'
        ];
        
        $data = array_merge($defaults, $attributes);
        
        return new Product(
            new ProductName($data['name']),
            new Price($data['price'], $data['currency'])
        );
    }
    
    public static function withDiscount(float $discount = 10): Product
    {
        $product = self::create();
        $product->applyDiscount($discount);
        return $product;
    }
}
```

#### Test Traits

```php
// tests/Traits/CreatesUsers.php
<?php

namespace Tests\Traits;

use App\Models\User;

trait CreatesUsers
{
    protected function createAdmin(): User
    {
        return User::factory()->admin()->create();
    }
    
    protected function createCustomer(): User
    {
        return User::factory()->customer()->create();
    }
    
    protected function createUserWithRole(string $role): User
    {
        $user = User::factory()->create();
        $user->assignRole($role);
        return $user;
    }
}
```

### 8. Test Configuration

#### PHPUnit Configuration

```xml
<!-- phpunit.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         executionOrder="random"
         failOnWarning="true"
         failOnRisky="true"
         failOnEmptyTestSuite="true"
         beStrictAboutOutputDuringTests="true"
         verbose="true">
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory>./app/Http/Middleware</directory>
            <file>./app/Http/Kernel.php</file>
        </exclude>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="MAIL_MAILER" value="array"/>
    </php>
</phpunit>
```

### 9. Continuous Integration

#### GitHub Actions

```yaml
# .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql
        coverage: xdebug
    
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
    
    - name: Copy environment file
      run: cp .env.testing .env
    
    - name: Generate application key
      run: php artisan key:generate
    
    - name: Run migrations
      run: php artisan migrate --force
    
    - name: Run tests
      run: php artisan test --coverage --min=80
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
```

Bu test stratejisi ile kod kalitesi ve güvenilirlik önemli ölçüde artacaktır.
