# CQRS (Command Query Responsibility Segregation) İmplementasyonu

## 🎯 Genel Bakış

Bu dokümantasyon, modularecommerce projesinde Phase 3C kapsamında implementasyonu tamamlanan CQRS pattern'ini açıklamaktadır.

## 📋 İçindekiler

1. [CQRS Nedir?](#cqrs-nedir)
2. [<PERSON><PERSON><PERSON>](#mimari-yapı)
3. [Implementasyon Detayları](#implementasyon-detayları)
4. [<PERSON>llan<PERSON><PERSON>](#kullanım-örnekleri)
5. [Test Stratejisi](#test-stratejisi)
6. [Performance ve Caching](#performance-ve-caching)
7. [Best Practices](#best-practices)

## 🔍 CQRS Nedir?

CQRS (Command Query Responsibility Segregation), okuma (Query) ve yazma (Command) işlemlerini ayrı sorumluluklar olarak ele alan bir mimari pattern'dir.

### Temel Prensipler

- **Command**: Sistem durumunu değ<PERSON><PERSON><PERSON> i<PERSON> (Create, Update, Delete)
- **Query**: Sistem durumunu okuyup veri dö<PERSON><PERSON>ren <PERSON> (Read)
- **Separation**: Command ve Query'lerin farklı model ve handler'larla işlenmesi
- **Scalability**: Okuma ve yazma işlemlerinin bağımsız olarak ölçeklenebilmesi

## 🏗️ Mimari Yapı

```
app/
├── Core/CQRS/                     # CQRS Core Infrastructure
│   ├── Contracts/                 # Interface'ler
│   │   ├── CommandInterface.php
│   │   ├── QueryInterface.php
│   │   ├── CommandHandlerInterface.php
│   │   ├── QueryHandlerInterface.php
│   │   ├── CommandBusInterface.php
│   │   └── QueryBusInterface.php
│   └── Base/                      # Base sınıflar
│       ├── BaseCommand.php
│       ├── BaseQuery.php
│       ├── BaseCommandHandler.php
│       └── BaseQueryHandler.php
│
├── Infrastructure/CQRS/           # CQRS Infrastructure
│   ├── CommandBus.php
│   ├── QueryBus.php
│   ├── CQRSServiceProvider.php
│   └── Middleware/
│       ├── ValidationMiddleware.php
│       ├── LoggingMiddleware.php
│       └── PerformanceMiddleware.php
│
└── Application/                   # Application Layer
    ├── Products/
    │   ├── Commands/
    │   │   └── CreateProductCommand.php
    │   ├── Queries/
    │   │   └── GetProductsQuery.php
    │   └── Handlers/
    │       ├── CreateProductHandler.php
    │       └── GetProductsHandler.php
    └── ...
```

## 🔧 Implementasyon Detayları

### 1. Command Pattern

```php
// Command örneği
class CreateProductCommand extends BaseCommand
{
    public function __construct(
        public readonly string $name,
        public readonly string $sku,
        public readonly float $price,
        // ... diğer parametreler
    ) {
        parent::__construct();
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'sku' => 'required|string|unique:products,sku',
            'price' => 'required|numeric|min:0',
        ];
    }
}
```

### 2. Command Handler Pattern

```php
// Command Handler örneği
class CreateProductHandler extends BaseCommandHandler
{
    public function getCommandClass(): string
    {
        return CreateProductCommand::class;
    }

    protected function execute(CommandInterface $command): ProductDTO
    {
        /** @var CreateProductCommand $command */
        // Business logic implementation
        return $productDTO;
    }
}
```

### 3. Query Pattern

```php
// Query örneği
class GetProductsQuery extends BaseQuery
{
    public function __construct(
        public readonly ?int $categoryId = null,
        public readonly int $limit = 10,
        // ... diğer parametreler
    ) {
        parent::__construct();
    }

    public function getCacheKey(): ?string
    {
        return 'products:list:' . md5(serialize($this->toArray()));
    }

    public function getCacheTtl(): ?int
    {
        return 300; // 5 dakika
    }
}
```

### 4. Query Handler Pattern

```php
// Query Handler örneği
class GetProductsHandler extends BaseQueryHandler
{
    public function getQueryClass(): string
    {
        return GetProductsQuery::class;
    }

    public function isCacheable(): bool
    {
        return true;
    }

    protected function execute(QueryInterface $query): array
    {
        /** @var GetProductsQuery $query */
        // Query implementation
        return $results;
    }
}
```

## 🚀 Kullanım Örnekleri

### Controller'da CQRS Kullanımı

```php
class CQRSProductController extends Controller
{
    public function __construct(
        private CommandBusInterface $commandBus,
        private QueryBusInterface $queryBus
    ) {}

    public function store(Request $request): JsonResponse
    {
        $command = new CreateProductCommand(
            name: $request->string('name')->toString(),
            sku: $request->string('sku')->toString(),
            price: $request->float('price'),
            // ... diğer parametreler
        );

        $result = $this->commandBus->dispatch($command);
        
        return ApiResponse::success($result, 'Product created successfully', 201);
    }

    public function index(Request $request): JsonResponse
    {
        $query = new GetProductsQuery(
            categoryId: $request->integer('category_id'),
            limit: $request->integer('limit', 10),
            // ... diğer parametreler
        );

        $result = $this->queryBus->dispatch($query);
        
        return ApiResponse::success($result, 'Products retrieved successfully');
    }
}
```

### Service'de CQRS Kullanımı

```php
class ProductApplicationService
{
    public function __construct(
        private CommandBusInterface $commandBus,
        private QueryBusInterface $queryBus
    ) {}

    public function createProduct(array $data): ProductDTO
    {
        $command = new CreateProductCommand(...$data);
        return $this->commandBus->dispatch($command);
    }

    public function getProducts(array $criteria): array
    {
        $query = new GetProductsQuery(...$criteria);
        return $this->queryBus->dispatch($query);
    }
}
```

## 🧪 Test Stratejisi

### Unit Test Örneği

```php
class CommandBusTest extends TestCase
{
    /** @test */
    public function it_can_dispatch_command(): void
    {
        // Arrange
        $command = new TestCommand('test data');
        $handler = Mockery::mock(TestCommandHandler::class);
        $handler->shouldReceive('handle')
            ->once()
            ->with($command)
            ->andReturn('success');

        $this->container->instance(TestCommandHandler::class, $handler);
        $this->commandBus->registerHandler(TestCommand::class, TestCommandHandler::class);

        // Act
        $result = $this->commandBus->dispatch($command);

        // Assert
        $this->assertEquals('success', $result);
    }
}
```

## ⚡ Performance ve Caching

### Query Caching

```php
// Otomatik cache
class GetProductsQuery extends BaseQuery
{
    public function getCacheKey(): ?string
    {
        return 'products:list:' . md5(serialize($this->toArray()));
    }

    public function getCacheTtl(): ?int
    {
        return 300; // 5 dakika
    }
}

// Handler'da cache kontrolü
class GetProductsHandler extends BaseQueryHandler
{
    public function isCacheable(): bool
    {
        return true; // Cache'leme aktif
    }
}
```

### Performance Monitoring

```php
// Middleware ile otomatik performance monitoring
'performance' => [
    'thresholds' => [
        'command' => [
            'warning' => 1000, // 1 saniye
            'critical' => 5000, // 5 saniye
        ],
        'query' => [
            'warning' => 500,  // 0.5 saniye
            'critical' => 2000, // 2 saniye
        ],
    ],
],
```

## 📝 Best Practices

### 1. Command Design

- Command'lar immutable olmalı
- Validation kuralları command içinde tanımlanmalı
- Business logic command'da değil handler'da olmalı

### 2. Query Design

- Query'ler side-effect'siz olmalı
- Cache stratejisi düşünülmeli
- Pagination parametreleri eklenmeli

### 3. Handler Design

- Single responsibility principle'a uymalı
- Transaction yönetimi command handler'larda yapılmalı
- Error handling ve logging eklenmeli

### 4. Performance

- Query'ler için cache kullanılmalı
- Command'lar için async processing düşünülmeli
- Performance threshold'ları belirlenmeli

## 🔧 Konfigürasyon

```php
// config/cqrs.php
return [
    'command_handlers' => [
        CreateProductCommand::class => CreateProductHandler::class,
        // ... diğer handler'lar
    ],
    
    'query_handlers' => [
        GetProductsQuery::class => GetProductsHandler::class,
        // ... diğer handler'lar
    ],
    
    'middlewares' => [
        'validation' => ['enabled' => true],
        'logging' => ['enabled' => true],
        'performance' => ['enabled' => true],
    ],
    
    'cache' => [
        'enabled' => true,
        'default_ttl' => 3600,
    ],
];
```

## 🎉 Sonuç

Phase 3C kapsamında CQRS pattern implementasyonu başarıyla tamamlanmıştır. Bu implementasyon:

- ✅ Command ve Query ayrımı sağlar
- ✅ Scalable architecture sunar
- ✅ Caching desteği içerir
- ✅ Performance monitoring sağlar
- ✅ Comprehensive test coverage'a sahiptir
- ✅ Middleware desteği sunar
- ✅ Clean Architecture prensiplerine uyar

Bir sonraki adım olarak Phase 4: Frontend Integration'a geçilebilir.
