<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    $columns = \Illuminate\Support\Facades\Schema::getColumnListing('shipping_companies');
    
    echo "Shipping Companies table columns:\n";
    foreach ($columns as $column) {
        echo "- $column\n";
    }
    
    // Test bir kargo şirketi oluşturmayı dene
    echo "\nTesting ShippingCompany creation...\n";
    
    $company = new \App\Models\ShippingCompany();
    $company->name = 'Test Kargo';
    $company->code = 'test_' . time();
    $company->description = 'Test açıklama';
    $company->is_active = true;
    
    $company->save();
    
    echo "✓ ShippingCompany created successfully with ID: " . $company->id . "\n";
    
    // Test kaydını sil
    $company->delete();
    echo "✓ Test record deleted\n";
    
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

?>
