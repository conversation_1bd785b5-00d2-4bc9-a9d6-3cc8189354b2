<?php

use App\Core\Infrastructure\Api\Documentation\Controllers\DocumentationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Documentation Routes
|--------------------------------------------------------------------------
|
| API documentation viewer ve generator route'ları
|
*/

// Documentation routes prefix
$prefix = config('api_documentation.routes.prefix', 'docs');
$middleware = config('api_documentation.routes.middleware', 'web');
$namePrefix = config('api_documentation.routes.name_prefix', 'api.docs.');

Route::prefix($prefix)
    ->middleware($middleware)
    ->name($namePrefix)
    ->group(function () {

        /*
        |--------------------------------------------------------------------------
        | Documentation Viewer Routes
        |--------------------------------------------------------------------------
        */

        // Main documentation page
        Route::get('/', [DocumentationController::class, 'index'])
            ->name('index');

        // Documentation with specific version
        Route::get('/v/{version}', [DocumentationController::class, 'index'])
            ->name('version')
            ->where('version', '[0-9]+\.[0-9]+');

        // Documentation with specific format
        Route::get('/{format}', [DocumentationController::class, 'index'])
            ->name('format')
            ->where('format', 'swagger-ui|redoc|rapidoc');

        // Documentation with version and format
        Route::get('/v/{version}/{format}', [DocumentationController::class, 'index'])
            ->name('version.format')
            ->where('version', '[0-9]+\.[0-9]+')
            ->where('format', 'swagger-ui|redoc|rapidoc');

        /*
        |--------------------------------------------------------------------------
        | API Specification Routes
        |--------------------------------------------------------------------------
        */

        // Get OpenAPI specification (latest version)
        Route::get('/spec', [DocumentationController::class, 'specification'])
            ->name('spec');

        // Get OpenAPI specification for specific version
        Route::get('/spec/v/{version}', [DocumentationController::class, 'specification'])
            ->name('spec.version')
            ->where('version', '[0-9]+\.[0-9]+');

        // Get specification in different formats
        Route::get('/spec.{format}', [DocumentationController::class, 'specification'])
            ->name('spec.format')
            ->where('format', 'json|yaml');

        // Get specification for specific version and format
        Route::get('/spec/v/{version}.{format}', [DocumentationController::class, 'specification'])
            ->name('spec.version.format')
            ->where('version', '[0-9]+\.[0-9]+')
            ->where('format', 'json|yaml');

        /*
        |--------------------------------------------------------------------------
        | Download Routes
        |--------------------------------------------------------------------------
        */

        // Download specification file
        Route::get('/download', [DocumentationController::class, 'download'])
            ->name('download');

        // Download specification for specific version
        Route::get('/download/v/{version}', [DocumentationController::class, 'download'])
            ->name('download.version')
            ->where('version', '[0-9]+\.[0-9]+');

        // Download in specific format
        Route::get('/download.{format}', [DocumentationController::class, 'download'])
            ->name('download.format')
            ->where('format', 'json|yaml');

        // Download specific version and format
        Route::get('/download/v/{version}.{format}', [DocumentationController::class, 'download'])
            ->name('download.version.format')
            ->where('version', '[0-9]+\.[0-9]+')
            ->where('format', 'json|yaml');

        /*
        |--------------------------------------------------------------------------
        | Postman Collection Routes
        |--------------------------------------------------------------------------
        */

        // Get Postman collection (latest version)
        Route::get('/postman', [DocumentationController::class, 'postman'])
            ->name('postman');

        // Get Postman collection for specific version
        Route::get('/postman/v/{version}', [DocumentationController::class, 'postman'])
            ->name('postman.version')
            ->where('version', '[0-9]+\.[0-9]+');

        /*
        |--------------------------------------------------------------------------
        | Utility Routes
        |--------------------------------------------------------------------------
        */

        // Get documentation metadata
        Route::get('/metadata', [DocumentationController::class, 'metadata'])
            ->name('metadata');

        // Compare API versions
        Route::get('/compare', [DocumentationController::class, 'compare'])
            ->name('compare');

        // Health check
        Route::get('/health', [DocumentationController::class, 'health'])
            ->name('health');

        /*
        |--------------------------------------------------------------------------
        | Admin Routes (Protected)
        |--------------------------------------------------------------------------
        */

        Route::middleware(['auth:sanctum', 'can:admin-api-docs'])
            ->prefix('admin')
            ->name('admin.')
            ->group(function () {

                // Generate documentation for all versions
                Route::post('/generate', [DocumentationController::class, 'generateAll'])
                    ->name('generate');

                // Clear documentation cache
                Route::delete('/cache', [DocumentationController::class, 'clearCache'])
                    ->name('cache.clear');

                // Regenerate specific version
                Route::post('/generate/v/{version}', [DocumentationController::class, 'generateVersion'])
                    ->name('generate.version')
                    ->where('version', '[0-9]+\.[0-9]+');

                // Validate documentation
                Route::post('/validate', [DocumentationController::class, 'validateDocumentation'])
                    ->name('validate');

                // Export documentation
                Route::post('/export', [DocumentationController::class, 'exportDocumentation'])
                    ->name('export');

            });

        /*
        |--------------------------------------------------------------------------
        | API Routes (JSON responses)
        |--------------------------------------------------------------------------
        */

        Route::prefix('api')
            ->name('api.')
            ->group(function () {

                // Get available versions
                Route::get('/versions', function () {
                    $versionManager = app(\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class);
                    
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'supported' => $versionManager->getSupportedVersions(),
                            'default' => $versionManager->getDefaultVersion(),
                            'latest' => $versionManager->getLatestVersion(),
                        ],
                    ]);
                })->name('versions');

                // Get supported formats
                Route::get('/formats', function () {
                    $generator = app(\App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface::class);
                    
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'specification' => $generator->getSupportedFormats(),
                            'ui' => ['swagger-ui', 'redoc', 'rapidoc'],
                            'download' => ['json', 'yaml'],
                        ],
                    ]);
                })->name('formats');

                // Get documentation statistics
                Route::get('/stats', function () {
                    $generator = app(\App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface::class);
                    $versionManager = app(\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class);
                    
                    $stats = [];
                    foreach ($versionManager->getSupportedVersions() as $version) {
                        try {
                            $spec = $generator->generateForVersion($version);
                            $stats[$version] = [
                                'paths' => count($spec['paths'] ?? []),
                                'schemas' => count($spec['components']['schemas'] ?? []),
                                'tags' => count($spec['tags'] ?? []),
                            ];
                        } catch (\Exception $e) {
                            $stats[$version] = [
                                'error' => $e->getMessage(),
                            ];
                        }
                    }
                    
                    return response()->json([
                        'success' => true,
                        'data' => $stats,
                    ]);
                })->name('stats');

                // Search documentation
                Route::get('/search', function (\Illuminate\Http\Request $request) {
                    $query = $request->get('q', '');
                    $version = $request->get('version');
                    
                    if (empty($query)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Search query is required',
                        ], 400);
                    }
                    
                    $generator = app(\App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface::class);
                    $versionManager = app(\App\Core\Infrastructure\Api\Versioning\Contracts\ApiVersionManagerInterface::class);
                    
                    $version = $version ?? $versionManager->getLatestVersion();
                    $spec = $generator->generateForVersion($version);
                    
                    $results = [];
                    
                    // Search in paths
                    foreach ($spec['paths'] ?? [] as $path => $methods) {
                        if (stripos($path, $query) !== false) {
                            $results[] = [
                                'type' => 'path',
                                'path' => $path,
                                'methods' => array_keys($methods),
                            ];
                        }
                        
                        foreach ($methods as $method => $operation) {
                            if (stripos($operation['summary'] ?? '', $query) !== false ||
                                stripos($operation['description'] ?? '', $query) !== false) {
                                $results[] = [
                                    'type' => 'operation',
                                    'path' => $path,
                                    'method' => $method,
                                    'summary' => $operation['summary'] ?? '',
                                ];
                            }
                        }
                    }
                    
                    // Search in schemas
                    foreach ($spec['components']['schemas'] ?? [] as $schemaName => $schema) {
                        if (stripos($schemaName, $query) !== false) {
                            $results[] = [
                                'type' => 'schema',
                                'name' => $schemaName,
                                'description' => $schema['description'] ?? '',
                            ];
                        }
                    }
                    
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'query' => $query,
                            'version' => $version,
                            'results' => $results,
                            'total' => count($results),
                        ],
                    ]);
                })->name('search');

            });

    });

/*
|--------------------------------------------------------------------------
| Redirect Routes
|--------------------------------------------------------------------------
*/

// Redirect /api-docs to main documentation
Route::redirect('/api-docs', "/{$prefix}", 301);

// Redirect old documentation URLs
Route::redirect('/swagger', "/{$prefix}/swagger-ui", 301);
Route::redirect('/redoc', "/{$prefix}/redoc", 301);

/*
|--------------------------------------------------------------------------
| Conditional Routes
|--------------------------------------------------------------------------
*/

// Only register routes if documentation is enabled
if (config('api_documentation.enabled', true)) {
    
    // Development-only routes
    if (app()->environment('local', 'development')) {
        Route::prefix($prefix)
            ->middleware($middleware)
            ->name($namePrefix . 'dev.')
            ->group(function () {
                
                // Debug route
                Route::get('/debug', function () {
                    $generator = app(\App\Core\Infrastructure\Api\Documentation\Contracts\DocumentationGeneratorInterface::class);
                    
                    return response()->json([
                        'metadata' => $generator->getMetadata(),
                        'config' => config('api_documentation'),
                        'routes' => collect(Route::getRoutes())->map(function ($route) {
                            return [
                                'uri' => $route->uri(),
                                'methods' => $route->methods(),
                                'name' => $route->getName(),
                            ];
                        })->values(),
                    ]);
                })->name('debug');
                
            });
    }
}
