<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\Products\CQRSProductController;

/**
 * CQRS API Routes
 * Phase 4: Frontend Integration
 * 
 * Bu route'lar CQRS pattern'e uygun olarak Command ve Query işlemlerini ayırır
 */

Route::prefix('cqrs')->name('cqrs.')->group(function () {
    
    // ==================== PRODUCT CQRS ROUTES ====================
    
    Route::prefix('products')->name('products.')->group(function () {
        
        // Query Routes (Read Operations)
        Route::prefix('queries')->name('queries.')->group(function () {
            Route::get('list', [CQRSProductController::class, 'index'])->name('list');
            Route::get('show/{id}', [CQRSProductController::class, 'show'])->name('show');
            Route::get('search', [CQRSProductController::class, 'search'])->name('search');
        });
        
        // Command Routes (Write Operations) - Auth Required
        Route::middleware(['auth:sanctum'])->prefix('commands')->name('commands.')->group(function () {
            Route::post('create', [CQRSProductController::class, 'store'])->name('create');
            Route::put('update/{id}', [CQRSProductController::class, 'update'])->name('update');
            Route::delete('delete/{id}', [CQRSProductController::class, 'destroy'])->name('delete');
            Route::post('bulk', [CQRSProductController::class, 'bulk'])->name('bulk');
        });
    });
    
    // ==================== CATEGORY CQRS ROUTES ====================
    
    Route::prefix('categories')->name('categories.')->group(function () {
        
        // Query Routes (Read Operations)
        Route::prefix('queries')->name('queries.')->group(function () {
            Route::get('list', [\App\Http\Controllers\Api\CategoryController::class, 'index'])->name('list');
            Route::get('tree', [\App\Http\Controllers\Api\CategoryController::class, 'tree'])->name('tree');
            Route::get('show/{id}', [\App\Http\Controllers\Api\CategoryController::class, 'show'])->name('show');
        });
        
        // Command Routes (Write Operations) - Auth Required
        Route::middleware(['auth:sanctum'])->prefix('commands')->name('commands.')->group(function () {
            Route::post('create', [\App\Http\Controllers\Api\CategoryController::class, 'store'])->name('create');
            Route::put('update/{id}', [\App\Http\Controllers\Api\CategoryController::class, 'update'])->name('update');
            Route::delete('delete/{id}', [\App\Http\Controllers\Api\CategoryController::class, 'destroy'])->name('delete');
            Route::post('bulk', [\App\Http\Controllers\Api\CategoryController::class, 'bulk'])->name('bulk');
        });
    });
    
    // ==================== ORDER CQRS ROUTES ====================
    
    Route::prefix('orders')->name('orders.')->group(function () {
        
        // Query Routes (Read Operations)
        Route::prefix('queries')->name('queries.')->group(function () {
            Route::get('list', [\App\Http\Controllers\Api\OrderController::class, 'index'])->name('list');
            Route::get('show/{id}', [\App\Http\Controllers\Api\OrderController::class, 'show'])->name('show');
            Route::get('user-orders', [\App\Http\Controllers\Api\OrderController::class, 'userOrders'])->name('user_orders');
        });
        
        // Command Routes (Write Operations) - Auth Required
        Route::middleware(['auth:sanctum'])->prefix('commands')->name('commands.')->group(function () {
            Route::post('create', [\App\Http\Controllers\Api\OrderController::class, 'store'])->name('create');
            Route::put('update-status/{id}', [\App\Http\Controllers\Api\OrderController::class, 'updateStatus'])->name('update_status');
            Route::post('cancel/{id}', [\App\Http\Controllers\Api\OrderController::class, 'cancel'])->name('cancel');
        });
    });
    
    // ==================== CQRS UTILITY ROUTES ====================
    
    // CQRS Status and Health Check
    Route::get('status', [CQRSProductController::class, 'status'])->name('status');
    
    // Command Handlers
    Route::get('commands/handlers', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'CreateProductCommand' => 'CreateProductHandler',
                'UpdateProductCommand' => 'UpdateProductHandler',
                'DeleteProductCommand' => 'DeleteProductHandler',
                'CreateCategoryCommand' => 'CreateCategoryHandler',
                'UpdateCategoryCommand' => 'UpdateCategoryHandler',
                'CreateOrderCommand' => 'CreateOrderHandler',
                'UpdateOrderStatusCommand' => 'UpdateOrderStatusHandler'
            ],
            'message' => 'Command handlers retrieved successfully'
        ]);
    })->name('commands.handlers');
    
    // Query Handlers
    Route::get('queries/handlers', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'GetProductsQuery' => 'GetProductsHandler',
                'GetProductQuery' => 'GetProductHandler',
                'SearchProductsQuery' => 'SearchProductsHandler',
                'GetCategoriesQuery' => 'GetCategoriesHandler',
                'GetCategoryTreeQuery' => 'GetCategoryTreeHandler',
                'GetOrdersQuery' => 'GetOrdersHandler',
                'GetOrderQuery' => 'GetOrderHandler',
                'GetUserOrdersQuery' => 'GetUserOrdersHandler'
            ],
            'message' => 'Query handlers retrieved successfully'
        ]);
    })->name('queries.handlers');
    
    // ==================== CACHE MANAGEMENT ====================
    
    Route::middleware(['auth:sanctum', 'role:admin'])->prefix('cache')->name('cache.')->group(function () {
        Route::post('clear', function (\Illuminate\Http\Request $request) {
            $cacheKey = $request->input('cache_key');
            
            if ($cacheKey) {
                \Illuminate\Support\Facades\Cache::forget($cacheKey);
                $message = "Cache key '{$cacheKey}' cleared successfully";
            } else {
                \Illuminate\Support\Facades\Cache::flush();
                $message = 'All cache cleared successfully';
            }
            
            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        })->name('clear');
    });
    
    // ==================== PERFORMANCE METRICS ====================
    
    Route::middleware(['auth:sanctum'])->prefix('metrics')->name('metrics.')->group(function () {
        
        Route::get('queries', function (\Illuminate\Http\Request $request) {
            $queryType = $request->input('query_type');
            
            // Mock metrics data - gerçek implementasyonda performance monitoring service'den gelecek
            $metrics = [
                'total_queries' => 1250,
                'avg_response_time' => 145, // ms
                'cache_hit_rate' => 78.5, // %
                'slow_queries' => 12,
                'error_rate' => 0.8, // %
                'queries_by_type' => [
                    'GetProductsQuery' => ['count' => 450, 'avg_time' => 120],
                    'GetProductQuery' => ['count' => 380, 'avg_time' => 95],
                    'SearchProductsQuery' => ['count' => 220, 'avg_time' => 180],
                    'GetCategoriesQuery' => ['count' => 150, 'avg_time' => 85],
                    'GetOrdersQuery' => ['count' => 50, 'avg_time' => 200]
                ]
            ];
            
            if ($queryType && isset($metrics['queries_by_type'][$queryType])) {
                $metrics = $metrics['queries_by_type'][$queryType];
            }
            
            return response()->json([
                'success' => true,
                'data' => $metrics,
                'message' => 'Query metrics retrieved successfully'
            ]);
        })->name('queries');
        
        Route::get('commands', function (\Illuminate\Http\Request $request) {
            $commandType = $request->input('command_type');
            
            // Mock metrics data
            $metrics = [
                'total_commands' => 320,
                'avg_response_time' => 280, // ms
                'success_rate' => 97.2, // %
                'failed_commands' => 9,
                'commands_by_type' => [
                    'CreateProductCommand' => ['count' => 120, 'avg_time' => 250, 'success_rate' => 98.5],
                    'UpdateProductCommand' => ['count' => 95, 'avg_time' => 200, 'success_rate' => 99.1],
                    'DeleteProductCommand' => ['count' => 25, 'avg_time' => 150, 'success_rate' => 96.0],
                    'CreateOrderCommand' => ['count' => 80, 'avg_time' => 450, 'success_rate' => 95.0]
                ]
            ];
            
            if ($commandType && isset($metrics['commands_by_type'][$commandType])) {
                $metrics = $metrics['commands_by_type'][$commandType];
            }
            
            return response()->json([
                'success' => true,
                'data' => $metrics,
                'message' => 'Command metrics retrieved successfully'
            ]);
        })->name('commands');
    });
    
    // ==================== HEALTH CHECK ====================
    
    Route::get('health', function () {
        return response()->json([
            'success' => true,
            'message' => 'CQRS API is healthy',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0',
            'services' => [
                'command_bus' => 'active',
                'query_bus' => 'active',
                'cache' => 'active',
                'database' => 'active'
            ]
        ]);
    })->name('health');
});
