<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// User-specific private channels
Broadcast::channel('user.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Order-specific private channels
Broadcast::channel('order.{orderId}', function ($user, $orderId) {
    // Order ownership kontrolü
    try {
        $orderRepository = app(\App\Infrastructure\Orders\Repositories\EloquentOrderRepository::class);
        $order = $orderRepository->findById($orderId);
        
        return $order && $order->getUserId() === $user->id;
    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::error('Order channel authorization failed', [
            'user_id' => $user->id,
            'order_id' => $orderId,
            'error' => $e->getMessage(),
        ]);
        
        return false;
    }
});

// Admin channels - sadece admin rolüne sahip kullanıcılar
Broadcast::channel('admin', function ($user) {
    return $user->hasRole('admin');
});

// Cart channels - kullanıcının kendi sepeti
Broadcast::channel('cart.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Notification channels - kullanıcının kendi bildirimleri
Broadcast::channel('notification.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Presence channels - online kullanıcılar için
Broadcast::channel('online', function ($user) {
    return [
        'id' => $user->id,
        'name' => $user->name,
        'avatar' => $user->avatar ?? null,
    ];
});

// Admin presence channel
Broadcast::channel('admin-presence', function ($user) {
    if ($user->hasRole('admin')) {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'role' => 'admin',
            'last_seen' => now()->toISOString(),
        ];
    }
    
    return false;
});

// Product tracking channels (public - authentication gerekmez)
// Bu channel'lar public olduğu için authorization callback'i yok

// Tracking channels (public - tracking number ile erişim)
// Bu channel'lar da public olduğu için authorization callback'i yok
