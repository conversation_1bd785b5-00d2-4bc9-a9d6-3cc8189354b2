<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Orders\OrderController;

/*
|--------------------------------------------------------------------------
| Admin Orders Routes
|--------------------------------------------------------------------------
|
| Admin panel sipariş yönetimi route'ları
|
*/

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    
    // Sipariş listesi ve detay
    Route::get('/orders', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{id}', [OrderController::class, 'show'])->name('orders.show');
    
    // Sipariş durumu güncelleme
    Route::patch('/orders/{id}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
    
    // Sipariş iptal etme
    Route::patch('/orders/{id}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
    
    // Durum bazlı sipariş listeleri (API endpoints)
    Route::get('/api/orders/pending', [OrderController::class, 'pending'])->name('orders.api.pending');
    Route::get('/api/orders/processing', [OrderController::class, 'processing'])->name('orders.api.processing');
    Route::get('/api/orders/ready-to-ship', [OrderController::class, 'readyToShip'])->name('orders.api.ready-to-ship');
    
    // Sipariş arama
    Route::get('/api/orders/search', [OrderController::class, 'search'])->name('orders.api.search');
    
});
