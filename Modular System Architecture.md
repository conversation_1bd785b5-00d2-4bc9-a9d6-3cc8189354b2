# Modular System Architecture

## Module System Overview

The modular system allows for enabling or disabling specific features across different projects. This document outlines how the module system works and how to create new modules.

## Core Concepts

### 1. Module Structure

Each module follows this structure:

```
Modules/
└── ModuleName/
    ├── Config/
    │   └── module.php       # Module configuration
    ├── Database/
    │   ├── Migrations/      # Database migrations
    │   ├── Factories/       # Model factories
    │   └── Seeders/         # Database seeders
    ├── Domain/              # Domain layer
    │   ├── Models/          # Domain models
    │   ├── ValueObjects/    # Value objects
    │   ├── Events/          # Domain events
    │   └── Interfaces/      # Domain interfaces
    ├── Application/         # Application layer
    │   ├── Services/        # Application services
    │   ├── DTOs/            # Data transfer objects
    │   └── Validators/      # Validation logic
    ├── Infrastructure/      # Infrastructure layer
    │   ├── Repositories/    # Repository implementations
    │   └── Services/        # External service integrations
    ├── Http/                # HTTP layer
    │   ├── Controllers/     # API and web controllers
    │   ├── Middleware/      # HTTP middleware
    │   ├── Requests/        # Form requests
    │   └── Resources/       # API resources
    ├── Providers/           # Service providers
    │   ├── ModuleServiceProvider.php  # Main service provider
    │   └── RouteServiceProvider.php   # Route service provider
    ├── Resources/           # Frontend resources
    │   ├── js/              # JavaScript/React components
    │   ├── css/             # CSS/Tailwind styles
    │   └── views/           # Blade views (if needed)
    ├── Routes/              # Route definitions
    │   ├── api.php          # API routes
    │   ├── web.php          # Web routes
    │   └── admin.php        # Admin routes
    └── Tests/               # Module tests
        ├── Unit/            # Unit tests
        ├── Feature/         # Feature tests
        └── Integration/     # Integration tests
```

### 2. Module Registration

Modules are registered in the `config/modules.php` file:

```php
return [
    'register' => [
        'Products',
        'Orders',
        'Users',
        'Payments',
        'CurrencyPricing',
        'MultiLanguage',
        // Add more modules as needed
    ],
    
    'enabled' => [
        'Products',
        'Orders',
        'Users',
        'Payments',
        // Only enabled modules are loaded
    ],
    
    'features' => [
        'currency_pricing' => env('FEATURE_CURRENCY_PRICING', false),
        'multi_language' => env('FEATURE_MULTI_LANGUAGE', true),
        // Feature flags for specific functionality
    ],
];
```

### 3. Module Service Provider

Each module has a service provider that registers its components:

```php
namespace Modules\ModuleName\Providers;

use Illuminate\Support\ServiceProvider;

class ModuleServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register module components
        $this->mergeConfigFrom(
            __DIR__.'/../Config/module.php', 'module_name'
        );
        
        // Register module bindings
        $this->app->bind(
            \Modules\ModuleName\Domain\Interfaces\RepositoryInterface::class,
            \Modules\ModuleName\Infrastructure\Repositories\Repository::class
        );
    }
    
    public function boot()
    {
        // Boot module components
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');
        $this->loadRoutesFrom(__DIR__.'/../Routes/api.php');
        $this->loadRoutesFrom(__DIR__.'/../Routes/web.php');
        $this->loadRoutesFrom(__DIR__.'/../Routes/admin.php');
        
        // Register module assets
        $this->publishes([
            __DIR__.'/../Resources/js' => resource_path('js/modules/module_name'),
        ], 'module-assets');
    }
}
```

### 4. Module Configuration

Each module has its own configuration file:

```php
// Modules/ModuleName/Config/module.php
return [
    'name' => 'ModuleName',
    'description' => 'Description of the module',
    'version' => '1.0.0',
    'features' => [
        'feature_one' => true,
        'feature_two' => false,
    ],
    // Module-specific configuration
];
```

### 5. Feature Management

Features can be enabled or disabled through the `Features` facade:

```php
use App\Support\Facades\Features;

// Check if a feature is enabled
if (Features::enabled('currency_pricing')) {
    // Implement currency pricing logic
}

// Check if a module is enabled
if (Features::module('CurrencyPricing')) {
    // Use CurrencyPricing module
}
```

## Module Development

### 1. Creating a New Module

To create a new module:

```bash
php artisan module:make ModuleName
```

This will generate the module structure with all necessary files.

### 2. Module Dependencies

Modules can depend on other modules:

```php
// Modules/ModuleName/Config/module.php
return [
    'name' => 'ModuleName',
    'dependencies' => [
        'Products',
        'Users',
    ],
    // Other configuration
];
```

The module system will ensure that dependencies are loaded first.

### 3. Module Assets

Module assets (JavaScript, CSS) are compiled separately and then merged:

```js
// vite.config.js
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                // Module assets
                'Modules/Products/Resources/js/app.js',
                'Modules/Orders/Resources/js/app.js',
                // Add more module assets as needed
            ],
            refresh: true,
        }),
        react(),
    ],
});
```

### 4. Module Routes

Module routes are defined in separate files:

```php
// Modules/ModuleName/Routes/api.php
Route::prefix('api/module-name')
    ->middleware('api')
    ->group(function () {
        Route::get('/', 'ModuleController@index');
        // Other routes
    });

// Modules/ModuleName/Routes/admin.php
Route::prefix('admin/module-name')
    ->middleware(['web', 'auth', 'admin'])
    ->group(function () {
        Route::get('/', 'AdminController@index');
        // Other routes
    });
```

### 5. Module Database Migrations

Module migrations are placed in the module's `Database/Migrations` directory:

```php
// Modules/ModuleName/Database/Migrations/2023_01_01_000000_create_module_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateModuleTable extends Migration
{
    public function up()
    {
        Schema::create('module_table', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('module_table');
    }
}
```

## Module Implementation Examples

### 1. CurrencyPricing Module

The CurrencyPricing module adds multi-currency support to the product pricing:

```php
// Modules/CurrencyPricing/Domain/Models/Currency.php
namespace Modules\CurrencyPricing\Domain\Models;

use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    protected $fillable = ['code', 'name', 'symbol', 'exchange_rate'];
    
    public function products()
    {
        return $this->hasMany(ProductPrice::class);
    }
}

// Modules/CurrencyPricing/Domain/Models/ProductPrice.php
namespace Modules\CurrencyPricing\Domain\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Products\Domain\Models\Product;

class ProductPrice extends Model
{
    protected $fillable = ['product_id', 'currency_id', 'price'];
    
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
}
```

### 2. MultiLanguage Module

The MultiLanguage module adds translation support:

```php
// Modules/MultiLanguage/Domain/Models/Language.php
namespace Modules\MultiLanguage\Domain\Models;

use Illuminate\Database\Eloquent\Model;

class Language extends Model
{
    protected $fillable = ['code', 'name', 'is_default', 'is_active'];
    
    public function translations()
    {
        return $this->hasMany(Translation::class);
    }
}

// Modules/MultiLanguage/Domain/Models/Translation.php
namespace Modules\MultiLanguage\Domain\Models;

use Illuminate\Database\Eloquent\Model;

class Translation extends Model
{
    protected $fillable = [
        'language_id', 
        'translatable_type', 
        'translatable_id', 
        'field', 
        'value'
    ];
    
    public function language()
    {
        return $this->belongsTo(Language::class);
    }
    
    public function translatable()
    {
        return $this->morphTo();
    }
}
```

## Module Integration

### 1. Module Communication

Modules communicate through events and interfaces:

```php
// Modules/Orders/Domain/Events/OrderCreated.php
namespace Modules\Orders\Domain\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Modules\Orders\Domain\Models\Order;

class OrderCreated
{
    use Dispatchable;
    
    public $order;
    
    public function __construct(Order $order)
    {
        $this->order = $order;
    }
}

// Modules/Payments/Domain/Listeners/ProcessPayment.php
namespace Modules\Payments\Domain\Listeners;

use Modules\Orders\Domain\Events\OrderCreated;
use Modules\Payments\Domain\Services\PaymentService;

class ProcessPayment
{
    protected $paymentService;
    
    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }
    
    public function handle(OrderCreated $event)
    {
        $this->paymentService->createPayment($event->order);
    }
}
```

### 2. Module Extension Points

Modules can provide extension points for other modules:

```php
// Modules/Products/Domain/Interfaces/PricingStrategyInterface.php
namespace Modules\Products\Domain\Interfaces;

use Modules\Products\Domain\Models\Product;

interface PricingStrategyInterface
{
    public function calculatePrice(Product $product, array $options = []);
}

// Modules/CurrencyPricing/Infrastructure/Services/CurrencyPricingStrategy.php
namespace Modules\CurrencyPricing\Infrastructure\Services;

use Modules\Products\Domain\Interfaces\PricingStrategyInterface;
use Modules\Products\Domain\Models\Product;
use Modules\CurrencyPricing\Domain\Models\Currency;

class CurrencyPricingStrategy implements PricingStrategyInterface
{
    protected $currency;
    
    public function __construct(Currency $currency)
    {
        $this->currency = $currency;
    }
    
    public function calculatePrice(Product $product, array $options = [])
    {
        $productPrice = $product->prices()
            ->where('currency_id', $this->currency->id)
            ->first();
            
        if ($productPrice) {
            return $productPrice->price;
        }
        
        // Fall back to base price converted to current currency
        return $product->price * $this->currency->exchange_rate;
    }
}
```

## Module Testing

### 1. Unit Testing

```php
// Modules/Products/Tests/Unit/ProductTest.php
namespace Modules\Products\Tests\Unit;

use Tests\TestCase;
use Modules\Products\Domain\Models\Product;

class ProductTest extends TestCase
{
    public function test_it_can_create_a_product()
    {
        $product = Product::factory()->create();
        
        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'name' => $product->name,
        ]);
    }
}
```

### 2. Feature Testing

```php
// Modules/Products/Tests/Feature/ProductApiTest.php
namespace Modules\Products\Tests\Feature;

use Tests\TestCase;
use Modules\Products\Domain\Models\Product;
use Modules\Users\Domain\Models\User;

class ProductApiTest extends TestCase
{
    public function test_it_can_list_products()
    {
        $user = User::factory()->create();
        $products = Product::factory()->count(3)->create();
        
        $response = $this->actingAs($user)
            ->getJson('/api/products');
            
        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');
    }
}
```

## Conclusion

This modular system provides a flexible architecture for building e-commerce applications with pluggable features. By following these guidelines, you can create, extend, and maintain modules that can be enabled or disabled based on project requirements.
