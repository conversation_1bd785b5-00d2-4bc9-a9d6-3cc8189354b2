<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use App\Models\OrderNote;
use App\Enums\OrderStatus;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderNoteFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrderNote::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $noteType = $this->faker->randomElement([
            OrderNote::TYPE_GENERAL,
            OrderNote::TYPE_STATUS_CHANGE,
            OrderNote::TYPE_PAYMENT,
            OrderNote::TYPE_SHIPPING,
            OrderNote::TYPE_CUSTOMER,
            OrderNote::TYPE_SYSTEM,
        ]);
        
        $isPrivate = $this->faker->boolean(70); // %70 ihtimalle özel not
        
        return [
            'order_id' => Order::factory(),
            'user_id' => User::factory(),
            'note' => $this->faker->sentence(),
            'is_private' => $isPrivate,
            'note_type' => $noteType,
            'is_customer_notified' => !$isPrivate && $this->faker->boolean(30), // Özel olmayan notlar için %30 ihtimalle müşteriye bildirim
        ];
    }
    
    /**
     * Indicate that the note is a status change note.
     */
    public function statusChange(): static
    {
        $statuses = OrderStatus::cases();
        $statusBefore = $statuses[array_rand($statuses)];
        
        // Farklı bir durum seç
        do {
            $statusAfter = $statuses[array_rand($statuses)];
        } while ($statusBefore === $statusAfter);
        
        return $this->state(fn (array $attributes) => [
            'note_type' => OrderNote::TYPE_STATUS_CHANGE,
            'note' => "Sipariş durumu değiştirildi: {$statusBefore->label()} -> {$statusAfter->label()}",
            'status_before' => $statusBefore->value,
            'status_after' => $statusAfter->value,
        ]);
    }
    
    /**
     * Indicate that the note is a shipping note.
     */
    public function shipping(): static
    {
        $trackingNumber = $this->faker->numerify('TN#########');
        $shippingCompany = $this->faker->randomElement(['Aras Kargo', 'Yurtiçi Kargo', 'MNG Kargo', 'PTT Kargo', 'UPS']);
        
        return $this->state(fn (array $attributes) => [
            'note_type' => OrderNote::TYPE_SHIPPING,
            'note' => "Kargo bilgileri güncellendi. Kargo Firması: {$shippingCompany}, Takip No: {$trackingNumber}",
            'is_private' => false,
            'is_customer_notified' => true,
        ]);
    }
    
    /**
     * Indicate that the note is a payment note.
     */
    public function payment(): static
    {
        return $this->state(fn (array $attributes) => [
            'note_type' => OrderNote::TYPE_PAYMENT,
            'note' => "Ödeme alındı. Ödeme yöntemi: " . $this->faker->randomElement(['Kredi Kartı', 'Havale/EFT', 'Kapıda Ödeme']),
            'is_private' => $this->faker->boolean(30), // %30 ihtimalle özel not
        ]);
    }
    
    /**
     * Indicate that the note is a customer note.
     */
    public function customer(): static
    {
        return $this->state(fn (array $attributes) => [
            'note_type' => OrderNote::TYPE_CUSTOMER,
            'note' => $this->faker->randomElement([
                "Lütfen kapıda bırakın, zile basmayın.",
                "İş yerime teslim edilecek, saat 9-18 arası ulaşabilirsiniz.",
                "Hediye paketi yapılmasını rica ediyorum.",
                "Siparişimi en kısa sürede gönderirseniz sevinirim.",
            ]),
            'is_private' => false,
        ]);
    }
}
