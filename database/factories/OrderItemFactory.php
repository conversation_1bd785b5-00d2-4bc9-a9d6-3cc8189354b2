<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Product;
use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $product = Product::inRandomOrder()->first() ?? Product::factory()->create();
        $quantity = $this->faker->numberBetween(1, 5);
        $price = $this->faker->randomFloat(2, 10, 500);
        $subtotal = $price * $quantity;
        
        return [
            'order_id' => Order::factory(),
            'product_id' => $product->id,
            'product_name' => $product->name,
            'price' => $price,
            'quantity' => $quantity,
            'subtotal' => $subtotal,
            'options' => $this->faker->optional(0.7)->randomElement([
                ['color' => 'Kırmızı', 'size' => 'M'],
                ['color' => 'Mavi', 'size' => 'L'],
                ['color' => 'Siyah', 'size' => 'S'],
                ['color' => 'Beyaz', 'size' => 'XL'],
                null
            ]),
        ];
    }
}
