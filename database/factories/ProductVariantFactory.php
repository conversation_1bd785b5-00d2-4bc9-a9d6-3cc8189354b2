<?php

namespace Database\Factories;

use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductVariant>
 */
class ProductVariantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductVariant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'sku' => 'SKU-' . strtoupper(Str::random(8)),
            'stock_code' => 'STK-' . strtoupper(Str::random(6)),
            'attribute_values' => [],
            'additional_price' => $this->faker->randomFloat(2, -10, 50),
            'stock' => $this->faker->numberBetween(0, 100),
            'status' => $this->faker->randomElement(['in_stock', 'out_of_stock', 'coming_soon']),
            'is_default' => false,
            'image' => null,
            'meta_title' => $this->faker->sentence(4),
            'meta_description' => $this->faker->sentence(10),
            'meta_keywords' => implode(', ', $this->faker->words(5)),
            'slug' => $this->faker->slug(),
        ];
    }

    /**
     * Varyantı belirli bir ürüne ata
     */
    public function forProduct(Product $product): self
    {
        return $this->state(function (array $attributes) use ($product) {
            return [
                'product_id' => $product->id,
                'slug' => $product->slug . '-' . Str::random(5),
            ];
        });
    }

    /**
     * Varyantı varsayılan olarak ayarla
     */
    public function asDefault(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => true,
            ];
        });
    }

    /**
     * Varyanta stok ekle
     */
    public function inStock(int $quantity = null): self
    {
        return $this->state(function (array $attributes) use ($quantity) {
            return [
                'stock' => $quantity ?? $this->faker->numberBetween(1, 100),
                'status' => 'in_stock',
            ];
        });
    }

    /**
     * Varyantı stok dışı olarak ayarla
     */
    public function outOfStock(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'stock' => 0,
                'status' => 'out_of_stock',
            ];
        });
    }

    /**
     * Varyanta özellik değerleri ekle
     */
    public function withAttributes(array $attributeValues): self
    {
        return $this->state(function (array $attributes) use ($attributeValues) {
            return [
                'attribute_values' => $attributeValues,
            ];
        });
    }
}
