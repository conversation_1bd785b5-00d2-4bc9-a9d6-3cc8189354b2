<?php

namespace Database\Seeders;

use App\Models\ShippingCompany;
use Illuminate\Database\Seeder;

class ShippingCompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Eğer kargo şirketleri zaten varsa, seed işlemini atla
        if (ShippingCompany::count() > 0) {
            $this->command->info('Kargo şirketleri zaten mevcut. Seed işlemi atlanıyor.');
            return;
        }

        $companies = [
            [
                'name' => 'Yurtiçi Kargo',
                'code' => 'yurtici',
                'tracking_url' => 'https://www.yurticikargo.com/tr/online-servisler/gonderi-sorgula?code={tracking_number}',
                'base_cost' => 25.00,
                'is_active' => true,
            ],
            [
                'name' => 'Aras Kargo',
                'code' => 'aras',
                'tracking_url' => 'https://kargotakip.araskargo.com.tr/Track/Tracking?code={tracking_number}',
                'base_cost' => 27.50,
                'is_active' => true,
            ],
            [
                'name' => 'MNG Kargo',
                'code' => 'mng',
                'tracking_url' => 'https://www.mngkargo.com.tr/gonderi-takip/?code={tracking_number}',
                'base_cost' => 26.00,
                'is_active' => true,
            ],
            [
                'name' => 'PTT Kargo',
                'code' => 'ptt',
                'tracking_url' => 'https://gonderitakip.ptt.gov.tr/Track/Tracking?code={tracking_number}',
                'base_cost' => 20.00,
                'is_active' => true,
            ],
            [
                'name' => 'Sürat Kargo',
                'code' => 'surat',
                'tracking_url' => 'https://suratkargo.com.tr/KargoTakip/?code={tracking_number}',
                'base_cost' => 24.00,
                'is_active' => true,
            ],
            [
                'name' => 'UPS',
                'code' => 'ups',
                'tracking_url' => 'https://www.ups.com/track?tracknum={tracking_number}',
                'base_cost' => 35.00,
                'is_active' => true,
            ],
            [
                'name' => 'DHL',
                'code' => 'dhl',
                'tracking_url' => 'https://www.dhl.com/tr-tr/home/<USER>/tracking-express.html?submit=1&tracking-id={tracking_number}',
                'base_cost' => 40.00,
                'is_active' => true,
            ],
            [
                'name' => 'FedEx',
                'code' => 'fedex',
                'tracking_url' => 'https://www.fedex.com/fedextrack/?trknbr={tracking_number}',
                'base_cost' => 45.00,
                'is_active' => true,
            ],
        ];

        foreach ($companies as $company) {
            ShippingCompany::updateOrCreate(
                ['code' => $company['code']],
                $company
            );
        }
    }
}
