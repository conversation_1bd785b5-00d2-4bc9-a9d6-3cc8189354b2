<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class MoreProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Kategorileri al
        $categories = Category::all();

        if ($categories->count() === 0) {
            $this->command->info('Önce kategorileri oluşturun!');
            return;
        }

        // Her kategori için 20 ürün oluştur
        foreach ($categories as $category) {
            for ($i = 1; $i <= 20; $i++) {
                $name = "Ürün " . $category->name . " - " . $i;

                Product::create([
                    'name' => $name,
                    'slug' => Str::slug($name),
                    'description' => "Bu, {$category->name} kategorisinde {$i}. test ürünüdür.",
                    'price' => rand(10, 1000) / 10,
                    'stock' => rand(0, 100),
                    'category_id' => $category->id,
                    'status' => rand(0, 1),
                ]);
            }
        }

        $this->command->info('Ek ürünler başarıyla oluşturuldu!');
    }
}
