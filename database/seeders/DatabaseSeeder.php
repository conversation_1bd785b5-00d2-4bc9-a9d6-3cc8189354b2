<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Admin kullanıcısı oluştur veya bul
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Test kullanıcısı oluştur veya bul
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Rolleri oluştur
        $this->call([
            RoleSeeder::class,
        ]);

        // Admin kullanıcısına admin rolünü ata
        $admin->assignRole('admin');

        // Test kullanıcısına customer rolünü ata
        $user->assignRole('customer');

        // Örnek kategori, ürün ve diğer verileri ekle
        $this->call([
            CategorySeeder::class,
            ProductSeeder::class,
            AttributeSeeder::class,
            ProductVariantSeeder::class,
            BankAccountSeeder::class,
            ShippingCompanySeeder::class,
            ShippingSeeder::class,
            EmailTemplatesSeeder::class,
            LocationSeeder::class,
            HomepageDataSeeder::class
        ]);
    }
}
