<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Facades\DB;

class HomepageDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Anasayfa için örnek veriler oluşturuluyor...');

        // Rastgele 8 ürünü vitrin ürünü olarak işaretle
        $this->command->info('Vitrin ürünleri oluşturuluyor...');
        $featuredProducts = Product::where('status', true)
            ->inRandomOrder()
            ->take(8)
            ->get();

        foreach ($featuredProducts as $product) {
            $product->update([
                'is_featured' => true
            ]);
        }
        $this->command->info($featuredProducts->count() . ' ürün vitrin ürünü olarak işaretlendi.');

        // Rastgele 8 ürünü indirimli olarak işaretle
        $this->command->info('İndirimli ürünler oluşturuluyor...');
        $saleProducts = Product::where('status', true)
            ->where('is_featured', false) // Vitrin ürünlerini hariç tut
            ->inRandomOrder()
            ->take(8)
            ->get();

        foreach ($saleProducts as $product) {
            // Orijinal fiyatın %10-30 arasında indirim uygula
            $discountPercent = rand(10, 30);
            $salePrice = $product->price * (1 - ($discountPercent / 100));

            $product->update([
                'is_on_sale' => true,
                'sale_price' => round($salePrice, 2),
                'sale_starts_at' => now()->subDays(rand(1, 5)), // Geçmiş tarih (indirim başlamış)
                'sale_ends_at' => now()->addDays(rand(5, 30))   // Gelecek tarih (indirim devam ediyor)
            ]);
        }
        $this->command->info($saleProducts->count() . ' ürün indirimli olarak işaretlendi.');

        // Rastgele ürünlere görüntülenme sayısı ekle
        $this->command->info('Ürün görüntülenme sayıları oluşturuluyor...');
        $viewedProducts = Product::where('status', true)
            ->inRandomOrder()
            ->take(20)
            ->get();

        foreach ($viewedProducts as $product) {
            DB::table('products')
                ->where('id', $product->id)
                ->update(['view_count' => rand(10, 100)]);
        }
        $this->command->info($viewedProducts->count() . ' ürüne görüntülenme sayısı eklendi.');

        $this->command->info('Anasayfa için örnek veriler başarıyla oluşturuldu!');
    }
}
