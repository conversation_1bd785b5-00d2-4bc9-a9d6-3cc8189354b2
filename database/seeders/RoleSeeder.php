<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Temel rolleri oluştur
        $roles = [
            'admin' => 'Tüm yetkilere sahip yönetici',
            'editor' => 'İçerik düzenleyebilen kullanıcı',
            'customer' => 'Normal müşteri',
        ];

        foreach ($roles as $name => $description) {
            Role::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web',
            ]);
        }

        // Temel izinleri oluştur
        $permissions = [
            // Kullanıcı yönetimi
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',

            // Ürün yönetimi
            'products.view',
            'products.create',
            'products.edit',
            'products.delete',

            // Kategori yönetimi
            'categories.view',
            'categories.create',
            'categories.edit',
            'categories.delete',

            // Sipariş yönetimi
            'orders.view',
            'orders.create',
            'orders.edit',
            'orders.delete',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // Admin rolüne tüm izinleri ver
        $adminRole = Role::findByName('admin');
        $adminRole->givePermissionTo(Permission::all());

        // Editor rolüne bazı izinleri ver
        $editorRole = Role::findByName('editor');
        $editorRole->givePermissionTo([
            'products.view',
            'products.create',
            'products.edit',
            'categories.view',
            'categories.create',
            'categories.edit',
            'orders.view',
            'orders.edit',
        ]);

        // Customer rolüne sadece görüntüleme izni ver
        $customerRole = Role::findByName('customer');
        $customerRole->givePermissionTo([
            'products.view',
            'categories.view',
        ]);
    }
}
