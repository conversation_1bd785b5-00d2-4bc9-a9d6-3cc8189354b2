<?php

namespace Database\Seeders;

use App\Models\BankAccount;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BankAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Eğer banka hesapları zaten varsa, seed işlemini atla
        if (BankAccount::count() > 0) {
            $this->command->info('Banka hesapları zaten mevcut. Seed işlemi atlanıyor.');
            return;
        }

        BankAccount::create([
            'bank_name' => 'Ziraat Bankası',
            'account_name' => 'E-Ticaret A.Ş.',
            'account_number' => '********',
            'iban' => 'TR12 3456 7890 1234 5678 9012 34',
            'branch_code' => '123',
            'swift_code' => 'TCZBTR2A',
            'is_active' => true,
        ]);

        BankAccount::create([
            'bank_name' => 'İş Bankası',
            'account_name' => 'E-Ticaret A.Ş.',
            'account_number' => '********',
            'iban' => 'TR98 7654 3210 9876 5432 1098 76',
            'branch_code' => '456',
            'swift_code' => 'ISBKTRIS',
            'is_active' => true,
        ]);

        BankAccount::create([
            'bank_name' => 'Garanti Bankası',
            'account_name' => 'E-Ticaret A.Ş.',
            'account_number' => '********',
            'iban' => 'TR13 5792 4680 1357 9246 8013 57',
            'branch_code' => '789',
            'swift_code' => 'TGBATRIS',
            'is_active' => true,
        ]);
    }
}
