<?php

namespace Database\Seeders;

use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Önceki verileri temizle
        DB::statement('PRAGMA foreign_keys = OFF');
        DB::table('attribute_values')->truncate();
        DB::table('attributes')->truncate();
        DB::statement('PRAGMA foreign_keys = ON');

        // Özellikler
        $attributes = [
            [
                'name' => 'Renk',
                'code' => 'color',
                'type' => 'select',
                'is_required' => false,
                'is_filterable' => true,
                'is_variant' => true,
                'position' => 1,
                'values' => [
                    ['value' => 'red', 'label' => 'Kırmızı'],
                    ['value' => 'blue', 'label' => 'Mavi'],
                    ['value' => 'green', 'label' => 'Yeşil'],
                    ['value' => 'black', 'label' => 'Siyah'],
                    ['value' => 'white', 'label' => 'Beyaz'],
                ]
            ],
            [
                'name' => 'Beden',
                'code' => 'size',
                'type' => 'select',
                'is_required' => false,
                'is_filterable' => true,
                'is_variant' => true,
                'position' => 2,
                'values' => [
                    ['value' => 'xs', 'label' => 'XS'],
                    ['value' => 's', 'label' => 'S'],
                    ['value' => 'm', 'label' => 'M'],
                    ['value' => 'l', 'label' => 'L'],
                    ['value' => 'xl', 'label' => 'XL'],
                    ['value' => 'xxl', 'label' => 'XXL'],
                ]
            ],
            [
                'name' => 'Malzeme',
                'code' => 'material',
                'type' => 'select',
                'is_required' => false,
                'is_filterable' => true,
                'is_variant' => false,
                'position' => 3,
                'values' => [
                    ['value' => 'cotton', 'label' => 'Pamuk'],
                    ['value' => 'polyester', 'label' => 'Polyester'],
                    ['value' => 'wool', 'label' => 'Yün'],
                    ['value' => 'leather', 'label' => 'Deri'],
                    ['value' => 'silk', 'label' => 'İpek'],
                ]
            ],
            [
                'name' => 'Marka',
                'code' => 'brand',
                'type' => 'select',
                'is_required' => false,
                'is_filterable' => true,
                'is_variant' => false,
                'position' => 4,
                'values' => [
                    ['value' => 'apple', 'label' => 'Apple'],
                    ['value' => 'samsung', 'label' => 'Samsung'],
                    ['value' => 'xiaomi', 'label' => 'Xiaomi'],
                    ['value' => 'huawei', 'label' => 'Huawei'],
                    ['value' => 'lg', 'label' => 'LG'],
                ]
            ],
        ];

        // Özellikleri ekle
        foreach ($attributes as $attributeData) {
            $values = $attributeData['values'];
            unset($attributeData['values']);

            $attribute = Attribute::create($attributeData);

            // Değerleri ekle
            foreach ($values as $index => $valueData) {
                $valueData['position'] = $index + 1;
                $attribute->values()->create($valueData);
            }
        }

        // Kategorilere özellikleri ekle
        $categories = Category::all();
        $allAttributes = Attribute::all();

        // Önce kategori-özellik ilişkilerini temizle
        DB::table('category_attributes')->truncate();

        foreach ($categories as $category) {
            // Elektronik kategorisi ve alt kategorileri için
            if (in_array($category->id, [1, 4, 5, 6])) {
                // Renk ve Marka özelliklerini ekle
                $category->attributes()->attach(
                    $allAttributes->whereIn('code', ['color', 'brand'])->pluck('id')->toArray(),
                    [
                        'is_required' => false,
                        'is_filterable' => true,
                        'position' => 1
                    ]
                );
            }

            // Giyim kategorisi ve alt kategorileri için
            if (in_array($category->id, [2, 7, 8, 9])) {
                // Renk, Beden ve Malzeme özelliklerini ekle
                $category->attributes()->attach(
                    $allAttributes->whereIn('code', ['color', 'size', 'material'])->pluck('id')->toArray(),
                    [
                        'is_required' => false,
                        'is_filterable' => true,
                        'position' => 1
                    ]
                );
            }

            // Ev & Bahçe kategorisi ve alt kategorileri için
            if (in_array($category->id, [3, 10, 11, 12])) {
                // Renk ve Malzeme özelliklerini ekle
                $category->attributes()->attach(
                    $allAttributes->whereIn('code', ['color', 'material'])->pluck('id')->toArray(),
                    [
                        'is_required' => false,
                        'is_filterable' => true,
                        'position' => 1
                    ]
                );
            }
        }
    }
}
