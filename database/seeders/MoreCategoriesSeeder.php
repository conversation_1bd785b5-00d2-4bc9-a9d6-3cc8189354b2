<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class MoreCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            'Elektronik' => [
                'Bilgisayarlar',
                'Telefonlar',
                'Tabletler',
                'Televizyonlar',
                'Kulaklıklar',
                'Kameralar',
                'Oyun Konsolları',
                'Akıllı Saatler',
            ],
            'G<PERSON>yim' => [
                'Erkek Giyim',
                'Kadın Giyim',
                'Çocuk Giyim',
                'Spor Giyim',
                'Ayakkabılar',
                'Çantalar',
                'Aksesuarlar',
            ],
            'Ev & Yaşam' => [
                'Mobilya',
                'Mutfak Eşyaları',
                'Banyo Ürünleri',
                'Dekorasyon',
                'Aydınlatma',
                'Bahçe Ürünleri',
            ],
            'Kit<PERSON><PERSON>' => [
                '<PERSON>',
                '<PERSON><PERSON><PERSON> Kurgu',
                '<PERSON><PERSON><PERSON>',
                '<PERSON><PERSON><PERSON>',
                'Çocuk Kitapları',
                'Kişisel Gelişim',
            ],
            'Spor & Outdoor' => [
                'Fitness Ekipmanları',
                'Kamp Malzemeleri',
                'Bisikletler',
                'Spor Ayakkabıları',
                'Su Sporları',
            ],
        ];
        
        // Ana kategorileri oluştur
        foreach ($categories as $mainCategory => $subCategories) {
            $category = Category::create([
                'name' => $mainCategory,
                'slug' => Str::slug($mainCategory),
                'description' => "{$mainCategory} kategorisi",
                'parent_id' => null,
                'status' => true,
            ]);
            
            // Alt kategorileri oluştur
            foreach ($subCategories as $subCategory) {
                Category::create([
                    'name' => $subCategory,
                    'slug' => Str::slug($subCategory),
                    'description' => "{$subCategory} alt kategorisi",
                    'parent_id' => $category->id,
                    'status' => true,
                ]);
            }
        }
        
        $this->command->info('Ek kategoriler başarıyla oluşturuldu!');
    }
}
