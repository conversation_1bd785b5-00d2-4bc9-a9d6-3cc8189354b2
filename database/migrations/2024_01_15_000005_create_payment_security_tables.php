<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Payment Security Blacklist
        Schema::create('payment_security_blacklist', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['ip_address', 'email', 'card_hash', 'device_fingerprint']);
            $table->string('value');
            $table->string('reason');
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
            
            $table->index(['type', 'value']);
            $table->index(['expires_at']);
        });

        // Payment Security Blocks (Geçici kullanıcı blokları)
        Schema::create('payment_security_blocks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('reason');
            $table->timestamp('expires_at');
            $table->timestamps();
            
            $table->index(['user_id', 'expires_at']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Payment Fraud Analysis
        Schema::create('payment_fraud_analysis', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payment_id');
            $table->string('transaction_id');
            $table->unsignedBigInteger('user_id')->nullable();
            
            // Risk scoring
            $table->integer('risk_score');
            $table->enum('risk_level', ['minimal', 'low', 'medium', 'high', 'critical']);
            $table->json('risk_factors');
            
            // Context bilgileri
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            
            $table->timestamps();
            
            $table->index(['payment_id']);
            $table->index(['transaction_id']);
            $table->index(['user_id', 'risk_level']);
            $table->index(['risk_level', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            
            $table->foreign('payment_id')->references('id')->on('payments')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // Payment Suspicious Activities
        Schema::create('payment_suspicious_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('ip_address');
            $table->json('activities'); // Tespit edilen şüpheli aktiviteler
            $table->enum('risk_level', ['minimal', 'low', 'medium', 'high', 'critical']);
            $table->boolean('investigated')->default(false);
            $table->text('investigation_notes')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'risk_level']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['risk_level', 'investigated']);
            $table->index('created_at');
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // Payment Logs (Genel payment log'ları)
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payment_id')->nullable();
            $table->string('transaction_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            
            // Log detayları
            $table->string('level'); // info, warning, error, critical
            $table->string('event'); // payment_started, fraud_detected, etc.
            $table->text('message');
            $table->json('context')->nullable();
            
            // Security bilgileri
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            
            $table->timestamps();
            
            $table->index(['payment_id', 'level']);
            $table->index(['transaction_id', 'level']);
            $table->index(['user_id', 'level']);
            $table->index(['level', 'created_at']);
            $table->index(['event', 'created_at']);
            $table->index('created_at');
            
            $table->foreign('payment_id')->references('id')->on('payments')->onDelete('set null');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_logs');
        Schema::dropIfExists('payment_suspicious_activities');
        Schema::dropIfExists('payment_fraud_analysis');
        Schema::dropIfExists('payment_security_blocks');
        Schema::dropIfExists('payment_security_blacklist');
    }
};
