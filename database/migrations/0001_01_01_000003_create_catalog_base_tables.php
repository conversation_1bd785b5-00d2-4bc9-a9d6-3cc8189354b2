<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Categories table - Base catalog structure
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->boolean('status')->default(true);
            $table->integer('position')->default(0);
            $table->integer('order')->default(0); // Added from later migrations
            $table->string('image')->nullable();
            $table->string('icon')->nullable();
            $table->boolean('featured')->default(false);
            $table->boolean('show_in_menu')->default(true);
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('cascade');
        });

        // Attributes table - Product attribute definitions
        Schema::create('attributes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->enum('type', ['select', 'multiple', 'text', 'boolean']);
            $table->boolean('is_required')->default(false);
            $table->boolean('is_filterable')->default(false);
            $table->boolean('is_variant')->default(false);
            $table->integer('position')->default(0);
            $table->timestamps();
        });

        // Attribute values table - Possible values for attributes
        Schema::create('attribute_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attribute_id')->constrained()->onDelete('cascade');
            $table->string('value');
            $table->string('label')->nullable();
            $table->integer('position')->default(0);
            $table->timestamps();
            
            $table->unique(['attribute_id', 'value']);
        });

        // Category attributes table - Links categories with their attributes
        Schema::create('category_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('attribute_id')->constrained()->onDelete('cascade');
            $table->boolean('is_required')->default(false);
            $table->boolean('is_filterable')->default(false); // Added from later migrations
            $table->integer('position')->default(0); // Added from later migrations
            $table->timestamps();
            
            $table->unique(['category_id', 'attribute_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('category_attributes');
        Schema::dropIfExists('attribute_values');
        Schema::dropIfExists('attributes');
        Schema::dropIfExists('categories');
    }
};
