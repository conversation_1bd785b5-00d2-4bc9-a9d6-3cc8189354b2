<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkout_processes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cart_id')->constrained()->onDelete('cascade');
            $table->string('status')->default('initiated'); // initiated, in_progress, completed, cancelled
            $table->string('current_step')->nullable(); // shipping_address, billing_address, shipping_method, payment_method, review
            $table->json('completed_steps')->nullable(); // Array of completed steps
            
            // Address information
            $table->json('shipping_address')->nullable();
            $table->json('billing_address')->nullable();
            
            // Shipping information
            $table->string('shipping_method')->nullable();
            $table->decimal('shipping_cost', 10, 2)->nullable();
            
            // Payment information
            $table->string('payment_method')->nullable();
            
            // Cancellation
            $table->string('cancellation_reason')->nullable();
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['cart_id', 'status']);
            $table->index(['status', 'current_step']);
            $table->index(['created_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkout_processes');
    }
};
