<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_refunds', function (Blueprint $table) {
            $table->id();
            
            // Refund identifiers
            $table->string('refund_id')->unique()->comment('Unique refund identifier');
            $table->unsignedBigInteger('payment_id')->index()->comment('Related payment ID');
            $table->string('payment_transaction_id')->index()->comment('Related payment transaction ID');
            
            // Refund amount information
            $table->decimal('refund_amount', 15, 2)->comment('Refund amount');
            $table->string('currency', 3)->comment('Refund currency');
            $table->decimal('fee_amount', 15, 2)->default(0)->comment('Refund fee amount');
            $table->decimal('net_refund_amount', 15, 2)->comment('Net refund amount after fees');
            
            // Refund details
            $table->string('refund_type')->comment('Refund type: full, partial');
            $table->string('refund_reason')->comment('Reason for refund');
            $table->text('refund_description')->nullable()->comment('Detailed refund description');
            $table->string('status')->index()->comment('Refund status');
            
            // Gateway information
            $table->string('gateway_provider')->index()->comment('Gateway provider identifier');
            $table->string('gateway_refund_id')->nullable()->index()->comment('Gateway refund ID');
            $table->json('gateway_response')->nullable()->comment('Gateway refund response');
            
            // User information
            $table->unsignedBigInteger('requested_by')->nullable()->index()->comment('User who requested refund');
            $table->unsignedBigInteger('processed_by')->nullable()->index()->comment('Admin who processed refund');
            
            // Additional information
            $table->json('metadata')->nullable()->comment('Additional refund metadata');
            
            // Timestamps
            $table->timestamp('requested_at')->nullable()->comment('When refund was requested');
            $table->timestamp('processed_at')->nullable()->comment('When refund was processed');
            $table->timestamp('completed_at')->nullable()->comment('When refund was completed');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['gateway_provider', 'status']);
            $table->index(['payment_id', 'status']);
            $table->index(['refund_type', 'status']);
            $table->index(['requested_at', 'status']);
            $table->index(['processed_at', 'status']);
            
            // Foreign key constraints
            $table->foreign('payment_id')->references('id')->on('payments')->onDelete('cascade');
            // $table->foreign('requested_by')->references('id')->on('users')->onDelete('set null');
            // $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_refunds');
    }
};
