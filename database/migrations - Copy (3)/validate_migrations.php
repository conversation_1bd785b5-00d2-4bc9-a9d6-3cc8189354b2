<?php

/**
 * Migration Doğrulama Script'i
 * 
 * Bu script yeni migration dosyalarının syntax ve bağımlılık kontrolünü yapar.
 * 
 * Kullanım:
 * php database/migrations_new/validate_migrations.php
 */

$migrationsDir = __DIR__;

echo "Migration Doğrulama Script'i\n";
echo "============================\n\n";

// Migration dosyalarını listele
$migrationFiles = glob($migrationsDir . '/0001_01_01_*.php');
sort($migrationFiles);

echo "Bulunan migration dosyaları:\n";
foreach ($migrationFiles as $index => $file) {
    echo ($index + 1) . ". " . basename($file) . "\n";
}
echo "\n";

// Her migration dosyasını kontrol et
$errors = [];
$warnings = [];
$tables = [];

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    echo "Kontrol ediliyor: $filename\n";
    
    // Dosya syntax kontrolü
    $output = [];
    $returnCode = 0;
    exec("php -l \"$file\"", $output, $returnCode);
    
    if ($returnCode !== 0) {
        $errors[] = "$filename: Syntax hatası - " . implode(' ', $output);
        continue;
    }
    
    // Dosya içeriğini oku
    $content = file_get_contents($file);
    
    // Schema::create çağrılarını bul
    preg_match_all('/Schema::create\([\'"]([^\'"]+)[\'"]/', $content, $createMatches);
    $createdTables = $createMatches[1];
    
    // Schema::dropIfExists çağrılarını bul
    preg_match_all('/Schema::dropIfExists\([\'"]([^\'"]+)[\'"]/', $content, $dropMatches);
    $droppedTables = $dropMatches[1];
    
    // Foreign key referanslarını bul
    preg_match_all('/->constrained\([\'"]?([^\'")\s]+)?[\'"]?\)/', $content, $foreignMatches);
    $foreignReferences = array_filter($foreignMatches[1]);
    
    // Explicit foreign key referanslarını bul
    preg_match_all('/->references\([\'"]([^\'"]+)[\'"]\)->on\([\'"]([^\'"]+)[\'"]/', $content, $explicitForeignMatches);
    $explicitForeignReferences = $explicitForeignMatches[2];
    
    $allForeignReferences = array_merge($foreignReferences, $explicitForeignReferences);
    
    foreach ($createdTables as $table) {
        $tables[$table] = [
            'file' => $filename,
            'foreign_refs' => $allForeignReferences
        ];
    }
    
    echo "  ✓ Oluşturulan tablolar: " . implode(', ', $createdTables) . "\n";
    if (!empty($allForeignReferences)) {
        echo "  ✓ Foreign key referansları: " . implode(', ', array_unique($allForeignReferences)) . "\n";
    }
    echo "\n";
}

// Bağımlılık kontrolü
echo "Bağımlılık kontrolü yapılıyor...\n";
$processedTables = [];

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    
    preg_match_all('/Schema::create\([\'"]([^\'"]+)[\'"]/', $content, $createMatches);
    $createdTables = $createMatches[1];
    
    preg_match_all('/->constrained\([\'"]?([^\'")\s]+)?[\'"]?\)/', $content, $foreignMatches);
    $foreignReferences = array_filter($foreignMatches[1]);
    
    preg_match_all('/->references\([\'"]([^\'"]+)[\'"]\)->on\([\'"]([^\'"]+)[\'"]/', $content, $explicitForeignMatches);
    $explicitForeignReferences = $explicitForeignMatches[2];
    
    $allForeignReferences = array_merge($foreignReferences, $explicitForeignReferences);
    
    foreach ($allForeignReferences as $refTable) {
        if (!in_array($refTable, $processedTables) && !in_array($refTable, $createdTables)) {
            $warnings[] = "$filename: '$refTable' tablosuna referans var ama henüz oluşturulmamış";
        }
    }
    
    $processedTables = array_merge($processedTables, $createdTables);
}

// Sonuçları göster
echo "\nDoğrulama Sonuçları:\n";
echo "===================\n";

if (empty($errors) && empty($warnings)) {
    echo "✓ Tüm migration dosyaları geçerli!\n";
    echo "✓ Bağımlılık sırası doğru!\n";
    echo "✓ Toplam " . count($tables) . " tablo tanımlandı.\n\n";
    
    echo "Tanımlanan tablolar:\n";
    foreach ($tables as $tableName => $info) {
        echo "  - $tableName ({$info['file']})\n";
    }
} else {
    if (!empty($errors)) {
        echo "HATALAR:\n";
        foreach ($errors as $error) {
            echo "  ✗ $error\n";
        }
        echo "\n";
    }
    
    if (!empty($warnings)) {
        echo "UYARILAR:\n";
        foreach ($warnings as $warning) {
            echo "  ⚠ $warning\n";
        }
        echo "\n";
    }
}

echo "Doğrulama tamamlandı.\n";
