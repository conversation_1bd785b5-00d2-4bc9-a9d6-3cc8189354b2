<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->integer('position')->default(0)->after('slug');
            $table->string('image')->nullable()->after('position');
            $table->string('icon')->nullable()->after('image');
            $table->boolean('featured')->default(false)->after('icon');
            $table->boolean('show_in_menu')->default(true)->after('featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropColumn('position');
            $table->dropColumn('image');
            $table->dropColumn('icon');
            $table->dropColumn('featured');
            $table->dropColumn('show_in_menu');
        });
    }
};
