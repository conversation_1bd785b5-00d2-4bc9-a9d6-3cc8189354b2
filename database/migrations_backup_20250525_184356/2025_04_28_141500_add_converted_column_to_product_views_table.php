<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_views', function (Blueprint $table) {
            // converted sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_views', 'converted')) {
                $table->boolean('converted')->default(0)->after('referrer_url');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_views', function (Blueprint $table) {
            // converted sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_views', 'converted')) {
                $table->dropColumn('converted');
            }
        });
    }
};
