<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_notes', function (Blueprint $table) {
            // is_private sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('order_notes', 'is_private')) {
                $table->boolean('is_private')->default(true)->after('note');
            }

            // note_type sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('order_notes', 'note_type')) {
                $table->string('note_type')->default('general')->after('note');
            }

            // status_change_from ve status_change_to sütunlarını status_before ve status_after olarak yeniden adlandır
            if (Schema::hasColumn('order_notes', 'status_change_from') && !Schema::hasColumn('order_notes', 'status_before')) {
                $table->renameColumn('status_change_from', 'status_before');
            }

            if (Schema::hasColumn('order_notes', 'status_change_to') && !Schema::hasColumn('order_notes', 'status_after')) {
                $table->renameColumn('status_change_to', 'status_after');
            }

            // is_visible_to_customer sütununu is_private ile uyumlu hale getir
            if (Schema::hasColumn('order_notes', 'is_visible_to_customer')) {
                // is_private sütunu eklendikten sonra veri güncellemesi yapılacak
                // Şimdilik sütunu kaldırmıyoruz
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_notes', function (Blueprint $table) {
            // is_private sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('order_notes', 'is_private')) {
                $table->dropColumn('is_private');
            }

            // note_type sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('order_notes', 'note_type')) {
                $table->dropColumn('note_type');
            }

            // status_before ve status_after sütunlarını status_change_from ve status_change_to olarak yeniden adlandır
            if (Schema::hasColumn('order_notes', 'status_before') && !Schema::hasColumn('order_notes', 'status_change_from')) {
                $table->renameColumn('status_before', 'status_change_from');
            }

            if (Schema::hasColumn('order_notes', 'status_after') && !Schema::hasColumn('order_notes', 'status_change_to')) {
                $table->renameColumn('status_after', 'status_change_to');
            }

            // is_visible_to_customer sütununu ekle
            if (!Schema::hasColumn('order_notes', 'is_visible_to_customer')) {
                $table->boolean('is_visible_to_customer')->default(false)->after('note');
            }
        });
    }
};
