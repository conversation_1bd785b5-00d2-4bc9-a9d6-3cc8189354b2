<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tabloyu temizle
        DB::table('search_queries')->truncate();
        
        // Örnek veriler ekle
        $queries = [
            [
                'query' => 'telefon',
                'results_count' => 25,
                'clicks_count' => 10,
                'conversion_count' => 2,
                'session_id' => 'session1',
                'ip_address' => '127.0.0.1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'query' => 'laptop',
                'results_count' => 15,
                'clicks_count' => 8,
                'conversion_count' => 3,
                'session_id' => 'session2',
                'ip_address' => '127.0.0.1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'query' => 'tablet',
                'results_count' => 10,
                'clicks_count' => 5,
                'conversion_count' => 1,
                'session_id' => 'session3',
                'ip_address' => '127.0.0.1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'query' => 'kulaklık',
                'results_count' => 30,
                'clicks_count' => 12,
                'conversion_count' => 4,
                'session_id' => 'session4',
                'ip_address' => '127.0.0.1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'query' => 'mouse',
                'results_count' => 20,
                'clicks_count' => 7,
                'conversion_count' => 2,
                'session_id' => 'session5',
                'ip_address' => '127.0.0.1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'query' => 'bulunamayan ürün',
                'results_count' => 0,
                'clicks_count' => 0,
                'conversion_count' => 0,
                'session_id' => 'session6',
                'ip_address' => '127.0.0.1',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
        
        DB::table('search_queries')->insert($queries);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Bu migrasyon geri alınamaz
    }
};
