<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('search_queries', function (Blueprint $table) {
            // clicks_count sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('search_queries', 'clicks_count')) {
                $table->integer('clicks_count')->default(0)->after('results_count');
            }
            
            // conversion_count sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('search_queries', 'conversion_count')) {
                $table->integer('conversion_count')->default(0)->after('clicks_count');
            }
            
            // user_agent sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('search_queries', 'user_agent')) {
                $table->string('user_agent')->nullable()->after('ip_address');
            }
            
            // filters sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('search_queries', 'filters')) {
                $table->json('filters')->nullable()->after('user_agent');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('search_queries', function (Blueprint $table) {
            // clicks_count sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('search_queries', 'clicks_count')) {
                $table->dropColumn('clicks_count');
            }
            
            // conversion_count sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('search_queries', 'conversion_count')) {
                $table->dropColumn('conversion_count');
            }
            
            // user_agent sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('search_queries', 'user_agent')) {
                $table->dropColumn('user_agent');
            }
            
            // filters sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('search_queries', 'filters')) {
                $table->dropColumn('filters');
            }
        });
    }
};
