<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_zone_methods', function (Blueprint $table) {
            if (!Schema::hasColumn('shipping_zone_methods', 'cost_per_desi')) {
                $table->decimal('cost_per_desi', 8, 2)->default(0)->after('cost_per_weight');
            }
            
            if (!Schema::hasColumn('shipping_zone_methods', 'min_desi')) {
                $table->decimal('min_desi', 8, 2)->default(0)->after('max_weight');
            }
            
            if (!Schema::hasColumn('shipping_zone_methods', 'max_desi')) {
                $table->decimal('max_desi', 8, 2)->default(0)->after('min_desi');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_zone_methods', function (Blueprint $table) {
            if (Schema::hasColumn('shipping_zone_methods', 'cost_per_desi')) {
                $table->dropColumn('cost_per_desi');
            }
            
            if (Schema::hasColumn('shipping_zone_methods', 'min_desi')) {
                $table->dropColumn('min_desi');
            }
            
            if (Schema::hasColumn('shipping_zone_methods', 'max_desi')) {
                $table->dropColumn('max_desi');
            }
        });
    }
};
