<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('category_attributes', function (Blueprint $table) {
            $table->boolean('is_filterable')->default(false)->after('is_required');
            $table->integer('position')->default(0)->after('is_filterable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('category_attributes', function (Blueprint $table) {
            $table->dropColumn('is_filterable');
            $table->dropColumn('position');
        });
    }
};
