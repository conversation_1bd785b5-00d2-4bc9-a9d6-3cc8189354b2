<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_companies', function (Blueprint $table) {
            if (!Schema::hasColumn('shipping_companies', 'description')) {
                $table->text('description')->nullable()->after('code');
            }
            
            if (!Schema::hasColumn('shipping_companies', 'logo')) {
                $table->string('logo')->nullable()->after('description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_companies', function (Blueprint $table) {
            if (Schema::hasColumn('shipping_companies', 'description')) {
                $table->dropColumn('description');
            }
            
            if (Schema::hasColumn('shipping_companies', 'logo')) {
                $table->dropColumn('logo');
            }
        });
    }
};
