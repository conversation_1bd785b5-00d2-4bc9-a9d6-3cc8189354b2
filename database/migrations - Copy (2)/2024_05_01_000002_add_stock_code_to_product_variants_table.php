<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_variants', function (Blueprint $table) {
            // stock_code sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_variants', 'stock_code')) {
                $table->string('stock_code')->nullable()->after('sku');
            }
            
            // slug sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_variants', 'slug')) {
                $table->string('slug')->nullable()->after('meta_keywords');
            }
            
            // meta_title sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_variants', 'meta_title')) {
                $table->string('meta_title')->nullable()->after('image');
            }
            
            // meta_description sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_variants', 'meta_description')) {
                $table->string('meta_description')->nullable()->after('meta_title');
            }
            
            // meta_keywords sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_variants', 'meta_keywords')) {
                $table->string('meta_keywords')->nullable()->after('meta_description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_variants', function (Blueprint $table) {
            // stock_code sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_variants', 'stock_code')) {
                $table->dropColumn('stock_code');
            }
            
            // slug sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_variants', 'slug')) {
                $table->dropColumn('slug');
            }
            
            // meta_title sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_variants', 'meta_title')) {
                $table->dropColumn('meta_title');
            }
            
            // meta_description sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_variants', 'meta_description')) {
                $table->dropColumn('meta_description');
            }
            
            // meta_keywords sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_variants', 'meta_keywords')) {
                $table->dropColumn('meta_keywords');
            }
        });
    }
};
