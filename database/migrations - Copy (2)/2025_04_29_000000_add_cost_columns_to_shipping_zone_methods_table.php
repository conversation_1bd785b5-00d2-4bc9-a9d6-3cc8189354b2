<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_zone_methods', function (Blueprint $table) {
            if (!Schema::hasColumn('shipping_zone_methods', 'cost_per_order_percent')) {
                $table->decimal('cost_per_order_percent', 8, 2)->default(0)->after('cost');
            }
            
            if (!Schema::hasColumn('shipping_zone_methods', 'cost_per_weight')) {
                $table->decimal('cost_per_weight', 8, 2)->default(0)->after('cost_per_order_percent');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_zone_methods', function (Blueprint $table) {
            if (Schema::hasColumn('shipping_zone_methods', 'cost_per_order_percent')) {
                $table->dropColumn('cost_per_order_percent');
            }
            
            if (Schema::hasColumn('shipping_zone_methods', 'cost_per_weight')) {
                $table->dropColumn('cost_per_weight');
            }
        });
    }
};
