<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_zone_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('zone_id')->constrained('shipping_zones')->onDelete('cascade');
            $table->foreignId('method_id')->constrained('shipping_methods')->onDelete('cascade');
            $table->decimal('cost', 10, 2)->default(0);
            $table->decimal('min_order_amount', 10, 2)->nullable();
            $table->decimal('max_order_amount', 10, 2)->nullable();
            $table->decimal('min_weight', 10, 2)->nullable();
            $table->decimal('max_weight', 10, 2)->nullable();
            $table->boolean('is_free_shipping')->default(false);
            $table->decimal('free_shipping_min_amount', 10, 2)->nullable();
            $table->integer('estimated_delivery_days')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Ensure each method appears only once per zone
            $table->unique(['zone_id', 'method_id'], 'zone_method_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_zone_methods');
    }
};
