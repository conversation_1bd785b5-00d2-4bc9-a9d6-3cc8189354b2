<?php

/**
 * This script helps clean up old migration files after verifying the new consolidated ones work correctly.
 * 
 * IMPORTANT: Only run this script after you have:
 * 1. Backed up your database
 * 2. Verified the new consolidated migrations work correctly
 * 3. Made sure you don't need the old migration files anymore
 * 
 * Usage:
 * php database/migrations/cleanup-old-migrations.php
 */

// List of consolidated migration files to keep
$filesToKeep = [
    '0001_01_01_000001_create_base_system_tables.php',
    '0001_01_01_000002_create_location_tables.php',
    '0001_01_01_000003_create_catalog_tables.php',
    '0001_01_01_000004_create_order_tables.php',
    '0001_01_01_000005_create_email_and_analytics_tables.php',
    '0001_01_01_000006_create_permission_tables.php',
    'README.md',
    'cleanup-old-migrations.php',
];

// Get all migration files
$migrationDir = __DIR__;
$allFiles = scandir($migrationDir);
$filesToDelete = [];

// Find files to delete
foreach ($allFiles as $file) {
    if ($file === '.' || $file === '..') {
        continue;
    }
    
    if (!in_array($file, $filesToKeep)) {
        $filesToDelete[] = $file;
    }
}

// Ask for confirmation
echo "The following migration files will be deleted:\n";
foreach ($filesToDelete as $file) {
    echo "- {$file}\n";
}
echo "\nTotal files to delete: " . count($filesToDelete) . "\n";
echo "Are you sure you want to delete these files? (yes/no): ";
$handle = fopen("php://stdin", "r");
$line = trim(fgets($handle));

if (strtolower($line) !== 'yes') {
    echo "Operation cancelled.\n";
    exit;
}

// Delete files
$deletedCount = 0;
foreach ($filesToDelete as $file) {
    $fullPath = $migrationDir . '/' . $file;
    if (unlink($fullPath)) {
        $deletedCount++;
        echo "Deleted: {$file}\n";
    } else {
        echo "Failed to delete: {$file}\n";
    }
}

echo "\nDeleted {$deletedCount} out of " . count($filesToDelete) . " files.\n";
echo "Cleanup completed.\n";
