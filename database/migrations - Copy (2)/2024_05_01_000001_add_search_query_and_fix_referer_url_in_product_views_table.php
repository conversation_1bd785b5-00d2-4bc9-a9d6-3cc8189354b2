<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_views', function (Blueprint $table) {
            // search_query sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_views', 'search_query')) {
                $table->string('search_query')->nullable()->after('referrer_url');
            }
            
            // referer_url sütununu ekle (eğer yoksa)
            if (!Schema::hasColumn('product_views', 'referer_url')) {
                $table->string('referer_url')->nullable()->after('user_agent');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_views', function (Blueprint $table) {
            // search_query sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_views', 'search_query')) {
                $table->dropColumn('search_query');
            }
            
            // referer_url sütununu kaldır (eğer varsa)
            if (Schema::hasColumn('product_views', 'referer_url')) {
                $table->dropColumn('referer_url');
            }
        });
    }
};
