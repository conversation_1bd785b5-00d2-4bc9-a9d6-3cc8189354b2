<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            
            // Payment identifiers
            $table->string('transaction_id')->unique()->comment('Unique transaction identifier');
            $table->unsignedBigInteger('order_id')->index()->comment('Related order ID');
            $table->unsignedBigInteger('user_id')->nullable()->index()->comment('User who made the payment');
            
            // Amount information
            $table->decimal('amount', 15, 2)->comment('Payment amount');
            $table->string('currency', 3)->default('TRY')->comment('Payment currency');
            $table->decimal('fee_amount', 15, 2)->default(0)->comment('Gateway fee amount');
            $table->decimal('total_amount', 15, 2)->comment('Total amount including fees');
            
            // Gateway information
            $table->string('gateway_name')->comment('Gateway display name');
            $table->string('gateway_provider')->index()->comment('Gateway provider identifier');
            $table->json('gateway_configuration')->nullable()->comment('Gateway configuration');
            
            // Payment details
            $table->string('payment_method')->comment('Payment method used');
            $table->string('status')->index()->comment('Payment status');
            $table->string('gateway_transaction_id')->nullable()->index()->comment('Gateway transaction ID');
            $table->json('gateway_response')->nullable()->comment('Gateway response data');
            
            // Additional information
            $table->text('description')->nullable()->comment('Payment description');
            $table->json('metadata')->nullable()->comment('Additional metadata');
            
            // Timestamps
            $table->timestamp('processed_at')->nullable()->comment('When payment was processed');
            $table->timestamp('expires_at')->nullable()->comment('When payment expires');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['gateway_provider', 'status']);
            $table->index(['user_id', 'status']);
            $table->index(['order_id', 'status']);
            $table->index(['created_at', 'status']);
            $table->index(['amount', 'currency']);
            
            // Foreign key constraints (if needed)
            // $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            // $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
