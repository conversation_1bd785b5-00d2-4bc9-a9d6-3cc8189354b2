<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Notifications Infrastructure Tables Migration
 * Bildirim altyapısı için ana tablolar
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ana bildirimler tablosu
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type', 100)->index(); // notification type (order.created, user.welcome, etc.)
            $table->string('channel', 50)->index(); // email, sms, push, in_app, webhook, etc.
            $table->string('status', 50)->default('pending')->index(); // pending, sending, sent, delivered, read, failed, retrying, cancelled, expired
            $table->string('priority', 20)->default('normal')->index(); // critical, urgent, high, medium, normal, low
            
            // Alıcı bilgileri
            $table->string('recipient_type', 50)->index(); // user, admin, system, guest, group, external
            $table->unsignedBigInteger('recipient_user_id')->nullable()->index();
            $table->string('recipient_email')->nullable()->index();
            $table->string('recipient_phone', 20)->nullable()->index();
            $table->json('recipient_data')->nullable(); // Ek alıcı bilgileri
            
            // İçerik
            $table->string('title', 500);
            $table->text('message');
            $table->json('data')->nullable(); // Ek veri
            
            // Template bilgileri
            $table->string('template_id', 100)->nullable()->index();
            $table->json('template_variables')->nullable();
            
            // Zamanlama
            $table->timestamp('scheduled_at')->nullable()->index();
            $table->timestamp('sent_at')->nullable()->index();
            $table->timestamp('read_at')->nullable()->index();
            $table->timestamp('expires_at')->nullable()->index();
            
            // Yeniden deneme
            $table->unsignedTinyInteger('retry_count')->default(0);
            $table->unsignedTinyInteger('max_retries')->default(3);
            $table->text('failure_reason')->nullable();
            
            // Teslimat denemeleri
            $table->json('delivery_attempts')->nullable();
            
            // Metadata
            $table->json('metadata')->nullable();
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // İndeksler
            $table->index(['recipient_type', 'recipient_user_id']);
            $table->index(['status', 'priority']);
            $table->index(['type', 'channel']);
            $table->index(['scheduled_at', 'status']);
            $table->index(['created_at', 'status']);
            $table->index(['retry_count', 'max_retries', 'status']);
        });

        // Bildirim kanalları konfigürasyon tablosu
        Schema::create('notification_channels', function (Blueprint $table) {
            $table->id();
            $table->string('channel', 50)->unique(); // email, sms, push, etc.
            $table->string('name', 100);
            $table->text('description')->nullable();
            $table->boolean('is_enabled')->default(true)->index();
            $table->json('config')->nullable(); // Kanal özel konfigürasyonları
            $table->json('rate_limits')->nullable(); // Rate limiting ayarları
            $table->unsignedInteger('daily_limit')->nullable();
            $table->unsignedInteger('hourly_limit')->nullable();
            $table->unsignedInteger('minute_limit')->nullable();
            $table->timestamps();
            
            $table->index(['is_enabled', 'channel']);
        });

        // Bildirim şablonları tablosu
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('template_id', 100)->unique();
            $table->string('name', 200);
            $table->text('description')->nullable();
            $table->string('type', 100)->index(); // notification type
            $table->string('channel', 50)->index(); // hangi kanal için
            $table->string('language', 10)->default('tr')->index();
            $table->string('subject', 500)->nullable(); // Email için subject
            $table->text('title_template'); // Başlık şablonu
            $table->text('message_template'); // Mesaj şablonu
            $table->json('variables')->nullable(); // Kullanılabilir değişkenler
            $table->json('default_values')->nullable(); // Varsayılan değerler
            $table->boolean('is_active')->default(true)->index();
            $table->unsignedInteger('version')->default(1);
            $table->timestamps();
            
            $table->index(['type', 'channel', 'language']);
            $table->index(['is_active', 'channel']);
        });

        // Bildirim kuyruk tablosu (öncelik sıralaması için)
        Schema::create('notification_queue', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('notification_id')->index();
            $table->string('priority', 20)->index();
            $table->string('channel', 50)->index();
            $table->timestamp('scheduled_for')->index();
            $table->timestamp('processing_started_at')->nullable();
            $table->unsignedTinyInteger('attempts')->default(0);
            $table->text('last_error')->nullable();
            $table->timestamps();
            
            $table->foreign('notification_id')->references('id')->on('notifications')->onDelete('cascade');
            $table->index(['scheduled_for', 'priority']);
            $table->index(['channel', 'scheduled_for']);
        });

        // Bildirim teslimat geçmişi tablosu
        Schema::create('notification_delivery_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('notification_id')->index();
            $table->string('channel', 50)->index();
            $table->string('status', 50)->index(); // sent, delivered, failed, bounced, etc.
            $table->text('response')->nullable(); // Provider response
            $table->string('external_id')->nullable()->index(); // Provider'dan gelen ID
            $table->json('metadata')->nullable();
            $table->timestamp('attempted_at');
            $table->timestamp('completed_at')->nullable();
            $table->unsignedInteger('duration_ms')->nullable(); // Teslimat süresi (milisaniye)
            
            $table->foreign('notification_id')->references('id')->on('notifications')->onDelete('cascade');
            $table->index(['notification_id', 'attempted_at']);
            $table->index(['channel', 'status']);
        });

        // Bildirim okuma geçmişi tablosu
        Schema::create('notification_read_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('notification_id')->index();
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('read_at');
            
            $table->foreign('notification_id')->references('id')->on('notifications')->onDelete('cascade');
            $table->index(['notification_id', 'read_at']);
            $table->index(['user_id', 'read_at']);
        });

        // Bildirim tercihleri tablosu
        Schema::create('notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('type', 100)->index(); // notification type
            $table->string('channel', 50)->index();
            $table->boolean('is_enabled')->default(true);
            $table->json('settings')->nullable(); // Kanal özel ayarlar
            $table->timestamps();
            
            $table->unique(['user_id', 'type', 'channel']);
            $table->index(['user_id', 'is_enabled']);
        });

        // Bildirim rate limiting tablosu
        Schema::create('notification_rate_limits', function (Blueprint $table) {
            $table->id();
            $table->string('key', 200)->index(); // user_id:channel, ip:channel, etc.
            $table->string('channel', 50)->index();
            $table->unsignedInteger('count')->default(1);
            $table->timestamp('window_start');
            $table->timestamp('window_end');
            $table->timestamps();
            
            $table->index(['key', 'window_end']);
            $table->index(['channel', 'window_end']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_rate_limits');
        Schema::dropIfExists('notification_preferences');
        Schema::dropIfExists('notification_read_logs');
        Schema::dropIfExists('notification_delivery_logs');
        Schema::dropIfExists('notification_queue');
        Schema::dropIfExists('notification_templates');
        Schema::dropIfExists('notification_channels');
        Schema::dropIfExists('notifications');
    }
};
