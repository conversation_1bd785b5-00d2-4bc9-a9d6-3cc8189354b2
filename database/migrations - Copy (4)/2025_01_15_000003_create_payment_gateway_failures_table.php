<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateway_failures', function (Blueprint $table) {
            $table->id();
            
            // Payment reference
            $table->unsignedBigInteger('payment_id')->index()->comment('Related payment ID');
            
            // Gateway information
            $table->string('gateway_provider')->index()->comment('Gateway provider identifier');
            
            // Failure details
            $table->string('failure_reason')->comment('Failure reason');
            $table->string('error_code')->nullable()->index()->comment('Gateway error code');
            $table->json('gateway_response')->nullable()->comment('Full gateway response');
            
            // Payment details
            $table->decimal('amount', 15, 2)->comment('Payment amount');
            $table->string('currency', 3)->comment('Payment currency');
            
            // User information
            $table->unsignedBigInteger('user_id')->nullable()->index()->comment('User who attempted payment');
            
            // Request information
            $table->string('ip_address')->nullable()->index()->comment('Client IP address');
            $table->text('user_agent')->nullable()->comment('Client user agent');
            
            // Additional tracking
            $table->json('metadata')->nullable()->comment('Additional failure metadata');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes
            $table->index(['gateway_provider', 'error_code']);
            $table->index(['gateway_provider', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['created_at', 'gateway_provider']);
            
            // Foreign key constraints
            $table->foreign('payment_id')->references('id')->on('payments')->onDelete('cascade');
            // $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateway_failures');
    }
};
