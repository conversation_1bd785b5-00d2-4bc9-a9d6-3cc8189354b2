<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Shipping companies table - Base shipping providers
        Schema::create('shipping_companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->boolean('status')->default(true);
            $table->text('description')->nullable(); // Added from later migrations
            $table->string('logo')->nullable(); // Added from later migrations
            $table->timestamps();
        });

        // Shipping zones table - Geographic shipping zones
        Schema::create('shipping_zones', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();
        });

        // Shipping zone locations table - Links zones with locations
        Schema::create('shipping_zone_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_zone_id')->constrained()->onDelete('cascade');
            $table->enum('location_type', ['country', 'state', 'city']);
            $table->unsignedBigInteger('location_id');
            $table->timestamps();
            
            $table->unique(['shipping_zone_id', 'location_type', 'location_id'], 'unique_zone_location');
        });

        // Shipping methods table - Available shipping methods
        Schema::create('shipping_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->boolean('status')->default(true);
            $table->foreignId('shipping_company_id')->nullable()->constrained()->onDelete('set null'); // Added from later migrations
            $table->string('delivery_time')->nullable(); // Added from later migrations
            $table->timestamps();
        });

        // Shipping zone methods table - Links zones with methods and pricing
        Schema::create('shipping_zone_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_zone_id')->constrained()->onDelete('cascade');
            $table->foreignId('shipping_method_id')->constrained()->onDelete('cascade');
            $table->decimal('base_cost', 10, 2)->default(0.00);
            $table->decimal('per_kg_cost', 10, 2)->default(0.00); // Added from later migrations
            $table->decimal('per_desi_cost', 10, 2)->default(0.00); // Added from later migrations
            $table->decimal('min_cost', 10, 2)->default(0.00); // Added from later migrations
            $table->decimal('max_cost', 10, 2)->nullable(); // Added from later migrations
            $table->decimal('free_shipping_threshold', 10, 2)->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();
            
            $table->unique(['shipping_zone_id', 'shipping_method_id'], 'unique_zone_method');
        });

        // Shipping zone method rates table - Detailed rate structures
        Schema::create('shipping_zone_method_rates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_zone_method_id')->constrained()->onDelete('cascade');
            $table->decimal('min_weight', 8, 2)->default(0.00);
            $table->decimal('max_weight', 8, 2)->nullable();
            $table->decimal('min_price', 10, 2)->default(0.00);
            $table->decimal('max_price', 10, 2)->nullable();
            $table->decimal('cost', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_zone_method_rates');
        Schema::dropIfExists('shipping_zone_methods');
        Schema::dropIfExists('shipping_methods');
        Schema::dropIfExists('shipping_zone_locations');
        Schema::dropIfExists('shipping_zones');
        Schema::dropIfExists('shipping_companies');
    }
};
