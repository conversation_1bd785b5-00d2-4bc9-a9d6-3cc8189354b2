<?php

/**
 * Migration Yapısını Yenileme Script'i
 * 
 * Bu script eski migration dosyalarını yedekley<PERSON>, yeni düzenli migration dosyalarını aktif hale getirir.
 * 
 * UYARI: Bu script'i çalıştırmadan önce:
 * 1. Veritabanınızı yedekleyin
 * 2. Mevcut migration durumunu kontrol edin
 * 3. Test ortamında deneyin
 * 
 * Kullanım:
 * php database/migrations_new/migrate_to_new_structure.php
 */

$rootDir = dirname(__DIR__, 2);
$oldMigrationsDir = $rootDir . '/database/migrations';
$newMigrationsDir = $rootDir . '/database/migrations_new';
$backupDir = $rootDir . '/database/migrations_backup_' . date('Ymd_His');

echo "Migration Yapısı Yenileme Script'i\n";
echo "==================================\n\n";

// Backup dizini oluştur
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "✓ Backup dizini oluşturuldu: $backupDir\n";
}

// Eski migration dosyalarını backup'la
$oldFiles = glob($oldMigrationsDir . '/*.php');
$oldFiles = array_merge($oldFiles, glob($oldMigrationsDir . '/*.md'));

foreach ($oldFiles as $file) {
    $filename = basename($file);
    $backupFile = $backupDir . '/' . $filename;
    
    if (copy($file, $backupFile)) {
        echo "✓ Yedeklendi: $filename\n";
    } else {
        echo "✗ Yedekleme hatası: $filename\n";
        exit(1);
    }
}

echo "\n" . count($oldFiles) . " dosya yedeklendi.\n\n";

// Eski migration dosyalarını sil
echo "Eski migration dosyaları siliniyor...\n";
foreach ($oldFiles as $file) {
    if (unlink($file)) {
        echo "✓ Silindi: " . basename($file) . "\n";
    } else {
        echo "✗ Silme hatası: " . basename($file) . "\n";
    }
}

// Yeni migration dosyalarını kopyala
echo "\nYeni migration dosyaları kopyalanıyor...\n";
$newFiles = glob($newMigrationsDir . '/*.php');
$newFiles = array_merge($newFiles, glob($newMigrationsDir . '/*.md'));

foreach ($newFiles as $file) {
    $filename = basename($file);
    $targetFile = $oldMigrationsDir . '/' . $filename;
    
    if (copy($file, $targetFile)) {
        echo "✓ Kopyalandı: $filename\n";
    } else {
        echo "✗ Kopyalama hatası: $filename\n";
        exit(1);
    }
}

echo "\n" . count($newFiles) . " yeni dosya kopyalandı.\n\n";

echo "Migration yapısı başarıyla yenilendi!\n\n";

echo "Sonraki adımlar:\n";
echo "1. Migration durumunu kontrol edin: php artisan migrate:status\n";
echo "2. Gerekirse migration'ları sıfırlayın: php artisan migrate:reset\n";
echo "3. Yeni migration'ları çalıştırın: php artisan migrate\n";
echo "4. Veritabanı yapısını kontrol edin\n\n";

echo "Backup dosyaları: $backupDir\n";
echo "Yeni migration dosyaları: $oldMigrationsDir\n\n";

echo "UYARI: Migration'ları çalıştırmadan önce veritabanınızı yedeklediğinizden emin olun!\n";
