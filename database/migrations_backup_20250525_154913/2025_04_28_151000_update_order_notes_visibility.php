<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_notes', function (Blueprint $table) {
            // is_visible_to_customer sütununu is_private ile uyumlu hale getir
            if (Schema::hasColumn('order_notes', 'is_visible_to_customer') && Schema::hasColumn('order_notes', 'is_private')) {
                // Önce mevcut verileri güncelle
                DB::statement('UPDATE order_notes SET is_private = NOT is_visible_to_customer');
                
                // Sonra sütunu kaldır
                $table->dropColumn('is_visible_to_customer');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_notes', function (Blueprint $table) {
            // is_visible_to_customer sütununu geri ekle
            if (!Schema::hasColumn('order_notes', 'is_visible_to_customer') && Schema::hasColumn('order_notes', 'is_private')) {
                $table->boolean('is_visible_to_customer')->default(false)->after('note');
                
                // Verileri geri güncelle
                DB::statement('UPDATE order_notes SET is_visible_to_customer = NOT is_private');
            }
        });
    }
};
