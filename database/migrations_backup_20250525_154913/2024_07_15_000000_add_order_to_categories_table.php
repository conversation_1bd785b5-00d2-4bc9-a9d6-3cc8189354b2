<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            if (!Schema::hasColumn('categories', 'order')) {
                $table->integer('order')->default(0)->after('position');
            }
        });

        // Copy position values to order for existing records
        if (Schema::hasColumn('categories', 'position') && Schema::hasColumn('categories', 'order')) {
            DB::statement('UPDATE categories SET `order` = `position`');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            if (Schema::hasColumn('categories', 'order')) {
                $table->dropColumn('order');
            }
        });
    }
};
