# Migration Structure

This directory contains the database migration files for the application. The migrations have been organized and consolidated based on their dependencies and logical groupings.

## Migration Files

The migrations are organized in the following order:

1. **0001_01_01_000001_create_base_system_tables.php**
   - Users, sessions, cache, jobs tables
   - Core system tables required by Laravel

2. **0001_01_01_000002_create_location_tables.php**
   - Countries, states, cities tables
   - Geographic location data

3. **0001_01_01_000003_create_catalog_tables.php**
   - Categories, products, attributes, variants tables
   - Product catalog management

4. **0001_01_01_000004_create_order_tables.php**
   - Orders, order items, carts, coupons tables
   - E-commerce order management

5. **0001_01_01_000005_create_email_and_analytics_tables.php**
   - Email templates, logs, settings tables
   - Analytics for search and product views

6. **0001_01_01_000006_create_permission_tables.php**
   - Roles and permissions tables (Spatie package)
   - User access control

## Running Migrations

To run all migrations in the correct order:

```bash
php artisan migrate
```

To rollback all migrations:

```bash
php artisan migrate:rollback
```

To refresh all migrations (rollback and migrate again):

```bash
php artisan migrate:refresh
```

## Migration Dependencies

The migrations have been ordered to respect their dependencies:

- Base system tables have no dependencies
- Location tables depend on base system tables
- Catalog tables depend on location tables
- Order tables depend on catalog and location tables
- Email and analytics tables depend on base system tables
- Permission tables depend on base system tables

## Notes

- These consolidated migrations replace the individual migration files that were previously in this directory
- The consolidated approach reduces the number of migration files and ensures proper dependency ordering
- Each migration file contains related tables grouped by functionality
- The down() methods in each migration drop tables in the reverse order of creation to respect foreign key constraints
