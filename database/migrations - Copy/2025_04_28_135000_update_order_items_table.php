<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            // name sütununu product_name olarak yeniden adlandır
            if (Schema::hasColumn('order_items', 'name') && !Schema::hasColumn('order_items', 'product_name')) {
                $table->renameColumn('name', 'product_name');
            }
            
            // Eğer name sütunu yoksa ve product_name sütunu da yoksa, product_name sütununu ekle
            if (!Schema::hasColumn('order_items', 'name') && !Schema::hasColumn('order_items', 'product_name')) {
                $table->string('product_name')->after('product_variant_id');
            }
            
            // unit_price sütununu price olarak yeniden adlandır
            if (Schema::hasColumn('order_items', 'unit_price') && !Schema::hasColumn('order_items', 'price')) {
                $table->renameColumn('unit_price', 'price');
            }
            
            // Eğer unit_price sütunu yoksa ve price sütunu da yoksa, price sütununu ekle
            if (!Schema::hasColumn('order_items', 'unit_price') && !Schema::hasColumn('order_items', 'price')) {
                $table->decimal('price', 10, 2)->after('product_name');
            }
            
            // total_price sütununu subtotal olarak yeniden adlandır
            if (Schema::hasColumn('order_items', 'total_price') && !Schema::hasColumn('order_items', 'subtotal')) {
                $table->renameColumn('total_price', 'subtotal');
            }
            
            // Eğer total_price sütunu yoksa ve subtotal sütunu da yoksa, subtotal sütununu ekle
            if (!Schema::hasColumn('order_items', 'total_price') && !Schema::hasColumn('order_items', 'subtotal')) {
                $table->decimal('subtotal', 10, 2)->after('quantity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            // Bu sütunları silmek istemiyoruz, çünkü veri kaybına neden olabilir
        });
    }
};
