<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Products table - Main product information
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->decimal('original_price', 10, 2)->nullable();
            $table->integer('stock')->default(0);
            $table->string('stock_code')->nullable()->unique();
            $table->boolean('status')->default(true);
            $table->boolean('is_featured')->default(false); // Added from later migrations
            $table->boolean('is_on_sale')->default(false); // Added from later migrations
            $table->decimal('sale_price', 10, 2)->nullable(); // Added from later migrations
            $table->timestamp('sale_starts_at')->nullable(); // Added from later migrations
            $table->timestamp('sale_ends_at')->nullable(); // Added from later migrations
            $table->decimal('desi', 8, 2)->nullable(); // Added from later migrations
            $table->unsignedBigInteger('category_id')->nullable();
            $table->string('image')->nullable();
            $table->integer('view_count')->default(0);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
        });

        // Product attributes table - Links products with their attributes
        Schema::create('product_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('attribute_id')->constrained()->onDelete('cascade');
            $table->boolean('is_variant_generator')->default(false);
            $table->boolean('is_required')->default(false);
            $table->timestamps();
            
            $table->unique(['product_id', 'attribute_id']);
        });

        // Product variants table - Product variations based on attributes
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('sku')->unique();
            $table->string('stock_code')->nullable(); // Added from later migrations
            $table->json('attribute_values');
            $table->decimal('additional_price', 10, 2)->default(0.00);
            $table->integer('stock')->default(0);
            $table->enum('status', ['in_stock', 'out_of_stock', 'disabled'])->default('out_of_stock');
            $table->string('image')->nullable();
            $table->boolean('is_default')->default(false);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('slug')->nullable();
            $table->timestamps();
        });

        // Product views table - Analytics for product views
        Schema::create('product_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->string('referrer_url')->nullable();
            $table->string('referer_url')->nullable(); // Added from later migrations
            $table->string('search_query')->nullable(); // Added from later migrations
            $table->boolean('converted')->default(false); // Added from later migrations
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_views');
        Schema::dropIfExists('product_variants');
        Schema::dropIfExists('product_attributes');
        Schema::dropIfExists('products');
    }
};
